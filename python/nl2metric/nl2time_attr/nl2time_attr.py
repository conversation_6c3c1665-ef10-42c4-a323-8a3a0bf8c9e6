from datetime import datetime, timedelta
from typing import Optional, Union

from colorama import Fore, Style
from dateutil.relativedelta import relativedelta
from pydantic import BaseModel

from common.llm.general import create_chat_model, create_chat_model_in_chain
from common.logging.logger import get_logger
from common.prompt_selector.prompt_selector_base import PromptSelectorBase
from common.types.base import ParamsExtractStage
from common.utils.json_utils import extract_json_from_string
from common.types import RelativeTimeFunction, SpecificTimeFunction
from langchain_core.runnables import Runnable, RunnableLambda
from langchain_core.runnables.utils import Input, Output
from langchain_core.output_parsers import StrOutputParser

logger = get_logger(__name__)


def is_later_than(compareTime: dict, baseTime: dict) -> bool:
    compare_time = time_param_to_date(compareTime)
    baseTime = time_param_to_date(baseTime)
    return compare_time > baseTime


def time_param_to_date(time_att: dict) -> datetime:
    try:
        return _time_param_to_date(time_att)
    except Exception as e:
        msg = f"attribute analysis time_param_to_date failed {e}, input is {time_att}"
        logger.error(msg)
        raise RuntimeError(e)


def _time_param_to_date(time_att: dict) -> datetime:
    today = datetime.now()
    if time_att["type"].startswith("recent"):
        if time_att["type"] == "recentDays" and time_att["days"] is not None:
            data_time = today - timedelta(days=time_att["days"])
        elif time_att["type"] == "recentMonths" and time_att["months"] is not None:
            data_time = today - relativedelta(months=time_att["months"])
        elif time_att["type"] == "recentQuarters" and time_att["quarters"] is not None:
            data_time = today - relativedelta(months=time_att["quarters"] * 3)
        else:  # params['type'] == "recentYears" and params['years'] is not None:
            data_time = today - relativedelta(years=time_att["years"])
    else:
        if time_att["type"] == "specificDate":
            data_time = datetime(time_att["year"], time_att["month"], time_att["day"])
        elif time_att["type"] == "specificMonth":
            data_time = datetime(time_att["year"], time_att["month"], 1)
        elif time_att["type"] == "specificQuarter":
            # Calculate the start month of the quarter
            start_month = (time_att["quarter"] - 1) * 3 + 1
            data_time = datetime(time_att["year"], start_month, 1)
        else:  # params['type'] == "specificYear":
            data_time = datetime(time_att["year"], 1, 1)
    return data_time


def redefine_time(time_in_params: dict) -> dict:
    print(f"Attribution time reference, time_in_params={time_in_params}.")
    time_in_params["baseTime"], time_in_params["compareTime"] = (
        time_in_params["compareTime"],
        time_in_params["baseTime"],
    )
    return time_in_params


class AnalysisTimeQueryParams(BaseModel):
    baseTime: Optional[Union[RelativeTimeFunction, SpecificTimeFunction]]
    compareTime: Optional[Union[RelativeTimeFunction, SpecificTimeFunction]]


# 调用大模型
# invoke with question
def call_nl2time_attr(
    model_type: str, prompt_selector: PromptSelectorBase
) -> Runnable[Input, Output]:
    chain = (
        RunnableLambda(
            prompt_selector.gen_prompt,
            name="PromptSelectorBase.gen_prompt:"
            + ParamsExtractStage.ATTR_ANALYSIS_TIME,
        ).bind(stage=ParamsExtractStage.ATTR_ANALYSIS_TIME)
        | RunnableLambda(create_chat_model_in_chain).bind(
            stage=ParamsExtractStage.ATTR_ANALYSIS_TIME
        )
        | StrOutputParser()
        | RunnableLambda(parse_time_attr_response, name="parse_time_attr_response")
    )
    chain.name = ParamsExtractStage.ATTR_ANALYSIS_TIME
    return chain


# 解析响应
def parse_time_attr_response(
    response: str,
) -> Optional[AnalysisTimeQueryParams]:
    if response is None:
        raise RuntimeError("attribute analysis time attr got none resp")
    try:
        obj = extract_json_from_string(response, "parse_time_attr_response")
        if obj and isinstance(obj, dict):
            # if ("baseTime" not in obj and "compareTime" in obj) or ("compareTime" not in obj and "baseTime" in obj):
            #     logger.error(f"attribute analysis parse time found null result {obj}")
            #     raise RuntimeError(f"attribute analysis parse time found null result {obj}")
            # 使用 Pydantic 模型进行验证
            if obj.get("compareTime") and obj.get("baseTime"):
                validity = is_later_than(obj["compareTime"], obj["baseTime"])
                if not validity:
                    obj = redefine_time(obj)
                param = AnalysisTimeQueryParams(**obj)
            elif obj.get("compareTime") and not obj.get("baseTime"):
                obj = {"baseTime": None, "compareTime": obj.get("compareTime")}
                param = AnalysisTimeQueryParams(**obj)
            elif not obj.get("compareTime") and obj.get("baseTime"):
                obj = {"baseTime": None, "compareTime": obj.get("baseTime")}
                param = AnalysisTimeQueryParams(**obj)
            else:
                param = None
        else:
            param = None
    except Exception as e:
        raise RuntimeError(
            f"attribute analysis parse time failed {e}, response is {response}"
        )
    # if param is None:
    #     raise RuntimeError(f"attribute analysis parse time got none, response is {response}")
    return param
