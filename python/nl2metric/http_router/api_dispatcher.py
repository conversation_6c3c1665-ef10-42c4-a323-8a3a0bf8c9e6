from common.types.dispatcher import MultiProcessingDispatcherBase
from common.utils.concurrent_utils import do_once
from config import app_config
from enum import Enum


class ApiType(Enum):
    params_extract_handler = 1
    project_params_extract_handler = 2
    agent_handler = 3
    agent_handler_after_manual_select = 4


class ApiDispatcher(MultiProcessingDispatcherBase):
    @classmethod
    def _worker_start(cls):
        from main import start

        start(False)

    @classmethod
    def _process_task(cls, task):
        req_type, args, kwargs = task
        if req_type == ApiType.params_extract_handler:
            from http_router.api.params_extract_handler import params_extract_handler

            return params_extract_handler(*args, **kwargs)
        elif req_type == ApiType.project_params_extract_handler:
            from http_router.api.project_params_extract_handler import (
                project_params_extract_handler,
            )

            return project_params_extract_handler(*args, **kwargs)
        elif req_type == ApiType.agent_handler:
            from http_router.api.agent_handler import agent_handler

            return agent_handler(*args, **kwargs)
        elif req_type == ApiType.agent_handler_after_manual_select:
            from http_router.api.agent_handler_after_manual_select import (
                agent_handler_after_manual_select,
            )

            return agent_handler_after_manual_select(*args, **kwargs)
        raise RuntimeError(f"req_type {req_type} not supported")

    def submit_task(self, req_type, trace_id, use_asyncio=True, *args, **kwargs):
        return super().submit_task((req_type, args, kwargs), trace_id, use_asyncio)


# 不要在dispatcher进程中调用get_api_dispatcher
# 按照当前的实现，这会导致创建一个新的dispatcher进程池
# 即使修改代码把任务提交到非本进程的其它dispatcher worker中，也极易发生“互相等待”的死锁
@do_once
def get_api_dispatcher():
    if not app_config.ENABLE_API_WORKER:
        return None
    return ApiDispatcher(app_config.API_WORKER_NUM, app_config.API_WORKER_THREAD_NUM)
