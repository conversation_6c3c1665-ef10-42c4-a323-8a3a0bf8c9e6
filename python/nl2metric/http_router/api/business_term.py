import csv
import json
import time
from datetime import datetime
from typing import List, Optional

import pandas as pd
from fastapi import APIRouter, Query, HTTPException, Body, UploadFile, File
from pydantic import BaseModel, Field
from starlette.responses import FileResponse

from vector_store.data_models import TermTypeEnum, PROJECT_HINT
from vector_store.hint_search_engine import (
    QueryResult,
    hint_search_engine,
    BusinessTermBase,
)


class BaseResponse(BaseModel):
    code: int = 0
    message: str = "success"


business_term_router = APIRouter(prefix="/api/v1/business_term")

output_fields = [
    "id",
    "text",
    "type",
    "creator",
    "created_at",
    "updated_at",
    "semantic_project_id",
    "semantic_scene_id",
    "agents",
    "tags",
    "extra_info",
]


class ListTermsResp(BaseResponse):
    data: List[BusinessTermBase] = []
    total: int = 0


@business_term_router.get("/", response_model=ListTermsResp)
async def list_terms(
    page: int = Query(1, ge=1, description="分页页码"),
    page_size: int = Query(10, ge=1, le=1000, alias="pageSize"),
    semantic_project_id: str = Query(
        "", description="语义项目ID", alias="semanticProjectId"
    ),
    semantic_scene_id: str = Query("", description="语义场景ID", alias="semanticSceneId"),
):
    offset = (page - 1) * page_size
    filter_list = []
    if semantic_project_id:
        filter_list.append(f"semantic_project_id == '{semantic_project_id}'")
    if semantic_scene_id:
        filter_list.append(f"semantic_scene_id == '{semantic_scene_id}'")
    filters = " and ".join(filter_list) if filter_list else None
    if not semantic_project_id and not semantic_scene_id:
        filters = None
    q: QueryResult = hint_search_engine.query(
        limit=page_size,
        offset=offset,
        filter=filters,
        output_fields=output_fields,
    )

    total: QueryResult = hint_search_engine.query(
        output_fields=["count(*)"],
        filter=filters,
    )

    return ListTermsResp(
        data=[BusinessTermBase(**item) for item in q.items],
        total=total.items[0]["count(*)"] if total else 0,
    )


# 单条创建
@business_term_router.post(
    "/upsert",
    response_model=ListTermsResp,
    status_code=201,
    summary="创建/更新业务术语",
)
async def create_term(
    term_in: BusinessTermBase,
):
    now = int(datetime.now().timestamp())
    term = BusinessTermBase(
        **term_in.model_dump(),
    )

    if term_in.id is not None:
        # 如果提供了ID，检查记录是否存在
        existing = hint_search_engine.get_by_ids(
            ids=[term_in.id], output_fields=["id", "created_at"]
        )
        if existing:
            # 如果记录存在，保留原有的created_at
            term.created_at = existing[0].get("created_at")
        else:
            # 如果记录不存在，设置新的created_at
            term.created_at = now
    else:
        # 如果没有提供ID，设置新的created_at
        term.created_at = now
    term.updated_at = now
    hint_search_engine.upsert([term])
    return ListTermsResp(data=[term])


@business_term_router.post(
    "/batch/get_by_ids", status_code=200, response_model=ListTermsResp
)
async def fetch_terms_by_ids(ids: List[str]) -> ListTermsResp:
    # 假设存在一个函数，根据ID列表获取对应的BusinessTerm对象
    # 这里仅作为示例，实际应用中需要根据实际情况实现
    terms = hint_search_engine.get_by_ids(
        ids,
        output_fields=output_fields,
    )

    return ListTermsResp(data=[BusinessTermBase(**item) for item in terms])


### 批量操作
# 批量删除
@business_term_router.post(
    "/batch/delete",
    status_code=200,
    response_model=BaseResponse,
)
async def batch_delete_ids(ids: List[str] = Body(...)):
    if len(set(ids)) != len(ids):
        raise ValueError("ID重复")
    hint_search_engine.delete(ids)
    return BaseResponse()


@business_term_router.get(
    "/download",
    status_code=200,
    response_class=FileResponse,
)
async def download_csv(
    semantic_project_id: str = Query(
        "", description="语义项目ID", alias="semanticProjectId"
    ),
    semantic_scene_id: str = Query("", description="语义场景ID", alias="semanticSceneId"),
    collection_name=Query("", description="集合名称"),
):
    filters = []
    if semantic_project_id:
        filters.append(f"semantic_project_id == '{semantic_project_id}'")
    if semantic_scene_id:
        filters.append(f"semantic_scene_id == '{semantic_scene_id}'")
    df = pd.DataFrame()
    filter_expr = " and ".join(filters)
    for item in hint_search_engine.query_iterator(
        collection_name=collection_name,
        filter=filter_expr,
        output_fields=output_fields,
    ):
        item = {
            k: json.dumps(v, ensure_ascii=False, indent=2)
            if isinstance(v, (list, dict, tuple))
            else str(v)
            for k, v in item.items()
        }
        row_df = pd.DataFrame(item, index=[0])
        df = pd.concat([df, row_df], ignore_index=True)
    file = f"{time.time()}.csv"
    df.to_csv(file, index=False)
    return FileResponse(file, media_type="text/csv", filename=file)


# CSV批量导入（兼容指标模型模块）
@business_term_router.post(
    "/batch/upload",
    status_code=201,
    response_model=ListTermsResp,
)
async def batch_upload(
    file: UploadFile = File(..., description="CSV格式的术语文件"),
):
    # 检查文件扩展名
    if not file.filename.endswith(".csv"):
        raise HTTPException(status_code=400, detail="只支持CSV文件格式")

    try:
        # 读取CSV内容
        content = await file.read()
        text_content = content.decode("utf-8")
        csv_reader = csv.DictReader(text_content.splitlines())

        # 验证CSV头部字段
        required_fields = {
            "text",
            "type",
            "creator",
            "tags",
            "semantic_project_id",
            "semantic_scene_id",
            "extra_info",
            "agents",
        }
        if not required_fields.issubset(csv_reader.fieldnames):
            raise HTTPException(
                status_code=400,
                detail="CSV文件必须包含text、type、creator 、tags、semantic_project_id、semantic_scene_id 和 extra_info字段",
            )

        now = int(datetime.now().timestamp())
        terms = []
        term_types = {e.value for e in TermTypeEnum}
        for row in csv_reader:
            # 验证type字段
            term_type = row.get("type", TermTypeEnum.DYNAMIC)
            if term_type not in term_types:
                term_type = TermTypeEnum.DYNAMIC

                # 构建术语对象
            term = {
                "text": row["text"],
                "type": term_type,
                "creator": row["creator"],
                "semantic_project_id": row["semantic_project_id"],
                "semantic_scene_id": row["semantic_scene_id"],
                "agents": json.loads(row["agents"]),
                "tags": json.loads(row["tags"]),
                "extra_info": row["extra_info"],
                "updated_at": now,
                "created_at": now,
            }

            # 如果提供了ID，则处理ID
            if "id" in row and row["id"]:
                try:
                    term["id"] = row["id"]
                    # 检查ID是否存在
                    existing = hint_search_engine.get_by_ids(
                        ids=[term["id"]], output_fields=["created_at"]
                    )
                    if existing:
                        term["created_at"] = existing[0].get("created_at")
                except ValueError:
                    continue
            terms.append(BusinessTermBase(**term))

        if not terms:
            raise HTTPException(status_code=400, detail="未找到有效的术语数据")

        # 批量插入数据
        hint_search_engine.upsert([t for t in terms])

        # 返回插入的数据
        return ListTermsResp(data=terms)

    except UnicodeDecodeError:
        raise HTTPException(status_code=400, detail="文件编码错误，请使用UTF-8编码")
    except csv.Error:
        raise HTTPException(status_code=400, detail="CSV文件格式错误")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"服务器处理错误: {str(e)}")


class SearchTermReq(BaseModel):
    pattern: str = Field(..., description="搜索关键词")
    limit: int = Field(10, description="搜索结果数量")
    semantic_project_id: Optional[str] = Field(
        None, description="语义项目ID", alias="semanticProjectId"
    )
    semantic_scene_id: Optional[str] = Field(
        None, description="语义场景ID", alias="semanticSceneId"
    )
    sparse_weight: float = Field(0.8, description="稀疏权重")
    dense_weight: float = Field(0.2, description="稠密权重")


class SearchData(BusinessTermBase):
    distance: Optional[float]


class SearchTermResp(ListTermsResp):
    data: List[SearchData] = []


# 在列表接口中添加搜索参数
@business_term_router.post(
    "/search_terms", response_model=SearchTermResp, summary="业务术语查询"
)
def search_terms(req: SearchTermReq):
    filter_list = []
    if req.semantic_project_id:
        filter_list.append(f"semantic_project_id == '{req.semantic_project_id}'")
    semantic_scene_ids = [PROJECT_HINT]
    if req.semantic_scene_id != PROJECT_HINT and req.semantic_scene_id:
        semantic_scene_ids.append(req.semantic_scene_id)
    filter_list.append(f"semantic_scene_id in {semantic_scene_ids}")

    filters = " and ".join(filter_list) if filter_list else None
    if req.sparse_weight + req.dense_weight != 1:
        raise ValueError("sparse_weight + dense_weight != 1")
    results = hint_search_engine.hybrid_search(
        query_text=req.pattern,
        filter=filters,
        limit=req.limit,
        sparse_weight=req.sparse_weight,
        dense_weight=req.dense_weight,
        output_fields=output_fields,
    )
    return SearchTermResp(
        data=[
            SearchData(**item["entity"], distance=item["distance"])
            for item in results.items
        ]
    )
