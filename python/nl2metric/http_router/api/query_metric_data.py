from backend_stage_reporter.reporter import get_reporter_cb
from common.logging.logger import get_logger
from common.prompt_selector.json_prompt_selector import MemPromptSelector
from common.trace import tracer
from common.types import ParamsExtractData
from common.types.base import (
    JobType,
    gen_chain_meta,
    CHAIN_META,
    ChainMeta,
    ChainRuntime,
)
from common.types.callback_handler import LogCallbackHandler
from config import app_config
from http_router.api.params_extract_handler import (
    params_extract_nl2metric_postprocess,
    params_extract_handler_postprocess,
)
from langchain_core.runnables import RunnableLambda, RunnableConfig
from nl2metric.service import nl2metric
from pre_filter.retrive import call_retrive
from vector_store.data_models import AgentType

logger = get_logger(__name__)


def query_metric_data_postprocess(
    response_data: ParamsExtractData, require_metric_num: int, config: RunnableConfig
):
    query_metric = response_data.query_metric
    assert query_metric != None
    if require_metric_num > 0:
        if (
            not query_metric.metricNames
            # calculator时要求查数结果只能有一个指标，且必须有一个指标
            # 上述规则容易出错，还是不要要求这么严格
            or len(query_metric.metricNames) < require_metric_num
        ):
            raise RuntimeError(
                f"query metric need {require_metric_num} metrics, got {query_metric.metricNames}"
            )
        query_metric.metricNames = query_metric.metricNames[:require_metric_num]
    return response_data


def _get_query_metric_data_chain(
    project_name,
    project_id,
    model_name,
    model_type,
    model_id,
    require_metric_num,
):
    prompt_selector = MemPromptSelector(
        project_name=project_name,
        project_id=project_id,
        model_name=model_name,
        model_type=model_type,
        model_id=model_id,
    )
    chain = (
        call_retrive()
        | nl2metric(
            project_name=project_name,
            model_name=model_name,
            model_type=model_type,
            prompt_selector=prompt_selector,
            job_type=JobType.PARAMS_EXTRACT,
        )
        | RunnableLambda(
            params_extract_nl2metric_postprocess,
            name="params_extract_nl2metric_postprocess",
        )
        | RunnableLambda(
            params_extract_handler_postprocess,
            name="params_extract_handler_postprocess",
        )
        | RunnableLambda(
            query_metric_data_postprocess, name="query_metric_data_postprocess"
        ).bind(require_metric_num=require_metric_num)
    )
    chain.name = "query_metric_data"
    return chain, prompt_selector


def query_metric_data(
    param_key,
    trace_id,
    question,
    project_name,
    project_id,
    model_name,
    model_label,
    model_type,
    model_id,
    user_id,
    force_exact_match,
    require_metric_num=0,
    additional_info=None,
    metric_ner=None,
    where_ner=None,
    hint_info=None,
):
    if not hint_info:
        hint_info = {
            f"{agent_type_enum.value}_hint": "" for agent_type_enum in AgentType
        }
    if isinstance(question, dict):
        question = question["question"]
    tracer.set_trace_id(trace_id)
    chain, prompt_selector = _get_query_metric_data_chain(
        project_name=project_name,
        project_id=project_id,
        model_name=model_name,
        model_type=model_type,
        model_id=model_id,
        require_metric_num=require_metric_num,
    )
    chain_metadata = gen_chain_meta(
        job_type=JobType.PARAMS_EXTRACT,
        project_name=project_name,
        project_id=project_id,
        model_name=model_name,
        model_label=model_label,
        model_id=model_id,
        model_type=model_type,
        prompt_selector=prompt_selector,
    )
    chain_metadata[ChainMeta.RUN_TIME][ChainRuntime.QUESTION] = question
    chain_metadata[ChainMeta.RUN_TIME][ChainRuntime.METRIC_NER] = metric_ner
    chain_metadata[ChainMeta.RUN_TIME][ChainRuntime.WHERE_NER] = where_ner
    # without this, wait_for_intent will keep waiting for a long time
    chain_metadata[ChainMeta.RUN_TIME][ChainRuntime.INTENT] = None
    chain_metadata[ChainMeta.FORCE_EXACT_MATCH] = force_exact_match
    chain_metadata[ChainMeta.CALCULATOR_PARAM_KEY] = param_key
    chain_metadata[ChainMeta.RUN_TIME][ChainRuntime.HINT] = hint_info

    if additional_info is not None:
        chain_metadata[ChainMeta.ADDITIONAL_INFO] = additional_info

    trace_name = f"QUERY_METRIC_DATA {model_type}: {question}"
    cbs = []
    backend_cb = None
    if app_config.ENABLE_LANGFUSE:
        backend_cb = get_reporter_cb(trace_id, None, trace_name, user_id)
        if backend_cb is not None:
            cbs.append(backend_cb)
    if not app_config.USE_ASYNC_LANGCHAIN_LOG:
        cbs.append(LogCallbackHandler(id=trace_id, host=app_config.CLUSTER_ID))
    try:
        result = chain.invoke(
            question,
            config={
                CHAIN_META: chain_metadata,
                "callbacks": cbs,
                "max_concurrency": app_config.MAX_CONCURRENCY,
            },
        )
        logger.info(f"query_metric_data question {question} result {result}")
        return result
    finally:
        if backend_cb is not None:
            backend_cb.close()
