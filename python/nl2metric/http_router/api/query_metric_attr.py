from langchain_core.output_parsers import Str<PERSON>utputParser

from attribution_analysis.service import nl2attribution
from backend_stage_reporter.reporter import get_reporter_cb
from common.llm.general import create_chat_model
from common.logging.logger import get_logger
from common.prompt_selector.json_prompt_selector import MemPromptSelector
from common.trace import tracer
from common.types.base import (
    JobType,
    gen_chain_meta,
    CHAIN_META,
    ChainMeta,
    ChainRuntime,
    add_model_to_chain_meta,
    del_model_from_chain_meta,
    ParamsExtractStage,
)
from common.types.callback_handler import LogCallbackHandler
from common.utils.llm_utils import create_llm_model_by_project_config
from config import app_config
from langchain_core.runnables import RunnableLambda, RunnableConfig

from config.project_config import get_project_config
from metastore.service import get_db_appstore
from nl2agent.dag.node import Node
from nl2agent.tools.lookup_data import FastLookupTool

logger = get_logger(__name__)


def _get_query_metric_attr_chain(
    project_name,
    project_id,
    model_name,
    model_type,
    model_id,
    do_query,
    fastlookup_tool,
):
    prompt_selector = MemPromptSelector(
        project_name=project_name,
        project_id=project_id,
        model_name=model_name,
        model_type=model_type,
        model_id=model_id,
    )
    metric_attr_chain = nl2attribution(
        prompt_selector=prompt_selector,
        model_type=model_type,
        project_name=project_name,
    )

    def _run_query_metric_attr(question, config: RunnableConfig):
        # 归因需要查数的中间结果，这个中间结果是通过config传递的
        fastlookup_tool.invoke(question, config=config)
        fast_lookup_target = fastlookup_tool.target

        query_metrics = fast_lookup_target.meta.get("query_metric_result", None)
        if not query_metrics:
            raise RuntimeError("归因未查询到相关数据")

        if do_query:
            # 取第一个，因为外面只需要一个result
            first_key = next(iter(query_metrics.keys()))
            query_metrics = {first_key: query_metrics[first_key]}

        app_store = get_db_appstore()

        def _run_metric_attr(model_id, attr_query_metric):
            if not attr_query_metric:
                raise RuntimeError("归因未查询到相关数据")
            semantic_model = app_store.get_model_by_id(model_id)
            add_model_to_chain_meta(config[CHAIN_META], semantic_model)
            config[CHAIN_META][ChainMeta.RUN_TIME][
                ChainRuntime.ATTR_QUERY_METRIC
            ] = attr_query_metric
            try:
                return metric_attr_chain.invoke(question, config=config)
            finally:
                del_model_from_chain_meta(config[CHAIN_META])
                config[CHAIN_META][ChainMeta.RUN_TIME][
                    ChainRuntime.ATTR_QUERY_METRIC
                ] = None

        # 通过config传递提参结果的话，这里只能串行执行
        return {
            model_id: _run_metric_attr(model_id, attr_query_metric)
            for model_id, attr_query_metric in query_metrics.items()
        }

    return (
        RunnableLambda(_rewrite_query, name="rewrite_metric_attr")
        | RunnableLambda(_run_query_metric_attr, name="query_metric_attr"),
        prompt_selector,
    )


def _rewrite_query(original_question: str, config: RunnableConfig) -> str:
    agent_input = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.AGENT_BRAIN_INPUT]
    agent_input["question"] = original_question
    project_config = get_project_config(
        config[CHAIN_META][ChainMeta.PROJECT_NAME],
        config[CHAIN_META][ChainMeta.MODEL_NAME],
    )
    model = create_llm_model_by_project_config("agent_model", project_config)

    def _gen_metric_attr_rewrite_prompt(agent_input):
        prompt_selector = config[CHAIN_META][ChainMeta.PROMPT_SELECTOR]
        prompt = prompt_selector.gen_prompt(
            input=agent_input,
            stage=ParamsExtractStage.METRIC_ATTR_REWRITE,
            config=config,
        )
        return prompt

    rewrite_chain = (
        RunnableLambda(
            _gen_metric_attr_rewrite_prompt, name="_gen_metric_attr_rewrite_prompt"
        )
        | model
        | StrOutputParser()
    )
    rewrite_chain.name = "metric_attr_rewrite"
    rewrite_result = rewrite_chain.invoke(agent_input)
    # reset history to empty after condense
    new_question = rewrite_result.rsplit("</think>")[-1].strip()
    return new_question


def query_metric_attr(
    trace_id,
    question,
    project_name,
    project_id,
    model_name,
    model_label,
    model_type,
    model_id,
    user_id,
    fastlookup_tool,
    manual_selects_result=None,
    additional_info=None,
    do_query=False,
    agent_input=None,
):
    if isinstance(question, dict):
        question = question["question"]
    tracer.set_trace_id(trace_id)
    chain, prompt_selector = _get_query_metric_attr_chain(
        project_name=project_name,
        project_id=project_id,
        model_name=model_name,
        model_type=model_type,
        model_id=model_id,
        do_query=do_query,
        fastlookup_tool=fastlookup_tool,
    )
    chain_metadata = gen_chain_meta(
        job_type=JobType.PARAMS_EXTRACT,
        project_name=project_name,
        project_id=project_id,
        model_name=model_name,
        model_label=model_label,
        model_id=model_id,
        model_type=model_type,
        prompt_selector=prompt_selector,
    )
    chain_metadata[ChainMeta.MANUAL_SELECTS_RESULT] = manual_selects_result
    chain_metadata[ChainMeta.RUN_TIME][ChainRuntime.QUESTION] = question
    # without this, wait_for_intent will keep waiting for a long time
    chain_metadata[ChainMeta.RUN_TIME][ChainRuntime.INTENT] = None
    chain_metadata[ChainMeta.RUN_TIME][ChainRuntime.AGENT_BRAIN_INPUT] = agent_input
    if additional_info is not None:
        chain_metadata[ChainMeta.ADDITIONAL_INFO] = additional_info

    trace_name = f"QUERY_METRIC_ATTR {model_type}: {question}"
    cbs = []
    backend_cb = None
    if app_config.ENABLE_LANGFUSE:
        backend_cb = get_reporter_cb(trace_id, None, trace_name, user_id)
        if backend_cb is not None:
            cbs.append(backend_cb)
    if not app_config.USE_ASYNC_LANGCHAIN_LOG:
        cbs.append(LogCallbackHandler(id=trace_id, host=app_config.CLUSTER_ID))
    try:
        result = chain.invoke(
            question,
            config={
                CHAIN_META: chain_metadata,
                "callbacks": cbs,
                "max_concurrency": app_config.MAX_CONCURRENCY,
            },
        )
        logger.info(f"query_metric_attr question {question} result {result}")
        return result
    finally:
        if backend_cb is not None:
            backend_cb.close()
