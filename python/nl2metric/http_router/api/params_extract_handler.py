#! coding=utf-8
from collections import OrderedDict
from fastapi import Header
from typing import List, Optional, Dict

from colorama import Fore
from pydantic import BaseModel
from attribution_analysis.service import nl2attribution
from common.utils.string_utils import remove_substrings
from metastore.service import get_db_appstore
from common.logging.logger import get_logger
from common.trace import tracer
from common.types import (
    Message,
    MessageWithExtraInfo,
    MetaResult,
    ParamsExtractData,
)
from common.types.base import (
    CHAIN_META,
    gen_chain_meta,
    JobType,
    ChainMeta,
    ChainRuntime,
)
from common.types.exceptions import RetryParamExtract
from common.types.exception_cacher import exception_to_code
from common.types.callback_handler import <PERSON>gCallback<PERSON>and<PERSON>
from cache_updater.cached import cached
from common.prompt_selector.json_prompt_selector import MemPromptSelector
from common.utils.base import json_equal_without_list_order
from common.utils.string_utils import class_to_dict
from config.project_config import get_project_config
from condense.condense_query import call_condense_query
from config import app_config
from langchain_core.runnables import (
    <PERSON><PERSON><PERSON>Lambda,
    RunnablePassthrough,
    RunnableBranch,
    RunnableConfig,
)
from nl2agent.agents.intent_bi_agent import IntentBiAgent
from nl2intent.nl2intent import call_nl2intent
from nl2intent.types import Intent, intent2type
from nl2meta.nl2meta import call_nl2meta
from http_router.api_dispatcher import get_api_dispatcher, ApiType
from metastore import get_metastore
from metastore.score import get_metric_score, get_dimension_value_score
from nl2metric.constants import MYSQL_DIALECT
from nl2metric.service import nl2metric
from backend_stage_reporter.reporter import get_reporter_cb
from pre_filter.retrive import call_retrive
from common.types.exceptions import ParamsExtractEmptyResult

logger = get_logger(__name__)


class ParamsExtractRequest(BaseModel):
    messages: List[MessageWithExtraInfo]
    model_type: str
    model_id: str
    task_id: Optional[str] = None
    force_exact_match: bool = False
    history_params_extract_data: Optional[ParamsExtractData] = None
    additional_info: Optional[Dict] = None

    def last_message(self):
        return self.messages[-1].content


class ParamsExtractResponse(BaseModel):
    data: ParamsExtractData
    code: int = 0


def params_extract_nl2metric_postprocess(input, config: RunnableConfig):
    response_data = ParamsExtractData(type=intent2type(Intent.query_metric))
    response_data.query_metric = input["query_metric"]
    response_data.query_metric_list = input["query_metric_list"]
    response_data.extra_info[ChainRuntime.IS_SUB.value] = config[CHAIN_META][
        ChainMeta.RUN_TIME
    ][ChainRuntime.IS_SUB]
    response_data.extra_info[ChainRuntime.GROUPBYS_WITH_LEVEL.value] = config[
        CHAIN_META
    ][ChainMeta.RUN_TIME][ChainRuntime.GROUPBYS_WITH_LEVEL]
    response_data.extra_info[ChainRuntime.METRIC_NER.value] = config[CHAIN_META][
        ChainMeta.RUN_TIME
    ].get(ChainRuntime.METRIC_NER, [])
    response_data.extra_info[ChainRuntime.WHERE_NER.value] = config[CHAIN_META][
        ChainMeta.RUN_TIME
    ].get(ChainRuntime.WHERE_NER, [])
    return response_data


def params_extract_nl2agent_postprocess(input, config: RunnableConfig):
    intent = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.INTENT].intent
    response_data = ParamsExtractData(type=intent2type(intent))
    response_data.calculator = input
    return response_data


def params_extract_nl2metric_merge_history(
    input: ParamsExtractData, config: RunnableConfig
):
    intent_tags = config[CHAIN_META][ChainMeta.RUN_TIME].get(
        ChainRuntime.INTENT_TAG, None
    )
    question = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.QUESTION]
    force_exact_match = config[CHAIN_META][ChainMeta.FORCE_EXACT_MATCH]
    history_params_extract_data: ParamsExtractData = config[CHAIN_META][
        ChainMeta.HISTORY_PARAMS_EXTRACT_DATA
    ]
    is_sub = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.IS_SUB]

    return _params_extract_nl2metric_merge_history(
        input=input,
        intent_tags=intent_tags,
        question=question,
        force_exact_match=force_exact_match,
        history_params_extract_data=history_params_extract_data,
        is_sub=is_sub,
    )


# we need intent_tags, so this should be after nl2intent_by_tags
def _params_extract_nl2metric_merge_history(
    input: ParamsExtractData,
    intent_tags,
    question,
    force_exact_match,
    history_params_extract_data,
    is_sub,
):
    if not input.is_query_metric():
        return input

    if intent_tags and (not intent_tags.is_follow_up):
        logger.info("question {question} is not follow-up, no need to merge history")
        return input

    # 宝武在提参出错时会走deepseek闲聊
    # 如果第一轮是查数，第二轮是无关的追问，也要走闲聊
    # eg. Q1 宝武集团职工人数; Q2 那总部在哪里？
    # Q2的提参结果完全是空的，这里不做merge，
    # 后面因为nl2metric_requires_metrics接口会报错
    current_metric = input.query_metric
    if (
        (not current_metric.metricNames)
        and (not current_metric.where)
        and (not current_metric.groupBys)
        and (not current_metric.timeQueryParams)
    ):
        logger.info("question {question} query_metric is likely chitchat")
        return input

    if (not history_params_extract_data) or (
        not history_params_extract_data.is_query_metric()
    ):
        return input
    if not input.query_metric_list:
        input.query_metric_list = history_params_extract_data.query_metric_list
    history_metric = history_params_extract_data.query_metric
    if history_metric == None:
        return input

    # whether or not merge where
    # eg.1 T: Q1: XXX Q2: 那5月份的呢？
    # eg.2 T: Q1: 宝武集团7月份营业外收入是多少 Q2: 那营业外支出呢
    # eg.3 F: Q1: 宝武集团7月份营业外收入是多少 Q2: 那各公司的营业外支出呢
    # eg.4 F: Q1: 宝武共享服务有限公司-合并今年1月的毛利 Q2: 各公司今年1月长期应付职工薪酬
    # eg.5 T: Q1: 宝武共享服务有限公司-合并今年1月的毛利 Q2: 所有子公司的呢  * currently might fail
    # eg.6 F: Q1: 7月宝武集团xx指标？Q2: 我想看7月所有集团xx指标？
    # eg.7 F: Q1: 宝武集团xx指标？Q2: 我想看所有集团xx指标？
    # eg.8 T: Q1: 7月宝武集团xx指标？Q2: 我想看8月xx指标？
    # eg.9 F: Q1: 宝武集团负债合计？Q2: 我想看所有集团资产总计？ * currently might fail

    did_merge_history = False
    should_merge_where = True
    # with where, there's nothing left for groupBys
    # subcompany question need father company, so keep where
    if current_metric.groupBys and not is_sub:
        should_merge_where = False
        logger.info(
            f"question {question} do not merge where, current_metric.groupBys {current_metric.groupBys}"
        )
    if (
        (current_metric.timeQueryParams == history_metric.timeQueryParams)
        and current_metric.metricNames
        and (sorted(current_metric.metricNames) == sorted(history_metric.metricNames))
    ):
        should_merge_where = False
        logger.info(
            f"question {question} do not merge where, current_metric.timeQueryParams {current_metric.timeQueryParams}"
            + f", history_metric.timeQueryParams {history_metric.timeQueryParams}"
            + f", current_metric.metricNames {current_metric.metricNames}"
            + f", history_metric.metricNames {history_metric.metricNames}"
        )

    # merge metric
    if not current_metric.metricNames:
        current_metric.metricNames = history_metric.metricNames
        if current_metric.metricNames:
            did_merge_history = True
        input.extra_info["metric_scores"] = history_params_extract_data.extra_info[
            "metric_scores"
        ]
        if force_exact_match:
            if current_metric.metricNames:
                # history metricNames is already chosen by user, so no need to choose again
                input.extra_info["metric_scores"] = {
                    key: 1 for key in input.extra_info["metric_scores"]
                }
                current_metric.isMetricNamesExactMatch = True
            else:
                current_metric.isMetricNamesExactMatch = False
    if not current_metric.timeQueryParams:
        current_metric.timeQueryParams = history_metric.timeQueryParams
        input.extra_info["timeQueryType"] = history_params_extract_data.extra_info.get(
            "timeQueryType", None
        )
        if current_metric.timeQueryParams:
            did_merge_history = True
    if not current_metric.groupBys:
        current_metric.groupBys = history_metric.groupBys
        if current_metric.groupBys:
            did_merge_history = True
        input.extra_info[
            ChainRuntime.GROUPBYS_WITH_LEVEL.value
        ] = history_params_extract_data.extra_info[
            ChainRuntime.GROUPBYS_WITH_LEVEL.value
        ]
    if should_merge_where and (not current_metric.where):
        current_metric.where = history_metric.where
        current_metric.where_json = history_metric.where_json
        if current_metric.where:
            did_merge_history = True
        if force_exact_match:
            if current_metric.where:
                # history where is already chosen by user, so no need to choose again
                current_metric.isWhereExactMatch = True
            else:
                current_metric.isWhereExactMatch = False

    input.extra_info["did_merge_history"] = did_merge_history
    return input


def params_extract_nl2attribution_postprocess(raw_input):
    input = raw_input["result"]
    response_data = ParamsExtractData(type=intent2type(Intent.attribution_analysis))
    if "type" in input:
        response_data.type = input["type"]
    response_data.attribution_analysis = input
    return response_data


def params_extract_nl2meta_postprocess(input):
    response_data = ParamsExtractData(type=input.type.value)
    param = input.param if input.param else ""
    response_data.meta_result = MetaResult(param=param)
    return response_data


def get_nl2attribution_chain(prompt_selector, model_type, project_name):
    return nl2attribution(prompt_selector, model_type, project_name) | RunnableLambda(
        params_extract_nl2attribution_postprocess,
        name="params_extract_nl2attribution_postprocess",
    )


def get_nl2meta_chain(prompt_selector, model_type):
    return call_nl2meta(model_type, prompt_selector) | RunnableLambda(
        params_extract_nl2meta_postprocess, name="params_extract_nl2meta_postprocess"
    )


def _cal_query_metric_score(response_data: ParamsExtractData, config: RunnableConfig):
    if not response_data.is_query_metric():
        return

    metastore = get_metastore(config[CHAIN_META][ChainMeta.PROJECT_ID])
    question = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.QUESTION]
    metric_ner = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.METRIC_NER]
    where_ner = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.WHERE_NER]

    # metric
    # merge_history will merge metric_scores
    if response_data.extra_info.get("metric_scores", None) is None:
        if metric_ner:
            metric_str = metric_ner
            use_max = True
        elif where_ner:
            metric_str = remove_substrings(question, where_ner)
            use_max = True
        else:
            metric_str = question
            use_max = False

        metric_names = response_data.query_metric.metricNames
        metric_scores = {}
        for m_name in metric_names:
            metric = metastore.get_metric(m_name)
            metric_scores[m_name] = get_metric_score(
                questions=metric_str,
                metric=metric,
                use_max=use_max,
                use_stop_words=False,  # stop_words might cause exact match score less than 1
            )
        response_data.extra_info["metric_scores"] = metric_scores

    # dimension_value
    # merge_history will merge dimension_scores
    if not isinstance(response_data.query_metric.where, str):
        if where_ner:
            dimension_value_str = where_ner
            use_max = True
        elif metric_ner:
            dimension_value_str = remove_substrings(question, metric_ner)
            use_max = True
        else:
            dimension_value_str = question
            use_max = False

        for (
            dimension_sub_where
        ) in response_data.query_metric.where.dimension_sub_wheres:
            for dimension_value_meta in dimension_sub_where.dimension_values:
                if not dimension_value_meta.score:
                    dimension_value_meta.score = get_dimension_value_score(
                        questions=dimension_value_str,
                        dimension_value=dimension_value_meta.dimension_value,
                        use_max=use_max,
                        use_stop_words=False,  # stop_words might cause exact match score less than 1
                    )
            dimension_sub_where.sort_dimension_values()


def _check_equal_history_query_metric(input: ParamsExtractData, config: RunnableConfig):
    # 本规则是对_params_extract_nl2metric_merge_history中“likely chitchat”规则的补充
    # 本质上是params_extract_nl2metric_merge_history的规则导致的问题
    # Q1: 宝武集团的营业利润是多少? Q2: 宝武集团的成立时间
    # 当前会识别成追问，然后因为没有成立时间这个指标，所以merge_history会把指标merge为营业利润
    # 因为Q2中有“宝武”，所以“likely chitchat”规则不生效
    # TODO(bhx): 后面如果放弃宝武的这一套规则，还是走condense的话，可以删除这个检查

    did_merge_history = input.extra_info.get("did_merge_history", False)
    if not did_merge_history:
        return input

    if not input.is_query_metric():
        return input

    intent_tags = config[CHAIN_META][ChainMeta.RUN_TIME].get(
        ChainRuntime.INTENT_TAG, None
    )
    if intent_tags and (not intent_tags.is_follow_up):
        logger.info("question {question} is not follow-up, no need to merge history")
        return input

    history_jsons = []

    def _add_history_json(history_data):
        history_metric_json = None
        if (history_data != None) and (history_data.is_query_metric()):
            history_metric = history_data.query_metric
            if history_metric != None:
                history_metric_json = history_metric.model_dump()

        if history_metric_json:
            history_jsons.append(history_metric_json)

    job_type = config[CHAIN_META][ChainMeta.JOB_TYPE]
    model_id = config[CHAIN_META][ChainMeta.MODEL_ID]
    history_params_extract_data: ParamsExtractData = config[CHAIN_META][
        ChainMeta.HISTORY_PARAMS_EXTRACT_DATA
    ]
    if job_type == JobType.PROJECT_PARAMS_EXTRACT:
        history_params_extract_data = history_params_extract_data.get(model_id, None)
    _add_history_json(history_params_extract_data)

    original_history_metric_data_json = (
        config[CHAIN_META]
        .get(ChainMeta.ADDITIONAL_INFO, {})
        .get("original_history_params_extract_data", None)
    )
    if original_history_metric_data_json and job_type == JobType.PROJECT_PARAMS_EXTRACT:
        original_history_metric_data_json = original_history_metric_data_json.get(
            model_id, None
        )
    if original_history_metric_data_json:
        original_history_metric_data = ParamsExtractData.model_validate(
            original_history_metric_data_json
        )
        _add_history_json(original_history_metric_data)

    if not history_jsons:
        return input

    current_json = input.query_metric.model_dump()

    current_equal_history = True
    for key, current_value in current_json.items():
        tmp = False
        for history_json in history_jsons:
            history_value = history_json[key]
            if json_equal_without_list_order(current_value, history_value):
                tmp = True
                break
        if not tmp:
            current_equal_history = False
            break
    if current_equal_history:
        raise ParamsExtractEmptyResult(
            f"current_metric_json equals history_metric_json: {current_json}"
        )
    return input


def params_extract_handler_postprocess(
    response_data: ParamsExtractData, config: RunnableConfig
):
    project_name = config[CHAIN_META][ChainMeta.PROJECT_NAME]
    model_name = config[CHAIN_META][ChainMeta.MODEL_NAME]
    project_config = get_project_config(
        project_name,
        model_name,
    )

    # add here because intent_tags is created in nl2intent
    tags = config[CHAIN_META][ChainMeta.RUN_TIME].get(ChainRuntime.INTENT_TAG, None)
    if tags:
        response_data.extra_info[ChainRuntime.INTENT_TAG.value] = tags.tags
    # 20241225 science city
    intent = config[CHAIN_META][ChainMeta.RUN_TIME].get(ChainRuntime.INTENT, None)
    if intent is not None:
        response_data.extra_info[ChainRuntime.ASK_BI.value] = intent.askbi
        response_data.extra_info[ChainRuntime.ASK_DOC.value] = intent.askdoc

    condense_question = config[CHAIN_META][ChainMeta.RUN_TIME].get(
        ChainRuntime.CONDENSE_QUESTION, None
    )
    if condense_question:
        response_data.extra_info[
            ChainRuntime.CONDENSE_QUESTION.value
        ] = condense_question

    time_query_type = config[CHAIN_META][ChainMeta.RUN_TIME].get(
        ChainRuntime.TIME_QUERY_TYPE
    )
    if time_query_type:
        response_data.extra_info[ChainRuntime.TIME_QUERY_TYPE.value] = time_query_type

    # filter metric by groupby
    metric_blacklist_by_groupby_dimension = (
        project_config.metric_blacklist_by_groupby_dimension
    )
    if (
        metric_blacklist_by_groupby_dimension
        and response_data.is_query_metric()
        and response_data.query_metric.groupBys
    ):
        all_blacklist_metrics = set()
        for dimension_name in response_data.query_metric.groupBys:
            all_blacklist_metrics.update(
                metric_blacklist_by_groupby_dimension.get(dimension_name, [])
            )
        response_data.query_metric.metricNames = [
            m
            for m in response_data.query_metric.metricNames
            if m not in all_blacklist_metrics
        ]

    _cal_query_metric_score(response_data, config)

    nl2metric_requires_metrics = project_config.nl2metric_requires_metrics
    if (
        nl2metric_requires_metrics
        and response_data.is_query_metric()
        and (not response_data.query_metric.metricNames)
    ):
        raise ParamsExtractEmptyResult(
            f"project {project_name} mode {model_name} params_extract got no metrics"
        )

    response_data.expand_where()

    # this func should be behind expand_where
    _check_equal_history_query_metric(response_data, config)
    return response_data


# TODO(bhx): refresh_cache for prompt_studio prompt_selector
@cached("{model_id}#{model_type}#{model}#{is_multi_param_extract}")
# create chain iteself costs about 300ms
# no need refresh cache. only model_id here needs mysql data.
# And if model_id does not exist, this will not be cached because of excaption.
def get_params_extract_handler_chain(
    model_id: str, model_type: str, model, is_multi_param_extract: bool = False
):
    project_name = model.semantic_project_name
    prompt_selector = MemPromptSelector(
        project_name=project_name,
        project_id=model.semantic_project_id,
        model_name=model.table_name,
        model_type=model_type,
        model_id=model_id,
    )

    query_metric_chain = nl2metric(
        project_name=project_name,
        model_name=model.table_name,
        model_type=model_type,
        prompt_selector=prompt_selector,
        job_type=JobType.PARAMS_EXTRACT,
    ) | RunnableLambda(
        params_extract_nl2metric_postprocess,
        name="params_extract_nl2metric_postprocess",
    )

    calculator_chain = IntentBiAgent() | RunnableLambda(
        params_extract_nl2agent_postprocess,
        name="params_extract_nl2agent_postprocess",
    )

    attribution_analysis_chain = get_nl2attribution_chain(
        prompt_selector, model_type, project_name
    )
    meta_chain = get_nl2meta_chain(prompt_selector, model_type)

    if app_config.PARALLEL_INTENT:
        # attribution_analysis should be after call_nl2intent
        # if the query is not attribution_analysis, calling attribution_analysis might raise Exception
        sub_chains = OrderedDict()
        sub_chains["query_metric_data"] = query_metric_chain
        sub_chains["intent_after"] = RunnablePassthrough.assign(
            intent=call_nl2intent(model_type, prompt_selector)
        ) | RunnableBranch(
            (
                lambda x: x["intent"].intent
                in {Intent.percentage, Intent.period_on_period, Intent.predict},
                calculator_chain,
            ),
            (
                lambda x: x["intent"].intent == Intent.attribution_analysis,
                attribution_analysis_chain,
            ),
            (
                lambda x: x["intent"].intent == Intent.query_metric,
                lambda x: None,
            ),
            meta_chain,
        )
        if is_multi_param_extract:
            sub_chains["intent_after"] = RunnableLambda(lambda x: None)
        chain = (
            call_condense_query(prompt_selector, model_type)
            | call_retrive()
            | sub_chains
            | RunnableLambda(
                lambda x: x["intent_after"]
                if x["intent_after"] != None
                else x["query_metric_data"],
                name="params_extract_parallel_intent_postprocess",
            )
            | RunnableLambda(
                params_extract_nl2metric_merge_history,
                name="params_extract_nl2metric_merge_history",
            )
            | RunnableLambda(
                params_extract_handler_postprocess,
                name="params_extract_handler_postprocess",
            )
        )
        chain.name = "PARALLEL_INTENT: " + JobType.PARAMS_EXTRACT
    else:
        chain = (
            call_condense_query(prompt_selector, model_type)
            | call_retrive()
            | RunnablePassthrough.assign(
                intent=call_nl2intent(model_type, prompt_selector)
            )
            | RunnableBranch(
                (
                    lambda x: x["intent"].intent == Intent.query_metric,
                    query_metric_chain,
                ),
                (
                    lambda x: x["intent"].intent == Intent.attribution_analysis,
                    attribution_analysis_chain,
                ),
                (
                    lambda x: x["intent"].intent
                    in {Intent.percentage, Intent.period_on_period, Intent.predict},
                    calculator_chain,
                ),
                meta_chain,
            )
            | RunnableLambda(
                params_extract_nl2metric_merge_history,
                name="params_extract_nl2metric_merge_history",
            )
            | RunnableLambda(
                params_extract_handler_postprocess,
                name="params_extract_handler_postprocess",
            )
        )
        chain.name = JobType.PARAMS_EXTRACT
    logger.info(
        f"params_extract chain for model_id {model_id} and model_type {model_type} created"
    )
    return prompt_selector, chain


@exception_to_code
def params_extract_handler(
    req: ParamsExtractRequest, userId: Optional[str] = Header(None)
):
    trace_id = tracer.get_trace_id()
    logger.info(
        f"params_extract_handler start for user {userId}, trace_id {trace_id}: {req.model_dump_json()}"
    )
    task_id = req.task_id
    if len(req.messages) <= 0:
        raise RuntimeError(f"model {req.model_id} no question asked")

    model = get_db_appstore().get_model_by_id(req.model_id)
    prompt_selector, chain = get_params_extract_handler_chain(
        req.model_id, req.model_type, model
    )

    try_cnt = 0
    input = req.messages
    sequential_first_question = None
    force_intent = None
    while True:
        try:
            chain_metadata = gen_chain_meta(
                job_type=JobType.PARAMS_EXTRACT,
                project_name=model.semantic_project_name,
                project_id=model.semantic_project_id,
                model_name=model.table_name,
                model_label=model.label,
                model_id=req.model_id,
                model_type=req.model_type,
                prompt_selector=prompt_selector,
                force_exact_match=req.force_exact_match,
                history_params_extract_data=req.history_params_extract_data,
                messages=input,
                user_id=userId,
            )
            if force_intent is not None:
                chain_metadata[ChainMeta.FORCE_INTENT] = force_intent
            if req.additional_info is not None:
                chain_metadata[ChainMeta.ADDITIONAL_INFO] = req.additional_info

            if try_cnt > 0:
                trace_name = f"RETRY{try_cnt} {req.model_type}: {input[-1].content}"
                new_trace_id = f"RETRY{try_cnt} {trace_id}"
            else:
                trace_name = f"{req.model_type}: {input[-1].content}"
                new_trace_id = trace_id
            cbs = []
            backend_cb = None
            if app_config.ENABLE_LANGFUSE:
                backend_cb = get_reporter_cb(new_trace_id, task_id, trace_name, userId)
                if backend_cb is not None:
                    cbs.append(backend_cb)
            if not app_config.USE_ASYNC_LANGCHAIN_LOG:
                cbs.append(
                    LogCallbackHandler(
                        id=new_trace_id, host=app_config.CLUSTER_ID, user_id=userId
                    )
                )
            response_data: ParamsExtractData = chain.invoke(
                input,
                config={
                    CHAIN_META: chain_metadata,
                    "callbacks": cbs,
                    "max_concurrency": app_config.MAX_CONCURRENCY,
                },
            )
            break
        except RetryParamExtract as e:
            try_cnt += 1
            if try_cnt >= app_config.PARAM_EXTRACT_RETRY_LIMIT:
                logger.error(f"model {req.model_id} retry too many times, err {e}")
                raise RuntimeError(
                    f"model {req.model_id} retry too many times, err {e}"
                )
            if e.question:
                input = [Message(role="user", content=e.question)]
            force_intent = e.intent
            logger.warning((f"model {req.model_id} retry {try_cnt}, intent {e.intent}"))
        finally:
            if backend_cb is not None:
                backend_cb.close()

    if sequential_first_question:
        response_data.extra_info[
            "sequential_first_question"
        ] = sequential_first_question
    if response_data.is_query_metric() and response_data.query_metric_empty():
        logger.error(
            Fore.RED + "大模型提参结果为空: %s" + Fore.RESET, class_to_dict(response_data)
        )
        raise ParamsExtractEmptyResult(
            f"project {model.semantic_project_id} mode {req.model_id} params_extract got nothing"
        )
    logger.info(
        "%s " + Fore.CYAN + "大模型提参结果: %s" + Fore.RESET,
        trace_id,
        class_to_dict(response_data),
    )
    return class_to_dict(ParamsExtractResponse(data=response_data))


async def async_params_extract_handler(
    req: ParamsExtractRequest, userId: Optional[str] = Header(None)
):
    future = get_api_dispatcher().submit_task(
        req_type=ApiType.params_extract_handler,
        trace_id=tracer.get_trace_id(),
        req=req,
        userId=userId,
    )
    return await future
