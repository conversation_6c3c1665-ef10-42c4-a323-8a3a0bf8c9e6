from typing import Any

from langchain_openai import ChatOpenAI
from pydantic import BaseModel
from smolagents import (
    Tool,
    OpenAIServerModel,
    CodeAgent,
    DuckDuckGoSearchTool,
    VisitWebpageTool,
)

from common.llm.general import create_chat_model
from config.app_config import (
    xengine_database,
    xengine_password,
    xengine_username,
    xengine_backend_host,
    xengine_backend_port,
    DEEPSEEK_14B_MODEL_NAME,
)
from tools.sql_executor import MysqlClient, Config
from nl2agent.fallback_llm.text2sql import create_nl2sql_converter, extract_sql


class XEngineSQLExecute(Tool):
    name = "sql_engine"
    description = "执行MYSQL的SQL语句, 必须要使用MYSQL方言"
    inputs = {
        "query": {
            "type": "string",
            "description": "The query to perform. This should be correct SQL.",
        }
    }
    output_type = "any"

    def __init__(self, conf: Config):
        super().__init__()
        self.di_engine = MysqlClient.get_instance(conf)

    def forward(self, query: str) -> Any:
        _, answer = self.di_engine.query(query)
        return answer


class NL2SQLConverter(Tool):
    name = "nl2sql_converter"
    description = "将自然语言转换成SQL语句"
    inputs = {
        "query": {
            "type": "string",
            "description": "The query to perform. This should be correct SQL.",
        }
    }
    output_type = "any"

    def __init__(self, model_type: str, semantic_scenes_id: str, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.converter = create_nl2sql_converter()
        self.model_type = model_type
        self.semantic_scenes_id = semantic_scenes_id

    def forward(self, query: str) -> Any:
        config = dict()
        chain = (
            self.converter.gen_chain(
                query=query,
                model_type=self.model_type,
                semantic_scenes_id=self.semantic_scenes_id,
                config=config,
            )
            | extract_sql
        )
        try:
            return chain.invoke(query, config=config)
        except Exception as e:
            return f"error: {str(e)}"


def transform_models(langchian_model: ChatOpenAI):
    model = OpenAIServerModel(
        model_id=langchian_model.model_name,
        api_base=langchian_model.openai_api_base,
        api_key=langchian_model.openai_api_key,
    )
    return model


def create_agent(
    model_type: str, semantic_scenes_id: str, is_web_search: bool = True, max_steps=5
):
    # 创建配置对象
    config = Config(
        xengine_backend_host,
        xengine_backend_port,
        xengine_username,
        xengine_password,
        xengine_database,
    )
    model = transform_models(create_chat_model(model_type))
    sql_engine = XEngineSQLExecute(conf=config)
    nl2sql = NL2SQLConverter(model_type, semantic_scenes_id)
    tools = [sql_engine, nl2sql]
    managed_agents = []
    if is_web_search:
        search_agent = CodeAgent(
            tools=[DuckDuckGoSearchTool(), VisitWebpageTool()],
            model=model,
            name="search_agent",
            description="This is an agent that can do web search.",
            max_steps=2,
        )
        managed_agents.append(search_agent)

    agent = CodeAgent(
        tools=tools, model=model, managed_agents=managed_agents, max_steps=max_steps
    )
    return agent


class ChatRequest(BaseModel):
    query: str
    is_web_search: bool = True
    model_type: str = DEEPSEEK_14B_MODEL_NAME
    model_id: str = "CYLuY5VklxU2UTK2"
    max_steps: int = 5


def chat_to_everything(req: ChatRequest):
    agent = create_agent(req.model_type, req.model_id, req.is_web_search, req.max_steps)

    return agent.run(req.query)
