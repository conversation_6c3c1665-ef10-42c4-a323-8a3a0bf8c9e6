#! coding: utf-8
from pathlib import Path
from dotenv import load_dotenv

try:
    import pyconcrete
except ModuleNotFoundError:
    print("pyconcrete 模块未安装，跳过相关功能")

# specify path to easily change env in docker
env_file = Path(__file__).parent / ".env"
print(f"load dotenv {env_file}")
load_dotenv(dotenv_path=env_file, override=True)
# both app_config and doc_config needs load_dotenv

import os

from common.llm.embedding import create_raw_embedding_model
from common.logging.logger import get_logger
from common.utils.startup_utils import register_global_exception_hook
from config import app_config
from contextlib import asynccontextmanager
from fastapi import (
    FastAPI,
    HTTPException,
    Request,
    Response,
)
from pydantic import BaseModel, Field
from typing import List, Optional, Union

logger = get_logger(__name__)
register_global_exception_hook()

embedding_model = create_raw_embedding_model(app_config.embedding_service_model)


@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info(f"embedding_service start succeed, pid {os.getpid()}")

    yield  # This will run the application

    logger.info(f"embedding_service stopped, pid {os.getpid()}")


app = FastAPI(lifespan=lifespan)


@app.get("/embedding_service/health")
async def health():
    return {"health": True}


# (CreateEmbeddingRequest, EmbeddingData, etc.) are copied from github inference


class CreateEmbeddingRequest(BaseModel):
    model: str
    input: Union[str, List[str]] = Field(description="The input to embed.")
    user: Optional[str] = None

    class Config:
        schema_extra = {
            "example": {
                "input": "The food was delicious and the waiter...",
            }
        }


class EmbeddingData(BaseModel):
    index: int
    object: str
    embedding: List[float]


class EmbeddingUsage(BaseModel):
    prompt_tokens: int
    total_tokens: int


class Embedding(BaseModel):
    object: str  #  Literal["list"]
    model: str
    data: List[EmbeddingData]
    usage: EmbeddingUsage


def _create_response(all_embeddings: List[List[float]]):
    embedding_list = []
    for index, data in enumerate(all_embeddings):
        embedding_list.append(
            EmbeddingData(index=index, object="embedding", embedding=data)
        )
    usage = EmbeddingUsage(prompt_tokens=0, total_tokens=0)
    return Embedding(
        object="list",
        model="",
        data=embedding_list,
        usage=usage,
    ).model_dump_json()


@app.post("/v1/embeddings")
async def create_embedding(request: Request) -> Response:
    payload = await request.json()
    body = CreateEmbeddingRequest.parse_obj(payload)

    try:
        if isinstance(body.input, str):
            embedding = await embedding_model.aembed_query(body.input)
            return Response(
                _create_response([embedding]), media_type="application/json"
            )
        else:
            embedding = await embedding_model.aembed_documents(body.input)
            return Response(_create_response(embedding), media_type="application/json")
    except RuntimeError as re:
        logger.error(re, exc_info=True)
        raise HTTPException(status_code=400, detail=str(re))
    except Exception as e:
        logger.error(e, exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


def start():
    log_config_file = str(
        Path(__file__).parent / "common/logging/fastapi_embedding_service_logging.ini"
    )
    import uvicorn

    uvicorn.run(
        app=app,
        host="0.0.0.0",
        port=app_config.EMBEDDING_SERVICE_PORT,
        workers=1,
        log_config=log_config_file,
    )


if __name__ == "__main__":
    start()
