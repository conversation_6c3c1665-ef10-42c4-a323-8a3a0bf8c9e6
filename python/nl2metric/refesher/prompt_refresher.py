import copy
import json
import os
import socket
import threading

import pymysql
from pymysql.cursors import DictCursor

from common.db_model.model import get_all_semantic_models
from common.logging.logger import get_logger
from common.utils.concurrent_utils import LoopRunnable
from config import app_config
from refesher.prompt_studio_sql import QUERY_ALL_DATA_SQL

logger = get_logger(__name__)

DB_CONFIG = {
    "user": os.getenv("DB_USER", "root"),
    "password": os.getenv("DB_PASSWORD", "AE3~sByGLG-.Prhwdpgb"),
    "host": os.getenv("DB_HOST", "**************"),
    "port": int(os.getenv("DB_PORT", 12306)),
    "database": os.getenv("DB_NAME", "operation"),
    "charset": "utf8mb4",
    "cursorclass": DictCursor,
}

CLUSTER_ID = os.getenv("CLUSTER_ID", "ask_bi_python")

current_project = {}

prompt_data = {}

few_shot_data = {}

version_dict = {}

lock = threading.Lock()


def get_host_ip():
    try:
        host_name = socket.gethostname()
        host_ip = socket.gethostbyname(host_name)
        return host_ip
    except socket.gaierror as e:
        logger.error(f"Error getting host IP: {e}")
        return None


def connect_to_database():
    return pymysql.connect(**DB_CONFIG)


def load_initial_data():
    logger.info("Load data start...")
    try:
        load_data_from_database()
        logger.info("Successfully loaded initial data from the database.")
    except Exception as e:
        logger.error("Failed loaded initial data from the database.", e)


def load_data_from_database():
    connection = connect_to_database()
    try:
        with connection.cursor() as cursor:
            sql = QUERY_ALL_DATA_SQL % (CLUSTER_ID)
            logger.info(f"Load data from database sql = {sql}")
            cursor.execute(sql)
            results = cursor.fetchall()
            for result in results:
                sub_task_type = result["sub_task_type"]
                scenes_id = result["scenes_id"]
                prompt = json.loads(result["prompt"])
                prompt_data[scenes_id + sub_task_type] = prompt
                few_shots = result["few_shots"]
                if few_shots is not None and few_shots != "":
                    few_shot = json.loads(few_shots)
                    few_shot_data[scenes_id + sub_task_type] = few_shot
    finally:
        logger.info(f"Load success.")
        connection.close()


def query_project_maps():
    logger.info("Query project and scenes.")
    semantic_models = get_all_semantic_models()
    for model in semantic_models:
        if model.semantic_project_id not in current_project:
            current_project[model.semantic_project_id] = []
        current_project[model.semantic_project_id].append(model.id)


def get_current_prompt(scenes_id, sub_task_type):
    with lock:
        key = scenes_id + sub_task_type
        if key in prompt_data:
            prompt = prompt_data[key]
            prompt_json = {}
            prompt_json["prompt_prefix"] = prompt["prefix"]
            prompt_json["prompt_suffix"] = prompt["suffix"]
            prompt_json["prompt_type"] = prompt["promptType"]
            if "promptTpl" in prompt:
                prompt_json["example_tpl"] = prompt["promptTpl"]
            logger.info(
                f"Get current prompt {scenes_id}, {sub_task_type} = {prompt_json}"
            )
            return prompt_json
        else:
            return None


def get_current_few_shots(scenes_id, sub_task_type):
    with lock:
        key = scenes_id + sub_task_type
        if key in few_shot_data:
            few_shots = few_shot_data[key]
            copy_few_shots = copy.deepcopy(few_shots)
            logger.info(
                f"Get current fewShots {scenes_id}, {sub_task_type} = {copy_few_shots}"
            )
            return copy_few_shots
        else:
            return None


def start_prompt_studio_updater():
    if not app_config.ENABLE_PROMPT_STUDIO:
        return
    load_initial_data()
    query_project_maps()
    update_refesher = PromptCacheRefresher(120)
    update_refesher.start()
    logger.info(f"ENABLE_PROMPT_STUDIO is true data loading....")


def contains_scenes(scenes_id):
    return scenes_id in prompt_data


def get_model_by_project(project_id, stage):
    if project_id not in current_project:
        return None
    for scenes_id in current_project[project_id]:
        if scenes_id + stage in prompt_data:
            return scenes_id
    logger.error(f"Get model by project {project_id} error, scenes is None.")
    return None


def update_prompt(req):
    with lock:
        scenes_id = req.scenesId
        logger.info(f"Update prompt scenesId={scenes_id}")
        prompt = req.promptValues
        few_shot = req.fewShotValues
        prompt_data[scenes_id] = prompt
        few_shot_data[scenes_id] = few_shot


class PromptCacheRefresher(LoopRunnable):
    def run_unthrowable(self):
        load_initial_data()
        query_project_maps()
