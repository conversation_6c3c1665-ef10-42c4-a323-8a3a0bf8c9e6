from datetime import datetime

from colorama import Fore, Style

from common.llm.general import llm_predict
from common.logging.logger import get_logger
from config.app_config import DEFAULT_MODEL_TYPE
from nl2time_query.nl2time_query import RelativeTimeFunction, SpecificTimeFunction

logger = get_logger(__name__)


def test_relative_time():
    f1 = RelativeTimeFunction(type="recentMonths", months=2)
    print(f1.to_date())


def test_specific_time():
    f1 = SpecificTimeFunction(type="specificDate", year=2023, month=12, day=30)
    assert f1.to_date() == datetime(year=2023, month=12, day=30)


prompt = """
Human: 
我希望你扮演专业的数据分析师，你精通SQL，专注于从用户问题中提取where条件参数。
请用中文回答。

你的目标是：根据问题的描述，只提取出符合MySQL语法的过滤where条件，忽略其他参数的提取，返回结果应遵循以下JSON格式:
```json
{
    "where": "dim1 = 'value1'"
}
```

你需要了解的信息包括：指标([Metric])和维度([Dimension])列表的信息
[Metric]指标列表的格式为(name: #指标英文字段, description: #指标中文含义)
[Dimension]维度列表的格式为(name: #维度英文字段, description: #维度中文含义, values: #维度码值)


你的处理步骤为：
1. 分析[Question]对应的问题
2. 确定相关指标和维度
3. 构建where条件

构建 where 条件的注意事项：
- **不要**提取时间相关的 where 条件
- **不要**提取 select、order by、group by 等其他SQL语句，我会在单独的提参任务中处理
- **不要**提取 order by（排序）、group by（分组）等其他SQL语句
- where 条件是一个标准的SQL片段，根据问题描述提取与数值比较相关的过滤信息，使用[Metric]或[Dimension]中的信息形成where条件；若无相关过滤信息，则返回空字符串
- 可以包含多个子条件，使用逻辑运算符AND、OR等连接。
- 子条件的右值**不能**是 select 子查询，只能是具体的值，如果找不到，请忽略该条件
- 子条件的右值如果在[Dimension]中存在完全一致的，优先使用完全一致的
- 用到的过滤信息的右值需要在[Dimension]对应项的values列表中选取最为匹配的，或者是一个具体的数字
- 当匹配到[Metric]指标的时候，只有当[Question]中要求某个指标等于、大于或者小于具体数字的时候才提取指标相关的过滤条件，其他时候不提取指标相关的过滤条件
- 不要添加无关的字段的过滤条件，不要进行假设猜想
- 不要添加 IS NOT NULL 的判断条件



下面是几个实例，每个示例提供的信息中[Question]是问题、[Metric]是指标信息、[Dimension]是维度码值信息，[Think]是思考过程，[Result]是根据思考过程得到的提参结果。请你仔细分析每个示例中的[Think]思考过程，学习如何从用户问题，指标列表和维度列表信息中提取出我想要的参数。



[Question]: 上海分行和广东分行2023年上半年哪个分行利息收入均值高
[Metric]:
(name: I1000_SUM, description: 利息收入)
(name: I2000_SUM, description: 总销售营收)
(name: I3500_SUM, description: 线下销售利润)

[Dimension]:
(name: bch_nme, description: 分行名称, values: ["北京市分行", "广西壮族自治区分行", "上海市分行"])
(name: sap_cnl_csf, description: 销售渠道, values: ["直销-网络发卡", "数据库营销"])

[Think]: 

让我们来一步一步思考
Question 需要同时查询上海分行和广东分行这两个的数据，对应的是bch_nme中的 '上海市分行' 和 '广东省分行'， 他们之间的关系是并列关系
因此提取 "bch_nme='上海市分行' or bch_nme='广东省分行'" 作为 where 参数
    
[Result]:
```json
{
    "where": "bch_nme='上海市分行' or bch_nme='广东省分行'"
}
```


[Question]: 23年以来广西壮族自治区分行手续费支出-银联收单品牌服务费支出趋势情况
[Metric]:
(name: C1505_SUM, description: 手续费支出-银联收单品牌服务费支出)
(name: I2000_SUM, description: 总销售营收)
(name: I3500_SUM, description: 线下销售利润)

[Dimension]:
(name: bch_nme, description: 分行名称, values: ["北京市分行", "广西壮族自治区分行", "上海市分行"])
(name: sap_cnl_csf, description: 销售渠道, values: ["直销-网络发卡", "数据库营销"])

[Think]: 

让我们来一步一步思考
Question 中的"23年以来"为时间信息，时间列不用提取为 where 参数   
Question 中的"广西壮族自治区分行"这个过滤条件与 Dimension 中的"bch_nme"的码值'广西壮族自治区分行'最为匹配，因此提取"bch_nme='广西壮族自治区分行'" 作为 where 参数
合并上述过滤条件，结果为 "bch_nme = '广西壮族自治区分行'
    
[Result]:
```json
{
    "where": "bch_nme = '广西壮族自治区分行'"
}
```


[Question]: 各发卡渠道的收费产品收入分别是多少
[Metric]:
(name: C1505_SUM, description: 手续费支出-银联收单品牌服务费支出)
(name: I2000_SUM, description: 总销售营收)
(name: I1307_SUM, description: 收费产品收入)

[Dimension]:
(name: bch_nme, description: 分行名称, values: ["北京市分行", "广西壮族自治区分行", "上海市分行"])
(name: sap_cnl_csf, description: 销售渠道, values: ["直销-网络发卡", "数据库营销"])

[Think]: 

让我们来一步一步思考:
Question中没有任何过滤条件，因此where条件为空
        
[Result]:
```json
{
    "where": ""
}
```


[Question]: 23年上半年上海分行线下营销渠道的总销售营收趋势情况
[Metric]:
(name: C1505_SUM, description: 手续费支出-银联收单品牌服务费支出)
(name: I2000_SUM, description: 总销售营收)
(name: I3500_SUM, description: 线下销售利润)

[Dimension]:
(name: bch_nme, description: 分行名称, values: ["北京市分行", "广西壮族自治区分行", "上海市分行"])
(name: sap_cnl_csf, description: 销售渠道, values: ["直销-网络发卡", "数据库营销"])

[Think]: 

让我们来一步一步思考:
Question 中的"23年上半年"为时间信息，时间列不用提取为 where 参数
Question 中的"上海分行"这个过滤条件与 Dimension 中的"bch_nme"的码值 "上海市分行" 最为匹配，因此提取"bch_nme='上海市分行'" 作为 where 参数
Question 中的"线下营销渠道"这个过滤条件与 Dimension 中的 "sap_cnl_csf" 的码值 '线下营销' 最为匹配，因此提取 "sap_cnl_csf='线下营销'" 作为 where 参数
合并上述过滤条件，结果为 bch_nme = '上海市分行' AND sap_cnl_csf='线下营销'
    
[Result]:
```json
{
    "where": "bch_nme = '上海市分行' AND sap_cnl_csf='线下营销'"
}
```


[Question]: 查询当日申购大于1000万的产品名称
[Metric]:

[Dimension]:
(name: c_pfundabbr, description: 产品名称, values: ["优利短债", "同利LOF", "增利短债", "安悦90天", "安利短债", "安恒60天", "招利短债", "安怡30天", "弘择"])

[Think]: 

让我们来一步一步思考
Question 中的"当日申购"和 Dimension 中的"f_buy_today"当日申购（万） 最匹配，和其条件'大于1000万'，因此提取"f_buy_today>1000" 作为 where 参数
    
[Result]:
```json
{
    "where": "f_buy_today>1000"
}
```


下面是用户的问题，请给出你的[Think]和[Result](JSON 提参结果)。注意:请以json格式给出提参结果，不要附带任何其他信息 
[Question]: 广东省分行2022年下半年业务部门-生产费用是多少？
[Metric]:
(name: C2000_SUM, description: 营业费用)(name: C2400_SUM, description: 生产费用)(name: C2500_SUM, description: 期间费用)(name: C4203_SUM, description: 分行占卡中心营销费用)(name: C6110_SUM, description: 业务部门)(name: C6111_SUM, description: 业务部门-人事费用)(name: C6112_SUM, description: 业务部门-办公费用)(name: C6113_SUM, description: 业务部门-营销费用)(name: C6116_SUM, description: 业务部门-生产费用)(name: C6126_SUM, description: 客服-生产费用)(name: C6202_SUM, description: 信审-生产费用)(name: C7306_SUM, description: 资产业务-生产费用)(name: I1301_SUM, description: 年费)(name: VVA17_SUM, description: 消费量)(name: VVA25_SUM, description: 年费收入)
[Dimension]:
(name: bch_id, description: 分行号, values: ["11", "71", "73", "74", "75"])(name: bch_nme, description: 分行名称, values: ["广东省分行", "东莞分行"])(name: bln_bch_id, description: 直属父级分行号, values: ["11", "71", "76"])(name: bln_bch_nme, description: 直属父级分行名称, values: ["上海市分行", "广东省分行", "广西壮族自治区分行"])(name: sap_cnl, description: 发卡渠道代码, values: ["2"])(name: sap_cnl_csf, description: 发卡渠道分类, values: ["分行"])
[Think]:
"""


def test_prompt():
    response_text = llm_predict(prompt, DEFAULT_MODEL_TYPE)
    logger.info(Fore.CYAN + "大模型返回：" + response_text + Fore.RESET)
