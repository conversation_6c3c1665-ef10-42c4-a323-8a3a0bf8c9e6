from typing import Optional
from pydantic import BaseModel

from common.llm.general import create_chat_model, create_chat_model_in_chain
from common.logging.logger import get_logger
from common.prompt_selector.prompt_selector_base import PromptSelectorBase
from common.types import TimeDimensionParam
from common.types.base import ParamsExtractStage, CHAIN_META, ChainMeta
from common.utils.json_utils import extract_json_from_string
from langchain_core.runnables import <PERSON><PERSON><PERSON>Lambda, RunnableConfig, RunnableBranch
from langchain_core.output_parsers import StrOutputParser

from metastore import get_metastore

logger = get_logger(__name__)


def retrieve_for_time_dimension(question: str, config: RunnableConfig):
    project_id = config[CHAIN_META][ChainMeta.PROJECT_ID]
    model_name = config[CHAIN_META][ChainMeta.MODEL_NAME]
    metastore = get_metastore(project_id)
    dimensions = metastore.list_dimensions_by_model_name(model_name, "time")
    dimensions = list(dimensions.values())
    if isinstance(question, dict):
        question = question["question"]
    return {
        "dimensions": dimensions,
        "question": question,
    }


def time_dimension_verify(input, config: RunnableConfig):
    if input is None:
        return None
    project_id = config[CHAIN_META][ChainMeta.PROJECT_ID]
    model_name = config[CHAIN_META][ChainMeta.MODEL_NAME]
    metastore = get_metastore(project_id)
    dimensions = metastore.list_dimensions_by_model_name(model_name, "time")
    if input.time_dimension[0] in dimensions:
        return input
    else:
        return None


def call_nl2time_dimension(
    model_type: str, prompt_selector: PromptSelectorBase
) -> Optional[TimeDimensionParam]:
    llm_chain = (
        RunnableLambda(
            prompt_selector.gen_prompt,
            name="PromptSelectorBase.gen_prompt:"
            + ParamsExtractStage.NL2TIME_DIMENSION,
        ).bind(stage=ParamsExtractStage.NL2TIME_DIMENSION)
        | RunnableLambda(create_chat_model_in_chain).bind(
            stage=ParamsExtractStage.NL2TIME_DIMENSION
        )
        | StrOutputParser()
        | RunnableLambda(
            parse_time_dimension_response, name="parse_time_dimension_response"
        )
        | RunnableLambda(time_dimension_verify, name="time_dimension_verify")
    )
    chain = RunnableLambda(
        retrieve_for_time_dimension, name="retrieve_for_time_dimension"
    ) | RunnableBranch(
        (lambda x: len(x["dimensions"]) == 0, lambda x: None),
        llm_chain,
    )
    chain.name = ParamsExtractStage.NL2TIME_DIMENSION
    return chain


# 解析响应
def parse_time_dimension_response(response: str) -> Optional[TimeDimensionParam]:
    obj = extract_json_from_string(response, "parse_time_query_response")
    if obj is None:
        logger.info("大模型没有返回有效的时间维度，返回 None：")
        return None
    # 使用 Pydantic 模型进行验证
    if not obj.get("time_dimension"):
        logger.error("大模型返回的结果不包含time_dimension: " + str(obj))
        return None
    return TimeDimensionParam(**obj)
