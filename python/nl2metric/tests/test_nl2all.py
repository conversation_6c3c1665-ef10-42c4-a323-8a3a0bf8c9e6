import csv
import json
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, TypedDict, Union

import pytest
import yaml
from colorama import Fore
from pydantic import BaseModel, Field

from common.db_model.model import (
    get_semantic_project_by_name,
    get_semantic_project_by_id,
    get_semantic_model_by_name,
)
from common.llm.general import create_chat_model, llm_predict
from common.logging.logger import get_logger
from common.prompt_selector.json_prompt_selector import MemPromptSelector
from common.types import QueryMetricResult
from common.types.base import (
    ChainMeta,
    ChainRuntime,
    CHAIN_META,
    gen_chain_meta,
    JobType,
)
from common.types.callback_handler import <PERSON>g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MemCallbackHandler
from common.utils.set_utils import (
    list_more_than,
    optional_list_contains,
    optional_set_equals,
)
from config.app_config import (
    DEFAULT_MODEL_TYPE,
    MODEL_TYPE_AZURE_GPT,
    NL2ALL_NAME,
    VLLM_MIXTRAL_MODEL_NAME,
    MODEL_TYPE_VLLM_MIXTRAL,
    MODEL_TYPE_GPT4,
)
from nl2attr_params.nl2attr_params import AttrParams, call_nl2attr_params
from nl2intent.nl2intent import IntentParams
from nl2meta.nl2meta import MetaIntentParams, call_nl2meta
from nl2metric.few_shots import JINGFEN_BI
from nl2metric.query_groupby import query_group_bys
from nl2metric.query_metric import query_metrics
from nl2metric.query_order_by import query_order_bys
from nl2metric.query_where import query_where
from nl2metric.service import replace_keywords
from nl2time_attr.nl2time_attr import call_nl2time_attr
from nl2time_query.nl2time_query import TimeQueryParams
from nl2time_query.nl2time_query import call_nl2time_query
from pre_filter.service import get_pre_filter_service
from yml_utils import replace_date_in_file

logger = get_logger(__name__)

name_to_test_case = {
    # "中原银行": {
    #     "model_name": "ggj_dtl_info",
    #     "project_id": get_semantic_project_by_name("某银行").id,
    #     "test_file": "test_nl2all_zhongyuan.yml",
    #     "model_type": DEFAULT_MODEL_TYPE,
    # },
    # "中原银行temp": {
    #     "model_name": "ggj_dtl_info",
    #     "project_id": get_semantic_project_by_name("某银行").id,
    #     "test_file": "test_temp.yml",
    #     "model_type": DEFAULT_MODEL_TYPE,
    # },
    # "中原银行swift": {
    #     "model_name": "ggj_dtl_info",
    #     "project_id": get_semantic_project_by_name("某银行").id,
    #     "test_file": "test_nl2all_zhongyuan_swift_agent.yml",
    #     "model_type": DEFAULT_MODEL_TYPE,
    # },
    "示例项目": {
        "model_name": "wide_table",
        "project_id": get_semantic_project_by_name("示例项目").id,
        "test_file": "test_nl2all_jiaohang.yml",
        "model_type": DEFAULT_MODEL_TYPE,
    },
    "天弘": {
        "model_name": "vt_wind_etf_flat_table",
        # "model_name": "vt_subscription_and_redemption_info",
        "project_id": get_semantic_project_by_name("天弘").id,
        "test_file": "test_nl2all_tianhong.yml",
        "model_type": MODEL_TYPE_AZURE_GPT,
    },
    "天弘etf": {
        "model_name": "vt_wind_etf_flat_table",
        # "model_name": "vt_subscription_and_redemption_info",
        "project_id": get_semantic_project_by_name("天弘").id,
        "test_file": "test_nl2all_tianhong_etf.yml",
        "model_type": MODEL_TYPE_AZURE_GPT,
    },
    "天弘subscription": {
        "model_name": "vt_subscription_and_redemption_info",
        "project_id": get_semantic_project_by_name("天弘").id,
        "test_file": "test_nl2all_tianhong_subscription.yml",
        "model_type": MODEL_TYPE_AZURE_GPT,
    },
    "天弘申购": {
        "model_name": "vt_subscription_and_redemption_info",
        "project_id": get_semantic_project_by_name("天弘").id,
        "test_file": "test_nl2all_tianhong_sg.yml",
        "model_type": MODEL_TYPE_AZURE_GPT,
    },
    # "宝武": {
    #     "model_name": "flat_table_all_report_items_metrics",
    #     "project_id": get_semantic_project_by_name("宝武").id,
    #     "test_file": "test_nl2all_baowu.yml",
    #     "model_type": DEFAULT_MODEL_TYPE,
    # },
    "电信经分BI": {
        "model_name": "dipeak.askdoc.vt_inner_winbidder_detail",
        "project_id": get_semantic_project_by_name("电信经分BI").id,
        "test_file": "test_jingfen_bi.yml",
        "model_type": DEFAULT_MODEL_TYPE,
    },
    "电信招标": {
        "model_name": "dipeak.csv.vt_zhaobiao_dateformat",
        "project_id": get_semantic_project_by_name("电信经分BI").id,
        "test_file": "test_dianxin_zhaobiao.yml",
        "model_type": DEFAULT_MODEL_TYPE,
    },
    "烽火1": {
        "model_name": "dipeak.fenghuopoc.vt_t_project_with_bugdet_detail",
        "project_id": get_semantic_project_by_name("烽火").id,
        "test_file": "test_nl2all_fenghuo1.yml",
        "model_type": DEFAULT_MODEL_TYPE,
    },
    "烽火3": {
        "model_name": "dipeak.fenghuopoc.vt_t_return_detail_reform",
        "project_id": get_semantic_project_by_name("烽火").id,
        "test_file": "test_nl2all_fenghuo3.yml",
        "model_type": DEFAULT_MODEL_TYPE,
    },
    "烽火5": {
        "model_name": "dipeak.fenghuopoc.vt_t_inventory_detail_reform",
        "project_id": get_semantic_project_by_name("烽火").id,
        "test_file": "test_nl2all_fenghuo5.yml",
        "model_type": DEFAULT_MODEL_TYPE,
    },
    "烽火7": {
        "model_name": "dipeak.fenghuopoc.vt_t_ticket_detail",
        "project_id": get_semantic_project_by_name("烽火").id,
        "test_file": "test_nl2all_fenghuo7.yml",
        "model_type": DEFAULT_MODEL_TYPE,
    },
    "烽火9": {
        "model_name": "dipeak.fenghuopoc.vt_t_guarantee_detail",
        "project_id": get_semantic_project_by_name("烽火").id,
        "test_file": "test_nl2all_fenghuo9.yml",
        "model_type": DEFAULT_MODEL_TYPE,
    },
}

model_type = name_to_test_case[NL2ALL_NAME]["model_type"]
project_id = name_to_test_case[NL2ALL_NAME]["project_id"]
model_name = name_to_test_case[NL2ALL_NAME]["model_name"]
test_file = name_to_test_case[NL2ALL_NAME]["test_file"]

# 启用测试的白名单

testCaseIndices: List[int] = [10, 11, 14, 32]

now = datetime.now()
formatted_time = now.strftime("%Y-%m-%d %H:%M:%S")
llm_model_type = os.environ.get("DEFAULT_MODEL_TYPE")

if llm_model_type is not None:
    print(f"The value of DEFAULT_MODEL_TYPE is: {llm_model_type}")
    llm_model_type = llm_model_type + "_"
else:
    llm_model_type = ""
    print("DEFAULT_MODEL_TYPE is not set in the environment.")

folder_path = "./tmp"
if not os.path.exists(folder_path):
    os.makedirs(folder_path)
file_pattern = os.path.join(folder_path, "{}_{}_{}.txt")
debug_mode = True

test_result_log = dict()


class TimeAttrParams(TypedDict):
    timeStartFunction: Dict[str, Optional[int]]
    timeEndFunction: Dict[str, Optional[int]]


class AnalysisTimeAttrParams(TypedDict):
    baseTime: TimeAttrParams
    compareTime: TimeAttrParams


class MetricParams(BaseModel):
    metricNames: List[str]
    where: Optional[List[str]] = Field(default_factory=list)
    groupBys: Optional[List[Union[str, List[str]]]] = Field(default_factory=list)
    orderBys: Optional[List[Union[str, List[str]]]] = Field(default_factory=list)
    timeQueryParams: Optional[TimeQueryParams] = None


def extract_as_set(key: str, s: Dict) -> Set[str]:
    if s is None or key not in s or not s.get(key):
        return set()
    elements = s.get(key)
    if isinstance(elements, str):
        return {elements}
    return {str(e) if e is not str else e for e in elements}


def compare_result_list(case: dict, result_list: List[QueryMetricResult]):
    failed_reason = []
    all_succeed = True
    if "queryParamsList" in case:
        query_params_list = case["queryParamsList"]
        for i, query_params in enumerate(query_params_list):
            if i < len(result_list):
                succeed, reason = compare_result(query_params, result_list[i])
                if not succeed:
                    all_succeed = False
                    failed_reason.extend(reason)
            else:
                failed_reason.append(f"Query decomposition failed, sub query {i}")
                all_succeed = False
        return all_succeed, failed_reason
    else:
        query_params = case["queryParams"]
        return compare_result(query_params, result_list[0])


def compare_result(
    query_params: Dict, result: QueryMetricResult
) -> Tuple[bool, List[str]]:
    # Extract from case labels
    metric_names = extract_as_set("metricNames", query_params)
    group_by_names = query_params.get("groupBys")
    where_constraint = extract_as_set("where", query_params)
    order_by_names = extract_as_set("orderBys", query_params)
    limit = query_params.get("limit")

    # Extract from response
    response_metric_names = result.metricNames
    response_group_by_names = result.groupBys
    response_where_constraint = result.where
    response_order_by_names = result.orderBys
    response_limit = result.limit

    success = True
    case_failed_reason = []
    if not list_more_than(response_metric_names, metric_names):
        msg = f"Metric names not matched: {response_metric_names} vs {metric_names}"
        case_failed_reason.append(msg)
        logger.info(f"✗ {msg}")
        success = False
    if not optional_list_contains(response_group_by_names, group_by_names):
        msg = f"Group by names not matched: {response_group_by_names} vs {group_by_names} "
        case_failed_reason.append(msg)
        logger.info(f"✗ {msg}")
        success = False
    if (response_where_constraint or where_constraint) and (
        response_where_constraint.lower().replace(" ", "")
        not in [w.lower().replace(" ", "") for w in where_constraint]
    ):
        msg = f"Where constraint not contained: {response_where_constraint} vs {where_constraint}"
        case_failed_reason.append(msg)
        logger.info(f"✗ {msg}")
        success = False
    if not optional_set_equals(order_by_names, response_order_by_names):
        msg = (
            f"Order by names not matched: {response_order_by_names} vs {order_by_names}"
        )
        case_failed_reason.append(msg)
        logger.info(f"✗ {msg}")
        success = False
    if limit != response_limit:
        msg = f"Limit not matched: {response_limit} vs {limit}"
        case_failed_reason.append(msg)
        logger.info(f"✗ {msg}")
        success = False
    return success, case_failed_reason


class YmlTestAll(TypedDict):
    index: str
    question: str
    unready: Optional[bool]
    intentParams: Optional[IntentParams]
    timeQueryParams: Optional[TimeQueryParams]
    timeQueryParamsOptions: Optional[List[TimeQueryParams]]
    timeAttrParams: Optional[AnalysisTimeAttrParams]
    timeAttrParamsOptions: Optional[List[AnalysisTimeAttrParams]]
    queryParams: Optional[MetricParams]
    attrParams: Optional[AttrParams]
    attrParamsOptions: Optional[List[AttrParams]]
    metaIntentParams: Optional[MetaIntentParams]
    modelName: Optional[str]


def load_tests_data_directly(test_filename):
    file_path = Path(__file__).parent / test_filename
    with open(file_path, "r", encoding="utf8") as file:
        file_content = file.read()

    # 替换当前年月日
    file_content = replace_date_in_file(file_content)

    tests_data = yaml.safe_load(file_content)["tests"]

    if testCaseIndices:
        tests_data = [
            test
            for test in tests_data
            if int(test["index"]) in testCaseIndices and not test.get("unready")
        ]
    else:
        tests_data = [test for test in tests_data if not test.get("unready")]

    data_query = []
    data_attr = []
    data_others = []

    for case in tests_data:
        if case["intent"] == "查数":
            data_query.append(case)
        elif case["intent"] == "归因":
            data_attr.append(case)
        else:
            data_others.append(case)

    return tests_data, data_query, data_attr, data_others


data_all, data_query, data_attr, data_others = load_tests_data_directly(
    test_filename=test_file
)


def strify(obj):
    if isinstance(obj, list):
        return ",".join([strify(item) for item in obj])
    elif isinstance(obj, str):
        return obj
    elif isinstance(obj, dict):
        return json.dumps(obj, default=strify)
    elif isinstance(obj, (int, float)):
        return str(obj)
    elif isinstance(obj, bool):
        return str(obj).lower()
    elif obj is None:
        return ""
    else:
        raise TypeError(f"Unhandled type: {type(obj)}")


check_sql_params_prompt = """你是一个SQL专家，我需要你帮我检查一下两组SQL的过滤条件参数是否相等。
以下是两组过滤条件参数，其中一组是模型生成的，另一组是人工生成的，我需要你判断一下模型生成的是否准确，判定规则如下：
1. 人工可能会生成多条可能的过滤条件参数，模型生成的等于其中一条即判断为准确
2. 过滤条件中的参数顺序不影响准确性
3. 一般认为'in'和'='是等价的

模型生成的:
{model_where_str}
人工生成的:
{expect_where_str}

你只需要输出true or false即可
"""


def llm_check_equal(input_val: str, candidates: Union[List[str]]) -> bool:
    if candidates is None or not isinstance(candidates, List):
        return False
    _prompt = check_sql_params_prompt.format(
        model_where_str=input_val,
        expect_where_str="\n".join(
            [candidate if candidate else " " for candidate in candidates]
        ),
    )
    result = llm_predict(
        _prompt,
        model_type=MODEL_TYPE_GPT4,
        temperature=0.0,
        timeout=60,
        max_tokens=1024,
    )
    logger.info(f"LLM check equal: {_prompt} -> {result}")
    return result.lower().strip() == "true"


def get_last_llm_input_output(traces: [dict]) -> tuple[str, str]:
    # 获取最后一个LLM的输入输出
    for trace in traces[::-1]:
        if trace["name"] == "ChatOpenAI":
            return trace["inputs"], trace["outputs"]


for test_case in data_all:
    test_result_log[int(test_case["index"])] = {
        "index": test_case["index"],
        "question": test_case["question"],
        "expect_queryMetric": (
            strify(test_case["queryParams"]["metricNames"])
            if "queryParams" in test_case and "metricNames" in test_case["queryParams"]
            else ""
        ),
        "predict_metric": "",
        "metric_equal": "",
        "metric_llm_input": "",
        "metric_llm_output": "",
        "expect_where": (
            strify(test_case["queryParams"]["where"])
            if "queryParams" in test_case and "where" in test_case["queryParams"]
            else ""
        ),
        "predict_where": "",
        "where_llm_input": "",
        "where_llm_output": "",
        "where_equal": "",
        "expect_groupBy": (
            strify(test_case["queryParams"]["groupBys"])
            if "queryParams" in test_case and "groupBys" in test_case["queryParams"]
            else ""
        ),
        "predict_group_bys": "",
        "group_by_equal": "",
        "groupby_llm_input": "",
        "groupby_llm_output": "",
        "expect_orderBy": (
            strify(test_case["queryParams"]["orderBys"])
            if "queryParams" in test_case and "orderBys" in test_case["queryParams"]
            else ""
        ),
        "predict_order_bys": "",
        "order_by_equal": "",
        "orderby_llm_input": "",
        "orderby_llm_output": "",
    }

mem_cb = MemCallbackHandler()


def gen_chain_param(project_id, model_name, model_type, question):
    project_name = get_semantic_project_by_id(project_id).name
    prompt_selector = MemPromptSelector(
        project_name=project_name,
        project_id=project_id,
        model_name=model_name,
        model_type=model_type,
    )
    model = get_semantic_model_by_name(project_id, model_name)
    chain_metadata = gen_chain_meta(
        job_type=JobType.PARAMS_EXTRACT,
        project_name=project_name,
        project_id=project_id,
        model_name=model.table_name,
        model_label=model.label,
        model_id=model.id,
        model_type=model_type,
        prompt_selector=prompt_selector,
        force_exact_match=False,
    )
    chain_metadata[ChainMeta.RUN_TIME][ChainRuntime.QUESTION] = question
    log_cb = LogCallbackHandler(id="")
    config = {
        CHAIN_META: chain_metadata,
        "callbacks": [log_cb, mem_cb],
        "max_concurrency": 2,
    }
    return prompt_selector, config


class TestAll(object):
    # @pytest.mark.parametrize(
    #     "test_case",
    #     data_all,
    #     ids=lambda test: (
    #         f"#{test['index']}: {test['question']}"
    #         if isinstance(test, dict)
    #         else "Unknown test case"
    #     ),
    # )
    # def test_nl2intent(self, test_case: YmlTestAll):
    #     if "modelName" in test_case and test_case["modelName"] != "":
    #         model_name = test_case["modelName"]
    #     else:
    #         model_name = name_to_test_case[NL2ALL_NAME]["model_name"]
    #     intent_params = call_nl2intent(
    #         model_type=model_type,
    #         prompt_selector=prompt_selector,
    #     )
    #     response = intent_params.invoke(test_case, config=config)
    #     logger.info(
    #         Fore.YELLOW + f"提取的 IntentParams 为：\n%s" + Fore.RESET,
    #         response.model_dump(exclude_none=True) if intent_params else "None",
    #     )
    #     logger.info(
    #         Fore.YELLOW + f"期望的 IntentParams 为：\n%s" + Fore.RESET,
    #         test_case["intentParams"],
    #     )
    #     if "intentParams" in test_case:
    #         assert test_case["intentParams"] == response.model_dump(
    #             exclude_none=True
    #         )
    #     else:
    #         pytest.fail("Test case does not contain 'intentParams'")

    # @pytest.mark.parametrize(
    #     "test_case",
    #     data_query,
    #     ids=lambda test: (
    #             f"#{test['index']}: {test['question']}"
    #             if isinstance(test, dict)
    #             else "Unknown test case"
    #     ),
    # )
    # def test_nl2time_query(self, test_case: YmlTestAll):
    #     time_query_chain = call_nl2time_query(
    #         model_type=model_type, prompt_selector=prompt_selector
    #     )
    #     time_query = time_query_chain.invoke(test_case, config=config)
    #     logger.info(
    #         Fore.YELLOW + f"提取的 TimeQuery 为：\n%s" + Fore.RESET,
    #         time_query.model_dump(exclude_none=True) if time_query else "None",
    #     )
    #     if "timeQueryParamsOptions" in test_case["queryParams"]:
    #         logger.info(
    #             Fore.YELLOW + f"期望的 timeQueryParamsOptions 为：\n%s" + Fore.RESET,
    #             test_case["queryParams"]["timeQueryParamsOptions"],
    #         )
    #         assert time_query and (
    #                 time_query.model_dump(exclude_none=True)
    #                 in test_case["queryParams"]["timeQueryParamsOptions"]
    #         )
    #     elif "timeQueryParams" in test_case["queryParams"]:
    #         logger.info(
    #             Fore.YELLOW + f"期望的 TimeQuery 为：\n%s" + Fore.RESET,
    #             test_case["queryParams"]["timeQueryParams"],
    #         )
    #         if not time_query:
    #             assert test_case["queryParams"]["timeQueryParams"] is None
    #         else:
    #             assert test_case["queryParams"][
    #                        "timeQueryParams"
    #                    ] == time_query.model_dump(exclude_none=True)
    #     else:
    #         pytest.fail(
    #             "Test case does not contain 'timeQueryParams' or 'timeQueryParamsOptions'"
    #         )

    # @pytest.mark.parametrize(
    #     "test_case",
    #     data_query,
    #     ids=lambda test: (
    #             f"#{test['index']}: {test['question']}"
    #             if isinstance(test, dict)
    #             else "Unknown test case"
    #     ),
    # )
    # def test_nl2metric(self, test_case: YmlTestAll):
    #     if "modelName" in test_case:
    #         model_name = test_case["modelName"]
    #     else:
    #         model_name = name_to_test_case[NL2ALL_NAME]["model_name"]
    #     # question = test_case["question"]
    #     nl2metric_ctx = gen_nl2metric_ctx(prompt_selector,
    #                                       project_id,
    #                                       NL2ALL_NAME,
    #                                       model_name,
    #                                       model_type)
    #
    #     chain = nl2metric(
    #         nl2metric_ctx
    #     )
    #     result_dict = chain.invoke(
    #         test_case, config=config
    #     )
    #     result = result_dict["query_metric"]
    #     result_list = []
    #     # success, case_failed_reason = compare_result_list(
    #     #     class_to_dict(test_case), result_list if result_list else [result]
    #     # )
    #
    #     test_case_dict = class_to_dict(test_case)
    #     res = result_list if result_list else [result]
    #     success, case_failed_reason = compare_result_list(test_case_dict, res)
    #     test_case_dict_string = "\n".join(
    #         [f"{key}={value}" for key, value in test_case_dict.items()]
    #     )
    #     msg = "index {},test_case: {}\nresult:\n{}\n".format(
    #         test_case["index"], test_case_dict_string, res
    #     )
    #
    #     msg_result = "index {},success: {}\n case_failed-reason:{}\n".format(
    #         test_case["index"], success, case_failed_reason
    #     )
    #     print(msg)
    #     nl_metric_record_file = file_pattern.format(
    #         llm_model_type, "metric_diff", formatted_time
    #     )
    #     if debug_mode:
    #         with open(nl_metric_record_file, "a") as file:
    #             file.write(msg + "\n")
    #             file.write(msg_result + "\n")
    #
    #     assert success, case_failed_reason

    @pytest.mark.parametrize(
        "test_case",
        data_query,
        ids=lambda test: (
            f"#{test['index']}: {test['question']}"
            if isinstance(test, dict)
            else "Unknown test case"
        ),
    )
    def test_nl2time_query(self, test_case: YmlTestAll):
        prompt_selector, config = gen_chain_param(
            project_id=project_id,
            model_name=model_name,
            model_type=DEFAULT_MODEL_TYPE,
            question=test_case["question"],
        )
        time_query_chain = call_nl2time_query(DEFAULT_MODEL_TYPE, prompt_selector)
        time_query = time_query_chain.invoke(test_case, config=config)
        logger.info(
            Fore.YELLOW
            + f"""Query:{test_case["question"]}，提取的 Time 为：\n%s"""
            + Fore.RESET,
            time_query.model_dump(exclude_none=True) if time_query else "None",
        )
        if "timeQueryParams" in test_case["queryParams"]:
            if time_query:
                logger.info(
                    Fore.YELLOW + f"期望的 timeQueryParams 为：\n%s" + Fore.RESET,
                    test_case["queryParams"]["timeQueryParams"],
                )
                assert (
                    time_query.model_dump(exclude_none=True)
                    == test_case["queryParams"]["timeQueryParams"]
                )
            else:
                logger.info(
                    Fore.YELLOW + f"期望的 timeQueryParams 为：\n%s" + Fore.RESET,
                    test_case["queryParams"]["timeQueryParams"],
                )
                assert test_case["queryParams"]["timeQueryParams"] == None
        else:
            # pytest.fail(
            #     "Test case does not contain 'timeQueryParams'"
            # )
            assert time_query == None

    # @pytest.mark.parametrize(
    #     "test_case",
    #     data_others,
    #     ids=lambda test: (
    #         f"#{test['index']}: {test['question']}"
    #         if isinstance(test, dict)
    #         else "Unknown test case"
    #     ),
    # )
    # def test_nl2meta(self, test_case: YmlTestAll):
    #     if "modelName" in test_case:
    #         model_name = test_case["modelName"]
    #     else:
    #         model_name = name_to_test_case[NL2ALL_NAME]["model_name"]
    #     prompt_selector, config = gen_chain_param(
    #         project_id=project_id,
    #         model_name=model_name,
    #         model_type=model_type,
    #         question=test_case["question"],
    #     )
    #     result_chain = call_nl2meta(model_type, prompt_selector)
    #     result = result_chain.invoke(test_case, config=config)
    #     logger.info(
    #         Fore.YELLOW + f"提取的 metaIntentParams 为：\n%s" + Fore.RESET,
    #         result.model_dump(exclude_none=True) if result else "None",
    #     )
    #     logger.info(
    #         Fore.YELLOW + f"期望的 metaIntentParams 为：\n%s" + Fore.RESET,
    #         test_case["metaIntentParams"],
    #     )
    #     if "metaIntentParams" in test_case:
    #         assert test_case["metaIntentParams"] == result.model_dump()
    #     else:
    #         pytest.fail("Test case does not contain 'intentParams'")

    @pytest.mark.parametrize(
        "test_case",
        data_query,
        ids=lambda test: (
            f"#{test['index']}: {test['question']}"
            if isinstance(test, dict)
            else "Unknown test case"
        ),
    )
    def test_query_metrics(self, test_case: YmlTestAll):
        question = test_case["question"]
        if NL2ALL_NAME == JINGFEN_BI:
            logger.info(f"before replace key words, input: {question}")
            question = replace_keywords(question)
            logger.info(f"after replace key words, args: {question}")
        metrics = get_pre_filter_service(
            project_id=project_id, model_name=model_name
        ).retrieve_metrics(question)
        input = {"question": question, "metrics": metrics}
        prompt_selector, config = gen_chain_param(
            project_id=project_id,
            model_name=model_name,
            model_type=model_type,
            question=question,
        )
        config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.METRICS] = metrics
        query_metric_chain = query_metrics(
            prompt_selector=prompt_selector, model_type=model_type
        )
        response_metric_names = query_metric_chain.invoke(input, config=config)
        test_result_log[int(test_case["index"])]["predict_metric"] = strify(
            response_metric_names
        )
        # (
        #     test_result_log[int(test_case["index"])]["metric_llm_input"],
        #     test_result_log[int(test_case["index"])]["metric_llm_output"],
        # ) = get_last_llm_input_output(mem_cb.trace)
        if list_more_than(
            response_metric_names, test_case["queryParams"]["metricNames"]
        ):
            test_result_log[int(test_case["index"])]["metric_equal"] = True
        else:
            test_result_log[int(test_case["index"])]["metric_equal"] = False
        assert list_more_than(
            response_metric_names, test_case["queryParams"]["metricNames"]
        )

    @pytest.mark.parametrize(
        "test_case",
        data_query,
        ids=lambda test: (
            f"#{test['index']}: {test['question']}"
            if isinstance(test, dict)
            else "Unknown test case"
        ),
    )
    def test_query_group_bys(self, test_case: YmlTestAll):
        pre_filter_service = get_pre_filter_service(
            project_id=project_id, model_name=model_name
        )
        question = test_case["question"]
        question = "2024年1月，政法行业的招标单位数量环比增长多少？"
        replace_list = ["教育行业事业部", "教育行业", "金融行业", "政法行业", "交通物流"]
        if NL2ALL_NAME == JINGFEN_BI:
            logger.info(f"before replace key words, input: {question}")
            question = replace_keywords(question)
            logger.info(f"after replace key words, args: {question}")
        prompt_selector, config = gen_chain_param(
            project_id=project_id,
            model_name=model_name,
            model_type=model_type,
            question=question,
        )
        metrics = pre_filter_service.retrieve_metrics(question)
        dimensions = pre_filter_service.retrieve_dimensions(question, config)
        config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.METRICS] = metrics
        config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.DIMENSIONS] = dimensions
        query_group_by_chain = query_group_bys(prompt_selector, model_type)
        input = {
            "question": question,
            "metrics": metrics,
            "dimensions": dimensions,
        }
        response_dimension_names = query_group_by_chain.invoke(input, config=config)
        # (
        #     test_result_log[int(test_case["index"])]["groupby_llm_input"],
        #     test_result_log[int(test_case["index"])]["groupby_llm_output"],
        # ) = get_last_llm_input_output(mem_cb.trace)
        test_case_group_bys = test_case["queryParams"].get("groupBys", [])
        # msg = "index {},test_case: {}\nresult:\n{}\n".format(
        #     test_case["index"], test_case_group_bys, response_dimension_names
        # )
        # test_result_log[int(test_case["index"])]["predict_group_bys"] = ",".join(
        #     response_dimension_names
        # )
        # nl_group_by_record_file = file_pattern.format(
        #     llm_model_type, "group_diff", formatted_time
        # )
        # if debug_mode:
        #     with open(nl_group_by_record_file, "a") as file:
        #         file.write(msg + "\n")
        # if optional_list_contains(response_dimension_names, test_case_group_bys):
        #     test_result_log[int(test_case["index"])]["group_by_equal"] = True
        # else:
        #     test_result_log[int(test_case["index"])]["group_by_equal"] = False
        if test_case_group_bys is None:
            assert response_dimension_names == []
        elif len(test_case_group_bys) <= 1:
            assert optional_list_contains(response_dimension_names, test_case_group_bys)
        else:
            flag = False
            for option in test_case_group_bys:
                if optional_list_contains(response_dimension_names, option):
                    flag = True
                    break
            assert flag

    @pytest.mark.parametrize(
        "test_case",
        data_query,
        ids=lambda test: (
            f"#{test['index']}: {test['question']}"
            if isinstance(test, dict)
            else "Unknown test case"
        ),
    )
    def test_query_order_bys(self, test_case: YmlTestAll):
        print("project_id:", project_id)
        pre_filter_service = get_pre_filter_service(
            project_id=project_id, model_name=model_name
        )
        question = test_case["question"]
        if NL2ALL_NAME == JINGFEN_BI:
            logger.info(f"before replace key words, input: {question}")
            question = replace_keywords(question)
            logger.info(f"after replace key words, args: {question}")
        prompt_selector, config = gen_chain_param(
            project_id=project_id,
            model_name=model_name,
            model_type=model_type,
            question=question,
        )
        dimensions = pre_filter_service.retrieve_dimensions(question, config)
        metrics = pre_filter_service.retrieve_metrics(question)
        input = {
            "question": question,
            "metrics": metrics,
            "dimensions": dimensions,
        }
        config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.METRICS] = metrics
        config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.DIMENSIONS] = dimensions
        query_order_by_chain = query_order_bys(
            prompt_selector=prompt_selector,
            model_type=model_type,
        )
        response_order_bys, response_limit = query_order_by_chain.invoke(
            input, config=config
        )
        test_result_log[int(test_case["index"])]["predict_order_bys"] = ",".join(
            response_order_bys
        )
        # (
        #     test_result_log[int(test_case["index"])]["orderby_llm_input"],
        #     test_result_log[int(test_case["index"])]["orderby_llm_output"],
        # ) = get_last_llm_input_output(mem_cb.trace)

        if optional_set_equals(
            response_order_bys, test_case["queryParams"].get("orderBys", [])
        ):
            test_result_log[int(test_case["index"])]["order_by_equal"] = True
        else:
            test_result_log[int(test_case["index"])]["order_by_equal"] = False
        assert optional_set_equals(
            response_order_bys, test_case["queryParams"].get("orderBys", [])
        )
        assert response_limit == test_case["queryParams"].get("limit")

    @pytest.mark.parametrize(
        "test_case",
        data_query,
        ids=lambda test: (
            f"#{test['index']}: {test['question']}"
            if isinstance(test, dict)
            else "Unknown test case"
        ),
    )
    def test_query_where(self, test_case: YmlTestAll):
        pre_filter_service = get_pre_filter_service(
            project_id=project_id, model_name=model_name
        )
        question = test_case["question"]
        if NL2ALL_NAME == "电信经分BI" or NL2ALL_NAME == "电信招标":
            logger.info(f"before replace key words, input: {question}")
            question = replace_keywords(question)
            logger.info(f"after replace key words, args: {question}")

        prompt_selector, config = gen_chain_param(
            project_id=project_id,
            model_name=model_name,
            model_type=model_type,
            question=question,
        )

        dimensions = pre_filter_service.retrieve_dimensions(question, config)
        metrics = pre_filter_service.retrieve_metrics(question)
        config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.METRICS] = metrics
        config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.DIMENSIONS] = dimensions
        input = {
            "question": question,
            "metrics": metrics,
            "dimensions": dimensions,
        }
        response_where_chain = query_where(prompt_selector, model_type)
        response_where = response_where_chain.invoke(input, config=config)
        test_result_log[int(test_case["index"])]["predict_where"] = response_where
        # (
        #     test_result_log[int(test_case["index"])]["where_llm_input"],
        #     test_result_log[int(test_case["index"])]["where_llm_output"],
        # ) = get_last_llm_input_output(mem_cb.trace)

        # 大模型输出有时候会多出额外的不影响结论的 where 条件
        response_where = response_where.replace("org_id_lv2 <> ''", "")
        response_where = response_where.replace("org_id_lv3 <> ''", "")
        response_where = response_where.replace("org_id <> ''", "")
        response_where = response_where.replace(" ", "")
        # assert bool(response_where) == bool(test_case["queryParams"].get("where"))
        test_result_log[int(test_case["index"])]["where_equal"] = True
        if response_where is None or response_where == "":
            if test_case["queryParams"].get("where") is None:
                if bool(response_where) == bool(test_case["queryParams"].get("where")):
                    test_result_log[int(test_case["index"])]["where_equal"] = True
                else:
                    test_result_log[int(test_case["index"])]["where_equal"] = False
                assert bool(response_where) == bool(
                    test_case["queryParams"].get("where")
                )
            else:
                if "" in [
                    w.lower().replace(" ", "") if w is not None else ""
                    for w in test_case["queryParams"].get("where", [])
                ]:
                    test_result_log[int(test_case["index"])]["where_equal"] = True
                else:
                    test_result_log[int(test_case["index"])]["where_equal"] = False
                assert "" in [
                    w.lower().replace(" ", "") if w is not None else ""
                    for w in test_case["queryParams"].get("where", [])
                ]
        if response_where:
            if response_where.lower() in [
                w.lower().replace(" ", "") if w is not None else ""
                for w in test_case["queryParams"].get("where", []) or []
            ] or llm_check_equal(
                response_where, test_case["queryParams"].get("where", [])
            ):
                assert 1 == 1
                # test_result_log[int(test_case["index"])]["where_equal"] = True
            else:
                # test_result_log[int(test_case["index"])]["where_equal"] = False
                assert response_where.lower() in [
                    w.lower().replace(" ", "") if w is not None else ""
                    for w in test_case["queryParams"].get("where", []) or []
                ]

    @pytest.mark.parametrize(
        "test_case",
        data_attr,
        ids=lambda test: (
            f"#{test['index']}: {test['question']}"
            if isinstance(test, dict)
            else "Unknown test case"
        ),
    )
    def test_nl2attr_params(self, test_case: YmlTestAll):
        if "modelName" in test_case:
            model_name = test_case["modelName"]
        else:
            model_name = name_to_test_case[NL2ALL_NAME]["model_name"]
        prompt_selector, config = gen_chain_param(
            project_id=project_id,
            model_name=model_name,
            model_type=model_type,
            question=test_case["question"],
        )
        attr_params_chain = call_nl2attr_params(model_type, prompt_selector)
        attr_params = attr_params_chain.invoke(test_case, config=config)
        logger.info(
            Fore.YELLOW + f"提取的 attrParams 为：\n%s" + Fore.RESET,
            attr_params,
        )
        if "attrParamsOptions" in test_case:
            logger.info(
                Fore.YELLOW + f"期望的 attrParamsOptions 为：\n%s" + Fore.RESET,
                test_case["attrParamsOptions"],
            )
            assert attr_params in [
                AttrParams.parse_obj(d) for d in test_case["attrParamsOptions"]
            ]
        elif "attrParams" in test_case:
            logger.info(
                Fore.YELLOW + f"期望的 attrParams 为：\n%s" + Fore.RESET,
                test_case["attrParams"],
            )
            assert AttrParams.parse_obj(test_case["attrParams"]) == attr_params
        else:
            pytest.fail(
                "Test case does not contain 'attrParams' or 'attrParamsOptions'"
            )

    @pytest.mark.parametrize(
        "test_case",
        data_attr,
        ids=lambda test: (
            f"#{test['index']}: {test['question']}"
            if isinstance(test, dict)
            else "Unknown test case"
        ),
    )
    def test_nl2time_attr(self, test_case: YmlTestAll):
        question = test_case["question"]
        # question = "2024年4月份电信、联通、移动中标金额总额变化的原因是什么？"
        if NL2ALL_NAME == JINGFEN_BI:
            logger.info(f"before replace key words, input: {question}")
            question = replace_keywords(question)
            logger.info(f"after replace key words, args: {question}")
        prompt_selector, config = gen_chain_param(
            project_id=project_id,
            model_name=model_name,
            model_type=model_type,
            question=question,
        )
        time_query_chain = call_nl2time_attr(model_type, prompt_selector)
        time_query = time_query_chain.invoke(test_case, config=config)
        logger.info("test_nl2time_attr: 提取的 TimeQuery 为：" + str(time_query))
        if "timeAttrParamsOptions" in test_case["queryParams"]:
            assert (
                time_query.model_dump(exclude_none=True)
                in test_case["queryParams"]["timeAttrParamsOptions"]
            )
        elif "timeAttrParams" in test_case["queryParams"]:
            assert test_case["queryParams"]["timeAttrParams"] == time_query.model_dump(
                exclude_none=True
            )
        else:
            pytest.fail(
                "Test case does not contain 'timeAttrParams' or 'timeAttrParamsOptions'"
            )

    # def test_write_log(self):
    #     with open(
    #         f'{NL2ALL_NAME}_test_results_{datetime.now().strftime("%Y%m%d%H%M%S")}.csv',
    #         "w",
    #         newline="",
    #     ) as file:
    #         writer = csv.writer(file)
    #         writer.writerow(
    #             [
    #                 "index",
    #                 "question",
    #                 "expect_queryMetric",
    #                 "predict_queryMetric",
    #                 "metric_equal",
    #                 "metric_llm_input",
    #                 "metric_llm_output",
    #                 "expect_where",
    #                 "predict_where",
    #                 "where_equal",
    #                 "where_llm_input",
    #                 "where_llm_output",
    #                 "expect_groupBy",
    #                 "predict_groupBy",
    #                 "group_by_equal",
    #                 "groupby_llm_input",
    #                 "groupby_llm_output",
    #                 "expect_orderBy",
    #                 "predict_orderBy",
    #                 "order_by_equal",
    #                 "orderby_llm_input",
    #                 "orderby_llm_output",
    #             ]
    #         )  # 写入CSV文件头
    #         for result in test_result_log.values():
    #             writer.writerow(
    #                 [
    #                     result["index"],
    #                     result["question"],
    #                     result["expect_queryMetric"],
    #                     result["predict_metric"],
    #                     result["metric_equal"],
    #                     result["metric_llm_input"],
    #                     result["metric_llm_output"],
    #                     result["expect_where"],
    #                     result["predict_where"],
    #                     result["where_equal"],
    #                     result["where_llm_input"],
    #                     result["where_llm_output"],
    #                     result["expect_groupBy"],
    #                     result["predict_group_bys"],
    #                     result["group_by_equal"],
    #                     result["groupby_llm_input"],
    #                     result["groupby_llm_output"],
    #                     result["expect_orderBy"],
    #                     result["predict_order_bys"],
    #                     result["order_by_equal"],
    #                     result["orderby_llm_input"],
    #                     result["orderby_llm_output"],
    #                 ]
    #             )
