from typing import List, Optional, TypedDict

import pytest
from colorama import Fore

from common.logging.logger import get_logger
from config.app_config import DEFAULT_MODEL_TYPE, get_default_project_id
from nl2meta.nl2meta import MetaIntentParams, call_nl2meta
from tests.yml_utils import load_tests_data_directly

logger = get_logger(__name__)


class YmlTest(TypedDict, total=False):
    index: str
    question: str
    unready: Optional[bool]
    intentParams: Optional[MetaIntentParams]


testCaseIndices: List[int] = []
test_filename = "./test_nl2meta.yml"
test_data_to_use = load_tests_data_directly(test_filename, testCaseIndices)


@pytest.mark.parametrize(
    "test_case",
    test_data_to_use,
    ids=lambda test: f"#{test['index']}: {test['question']}",
)
def test_nl2meta(test_case: YmlTest):
    result = call_nl2meta(
        test_case["question"],
        model_type=DEFAULT_MODEL_TYPE,
        model_name="jiaohang_demo",
        project_id=get_default_project_id(),
    )
    logger.info(
        Fore.YELLOW + f"提取的 metaIntentParams 为：\n%s" + Fore.RESET,
        result.model_dump(exclude_none=True) if result else "None",
    )
    logger.info(
        Fore.YELLOW + f"期望的 metaIntentParams 为：\n%s" + Fore.RESET,
        test_case["metaIntentParams"],
    )
    if "metaIntentParams" in test_case:
        assert test_case["metaIntentParams"] == result.model_dump()
    else:
        pytest.fail("Test case does not contain 'intentParams'")
