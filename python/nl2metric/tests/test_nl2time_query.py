from typing import Dict, List, Optional, TypedDict

import pytest
from colorama import Fore

from common.logging.logger import get_logger
from config.app_config import DEFAULT_MODEL_TYPE
from nl2time_query.nl2time_query import call_nl2time_query
from tests.yml_utils import load_tests_data_directly

logger = get_logger(__name__)


# Define the types
class TimeQueryParams(TypedDict, total=False):
    timeStartFunction: Dict[str, Optional[int]]
    timeEndFunction: Dict[str, Optional[int]]
    timeGranularity: str


class YmlTest(TypedDict, total=False):
    index: str
    question: str
    unready: Optional[bool]
    timeQueryParams: Optional[TimeQueryParams]
    timeQueryParamsOptions: Optional[List[TimeQueryParams]]


# If testCaseIndices is non-empty, only run specified test cases
testCaseIndices: List[int] = []
test_filename = "./test_nl2time_query.yml"
test_data_to_use = load_tests_data_directly(test_filename, testCaseIndices)


@pytest.mark.parametrize(
    "test_case",
    test_data_to_use,
    ids=lambda test: f"#{test['index']}: {test['question']}",
)
def test_nl2time_query(test_case: YmlTest):
    time_query = call_nl2time_query(test_case["question"], DEFAULT_MODEL_TYPE)
    logger.info(
        Fore.YELLOW + f"提取的 TimeQuery 为：\n%s" + Fore.RESET,
        time_query.model_dump(exclude_none=True) if time_query else "None",
    )
    if "timeQueryParamsOptions" in test_case:
        logger.info(
            Fore.YELLOW + f"期望的 timeQueryParamsOptions 为：\n%s" + Fore.RESET,
            test_case["timeQueryParamsOptions"],
        )
        assert (
            time_query.model_dump(exclude_none=True)
            in test_case["timeQueryParamsOptions"]
        )
    elif "timeQueryParams" in test_case:
        logger.info(
            Fore.YELLOW + f"期望的 TimeQuery 为：\n%s" + Fore.RESET,
            test_case["timeQueryParams"],
        )
        assert test_case["timeQueryParams"] == time_query.model_dump(exclude_none=True)
    else:
        pytest.fail(
            "Test case does not contain 'timeQueryParams' or 'timeQueryParamsOptions'"
        )
