#! coding: utf-8


import sys
from typing import List

import yaml
from llama_index import QueryBundle
from llama_index.schema import NodeWithScore

from common.logging.logger import get_logger
from common.utils.set_utils import optional_set_difference
from common.utils.string_utils import remove_suffix
from config import app_config
from metastore.custom_yaml import get_custom_metastore
from metastore.db_metastore import get_db_metastore
from pre_filter.index.builder import SchemaIndexBuilder
from pre_filter.index.const import DIMENSION_NAME_KEY, METRIC_NAME_KEY, MODEL_NAME_KEY
from pre_filter.service import get_pre_filter_service

logger = get_logger(__name__)


def top_nodes(
    nodes: List[NodeWithScore], top_k: int, relative_score: float
) -> List[NodeWithScore]:
    result = []
    max_score = nodes[0].score
    for n in nodes:
        if len(result) > top_k and n.score < (max_score - relative_score):
            break
        result.append(n)
    return result


def test_pre_filter_query1():
    service = get_pre_filter_service(
        project_id="ca6224d2-cfsa-416a-92d7-0f0123f8b638", model_name="wide_table"
    )
    print(service.retrieve_dimensions("上海分行平均利息收入"))


def test_pre_filter_query():
    with open(app_config.TEST_CONFIG_PATH) as f:
        data = yaml.safe_load(f)
    builder = SchemaIndexBuilder()
    metastore = get_custom_metastore()
    builder.with_metrics(metastore.list_metrics())
    builder.with_dimensions(metastore.list_dimensions())
    schema_index = builder.build()
    metric_succeed = 0
    metric_failed = 0
    group_by_succeed = 0
    group_by_failed = 0
    order_by_succeed = 0
    order_by_failed = 0
    where_succeed = 0
    where_failed = 0

    metrics_avg_values = []
    dimensions_avg_values = []
    for test_case in data["tests"]:
        if test_case.get("unready", False):
            continue
        question = test_case["question"]
        index = test_case["index"]
        metric_names = test_case["queryParams"].get("metricNames") or []
        group_bys = test_case["queryParams"].get("groupBys") or []
        order_bys = test_case["queryParams"].get("orderBys") or []
        wheres = test_case["queryParams"].get("whereNames") or []
        order_bys = [
            remove_suffix(remove_suffix(n.strip(), " desc"), " asc").strip()
            for n in order_bys
        ]

        metric_retriever = schema_index.create_metric_retriever(top_k=10)
        all_metric_retriever = schema_index.create_metric_retriever(top_k=sys.maxsize)
        metric_nodes = metric_retriever.retrieve(QueryBundle(question))
        all_metric_nodes = all_metric_retriever.retrieve(QueryBundle(question))
        all_metric_name_to_score = {
            n.metadata[METRIC_NAME_KEY]: n.score for n in all_metric_nodes
        }
        matched_metric_names = [n.metadata[METRIC_NAME_KEY] for n in metric_nodes]
        dimension_retriever = schema_index.create_dimension_retriever(top_k=10)
        all_dimension_retriever = schema_index.create_dimension_retriever(
            top_k=sys.maxsize
        )
        dimension_nodes = dimension_retriever.retrieve(QueryBundle(question))
        all_dimension_nodes = all_dimension_retriever.retrieve(QueryBundle(question))
        all_dimension_name_to_score = {
            n.metadata[DIMENSION_NAME_KEY]: n.score for n in all_dimension_nodes
        }
        matched_dimension_names = [
            n.metadata[DIMENSION_NAME_KEY] for n in dimension_nodes
        ]
        absent_metric_names = optional_set_difference(
            set(metric_names), set(matched_metric_names)
        )
        absent_group_by_names = optional_set_difference(
            set(group_bys),
            set(matched_dimension_names) | set(matched_metric_names),
        )
        absent_order_by_names = optional_set_difference(
            set(order_bys),
            set(matched_dimension_names) | set(matched_metric_names),
        )
        absent_where_names = optional_set_difference(
            set(wheres),
            set(matched_dimension_names) | set(matched_metric_names),
        )
        if (
            len(absent_metric_names) == 0
            and len(absent_group_by_names) == 0
            and len(absent_order_by_names) == 0
            and len(absent_where_names) == 0
        ):
            logger.info(
                "[✅][%d] question=%s, metrics_count=%d, dimension_count=%d",
                index,
                question,
                len(matched_metric_names),
                len(matched_dimension_names),
            )
            logger.info(
                "metric_nodes=%s",
                [
                    f"{n.metadata[METRIC_NAME_KEY]}:{n.score or 0.0:-.3f}"
                    for n in metric_nodes
                ],
            )
            logger.info(
                "dimension_nodes=%s",
                [
                    f"{n.metadata[DIMENSION_NAME_KEY]}:{n.score or 0.0:-.3f}"
                    for n in dimension_nodes
                ],
            )

        else:
            logger.info(
                "[❌][%d] question=%s, absent_metric_names=%s, absent_group_by=%s, absent_order_by=%s, absent_wheres=%s",
                index,
                question,
                [
                    f"{name}:{all_metric_name_to_score.get(name, 0.0):-.3f}"
                    for name in absent_metric_names
                ],
                absent_group_by_names,
                absent_order_by_names,
                absent_where_names,
            )
            # logger.info(
            #     "matched_metrics=%s",
            #     [
            #         f"{name}:{all_metric_name_to_score[name]}"
            #         for name in matched_metric_names
            #     ],
            # )
            logger.info(
                "metric_nodes=%s",
                [
                    f"{n.metadata[METRIC_NAME_KEY]}:{n.score or 0.0:-.3f}"
                    for n in metric_nodes
                ],
            )
        metrics_avg_values.append((len(matched_metric_names), len(all_metric_nodes)))
        dimensions_avg_values.append(
            (len(matched_dimension_names), len(all_dimension_name_to_score))
        )

        # logger.info("matched_dimension=%s", matched_dimension_names)
        if len(absent_metric_names) == 0:
            metric_succeed += 1
        else:
            metric_failed += 1

        if len(absent_group_by_names) == 0:
            group_by_succeed += 1
        else:
            group_by_failed += 1

        if len(absent_order_by_names) == 0:
            order_by_succeed += 1
        else:
            order_by_failed += 1

        if len(absent_where_names) == 0:
            where_succeed += 1
        else:
            where_failed += 1

    print(f"metric recall rate {metric_succeed*1.0 / (metric_succeed + metric_failed)}")
    print(
        f"groupby recall rate {group_by_succeed*1.0 / (group_by_succeed + group_by_failed)}"
    )
    print(
        f"orderby recall rate {order_by_succeed*1.0 / (order_by_succeed + order_by_failed)}"
    )
    print(f"where recall rate {where_succeed*1.0 / (where_succeed + where_failed)}")
    print(
        "avg metric retrieve ratio: ",
        sum(a[0] for a in metrics_avg_values)
        * 1.0
        / sum(a[1] for a in metrics_avg_values),
    )
    if dimensions_avg_values:
        print(
            "avg dimension retrieve ratio: ",
            sum(a[0] for a in dimensions_avg_values)
            * 1.0
            / sum(a[1] for a in dimensions_avg_values),
        )


def test_index_builder():
    builder = SchemaIndexBuilder()
    metastore = get_custom_metastore()
    builder.with_metrics(metastore.list_metrics())
    builder.with_dimensions(metastore.list_dimensions())
    schema_index = builder.build()
    assert schema_index is not None


if __name__ == "__main__":
    test_pre_filter_query()
