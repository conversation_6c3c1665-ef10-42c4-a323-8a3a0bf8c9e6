from pathlib import Path
from typing import Dict, List, Optional, TypedDict

import pytest
import yaml

from common.logging.logger import get_logger
from config.app_config import DEFAULT_MODEL_TYPE, get_default_project_id
from nl2attr_params.nl2attr_params import AttrParams, call_nl2attr_params

logger = get_logger(__name__)


# Define the types
class YmlTest(TypedDict, total=False):
    index: str
    question: str
    unready: Optional[bool]
    attrParams: Optional[AttrParams]
    attrParamsOptions: Optional[List[AttrParams]]


class YmlTests(TypedDict):
    tests: List[YmlTest]


# If testCaseIndices is non-empty, only run specified test cases
testCaseIndices: List[int] = []


# Fixture to load tests data from YAML file
# @pytest.fixture(scope="module")
def load_tests_data_directly(test_filename):
    file_path = Path(__file__).parent / test_filename
    with open(file_path, "r", encoding="utf8") as file:
        tests_data = yaml.safe_load(file)["tests"]

    if testCaseIndices:
        tests_data = [
            test
            for test in tests_data
            if int(test["index"]) in testCaseIndices and not test.get("unready")
        ]
    else:
        tests_data = [test for test in tests_data if not test.get("unready")]

    return tests_data


test_data_to_use = load_tests_data_directly("test_nl2attr_params.yml")


@pytest.mark.parametrize(
    "test_case",
    test_data_to_use,
    ids=lambda test: f"#{test['index']}: {test['question']}",
)
def test_nl2time_query(test_case: YmlTest):
    attr_params = call_nl2attr_params(
        test_case["question"], DEFAULT_MODEL_TYPE, get_default_project_id()
    )
    logger.info("提取的 attrParams 为：%s", attr_params)
    if "attrParamsOptions" in test_case:
        assert attr_params in [
            AttrParams.parse_obj(d) for d in test_case["attrParamsOptions"]
        ]
    elif "attrParams" in test_case:
        assert AttrParams.parse_obj(test_case["attrParams"]) == attr_params
    else:
        pytest.fail("Test case does not contain 'attrParams' or 'attrParamsOptions'")
