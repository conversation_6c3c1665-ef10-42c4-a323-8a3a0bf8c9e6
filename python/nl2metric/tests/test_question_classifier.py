from typing import List

from pydantic import BaseModel

from nl2metric.question_classifier import QuestionClassifier


class Case(BaseModel):
    question: str
    labels: List[str]


TEST_CASES = [
    Case(question="各机构商户的优质商户的数量", labels=["simple"]),
    Case(
        question="查询各机构商户个人商户、对公商户、小微商户、优质商户、活跃商户、预审批客户、三方绑卡客户、信用卡客户、信用卡消费客户的数量",
        labels=["simple"],
    ),
    Case(
        question="济南分行三方绑卡客户、信用卡客户、信用卡消费客户的数量",
        labels=["simple"],
    ),
    Case(question="各机构商户个人商户、对公商户、小微商户的数量及排名", labels=["rank"]),
    Case(
        question="各分行本年中收、本月中收、本年交易额、本月交易额、时点资产、时点存款、年日均存款、年日均资产的排名是多少",
        labels=["rank"],
    ),
    Case(question="查询各分行'优质商户'的数量及排名", labels=["rank"]),
    Case(question="查询每个机构中月交易金额最低的商户及其交易金额", labels=["rank_top"]),
    Case(question="查询每个机构中信用卡消费金额最高的商户", labels=["rank_top"]),
    Case(question="查询每个机构在最近3个月内活跃商户数量的变化趋势", labels=["trend"]),
    Case(question="查询每个机构最近3个季度的信用卡消费笔数的趋势", labels=["trend"]),
    Case(question="查询各分行商户中优质且活跃商户的数量及占比是多少？", labels=["ratio"]),
    Case(
        question='查询各分行商户中"信用卡消费客户"的数量及占比是多少？',
        labels=["ratio"],
    ),
    Case(
        question="查询每个机构中活跃商户的数量及其所占总商户数量的比例？",
        labels=["ratio"],
    ),
    Case(
        question="查询济南分行、莱芜分行信用卡客户和信用卡消费客户的数量及占比？",
        labels=["ratio"],
    ),
    Case(question="上海市分行2023年上半年全渠道户均客户价值情况", labels=["simple"]),
    Case(question="上海市分行2023年上半年客户价值营收及各项成本收入", labels=["simple"]),
    Case(
        question="上海市分行2023年上半年收入中占比最大的项目，占比是多少",
        labels=["ratio", "rank_top"],
    ),
    Case(question="上海市分行2023上半年户均收入及各项明细金额及占比", labels=["ratio"]),
    Case(question="2023年上半年全渠道户均客户价值排名前2名的分行", labels=["rank_top"]),
    Case(
        question="上海分行和广东浙江省分行023年上半年哪个户均客户价值高",
        labels=["rank_top"],
    ),
    Case(
        question="请分析上海市分行收入增长或者下降原因有那几点，按绝对占比降序排序",
        labels=["rank", "ratio"],
    ),
]


def test_question_classifier():
    classifier = QuestionClassifier()
    for case in TEST_CASES:
        assert set(classifier.match(case.question)) == set(case.labels), case


if __name__ == "__main__":
    test_question_classifier()
