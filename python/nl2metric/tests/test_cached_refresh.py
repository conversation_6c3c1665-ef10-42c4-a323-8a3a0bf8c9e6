import time
import pytest

from dotenv import load_dotenv
from pathlib import Path

# specify path to easily change env in docker
env_file = Path(__file__).parent.parent / ".env"
print(f"load dotenv {env_file}")
load_dotenv(dotenv_path=env_file, override=True)

from cache_updater.async_cached import (
    init_async_cached_holder,
    async_refresh_cache,
    clear_async_cached,
)
from cache_updater.async_cached_service.client import AsyncCachedClient
from common.logging.logger import get_logger
from common.types.base import (
    CHAIN_META,
    gen_chain_meta,
    JobType,
)
from common.utils.shared_service_utils import refresh_shared_service
from metastore.custom_json import set_custom_response
from pathlib import Path
from pre_filter.service import get_pre_filter_service

logger = get_logger(__name__)

# ATTATION: python3 main_shared_service.py before this ut
# and add these to main_shared_service.py:
#     from metastore import set_use_json_meta
#     set_use_json_meta(True)

project_id = "Soh3OFPJognZD6BG"
model_name = "dipeak.baowu_test.T_ADS_FACT_WSSJ_TOTAL_INDEX"

chain_metadata = gen_chain_meta(
    job_type=JobType.CHART_INSIGHT,
    project_name=None,
    project_id=None,
    model_name=None,
    model_label=None,
    model_id=None,
    model_type=None,
    prompt_selector=None,
)
config = {CHAIN_META: chain_metadata}

json_file = Path(__file__).parent / "test_cached_refresh_baowu.json"


def refresh_cache():
    refresh_shared_service()
    time.sleep(5)
    async_refresh_cache()


def _clear_cache():
    print("test start")
    AsyncCachedClient.clear_cached()
    clear_async_cached()


@pytest.fixture(autouse=True)
def clear_cache():
    _clear_cache()


def test_refresh_cache_simple():
    set_custom_response(json_file, None, None)
    pre_filter = get_pre_filter_service(project_id, model_name)
    question = "邯郸"
    metrics = pre_filter.retrieve_metrics(question)
    assert len(metrics) == 6
    dimensions = pre_filter.retrieve_dimensions(question, config)
    assert len(dimensions) == 1

    delta_metrics = [
        {
            "id": "XXX",
            "name": "SUMIXXX",
            "label": "天气",
            "synonyms": [],
            "description": "天气",
            "type": "simple",
        }
    ]

    delta_dimensions = [
        {
            "name": "COMPANY_INNER_CODE_DES_XXX",
            "label": "子公司名_XXX",
            "synonyms": [],
            "description": "合并账套关联内码_XXX",
            "expr": "T_ADS_FACT_WSSJ_TOTAL_INDEX.COMPANY_INNER_CODE_DES_XXX",
            "type": "categorical",
            "typeParams": {},
            "values": ["邯郸"],
        }
    ]

    refresh_cache()

    # add metric dimension
    time.sleep(1)
    set_custom_response(json_file, delta_metrics, delta_dimensions)

    refresh_cache()

    metrics = pre_filter.retrieve_metrics(question)
    assert len(metrics) == 6
    dimensions = pre_filter.retrieve_dimensions(question, config)
    assert len(dimensions) == 1

    pre_filter = get_pre_filter_service(project_id, model_name)
    metrics = pre_filter.retrieve_metrics(question)
    assert len(metrics) == 7
    dimensions = pre_filter.retrieve_dimensions(question, config)
    assert len(dimensions) == 2

    refresh_cache()

    pre_filter = get_pre_filter_service(project_id, model_name)
    metrics = pre_filter.retrieve_metrics(question)
    assert len(metrics) == 7
    dimensions = pre_filter.retrieve_dimensions(question, config)
    assert len(dimensions) == 2

    # delete metric dimension
    time.sleep(1)
    set_custom_response(json_file, None, None)

    refresh_cache()

    pre_filter = get_pre_filter_service(project_id, model_name)
    metrics = pre_filter.retrieve_metrics(question)
    assert len(metrics) == 6
    dimensions = pre_filter.retrieve_dimensions(question, config)
    assert len(dimensions) == 1

    # trigger refresh again
    refresh_cache()

    time.sleep(1)
    set_custom_response(json_file, delta_metrics, delta_dimensions)

    refresh_cache()

    pre_filter = get_pre_filter_service(project_id, model_name)
    metrics = pre_filter.retrieve_metrics(question)
    assert len(metrics) == 7
    dimensions = pre_filter.retrieve_dimensions(question, config)
    assert len(dimensions) == 2
    logger.info("test succeed")


if __name__ == "__main__":
    init_async_cached_holder()
    _clear_cache()
    test_refresh_cache_simple()
