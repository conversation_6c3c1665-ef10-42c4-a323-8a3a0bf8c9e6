from typing import List, Optional, TypedDict

import pytest
from colorama import Fore
from tests.yml_utils import load_tests_data_directly

from common.logging.logger import get_logger
from config.app_config import DEFAULT_MODEL_TYPE, get_default_project_id
from nl2intent.nl2intent import IntentParams, call_nl2intent

logger = get_logger(__name__)


class YmlTest(TypedDict, total=False):
    index: str
    question: str
    unready: Optional[bool]
    intentParams: Optional[IntentParams]


# If testCaseIndices is non-empty, only run specified test cases
testCaseIndices: List[int] = []
test_filename = "./test_nl2intent.yml"
test_data_to_use = load_tests_data_directly(test_filename, testCaseIndices)


@pytest.mark.parametrize(
    "test_case",
    test_data_to_use,
    ids=lambda test: f"#{test['index']}: {test['question']}",
)
def test_nl2intent(test_case: YmlTest):
    intent_params = call_nl2intent(
        test_case["question"],
        DEFAULT_MODEL_TYPE,
        model_name="jiaohang_demo",
        project_id=get_default_project_id(),
    )
    logger.info(
        Fore.YELLOW + f"提取的 IntentParams 为：\n%s" + Fore.RESET,
        intent_params.model_dump(exclude_none=True) if intent_params else "None",
    )
    logger.info(
        Fore.YELLOW + f"期望的 IntentParams 为：\n%s" + Fore.RESET, test_case["intentParams"]
    )
    if "intentParams" in test_case:
        assert test_case["intentParams"] == intent_params.model_dump(exclude_none=True)
    else:
        pytest.fail("Test case does not contain 'intentParams'")
