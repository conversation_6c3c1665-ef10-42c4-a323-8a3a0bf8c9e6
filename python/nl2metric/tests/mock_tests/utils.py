import functools
import time

from dotenv import load_dotenv
from pathlib import Path

env_file = Path(__file__).parent.parent.parent / ".env"
print(f"load dotenv {env_file}")
load_dotenv(dotenv_path=env_file, override=True)

from backend_stage_reporter.reporter import backend_stage_reporter_monitor_start
from cache_updater.async_cached import init_async_cached_holder
from common.trace import tracer
from config import app_config


def init_mock_env(enable_langfuse=False):
    app_config.ENABLE_NL2DOCUMENT = False
    app_config.ENABLE_NL2DOCUMENT_BUILDER = False
    app_config.ENABLE_LANGFUSE = enable_langfuse
    app_config.LANGFUSE_HOST = "http://**************:30007"
    app_config.LANGFUSE_SECRET_KEY = "******************************************"
    app_config.LANGFUSE_PUBLIC_KEY = "pk-lf-7373ae64-e54c-44ee-bd06-58423cd8056d"
    app_config.ASK_BI_HOST = "http://***************:8000"  # pre
    init_async_cached_holder()


def start_langfuse(fn):
    @functools.wraps(fn)
    def wrapper(*args, **kwargs):
        backend_stage_reporter_monitor_start()
        tracer.set_trace_id(None)

        fn(*args, **kwargs)

        time.sleep(15)  # wait for langfuse report

    return wrapper
