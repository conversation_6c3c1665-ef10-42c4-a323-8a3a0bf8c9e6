from langchain_core.messages import HumanMessage

from common.utils.llm_utils import create_llm_model_by_project_config
from config.project_config import get_project_config


def test_project_config():
    project_name = "mock"
    model_name = "mock"
    # mock.yaml does not exist, so it will use default config
    project_config = get_project_config(
        project_name=project_name, model_name=model_name
    )
    assert project_config.enable_time_dimension is True

    project_name = "mock_test"
    project_config = get_project_config(
        project_name=project_name, model_name=model_name
    )
    # brain_model share default_model config in mock_test.yaml
    assert project_config.llm_model_params("brain_model") == {
        "model_name": "qwen3.0",
        "openai_api_base": "https://api.openai.com/v1",
        "openai_api_key": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
    }
