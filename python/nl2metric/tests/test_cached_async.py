import pytest
import time

# ATTATION: python3 main_shared_service.py before this ut

from cache_updater.async_cached import (
    init_async_cached_holder,
    async_cached,
    async_refresh_cache,
    clear_async_cached,
)
from cache_updater.async_cached_service.client import AsyncCachedClient
from common.utils.shared_service_utils import refresh_shared_service
from tests.test_cached_common import TestCached1, TestCached2, TestCached3

init_async_cached_holder()
cache_3 = [1, 2]


# Test functions to be decorated
@async_cached("{a}")
def cache_func1(a):
    return TestCached1(a)


@async_cached("{a}_{b}")
def cache_func2(a, b):
    return TestCached2(a, b)


@async_cached("")
def cache_func3():
    return TestCached3(cache_3)


@pytest.fixture(autouse=True)
def clear_cache():
    print("test start")
    AsyncCachedClient.clear_cached()
    clear_async_cached()


def refresh_cache():
    refresh_shared_service()
    time.sleep(1)
    async_refresh_cache()


def test_cache_single_item():
    for i in range(10):
        tmp = cache_func1("a")
        assert f"TestCached1_a_{i + 1}" == tmp.get()

    refresh_cache()

    for i in range(20):
        tmp = cache_func1("a")
        assert f"TestCached1_a_{i + 1}" == tmp.get()


def test_cache_multiple_items():
    tmp2 = cache_func1("b")
    tmp2.get()

    for i in range(10):
        tmp = cache_func1("a")
        assert f"TestCached1_a_{i + 1}" == tmp.get()

        tmp2 = cache_func1("b")
        assert f"TestCached1_b_{i + 2}" == tmp2.get()

        tmp3 = cache_func2("c", "d")
        assert f"TestCached2_c_d_{i + 1}" == tmp3.get()

        tmp4 = cache_func3()
        cache_3.append(i)
        assert tmp4.len() == 2

    refresh_cache()

    assert cache_func3().len() == 2

    for i in range(20):
        tmp = cache_func1("a")
        assert f"TestCached1_a_{i + 1}" == tmp.get()

        tmp2 = cache_func1("b")
        assert f"TestCached1_b_{i + 1}" == tmp2.get()

        tmp3 = cache_func2("c", "d")
        assert f"TestCached2_c_d_{i + 1}" == tmp3.get()

        tmp4 = cache_func3()
        cache_3.append(i)
        assert tmp4.len() == 2

    refresh_cache()
    tmp3 = cache_func2("c", "d")
    assert f"TestCached2_c_d_1" == tmp3.get()
