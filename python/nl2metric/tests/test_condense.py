import os
from datetime import datetime
from typing import List, Optional, TypedDict

import pytest
import yaml
import csv
from yml_utils import load_tests_data_directly

from common.types import Message
from common.utils.json_utils import extract_json_from_string
from condense.condense_query import condense_query, diff_nl
from config.app_config import DEFAULT_MODEL_TYPE


class Question(TypedDict, total=False):
    question: str
    assistant: str


class CondenseContent(TypedDict, total=False):
    index: str
    type: str
    questions: List[Question]
    condense: Optional[str]


testCaseIndices: List[int] = []
test_filename = "./test_condense.yml"
folder_path = "./tmp"
if not os.path.exists(folder_path):
    os.makedirs(folder_path)
file_pattern = os.path.join(folder_path, "{}_condense_diff_{}_{}.txt")
test_data_to_use = load_tests_data_directly(test_filename, testCaseIndices)
now = datetime.now()
formatted_time = now.strftime("%Y-%m-%d %H:%M:%S")

test_logs = []


@pytest.mark.parametrize(
    "test_case",
    test_data_to_use,
    ids=lambda test: f"#{test['index']}: {test['questions']}",
)
def test_condense_query_batch(test_case: CondenseContent):
    type = test_case["type"]
    questions = test_case["questions"]
    expected_query_nl = test_case["condense"]
    msgs: List[Message] = []
    for ctx in questions:
        msgs.append(Message(role="user", content=ctx["question"]))
        if "assistant" in ctx.keys():
            msgs.append(Message(role="assistant", content=ctx["assistant"]))
    new_query = condense_query(
        msgs,
        DEFAULT_MODEL_TYPE,
    )
    diff_res = diff_nl([expected_query_nl, new_query], "vllm-mixtral-8x7b-chat")
    data = extract_json_from_string(diff_res)
    msg = "index {} type {}\n expected_query_nl : {}\nnew_generated_query: {}\nllm diff:\n{}\n".format(
        test_case["index"], type, expected_query_nl, new_query, diff_res
    )
    print(msg)
    test_logs.append(
        {
            "question": questions,
            "condense_result": new_query,
            "expected_condense_result": expected_query_nl,
            "T/F": data.get("result") == "一致",
        }
    )
    record_file = file_pattern.format(DEFAULT_MODEL_TYPE, type, formatted_time)
    with open(record_file, "a") as file:
        file.write(msg + "\n")
    assert data.get("result") == "一致"


def test_write_log():
    with open(
        f'test_condense_results_{datetime.now().strftime("%Y%m%d%H%M%S")}.csv',
        "w",
        newline="",
    ) as file:
        writer = csv.writer(file)
        writer.writerow(
            ["questions", "condense_result", "expected_condense_result", "T/F"]
        )  # 写入CSV文件头
        for result in test_logs:
            writer.writerow(
                [
                    result["question"],
                    result["condense_result"],
                    result["expected_condense_result"],
                    result["T/F"],
                ]
            )


def read_yaml(file_path):
    with open(file_path, "r", encoding="utf-8") as file:
        data = yaml.safe_load(file)
    return data
