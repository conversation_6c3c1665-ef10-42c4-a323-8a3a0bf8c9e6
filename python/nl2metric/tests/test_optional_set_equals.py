from common.utils.set_utils import optional_set_equals


def test_optional_set_equals():
    l = {"aaa", "bbb"}
    r = {"aaa", "bbb"}
    assert optional_set_equals(l, r)

    l = {"aaa?", "bbb"}
    r = {"aaa", "bbb"}
    assert optional_set_equals(l, r)

    l = {"aaa?", "bbb"}
    r = {"bbb"}
    assert optional_set_equals(l, r)

    l = {"aaa?"}
    r = set()
    assert optional_set_equals(l, r)

    l = set()
    r = set()
    assert optional_set_equals(l, r)

    l = {"bbb?"}
    r = {"aaa"}
    assert not optional_set_equals(l, r)
