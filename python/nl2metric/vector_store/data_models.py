from typing import Optional, List, Any, Dict

from pydantic import BaseModel, Field, model_validator
import uuid
from dataclasses import dataclass
from datetime import datetime
from enum import Enum


class AgentType(str, Enum):
    AGENT_TYPE_CHAT = "chat"
    AGENT_TYPE_BI = "bi"
    AGENT_TYPE_DOC = "doc"
    AGENT_TYPE_CONDENSE = "condense"
    AGENT_TYPE_BRAIN = "brain"
    AGENT_TYPE_Judge = "judge"
    AGENT_TYPE_Where = "where"
    AGENT_TYPE_Metric = "metric"
    AGENT_TYPE_GroupBy = "groupby"
    AGENT_TYPE_TimeRange = "time_range"
    AGENT_TYPE_TableTools = "table_tools"


class HintTagType(str, Enum):
    TYPE_LOGIC_THINKING = "思考逻辑"
    TYPE_TIME_PROCESSING = "时间处理"
    TYPE_METRIC_STANDARDIZATION = "指标标准化"
    TYPE_BUSINESS_CALCULATION = "业务计算"


class TermTypeEnum(str, Enum):
    DYNAMIC = "dynamic"
    FIXED = "fixed"


PROJECT_HINT = "-"


class BusinessTermBase(BaseModel):
    # uuid
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="业务术语ID")
    text: str = Field(default_factory=lambda: "", max_length=4096, description="召回关键描述")
    type: TermTypeEnum = Field(TermTypeEnum.DYNAMIC, description="业务术语类型")
    creator: str = Field(..., max_length=255)
    semantic_project_id: str = Field(..., max_length=255, alias="semanticProjectId")
    semantic_scene_id: str = Field(..., max_length=255, alias="semanticSceneId")
    created_at: Optional[int] = Field(
        default_factory=lambda: int(datetime.now().timestamp()),
        description="创建时间",
        alias="createdAt",
    )
    updated_at: Optional[int] = Field(
        default_factory=lambda: int(datetime.now().timestamp()),
        description="更新时间",
        alias="updatedAt",
    )
    agents: List[AgentType] = Field(
        description="agent类型",
    )
    tags: List[HintTagType] = Field(
        description="标签",
    )
    extra_info: str = Field(
        default_factory=lambda: "",
        description="业务术语：召回关键描述补充信息",
        max_length=4096,
        alias="extraInfo",
    )

    class Config:
        populate_by_name = True  # 优先使用字段名/别名映射

    @model_validator(mode="after")
    def validate_environment(self):
        if not self.extra_info:
            raise ValueError("extraInfo is required")
        if not self.text or len(self.text) == 0:
            self.text = self.extra_info
        return self


@dataclass
class SearchResults:
    """搜索结果集合"""

    items: List[Dict[str, Any]]
    total: int
    page: Dict[str, int]  # {"offset": x, "limit": y}


@dataclass
class DeleteResult:
    """删除操作结果"""

    deleted_count: int
    cost: Optional[float] = None

    @classmethod
    def from_milvus_result(cls, result: Dict[str, Any]) -> "DeleteResult":
        return cls(deleted_count=result.get("delete_count", 0), cost=result.get("cost"))


@dataclass
class QueryResult:
    """查询结果"""

    items: List[Dict[str, Any]]
    page: Dict[str, int]


@dataclass
class HintSearchResult:
    """混合搜索结果"""

    items: List[Dict[str, Any]]
    total: int
    weights: Dict[str, float]  # {"dense": x, "sparse": y}

    @classmethod
    def from_milvus_results(
        cls, results: List[Dict[str, Any]], weights: Dict[str, float]
    ) -> "HintSearchResult":
        if not results:
            return cls(items=[], total=0, weights=weights)

        return cls(items=results, total=len(results), weights=weights)
