#! coding: utf-8
import os
import tracemalloc
import threading
from datetime import datetime
from collections import deque
from dotenv import load_dotenv
from pathlib import Path
from prometheus_fastapi_instrumentator import Instrumentator

try:
    import pyconcrete
except ModuleNotFoundError:
    print("pyconcrete 模块未安装，跳过相关功能")

# specify path to easily change env in docker
env_file = Path(__file__).parent / ".env"
print(f"load dotenv {env_file}")
load_dotenv(dotenv_path=env_file, override=True)
# both app_config and doc_config needs load_dotenv
from common.http.fastapi_hook import TracerMiddleware
from common.trace.debug import debug_router
from common.utils.startup_utils import register_global_exception_hook
from config import app_config, doc_config
from contextlib import asynccontextmanager
from fastapi import FastAPI
from http_router.api_dispatcher import get_api_dispatcher
from http_router.router import get_ask_bi_router
from http_router.evaluate_router import evaluate_router


register_global_exception_hook()


def start(check_backend_stage_reporter=True):
    from cache_updater.async_cached import init_async_cached_holder
    from cache_updater.updater import refresh_timer_start

    # TODO(bhx): from common.http import hook
    from common.trace import tracer
    from refesher.prompt_refresher import start_prompt_studio_updater
    from common.utils.embedding_service_utils import wait_for_embedding_service
    from common.manual_select.manual_select import init_manual_select_service

    init_manual_select_service()
    init_async_cached_holder()
    if app_config.ENABLE_ASKBI:
        from cache_updater.async_cached import async_refresh_cache
        from common.utils.shared_service_utils import wait_for_shared_service

        wait_for_shared_service()
        async_refresh_cache()
        wait_for_embedding_service()
    start_prompt_studio_updater()
    refresh_timer_start(check_backend_stage_reporter)

    tracer.init_trace_provider(app_config.APP_NAME)
    # TODO(bhx): hook.register_request_hooks(app, app_config.APP_NAME)


@asynccontextmanager
async def lifespan(app: FastAPI):
    from backend_stage_reporter.reporter import backend_stage_reporter_monitor_start
    from common.stream_reporter.service import init_stream_reporter

    # do not add call this in start()
    # call this before init_async_cached_holder to save memory
    backend_stage_reporter_monitor_start()
    # this should be before api_dispatcher
    # so that api_dispatcher have access to the queue
    init_stream_reporter()
    api_dispatcher = get_api_dispatcher()
    # Startup logic
    start()

    if app_config.ENABLE_YISHUITONG:
        from playwright.async_api import async_playwright

        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch()
        app.state.playwright = playwright
        app.state.browser = browser

    yield  # This will run the application
    if api_dispatcher is not None:
        api_dispatcher.stop()
    if app_config.ENABLE_YISHUITONG:
        await browser.close()
        await playwright.stop()


app = FastAPI(lifespan=lifespan)
app.add_middleware(TracerMiddleware)
app.include_router(get_ask_bi_router())
app.include_router(debug_router)
app.include_router(evaluate_router)
if app_config.ENABLE_HINT_API:
    from http_router.api.business_term import business_term_router

    app.include_router(business_term_router)

if app_config.ENABLE_REPORT_GENERATE:
    from nl2document.report_generate.service.service import report_generate_router
    from biz.cmcc_jingfen_report_biz.service.jingfen_report_service import (
        jingfen_report_generate_router,
    )

    app.include_router(report_generate_router)
    app.include_router(jingfen_report_generate_router)
if app_config.ENABLE_YISHUITONG:
    from biz.yishuitong.service import yishuitong_report_generate_router

    app.include_router(yishuitong_report_generate_router)
if app_config.ENABLE_NL2DOCUMENT:
    from nl2document.index.service.service import document_index_service

    app.include_router(document_index_service.router)

Instrumentator(
    should_group_status_codes=True,  # 按状态码分组
    excluded_handlers=["/metrics"],  # 排除metrics的状态
).instrument(app).expose(app)


class MemoryMonitor:
    def __init__(self, interval=60, max_reports=20, save_dir="memory_reports"):
        self.interval = interval  # 执行间隔（秒）
        self.max_reports = max_reports  # 最大保留报告数
        self.save_dir = save_dir
        self._stop_event = threading.Event()
        self._thread = None
        self.file_queue = deque(maxlen=max_reports)  # 文件滚动队列
        self.main_pid = os.getpid()
        os.makedirs(self.save_dir, exist_ok=True)

    def start(self):
        if os.getpid() == self.main_pid:
            tracemalloc.start(25)  # 追踪25层堆栈[1,4](@ref)
            self._thread = threading.Thread(target=self._run)
            self._thread.daemon = True  # 设为守护线程避免阻塞主程序退出[5](@ref)
            self._thread.start()

    def _run(self):
        while not self._stop_event.is_set():
            try:
                self._analyze_and_save()
            except Exception as e:
                print(f"监控线程异常: {str(e)}")
            finally:
                self._stop_event.wait(self.interval)

    def _analyze_and_save(self):
        # 拍摄快照并生成CSV
        snapshot = tracemalloc.take_snapshot()
        top_stats = snapshot.statistics("lineno")[:20]  # 取Top20[4](@ref)

        # 生成带时间戳的文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_file = os.path.join(self.save_dir, f"memory_report_{timestamp}.txt")
        import inspect

        # 写入CSV
        with open(csv_file, "w", newline="") as f:
            for stat in top_stats:
                frame = stat.traceback[0]
                filename = frame.filename
                lineno = frame.lineno

                traceback = stat.traceback.format()[-1]
                parts = traceback.rsplit(":", 1)  # 关键修正点
                if len(parts) == 1:
                    file_line = traceback.strip()
                    code = "<unknown>"
                else:
                    file_line, code = parts[0].strip(), parts[1].strip()

                f.write(
                    f"文件: {filename}, 行号: {lineno}, 函数: {file_line}  {code},"
                    f" 内存占用: {stat.size / 1024 / 1024:.2f} MB, 分配次数: {stat.count}\n"
                )

        # 维护文件队列并清理旧文件
        self.file_queue.append(csv_file)
        self._clean_old_reports()

    def _clean_old_reports(self):
        # 获取目录下所有报告并按时间排序
        all_files = sorted(
            [os.path.join(self.save_dir, f) for f in os.listdir(self.save_dir)],
            key=os.path.getctime,
            reverse=True,
        )
        # 删除超出最大保留数的旧文件
        for old_file in all_files[self.max_reports :]:
            if os.path.exists(old_file):
                os.remove(old_file)

    def stop(self):
        self._stop_event.set()
        tracemalloc.stop()


if __name__ == "__main__":
    log_config_file = str(
        Path(__file__).parent / "common/logging/fastapi_main_logging.ini"
    )
    if app_config.ENABLE_DUMP_MEMORY:
        monitor = MemoryMonitor(interval=900)  # 每5分钟执行一次
        monitor.start()
    import uvicorn

    uvicorn.run(
        app=app,
        host="0.0.0.0",
        port=app_config.DEFAULT_BIND_PORT,
        # fastapi的多workers以及nginx并配置least_conn经测试都出现了负载不均衡的情况
        # 可能是因为我们qps实在太低了(我们希望的是8个进程，8个请求，每个进程处理一个请求)，
        # 也有可能确实是nginx没用对。
        # 总之，自己实现一个进程池也很简单。所以就干脆自己实现了get_api_dispatcher，然后fastapi只开单worker
        workers=1,
        log_config=log_config_file,
    )
