import json

from enum import Enum
from typing import List
import calendar

from common.types.base import STAGE_TO_METRIC_FEW_SHOT
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta

from metastore import get_metastore
from metastore.base import Metric, Dimension, DimensionValue
from metastore.service import get_db_metastore
from nl2metric.few_shots import CHINA_LIFE_NAME, LINGSHI_NAME


class PromptType(str, Enum):
    BASIC = "basic"
    FEW_SHOT = "few_shot"


DEFAULT_PROMPT = "default"


class PromptKeyword(str, Enum):
    PROMPT_TYPE = "prompt_type"
    TEMPLATE_FORMAT = "template_format"  # "f-string", "mustache", "jinja2"
    PROMPT_PREFIX = "prompt_prefix"
    PROMPT_SUFFIX = "prompt_suffix"
    EXAMPLE_TPL = "example_tpl"

    FEWSHOT_EXAMPLES = "few_shot_examples"
    FEWSHOT_FALLBACK_EXAMPLES = "few_shot_fallback_examples"
    FEWSHOT_BASE_EXAMPLES = "few_shot_base_examples"


def get_few_shot_examples(project_id: str, stage: str):
    _meta_store = get_metastore(project_id)
    return _meta_store.get_nl_metric_few_shot(STAGE_TO_METRIC_FEW_SHOT[stage])
    # if (not few_shot_examples or len(few_shot_examples) == 0) and len(
    #     default_project_name
    # ) > 0:
    #     # 使用默认项目
    #     few_shot_examples = get_db_metastore(
    #         get_semantic_project_by_name(default_project_name).id
    #     ).get_nl_metric_few_shot(STAGE_TO_METRIC_FEW_SHOT[stage])
    # return few_shot_examples


def gen_time_partial_variables(project_name=None):
    now = datetime.now()
    if project_name and project_name == CHINA_LIFE_NAME:
        specific_year = 2019
        now = datetime(
            specific_year,
            now.month,
            now.day,
            now.hour,
            now.minute,
            now.second,
            now.microsecond,
        )

    if project_name and project_name == LINGSHI_NAME:  # 零食有鸣场景
        now = datetime(2024, 10, 28, now.hour, now.minute, now.second, now.microsecond)

    current_year = now.year
    current_month = now.month
    current_day = now.day
    yesterday = (now - relativedelta(days=1)).strftime(f"%Y年%m月%d日")
    current_date_str = now.strftime(f"%Y年%m月%d日")
    last_year = now.year - 1
    end_of_previous_month = datetime(current_year, current_month, 1) - timedelta(days=1)
    start_of_previous_month = datetime(
        end_of_previous_month.year, end_of_previous_month.month, 1
    )
    one_year_ago = now - relativedelta(years=1)
    one_year_ago_str = one_year_ago.strftime("%Y年%m月%d日")
    end_of_last_month = now - timedelta(days=now.day)
    end_of_last_month_str = end_of_last_month.strftime("%Y年%m月%d日")
    thirty_days_ago = now - timedelta(days=29)
    thirty_days_ago_str = thirty_days_ago.strftime("%Y年%m月%d日")
    three_months_ago = now - relativedelta(months=3)
    three_months_ago_str = three_months_ago.strftime("%Y年%m月%d日")
    six_months_ago = now - relativedelta(months=6)
    six_months_ago_str = six_months_ago.strftime("%Y年%m月%d日")
    current_quarter = (now.month + 2) // 3
    current_quarter_start_month = current_quarter * 3 - 2
    current_month_day = calendar.monthrange(current_year, current_month)[1]
    current_quarter_end_day = calendar.monthrange(
        current_year, current_quarter_start_month + 2
    )[1]
    # 如果是1月份,需要获取上一年12月的天数
    if current_month == 1:
        last_month = 12
        last_month_year = current_year - 1
        last_month_day = calendar.monthrange(last_month_year, last_month)[1]
        last_quarter = 4
        last_quarter_year = current_year - 1
        last_last_quarter = 3
        last_last_quarter_year = current_year - 1
    else:
        last_month = current_month - 1
        last_month_year = current_year
        last_month_day = calendar.monthrange(last_month_year, last_month)[1]
        last_quarter = current_quarter - 1 if current_quarter > 1 else 4
        last_quarter_year = current_year if current_quarter > 1 else current_year - 1
        last_last_quarter = (
            current_quarter - 2
            if current_quarter > 2
            else (4 if current_quarter == 2 else 3)
        )
        last_last_quarter_year = (
            current_year if current_quarter > 2 else current_year - 1
        )

    return {
        "now": now,
        "current_year": current_year,
        "current_month": current_month,
        "current_day": current_day,
        "current_date_str": current_date_str,
        "last_year": last_year,
        "yesterday": yesterday,
        "end_of_previous_month": end_of_previous_month,
        "start_of_previous_month": start_of_previous_month,
        "one_year_ago": one_year_ago,
        "one_year_ago_str": one_year_ago_str,
        "end_of_last_month": end_of_last_month,
        "end_of_last_month_str": end_of_last_month_str,
        "thirty_days_ago": thirty_days_ago,
        "thirty_days_ago_str": thirty_days_ago_str,
        "three_months_ago": three_months_ago,
        "three_months_ago_str": three_months_ago_str,
        "six_months_ago": six_months_ago,
        "six_months_ago_str": six_months_ago_str,
        "current_quarter": current_quarter,
        "last_month_day": last_month_day,
        "current_quarter_start_month": current_quarter_start_month,
        "current_quarter_end_day": current_quarter_end_day,
        "current_month_day": current_month_day,
        "last_quarter": last_quarter,
        "last_quarter_year": last_quarter_year,
        "last_last_quarter": last_last_quarter,
        "last_last_quarter_year": last_last_quarter_year,
        "last_month": last_month,
        "last_month_year": last_month_year,
    }


def parse_metrics(metric_jsons):
    if metric_jsons is None or metric_jsons == "":
        return []
    if isinstance(metric_jsons, str):
        metric_jsons = json.loads(metric_jsons)
    ret = []
    for raw_json in metric_jsons:
        ret.append(Metric.model_validate(raw_json))
    return ret


def parse_dimensions(dimension_jsons):
    if dimension_jsons is None or dimension_jsons == "":
        return []
    if isinstance(dimension_jsons, str):
        dimension_jsons = json.loads(dimension_jsons)
    ret = []
    for raw_json in dimension_jsons:
        if "values" in raw_json:
            raw_json_values = raw_json.get("values", [])
            raw_json["values"] = DimensionValue.from_list(raw_json_values)
        ret.append(Dimension.model_validate(raw_json))
    return ret


def parse_result(result):
    if isinstance(result, str):
        return json.loads(result)
    return result
