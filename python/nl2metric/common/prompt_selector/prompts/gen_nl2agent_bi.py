from common.prompt_selector.prompts.common import gen_basic_json
from common.types.base import ParamsExtractStage
from common.prompt_selector.utils import DEFAULT_PROMPT
from nl2metric.few_shots import JINGFEN_BI, HUAXIA_PROJECT_NAME

nl2agent_bi_prompt = """
现在需要提取或者计算问题中的指标，你会得到一个指标召回列表以及一个用户的请求，你需要调用可选的工具，完成用户的请求。

可用工具:
{"name": "lookup_data", "description": "查数工具，根据请求执行对应的查询", "parameters": {"type": "object","properties": {"query": {"type": "string", "description": "要执行的数据查询"}, "metric_name": {"type": "string", "description": "对查询结果的描述，生成指标名，如果有多个指标用,分隔"}}, "required": ["query", "description"]}}
{"name": "calculator", "description": "执行数学计算，包括基本算术、微分、积分和平方根", "parameters": {"type": "object", "properties": {"expression": {"type": "string", "description": "要计算的数学表达式，例如 '(`b` - `a`) / `a`'或 '(`a` + `b`) / `a`'"}, "description": {"type": "string", "description": "对计算结果的描述，生成新的指标名"}, "mapping": {"type": "dict", "description": "传入的变量的映射"}}, "required": ["expression", "description", "mapping"]}}
{"name": "table_tools", "description": "动态模拟工具，根据请求执行对应的表操作", "parameters": {"type": "object", "properties": {"mission": {"type": "string", "description": "传入的任务描述"}}, "required": ["mission"]}}

思考流程:
step1. 明确任务类型
- 判断是否为简单查数问题，通过lookup_data工具可以直接完成。通常查询某具体的实体；查询指标的最大值/最小值；按照指标进行排序；按照指标筛选过滤条件；或者需要查询的指标的占比/同比/环比，在[Metric]中可以直接得到，不需要计算。
- 判断为计算类问题，需要先调用lookup_data工具查询数据，再调用calculator和table_tools完成后续计算。计算类问题通常为计算指标与指标之间计算差值/增长/占比/同比/环比等
- 如果为查数类问题，需要极短的思考过程，完成规划；如果为计算类问题，继续思考
step2. [Hint]理解
- 如果[Hint]为空，跳过此步骤
- 如果有[Hint]，需要仔细阅读每一条Hint，判断与question是否相关，如果相关，请根据Hint对question进行改写
step3. 工具选择
- 如果lookup_data查询结果为一个指标，选择calculator进行计算，需要按照步骤进行
- 如果lookup_data查询结果为多个指标，或者需要对计算结果进行排序、过滤计算结果满足某项条件等，选择table_tools进行计算
step4. 结果检查
- 将回答结果整理成要求的JSON格式，检查是否有违背注意事项的思考过程

注意事项:
- 你的think需要严格根据我的思考流程来回答，按照step1、step2依次生成
- 你调用的第一个工具必须是lookup_data，你所有传入calculator工具和table_tools工具的数据必须由lookup_data工具查询得到。第一个工具不能直接调用calculator工具和table_tools工具。
- 问题中需要过滤、排序不作为需要计算的问题，可以直接调用lookup_data工具得到，需要计算比例、占比、差值等数值运算的情况，才需要用到计算工具calculator工具和table_tools工具。
- 计算工具calculator工具和table_tools工具调用过程中，传入下一个工具的输入，必须是上一个工具的输出，不能编造不存在的数据作为工具的输入。
- calculator的表达式中，所有计算对象必须用反引号``包裹
- 候选[Metric]如果没有出现指标的差值、占比、同比等，需要你调用calculator或者tabletool进行计算，无法直接通过lookupdata查询得到
- 注意问题中的同比、环比不能错误理解，不能随意替换
- 对于 query 中所有时间描述，需要根据 env 中的当日时间，全部转换为绝对时间，传入的查询的时间描述需要转换成准确的描述。
- 查询各个维度下的指标，只需要调用一次lookup_data即可完成，不需要分别多次对维度下所有值进行查询
- 问题中需要查询指标的占比/同比/环比，但是可以从[Metric]中找到对应指标名为xx占比/同比/环比，此时只需要调用lookup_data，不需要计算

思考示例格式:
查数问题
1. 任务类型判断：问题中需要查询具体指标，属于查数类问题，直接调用lookup_data工具完成查询
2. [Hint]理解：[Hint]中没有和问题相关描述，结束think，直接将问题传入lookup_data中
3. 结束思考：生成JSON结果

计算问题
1. 任务类型判断：问题中需要计算占比/同比/环比/差值，且[Metric]中没有出现对应的指标，需要自行计算，属于计算类问题
2. [Hint]理解：提供的[Hint]中的描述，修改问题，使其符合hint的逻辑
3. 工具选择：根据问题描述判断，什么情况选择table_tools，什么情况选择calculator问题。先执行lookup_data，再根据lookup_data的结果调用工具进行计算
4. 参数判断：calculator需要输出正确的表达式，table_tools需要将问题中完成的子操作传递到table_tools
5. 结束思考：检查是否遵循所有[Hint]的逻辑，检查是否遵循所有注意事项，检查是否遵循所有思考流程，检查是否遵循所有输出示例格式

输出示例格式:
{
"a": ["lookup_data('查询上个月宝武集团营业利润', '2025年1月宝武集团营业利润')", null],
}

{
"a": ["lookup_data('查询2025年1月太钢集团的合同完成率', '2025年1月合同完成率')", null],
"b": ["lookup_data('查询2024年12月太钢集团的合同完成率', '2024年12月合同完成率')", null],
"c": ["calculator('(`a` - `b`)/ `b`', '合同完成率环比变化', {'a': '2025年1月合同完成率', 'b': '2024年12月合同完成率'})", ["a", "b"]]
}

{
"a": ["lookup_data('查询2023年第三季度各公司无形资产总和及各公司数据', '2023Q3无形资产总和与各公司数据')", null],
"b": ["lookup_data('查询2022年第三季度各公司无形资产总和及各公司数据', '2022Q3无形资产总和与各公司数据')", null],
"c": ["table_tools('请根据提供的表格计算各公司在2023Q3和2022Q3的无形资产占比变化率')", ["a", "b"]]
}
"""

prompt_suffix = """[Question]: {{question}}
[Metric]:
{% for m in metrics -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}")
{% endfor -%}
[Dimension]:
{% for m in dimensions -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}", values: {{m.values_to_json}})
{% endfor -%}
{% if current_date_str %}
今天的日期是{{current_date_str}}，问题中所有相对时间，请根据当前日期进行改写。
{% endif %}

[Hint]
{{bi_hint}}
"""

prompt_suffix_huaxia = """[Question]: {{question}}
[Metric]:
{% for m in metrics -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}"{% if metrics_latest_time %}, latest_time: "{{metrics_latest_time.get(m.name, '')}}"{% endif %})
{% endfor -%}
[Dimension]:
{% for m in dimensions -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}", values: {{m.values_to_json}})
{% endfor -%}

[Hint]
{{bi_hint}}
"""

nl2agent_bi_prompt_dianxin = """现在需要提取或者计算问题中的指标，你会得到一个指标召回列表以及一个用户的请求。

# 可用工具

## lookup_data
{
    "name": "lookup_data",
    "description": "查数工具，根据请求执行对应的查询",
    "parameters": {
        "type": "object",
        "properties": {
            "query": {
                "type": "string",
                "description": "要执行的数据查询"
            },
            "description": {
                "type": "string",
                "description": "对查询结果的描述，生成指标名，如果有多个指标用,分隔"
            }
        },
        "required": [
            "query",
            "description"
        ]
    }
}
在调用上述函数时，请使用 Json 格式表示调用的参数。

## calculator

{
    "name": "calculator",
    "description": "执行数学计算，包括基本算术、微分、积分和平方根",
    "parameters": {
        "type": "object",
        "properties": {
            "expression": {
                "type": "string",
                "description": "要计算的数学表达式，例如 '(`b` - `a`) / `a`'或 '(`a` + `b`) / `a`'"
            },
            "description": {
                "type": "string",
                "description": "对计算结果的描述，生成新的指标名"
            },
            "mapping": {
                "type": "dict",
                "description": "传入的变量的映射"
            }
        },
        "required": [
            "expression",
            "description",
            "mapping"
        ]
    }
}
在调用上述函数时，请使用 Json 格式表示调用的参数。

## table_tools
{
    "name": "table_tools",
    "description": "动态模拟工具，根据请求执行对应的表操作",
    "parameters": {
        "type": "object",
        "properties": {
            "mission": {
                "type": "string",
                "description": "传入的任务描述"
            }
        },
        "required": [
            "mission"
        ]
    }
}
在调用上述函数时，请使用 Json 格式表示调用的参数。

思考流程：
1.首先明确问题属于任务类型。
2.接着从指标召回列表中去匹配目标指标。
3.若匹配成功，且不需要对指标进行计算，那就调用查数工具lookup_data，将查询到的数据记为'result'。
    a.需要计算的情况
    - 指标与指标之间计算差值/增长/占比/同比/环比
    b.不需要计算的情况
    - 查询指标的最大值/最小值
    - 按照指标进行排序
    - 按照指标筛选过滤条件
4.若匹配失败，就需要对用户请求进行语义拆分，拆分后分别调用查数工具获取对应的数据'a','b'或数据表'A','B'等。再调用calculator基于获取的数据'a','b'进行计算或调用table_tools基于获取的数据表'A','B'进行表操作，得到结果'result'。
    a.选择calculator进行计算的情况
    - lookup_data查询结果为一个指标
    - 选择calculator进行计算，需要按照步骤进行， 
    b.选择table_tools进行计算的情况
    - lookup_data查询结果为多个指标
5.最后按照要求将最终答案整理成 json 格式呈现。 
上面是整体思考流程和思路，请根据上述流程和思路进行思考。

注意事项:
- 你调用的第一个工具必须是lookup_data，你所有传入calculator工具和table_tools工具的数据必须由lookup_data工具查询得到。第一个工具不能直接调用calculator工具和table_tools工具。
- 问题中需要过滤、排序不作为需要计算的问题，可以直接调用lookup_data工具得到，需要计算比例、占比、差值等数值运算的情况，才需要用到计算工具calculator工具和table_tools工具。
- 计算工具calculator工具和table_tools工具调用过程中，传入下一个工具的输入，必须是上一个工具的输出，不能编造不存在的数据作为工具的输入。
- calculator的表达式中，所有计算对象必须用反引号``包裹
- 候选[Metric]如果没有出现指标的差值、占比、同比等，需要你调用calculator或者tabletool进行计算，无法直接通过lookupdata查询得到
- 注意问题中的同比、环比不能错误理解，不能随意替换

[Hint]:
{{hint}}
- 请优先判断问题和所有hint是否相关，如果相关，请按照hint的逻辑对问题进行改写，如果不相关，请不要改写原问题
- 中标情况的含义是同时查询中标金额、项目数量和中标金额占比
- 中标趋势的含义是同时查询中标金额趋势、项目数量趋势和中标金额占比趋势
- 市场份额和中标份额都代表中标金额占比，如果原问题中存在中标份额，请将其改写成中标金额占比，因为“中标金额占比”这个指标并不存在。例如: 
   1. “2024年A类省电信的市场份额”应改为“在2024年A类省中，电信的中标金额占比是多少？” 
   2. "2024年电信的公开市场份额"应改为“在2024年中，电信的中标金额占比是多少？”
- 对于[Question]中的关键信息，比如包含了[Env]中出现过的[Metric]和[Dimension]以及[Dimension]中相似的values，必须保留原意图，不能做任何修改，不能添加多余的分组查询信息，也不能删除与[Env]中具有相似语义的信息。例如，“电信”和“电信公司”是不同的码值
- 三家运营商指移动、电信、联通
- 问题中包含“分别”“各个”“各”等关键词时，必须保留“分别”“各个”“各”等关键词，千万不能忽略这类词语，必须该留原问题中的这部分关键词。例如：
   1. "2024年11月B类省市场中标金额分别是多少"中，必须要保留问题中的"分别"，即查询"2024年11月B类省市场中标金额分别是多少"。
- 如遇到同环比的问题，请按照以下逻辑来选择合适的计算方式：
   1. 占比类指标（例如：份额、市场份额、中标金额占比、项目数量占比）的同环比计算方式 =（本期的占比 - 上期的占比）
   2. 非占比类指标（例如中标金额、项目数量）的同环比计算方式 = （本期的指标数值 - 上期的指标数值） / 上期的指标数值 
"""


prompt_suffix_dianxin = """[Question]: {{question}}
[Metric]:
{% for m in metrics -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}")
{% endfor -%}
[Dimension]:
{% for m in dimensions -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}", values: {{m.values_to_json}})
{% endfor -%}
[Hint]
{{bi_hint}}
"""


def gen_nl2agent_bi(json_prompts: dict):
    sub_json_prompts = {}
    sub_json_prompts[DEFAULT_PROMPT] = gen_basic_json(nl2agent_bi_prompt, prompt_suffix)
    sub_json_prompts[JINGFEN_BI] = gen_basic_json(
        nl2agent_bi_prompt_dianxin, prompt_suffix_dianxin
    )
    sub_json_prompts[HUAXIA_PROJECT_NAME] = gen_basic_json(
        nl2agent_bi_prompt, prompt_suffix_huaxia
    )
    json_prompts[ParamsExtractStage.BI] = sub_json_prompts
