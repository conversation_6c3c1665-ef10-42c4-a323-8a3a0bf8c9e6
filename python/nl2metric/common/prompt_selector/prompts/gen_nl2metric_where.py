from common.prompt_selector.prompts.common import gen_fewshot_json, gen_basic_json
from common.prompt_selector.utils import DEFAULT_PROMPT
from common.types.base import ParamsExtractStage
from nl2metric.few_shots import (
    BAOWU_PROJECT_NAME,
    BEIJIAOSUO_PROJECT_NAME,
)


PROMPT_PREFIX_BEIJIAOSUO = """我希望你扮演专业的数据分析师，你精通SQL，专注于从用户问题中提取where条件参数。
请用中文回答。

你的目标是：根据问题的描述，找出其中的过滤信息，并把这些信息转化成命名实体识别任务（NER）的结果以及符合MySQL语法的where子句，并返回以下JSON格式的结果:
```json
{
    "ner": ["问题中与码值有关的片段"],
    "where": "筛选条件",
}
```

除了问题([Question])以外，我会额外给你提供指标([Metric])和维度([Dimension])列表两个信息，它们的格式和解释如下：
1. [Metric]指标列表的格式为：(name: #指标英文字段, description: #指标的中文含义，多个同义词用';'隔开)
2. [Dimension]维度列表的格式为：(name: #维度英文字段, description: #维度的中文含义，多个同义词用';'隔开, values: #维度码值数组)
注意，[Metric]和[Dimension]中的信息是根据问题召回的相关指标和维度码值信息。

你的处理步骤为：
1. 理解问题和信息（阅读[Question]内容理解意图，分析[Metric]识别指标，检查[Dimension]了解维度和码值）；
2. 判断[Question]是否提到[Dimension]中的values，如果没有，请跳过；如果有，标注[Question]中的片段与values相匹配的部分，填入ner中，并转换成SQL条件填入where中
3. 判断[Question]中是否要求对[Metric]中的指标进行数值过滤，如果没有，请跳过；如果有，只需要将数值过滤转换为SQL条件填入where中，不要填入ner中

维度过滤规则：
1. 仔细检查问题中提到的每一个关键词，将其与[Dimension]中的values列表进行匹配，将问题中与values匹配到的关键词添加到ner中
2. 匹配时要考虑以下情况：
   - 完全匹配：问题中的词与码值完全一致
   - 部分匹配：问题中的词是码值的一部分，或码值是问题中词的一部分
   - 同义词匹配：问题中的词与码值在语义上相同
   - 以“xx”开头，需要用到LIKE表示
3. 对于一个维度，如果问题中提到了多个相关码值，要全部提取并使用IN语句
4. 如果问题中提到的词在多个维度的values中都能找到匹配，需要根据上下文判断具体属于哪个维度
5. 提取码值时要注意数字的不同表达方式，如"15"和"十五"都要能识别
6. 注意values中的较短的码值，如"集团"等，这些码值容易被忽略，需要提取

指标过滤提取规则：
1. 仔细检查问题中的数值比较信息：
   - 大于/小于/等于/不等于等比较符号
   - 具体的数值，包括各种表达方式（如"500万"、"5千万"、"0.5亿"等）
   - 百分比表达（如"20%"需转换为20）
2. 匹配指标时需注意：
   - 通过[Metric]中的description字段识别指标的中文表达
   - 考虑同义词，如"营收"对应"营业收入"
   - 一个中文词可能对应多个指标时，需根据上下文选择最合适的
3. 数值处理规则：
   - 统一转换为标准数值（如"万"转换为×10000）
   - 注意金额单位的统一（元/万元/亿元）
   - 处理特殊表达，如"百万级别"转换为范围条件
4. 盈亏判断规则：
   - 遇到"亏损"，"亏损前十"，是一个指标过滤条件，相当于提问"利润小于0"
   - 遇到"盈利"，"盈利前十"，是一个指标过滤条件，相当于提问"利润大于0"
5. 避免错误提取：
   - 不要将排序条件（最大值/最小值）作为过滤条件
   - 不要臆测问题中未明确的条件（如不要自动添加">0"）
   - 注意区分分组统计和过滤条件

注意事项：
- 问题中包含指标过滤条件时，生成的where表达式，优先使用>=、<=符号，如果有明确要求，才使用>、<符号
- 对于问题中没有出现的可能属于维度过滤条件的码值，根据实际情况判断，如果确实属于维度过滤条件，则添加到ner和where表达式中
"""

PROMPT_PREFIX_BAOWU = """我希望你扮演专业的数据分析师，你精通SQL，专注于从用户问题中提取where条件参数。
请用中文回答。

你的目标是：根据问题的描述，找出其中除了时间以外的其他过滤信息，并把这些信息转化成命名实体识别任务（NER）的结果以及符合MySQL语法的where子句，并返回以下JSON格式的结果:
```json
{
    "ner": ["问题中与码值有关的片段"],
    "where": "筛选条件",
}
```

除了问题([Question])以外，我会额外给你提供指标([Metric])和维度([Dimension])列表两个信息，它们的格式和解释如下：
1. [Metric]指标列表的格式为：(name: #指标英文字段, description: #指标的中文含义，多个同义词用';'隔开)
2. [Dimension]维度列表的格式为：(name: #维度英文字段, description: #维度的中文含义，多个同义词用';'隔开, values: #维度码值数组)
注意，[Metric]和[Dimension]中的信息是根据问题召回的相关指标和维度码值信息。请你自行甄别哪些是问题中出现的，问题中没有出现的指标和维度码值请不要使用。

你的处理步骤为：
1. 理解问题和信息（阅读[Question]内容理解意图，分析[Metric]识别指标，检查[Dimension]了解维度和码值）；
2. 判断[Question]是否提到[Dimension]中的values，如果没有，请跳过；如果有，标注[Question]中的片段与values相匹配的部分，填入ner中，并转换成SQL条件填入where中
3. 判断[Question]中是否要求对[Metric]中的指标进行数值过滤，如果没有，请跳过；如果有，只需要将数值过滤转换为SQL条件填入where中，不要填入ner中

维度过滤规则：
1. 仔细检查问题中提到的每一个关键词，将其与[Dimension]中的values列表进行匹配，将问题中与values匹配到的关键词添加到ner中
2. 匹配时要考虑以下情况：
   - 完全匹配：问题中的词与码值完全一致
   - 部分匹配：问题中的词是码值的一部分，或码值是问题中词的一部分
   - 同义词匹配：问题中的词与码值在语义上相同
3. 对于一个维度，如果问题中提到了多个相关码值，要全部提取并使用IN语句
4. 如果问题中提到的词在多个维度的values中都能找到匹配，需要根据上下文判断具体属于哪个维度
5. 提取码值时要注意数字的不同表达方式，如"15"和"十五"都要能识别
6. 注意values中的较短的码值，如"集团"等，这些码值容易被忽略，需要提取

指标过滤提取规则：
1. 仔细检查问题中的数值比较信息：
   - 大于/小于/等于/不等于等比较符号
   - 具体的数值，包括各种表达方式（如"500万"、"5千万"、"0.5亿"等）
   - 百分比表达（如"20%"需转换为20）
2. 匹配指标时需注意：
   - 通过[Metric]中的description字段识别指标的中文表达
   - 考虑同义词，如"营收"对应"营业收入"
   - 一个中文词可能对应多个指标时，需根据上下文选择最合适的
3. 数值处理规则：
   - 统一转换为标准数值（如"万"转换为×10000）
   - 注意金额单位的统一（元/万元/亿元）
   - 处理特殊表达，如"百万级别"转换为范围条件
4. 盈亏判断规则：
   - 遇到"亏损"，"亏损前十"，是一个指标过滤条件，相当于提问"利润小于0"
   - 遇到"盈利"，"盈利前十"，是一个指标过滤条件，相当于提问"利润大于0"
5. 避免错误提取：
   - 不要将排序条件（最大值/最小值）作为过滤条件
   - 不要臆测问题中未明确的条件（如不要自动添加">0"）
   - 注意区分分组统计和过滤条件

{% if last_data %}
[X]:
{{ last_data }}
{% endif %}
"""

EXAMPLE_TPL = """[Question]: {{question}}
[Metric]:
{% for m in metrics -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}")
{% endfor -%}
[Dimension]:
{% for m in dimensions -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}", values: {{m.values_to_json}})
{% endfor -%}
[Think]:
{{think}}
[Result]:
```json
{{ result | pretty_to_json }}
```"""

PROMPT_SUFFIX = """下面是用户的问题，请给出你的[Think]和[Result](JSON 提参结果)。
[Hint]
{{where_hint}}
[Question]: {{question}}
[Metric]:
{% for m in metrics -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}")
{% endfor -%}
[Dimension]:
{% for m in dimensions -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}", values: {{m.values_to_json}})
{% endfor -%}
[Think]:
[Result]:
输出你的[Think]和[Result]"""

nl2metric_where_prompt = """
你的任务是根据用户问题和环境提取问题对应的where条件
<output-format>
{"where":"NAME=VALUE"}
</output-format>
<rules>
- 你的输出必须是一个json对象，不要包含任何其他多余内容
- where条件的值必须存在于环境中
- 当问题没有涉及维度时，直接输出空where条件，即 {"where":""}
- 当问题涉及多个维度时，可以使用"AND"连接，格式为{"where":"NAME1=VALUE1 AND NAME2=VALUE2"}
- 当问题涉及多个码值时，可以使用"IN"操作，格式为{"where":"DNAME IN ("VALUE1", "VALUE2")"}
- 当问题涉及码值比较时，可以使用">", "<", ">=", "<="操作符，例如{"where":"NAME>VALUE"}
</rules>
下面是用户问题和环境，请根据这些信息提取分组条件：
[Hint]
{{where_hint}}
[Question]: {{question}}
[Metric]:
{% for m in metrics -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}")
{% endfor -%}
[Dimension]:
{% for m in dimensions -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}", values: {{m.values_to_json}})
{% endfor -%}
"""


def gen_nl2metric_where(json_prompts: dict):
    sub_json_prompts = {}

    sub_json_prompts[DEFAULT_PROMPT] = gen_basic_json(nl2metric_where_prompt, "")

    sub_json_prompts[BAOWU_PROJECT_NAME] = gen_fewshot_json(
        prompt_prefix=PROMPT_PREFIX_BAOWU,
        prompt_suffix=PROMPT_SUFFIX,
        example_tpl=EXAMPLE_TPL,
    )

    sub_json_prompts[BEIJIAOSUO_PROJECT_NAME] = gen_fewshot_json(
        prompt_prefix=PROMPT_PREFIX_BEIJIAOSUO,
        prompt_suffix=PROMPT_SUFFIX,
        example_tpl=EXAMPLE_TPL,
    )

    json_prompts[ParamsExtractStage.NL2METRIC_WHERE] = sub_json_prompts
