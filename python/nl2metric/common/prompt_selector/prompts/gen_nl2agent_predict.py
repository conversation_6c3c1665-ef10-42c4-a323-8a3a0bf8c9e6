from common.prompt_selector.prompts.common import gen_basic_json
from common.types.base import ParamsExtractStage
from common.prompt_selector.utils import DEFAULT_PROMPT

nl2agent_predict_prompt = """你现在需要提取或计算预测指标的历史指标，并根据提取或计算到的历史指标预测未来指标，你会得到一个指标召回列表以及一个用户的请求。

# 可用工具

## lookup_data
{
    "name": "lookup_data",
    "description": "查数工具，根据请求执行对应的查询",
    "parameters": {
        "type": "object",
        "properties": {
            "query": {
                "type": "string",
                "description": "要执行的数据查询"
            }
        },
        "required": [
            "query"
        ]
    }
}
在调用上述函数时，请使用 Json 格式表示调用的参数。

## calculator

{
    "name": "calculator",
    "description": "执行数学计算，包括基本算术、微分、积分和平方根",
    "parameters": {
        "type": "object",
        "properties": {
            "expression": {
                "type": "string",
                "description": "要计算的数学表达式，例如 'x**2 + 2*x + 1' 或 '3 + 5'"
            },
            "operation": {
                "type": "string",
                "description": "操作类型，选项包括 'evaluate'（基本算术），'differentiate'（求导），'integrate'（积分），'sqrt'（平方根）"
            },
            "variable": {
                "type": "string",
                "description": "用于微积分操作的变量，例如 'x' 用于求导或积分",
                "nullable": "True"
            }
        },
        "required": [
            "expression",
            "operation"
        ]
    }
}
在调用上述函数时，请使用 Json 格式表示调用的参数。


## time_range
{
    "name":"time_series_service",
    "description":"预测工具，根据历史数据，按照时间粒度预测时间范围的数据",
    "parameters":{
        "type":"object",
        "properties":{
            "data":{
                "type":"dataframe",
                "description":"历史数据，包含历史数据的时间和对应的历史数据的指标数值",
                "parameters":{
                    "type":"object",
                    "properties":{
                        "date":{
                            "type":"string",
                            "description":"历史数据的时间"
                        },
                        "metric":{
                            "type":"float",
                            "description":"历史数据的指标数值"
                        }
                    }
                }
            },
            "time_range":{
                "type":"dict",
                "description":"预测数据时间范围，包括预测起始时间、终止时间和预测时间粒度"
            }
        },
        "required":[
            "data",
            "time_range"
        ]
    }
}

在调用上述函数时，请使用 Json 格式表示调用的参数。

预测流程：
1.先确定问题是预测任务类型，明确历史查询粒度。
2.从指标召回列表匹配目标指标历史数据。如果匹配成功则调用查数工具。如果匹配失败则拆分用户请求，再查数、计算。
3.调用时间范围提取工具，具像化预测时间范围time。
4.调用预测工具，并传入a和time，得到预测结果result。
5.最后整理答案为 json 格式。
上面是整体预测流程和思路
"""

prompt_suffix = """下面是用户问题及召回列表：
[Question]: {{question}}
[Metric]:
{% for m in metrics -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}")
{% endfor -%}
[Dimension]:
{% for m in dimensions -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}")
{% endfor -%}
"""


def gen_nl2agent_predict(json_prompts: dict):
    sub_json_prompts = {}
    sub_json_prompts[DEFAULT_PROMPT] = gen_basic_json(
        nl2agent_predict_prompt, prompt_suffix
    )
    json_prompts[ParamsExtractStage.PREDICT] = sub_json_prompts
