from common.prompt_selector.prompts.common import gen_fewshot_json, gen_basic_json
from common.prompt_selector.utils import DEFAULT_PROMPT
from common.types.base import ParamsExtractStage
from metastore.base import Metric
from nl2metric.example_selector import SimpleFilterExampleSelector
from nl2metric.few_shots import (
    BAOWU_PROJECT_NAME,
    FEW_SHOT_PROJECT_NAME_KEY,
    JINGFEN_PROJECT_NAME,
    CHINA_LIFE_NAME,
)

PROMPT_PREFIX_NER = """
我希望你扮演专业的数据分析师，你精通SQL领域知识，并能将其运用到从用户问题中提取参数。
你的目标是：根据问题的描述，识别问题中包含的metrics，并把这些信息转化成命名实体识别任务（NER）的结果以及，对应的指标名，忽略其他参数的提取，并按照指定 JSON 格式返回，格式类似:
```json
{
    "ner": []
    "metrics": []
}
```
你需要了解的信息包括：指标([Metric])列表的信息，维度([Dimension])信息
[Metric]指标列表的格式为(name: #指标英文字段, description: #指标中文含义)
[Dimension]维度列表的格式为：(name: #维度英文字段, description: #维度的中文含义，多个同义词用';'隔开, values: #维度码值数组)

你的处理步骤为：
1. 理解分析:
   - 仔细阅读问题(Question)内容,理解用户查询意图
   - 分析所有可用的指标(Metrics)及其含义

2. 提取NER:
   - 从问题中识别与[Metric]相关的关键短语和描述，与[Dimension]相关的描述请忽略
   - 将这些关键短语添加到ner数组中
   - 确保提取的短语准确反映问题中的指标需求

3. 匹配Metrics:
   - 对每个识别出的NER片段:
     - 在Metrics列表中寻找语义相近或完全匹配的指标
     - 进行部分匹配时，应忽略业务前缀词（如"宝武"等），重点关注核心指标含义
     - 将匹配到的指标name添加到metrics数组
   - 如果问题中没有相关指标,则返回空数组

注意:
- 只关注与指标相关的内容,忽略其他参数(时间词、维度名和业务前缀词)
- 确保提取的指标与问题核心语义相符
- 不是所有问题都包含指标，当找不到匹配指标时返回空结果
- 判断是否需要提取[Metric]时，需要排除[Dimension]中的name和values带来的影响
"""

EXAMPLE_TPL = """
[Question]: {{question}}
[Hint]
{{metric_hint}}
[Metric]:
{% for m in metrics -%}
(name: {{ m.name }}, description: {{m.prompt_description}})
{% endfor %}

[Think]: 
{{think}}
[Result]:
```json
{{result | pretty_to_json }}
```
"""

PROMPT_SUFFIX_NER = """
下面是用户的问题，请给出你的[Think]和[Result](JSON 提参结果)。注意:请以json格式给出提参结果，不要附带任何其他信息 
[Question]: {{question}}
[Metric]:
{% for m in metrics -%}
(name: {{ m.name }}, description: {{m.prompt_description}})
{% endfor %}
{% for m in dimensions -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}", values: {{m.values_to_json}})
{% endfor -%}
[Think]:
[Result]:
请输出你的[Think]和[Result]
"""


nl2metric_metrics_prompt = """
你的任务是根据用户问题和指标列表提取问题对应的指标
<output-format>
{"metrics":["METRIC_NAME"]}
</output-format>
<rules>
- 你的输出必须是一个json对象，不要包含任何其他多余内容
- 指标的值必须是指标列表中一个元素的name值
- 当问题没有涉及指标时，直接输出空列表，即 {"metrics":[]}
- 当问题涉及多个指标时，可以使用逗号连接，格式为{"metrics":["METRIC_NAME_1","METRIC_NAME_2"]}
</rules>
下面是用户问题和指标列表，请根据这些信息提取指标
[Question]: {{question}}
[Hint]
{{metric_hint}}
[Metric]:
{% for m in metrics -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}")
{% endfor -%}
"""


def gen_nl2metric_metrics(json_prompts: dict):
    sub_json_prompts = {}
    sub_json_prompts[DEFAULT_PROMPT] = gen_basic_json(nl2metric_metrics_prompt, "")

    sub_json_prompts[BAOWU_PROJECT_NAME] = gen_fewshot_json(
        prompt_prefix=PROMPT_PREFIX_NER,
        prompt_suffix=PROMPT_SUFFIX_NER,
        example_tpl=EXAMPLE_TPL,
    )

    json_prompts[ParamsExtractStage.NL2METRIC_METRICS] = sub_json_prompts
