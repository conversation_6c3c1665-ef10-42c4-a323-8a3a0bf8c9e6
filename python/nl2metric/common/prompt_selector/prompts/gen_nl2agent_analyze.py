from common.prompt_selector.prompts.common import gen_basic_json
from common.types.base import ParamsExtractStage
from common.prompt_selector.utils import DEFAULT_PROMPT

nl2agent_analyze_prompt = """
# 你的任务是为用户的一个问题设计出一套解决方案。
## 可用工具
[
    {
            "name": "NER",
            "description": "命名实体识别工具，用于提取问题中涉及到的特定参数",
            "parameters": {
                "type": "object",
                "properties": {
                                "query": {
                        "type": "string",
                        "description": "自然语言问题"
                    },
                    "need": {
                        "type": "list of string",
                        "description": "需要提取的参数列表，可选参数有：metric(指标信息)，time_attr（时间信息，用于归因类问题)，filter_info（过滤信息）"
                    }
                },
                "required": [
                                "query",
                    "need"
                ]
            }
        }，
        {
            "name": "attribution_analysis",
            "description": "对指标数值异动进行归因：计算两个时间点下不同维度、码值的指标波动及其对于大盘指标异动的贡献度，并找出topk个根因。",
            "parameters": {
                "type": "object",
                "properties": {
                    "params_set": {
                        "type": "dict",
                        "description": "归因计算所涉及到的三个参数：metric_name(指标信息)，time_query_param（时间信息)，filter_info（过滤条件）"
                    }
                },
                "required": [
                    "params_set"
                ]
            }
        },
        {
            "name": "metric_analysis",
            "description": "对指标数值进行维度下钻分析：计算同一时间点下不同维度、码值对应的指标数值，进行排序分析。并结合用户的问题输出分析报告",
            "parameters": {
                "type": "object",
                "properties": {
                    "params_set": {
                        "type": "dict",
                        "description": "指标分析所涉及到的三个参数：metric_name(指标信息)，time_query_param（时间信息)，filter_info（过滤条件）"
                    },
                    "question": {
                        "type": "string",
                        "description": "用户问题"
                    },
                },
                "required": [
                    "params_set",
                    "question"
                ]
            }
        },
        {
            "name": "lookup_data",
            "description": "查数工具，根据请求执行对应的查询",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "要执行的数据查询"
                    }
                },
                "required": [
                    "query"
                ]
            }
        },
        {
            "name": "calculator",
            "description": "执行数学计算，包括基本算术、微分、积分和平方根",
            "parameters": {
                "type": "object",
                "properties": {
                    "expression": {
                        "type": "string",
                        "description": "要计算的数学表达式，例如 'x**2 + 2*x + 1' 或 '3 + 5'"
                    },
                    "operation": {
                        "type": "string",
                        "description": "操作类型，选项包括 'evaluate'（基本算术），'differentiate'（求导），'integrate'（积分），'sqrt'（平方根）"
                    },
                    "variable": {
                        "type": "string",
                        "description": "用于微积分操作的变量，例如 'x' 用于求导或积分",
                        "nullable": "True"
                    }
                },
                "required": [
                    "expression",
                    "operation"
                ]
            }
        },
        {
            "name":"time_series_service",
            "description":"预测工具，根据历史数据，按照时间粒度预测时间范围的数据",
            "parameters":{
                "type":"object",
                "properties":{
                    "data":{
                        "type":"dataframe",
                        "description":"历史数据，包含历史数据的时间和对应的历史数据的指标数值",
                        "parameters":{
                            "type":"object",
                            "properties":{
                                "date":{
                                    "type":"string",
                                    "description":"历史数据的时间"
                                },
                                "metric":{
                                    "type":"float",
                                    "description":"历史数据的指标数值"
                                }
                            }
                        }
                    },
                    "time_range":{
                        "type":"dict",
                        "description":"预测数据时间范围，包括预测起始时间、终止时间和预测时间粒度"
                    }
                },
                "required":[
                    "data",
                    "time_range"
                ]
            }
        }
]

## 方案设计流程：
1. 先对用户问题进行分类：归因/预测/其他
2. 根据类别继续下面的流程：
   2.1 归因流程
   2.1.1 根据[Metric]列表匹配指标信息。如果匹配成功，继续下面的步骤。
   2.1.2 确定用户的问题是指标异动归因还是指标分析。通常指标异动归因涉及到的是两个时间点下的数值对比，而指标分析是在一个时间点下对指标进行维度下钻分析。选择合适的归因工具
   2.1.3 根据选择的归因工具的描述，确定需要从用户问题中提取的参数，并调用NER工具，制定需要的参数信息。
   2.1.4 确定每个任务的先后顺序和依赖关系，按照 json 格式输出
   2.2 预测流程
   2.2.1 从指标召回列表匹配目标指标历史数据。如果匹配成功则调用查数工具。如果匹配失败则拆分用户请求，再查数、计算。
   2.2.2 调用时间范围提取工具，具像化预测时间范围time。
   2.2.3 调用预测工具，并传入a和time，得到预测结果result。
   2.2.4 确定每个任务的先后顺序和依赖关系，按照 json 格式输出。
   2.3 其他流程
   2.3.1 如果分类为其他，直接输出空`{}`

# 现在请你分析下面[Question]，结合[Metric]、[Dimension]信息并选择适当的工具生成一个解决方案:
"""

prompt_suffix = """[Question]: {{question}}
[Metric]:
{% for m in metrics -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}")
{% endfor -%}
[Dimension]:
{% for m in dimensions -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}", values: {{m.values_to_json}})
{% endfor -%}
"""


def gen_nl2agent_analyze(json_prompts: dict):
    sub_json_prompts = {}
    sub_json_prompts[DEFAULT_PROMPT] = gen_basic_json(
        nl2agent_analyze_prompt, prompt_suffix
    )
    json_prompts[ParamsExtractStage.ANALYZE] = sub_json_prompts
