from common.logging.logger import get_logger
from common.prompt_selector.prompts.gen_prompts import gen_json_prompts
from common.types.callback_handler import BIJsonEncoder

logger = get_logger(__name__)


def dump_prompts():
    import simplejson as json
    import os
    from common.prompt_selector.utils import DEFAULT_PROMPT
    from config.app_config import FILE_PROMPT_SELECTOR_DIR

    json_prompts = gen_json_prompts()
    for stage, projects in json_prompts.items():
        for project_name, prompt_json in projects.items():
            if project_name == DEFAULT_PROMPT:
                file_name = f"default_prompt#{stage}.json"
            else:
                file_name = f"prompt#{project_name}#{stage}.json"

            json_file_name = os.path.join(FILE_PROMPT_SELECTOR_DIR, file_name)
            with open(json_file_name, "w", encoding="utf-8") as f:
                json.dump(
                    prompt_json, f, ensure_ascii=False, indent=2, cls=BIJsonEncoder
                )
                logger.info(f"dump prompt {json_file_name} succeed")


if __name__ == "__main__":
    dump_prompts()
