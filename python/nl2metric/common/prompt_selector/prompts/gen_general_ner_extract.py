from common.prompt_selector.prompts.common import gen_basic_json
from common.prompt_selector.utils import DEFAULT_PROMPT
from common.types.base import ParamsExtractStage


ner_extract_prompt = """你是一个结构化信息抽取专家。  

---

【任务目标】  
给定：
1. 候选的**指标（Metric）**和**维度（Dimension）**列表
2. 查询改写（模型生成的工具函数调用，通常为数据查询）  
3. 一个原始Query（用户自然语言查询）

你的任务是，抽取出每条查询改写中所涉及的：
- 原始Query中出现的指标型实体（metric_ner）与其在改写中对应的指标  
- 原始Query中出现的码值型实体（where_ner）与其在改写中对应的维度值或描述

---

【补充信息】  
我们提供的指标与维度列表可以辅助你进行NER匹配，但它**可能不完全**。你需要结合原始Query、改写内容以及召回列表进行语言理解，推理可能缺失但隐含的重要NER。

---

【Metric 候选指标】：
{metrics}

【Dimension 候选维度】：
{dimensions}

说明：
- 指标（Metric）以 `(name, description)` 的形式给出，仅需关注 description 是否与 query/改写中的实体存在匹配或语义相似。
- 维度（Dimension）有 `name`, `description`, 以及可能的 `values` 值，常用于区域、产品、机构等过滤属性。

---

【输出格式要求】
请严格按照如下JSON格式输出：
{
  "a": {
    "metric_ner": { 原始指标: [改写指标, ...] },
    "where_ner": { 原始码值: [改写码值, ...] }
  },
  ...
}

---

【示例1】（基础）  
[Metric]:
(name: demo_m001, description: "对公存款日均")
(name: demo_m002, description: "储蓄存款日均")
(name: demo_m003, description: "活期存款日均")

[Dimension]:
(name: bch_nme, description: "分行名称", values: [])
(name: bch_id, description: "分行号", values: [])

原始Query：  
北京分行的对公存款和零售存款

查询改写：  
{
  "a": ["lookup_data('查询2023-05-15北京分行的对公存款日均', '2023-05-15北京分行对公存款日均')", null],
  "b": ["lookup_data('查询2023-12-30北京分行的储蓄存款日均和活期存款日均', '2023-12-30北京分行储蓄存款日均、活期存款日均')", null]
}

输出：  
{
  "a": {
    "metric_ner": {
      "对公存款": ["对公存款日均"]
    },
    "where_ner": {
      "北京分行": ["北京分行"]
    }
  },
  "b": {
    "metric_ner": {
      "零售存款": ["储蓄存款日均", "活期存款日均"]
    },
    "where_ner": {
      "北京分行": ["北京分行"]
    }
  }
}

---

【示例2】（仅有码值）  
[Metric]:
[Dimension]:
(name: bch_nme, description: "分行名称", values: [])
(name: bln_bch_nme, description: "直属父级分行名称", values: [])

原始Query：  
苏州辖内的机构有哪些？

查询改写：  
{
  "a": ["lookup_data('查询苏州辖内机构清单', '苏州辖内机构列表')", null]
}

输出：  
{
  "a": {
    "metric_ner": {},
    "where_ner": {
      "苏州辖内": ["苏州辖内"]
    }
  }
}

---

【示例3】（指标召回缺失，需要推理）  
[Metric]:
(name: demo_m005, description: "贷款余额")
(name: demo_m006, description: "贴现余额")
[Dimension]:

原始Query：  
贷款总额

查询改写：  
{
  "a": ["lookup_data('查询2023年贷款余额和贴现余额', '贷款余额、贴现余额')", null]
}

说明：虽然“贷款总额”未出现在召回列表中，但可以通过语言理解推断其对应为“贷款余额 + 贴现余额”

输出：  
{
  "a": {
    "metric_ner": {
      "贷款总额": ["贷款余额", "贴现余额"]
    },
    "where_ner": {}
  }
}

---
"""

prompt_suffix = """
【现在请处理以下输入】
请提取每个调用语句中的metric_ner和where_ner映射。

[Metric]:
{% for m in metrics -%}
(name: {{ m.label }}, description: "{{m.prompt_description}}"
{% endfor -%}
[Dimension]:
{% for m in dimensions -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}", values: {{m.values_to_json}})
{% endfor -%}


原始Query：  
{{question}}

查询改写：
{{bi_planning_result}}
"""


def gen_general_ner_extract(json_prompts: dict):
    sub_json_prompts = {}
    sub_json_prompts[DEFAULT_PROMPT] = gen_basic_json(ner_extract_prompt, prompt_suffix)

    json_prompts[ParamsExtractStage.GENERAL_NER_EXTRACT] = sub_json_prompts
