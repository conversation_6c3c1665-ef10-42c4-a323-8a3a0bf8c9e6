import json

from common.logging.logger import get_logger
from common.prompt_selector.prompts.gen_chart_insight import gen_chart_insight
from common.prompt_selector.prompts.gen_condense_query import gen_condense_query
from common.prompt_selector.prompts.gen_condense_query_v2 import gen_condense_query_v2
from common.prompt_selector.prompts.gen_general_ner_extract import (
    gen_general_ner_extract,
)
from common.prompt_selector.prompts.gen_generate_chart_title import (
    gen_generate_chart_title,
)
from common.prompt_selector.prompts.gen_measure_dim_recommendation import (
    gen_measure_dim_recommendation,
)
from common.prompt_selector.prompts.gen_metric_attr_rewrite import (
    gen_metric_attr_rewrite,
)
from common.prompt_selector.prompts.gen_nl2agent_condense import (
    gen_nl2agent_condense,
    gen_nl2agent_condense_no_history_response,
)
from common.prompt_selector.prompts.gen_nl2agent_doc_chat import gen_nl2agent_doc_chat
from common.prompt_selector.prompts.gen_nl2agent_percentage import (
    gen_nl2agent_percentage,
)
from common.prompt_selector.prompts.gen_nl2agent_period_on_period import (
    gen_nl2agent_period_on_period,
)
from common.prompt_selector.prompts.gen_nl2agent_predict import gen_nl2agent_predict
from common.prompt_selector.prompts.gen_nl2agent_table_tools import (
    gen_nl2agent_table_tools,
)
from common.prompt_selector.prompts.gen_nl2agent_analyze import gen_nl2agent_analyze
from common.prompt_selector.prompts.gen_nl2agent_bi import gen_nl2agent_bi
from common.prompt_selector.prompts.gen_nl2agent_brain import gen_nl2agent_brain
from common.prompt_selector.prompts.gen_nl2agent_judge import gen_nl2agent_judge
from common.prompt_selector.prompts.gen_nl2agent_chat import gen_nl2agent_chat
from common.prompt_selector.prompts.gen_nl2agent_python_code_tool import (
    gen_nl2agent_python_code_tool,
)
from common.prompt_selector.prompts.gen_nl2agent_metric_meta import (
    gen_nl2agent_metric_meta,
)
from common.prompt_selector.prompts.gen_nl2intent import gen_nl2intent
from common.prompt_selector.prompts.gen_nl2intent_by_tag import gen_nl2intent_by_tag
from common.prompt_selector.prompts.gen_nl2intent_by_tag_v2 import (
    gen_nl2intent_by_tag_v2,
)
from common.prompt_selector.prompts.gen_nl2meta import gen_nl2meta
from common.prompt_selector.prompts.gen_attr_analysis_param import (
    gen_attr_analysis_param,
)
from common.prompt_selector.prompts.gen_attr_analysis_time import gen_attr_analysis_time
from common.prompt_selector.prompts.gen_nl2meta_dimension_detail import (
    gen_nl2meta_dimension_detail_prompt,
)
from common.prompt_selector.prompts.gen_nl2meta_metric_detail import (
    gen_nl2meta_metric_detail_prompt,
)
from common.prompt_selector.prompts.gen_nl2metric_group_bys import (
    gen_nl2metric_group_bys,
)
from common.prompt_selector.prompts.gen_nl2metric_metrics import gen_nl2metric_metrics
from common.prompt_selector.prompts.gen_nl2metric_order_bys import (
    gen_nl2metric_order_bys,
)
from common.prompt_selector.prompts.gen_nl2metric_time_query_v2 import (
    gen_nl2metric_time_query_v2,
)
from common.prompt_selector.prompts.gen_nl2metric_where import gen_nl2metric_where
from common.prompt_selector.prompts.gen_nl2metric_time_query import (
    gen_nl2metric_time_query,
)
from common.prompt_selector.prompts.gen_web_search import (
    gen_web_search,
)
from common.prompt_selector.prompts.gen_result_analysis import (
    gen_result_analysis,
)
from common.prompt_selector.prompts.gen_nl2time_dimension import gen_nl2time_dimension
from common.prompt_selector.prompts.gen_nl2meeting_intent import gen_nl2meeting_intent
from common.prompt_selector.prompts.gen_nl2meeting_param import gen_nl2meeting_param
from common.prompt_selector.prompts.gen_nl2meeting_time import gen_nl2meeting_time_query
from metastore.base import Dimension, DimensionValue
from refesher import prompt_refresher

logger = get_logger(__name__)


def gen_json_prompts():
    json_prompts = {}
    gen_condense_query(json_prompts)
    gen_condense_query_v2(json_prompts)
    gen_nl2agent_percentage(json_prompts)
    gen_nl2agent_period_on_period(json_prompts)
    gen_nl2agent_predict(json_prompts)
    gen_nl2agent_table_tools(json_prompts)
    gen_nl2agent_analyze(json_prompts)
    gen_nl2agent_bi(json_prompts)
    gen_nl2agent_brain(json_prompts)
    gen_nl2agent_condense(json_prompts)
    gen_nl2agent_condense_no_history_response(json_prompts)
    gen_nl2agent_judge(json_prompts)
    gen_nl2agent_chat(json_prompts)
    gen_nl2agent_doc_chat(json_prompts)
    gen_nl2agent_python_code_tool(json_prompts)
    gen_nl2agent_metric_meta(json_prompts)
    gen_nl2intent(json_prompts)
    gen_nl2intent_by_tag(json_prompts)
    gen_nl2intent_by_tag_v2(json_prompts)
    gen_nl2meta(json_prompts)
    gen_nl2metric_group_bys(json_prompts)
    gen_nl2metric_metrics(json_prompts)
    gen_nl2metric_order_bys(json_prompts)
    gen_nl2metric_where(json_prompts)
    gen_nl2metric_time_query(json_prompts)
    gen_nl2metric_time_query_v2(json_prompts)
    gen_nl2time_dimension(json_prompts)
    gen_attr_analysis_time(json_prompts)
    gen_attr_analysis_param(json_prompts)
    gen_generate_chart_title(json_prompts)
    gen_chart_insight(json_prompts)
    gen_measure_dim_recommendation(json_prompts)
    gen_nl2meeting_intent(json_prompts)
    gen_nl2meeting_param(json_prompts)
    gen_nl2meeting_time_query(json_prompts)
    gen_nl2meta_dimension_detail_prompt(json_prompts)
    gen_nl2meta_metric_detail_prompt(json_prompts)
    gen_web_search(json_prompts)
    gen_result_analysis(json_prompts)
    gen_metric_attr_rewrite(json_prompts)
    gen_general_ner_extract(json_prompts)
    logger.info(f"gen_json_prompts succeed, len(json_prompts): {len(json_prompts)}")
    return json_prompts


def get_json_prompt_db(model_id, project_id, stage):
    if project_id is None and project_id is None:
        return None
    if model_id is None:
        model_id = prompt_refresher.get_model_by_project(project_id, stage)
        if model_id is None:
            logger.error(f"Get scenes by model id is none {project_id}, {stage}")
            return None
    prompt = prompt_refresher.get_current_prompt(model_id, stage)
    return prompt


def get_json_few_shots_db(model_id, stage, project_id):
    if model_id is None:
        model_id = prompt_refresher.get_model_by_project(project_id, stage)
        if model_id is None:
            logger.error(f"Get scenes by model id is none {project_id}, {stage}")
            return None
    few_shots = prompt_refresher.get_current_few_shots(model_id, stage)
    return few_shots


def gen_few_shots(few_shots):
    transformed_data = []
    for item in few_shots:
        if "dimensions" in item:
            dimensions_data = item["dimensions"]
            json_data = json.loads(dimensions_data)
            few_shot = {
                "question": item["question"],
                "think": item["think"],
                "dimensions": [
                    Dimension(
                        name=dim["name"],
                        label=dim["label"],
                        values=DimensionValue.from_list(dim.get("values"))
                        if "values" in dim
                        else [],
                    )
                    for dim in json_data
                ],
                "result": item["result"],
            }
            transformed_data.append(few_shot)
        else:
            few_shot = {
                "question": item["question"],
                "think": item["think"],
                "result": item["result"],
            }
            transformed_data.append(few_shot)
    return transformed_data
