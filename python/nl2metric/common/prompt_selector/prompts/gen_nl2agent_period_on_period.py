from common.prompt_selector.prompts.common import gen_basic_json
from common.types.base import ParamsExtractStage
from common.prompt_selector.utils import DEFAULT_PROMPT

nl2agent_period_on_period_prompt = """你现在需要提取或者计算同环比指标，你会得到一个指标召回列表以及一个用户的请求。

# 可用工具

## lookup_data
{
    "name": "lookup_data",
    "description": "查数工具，根据请求执行对应的查询",
    "parameters": {
        "type": "object",
        "properties": {
            "query": {
                "type": "string",
                "description": "要执行的数据查询"
            }
        },
        "required": [
            "query"
        ]
    }
}
在调用上述函数时，请使用 Json 格式表示调用的参数。

## calculator

{
    "name": "calculator",
    "description": "执行数学计算，包括基本算术、微分、积分和平方根",
    "parameters": {
        "type": "object",
        "properties": {
            "expression": {
                "type": "string",
                "description": "要计算的数学表达式，例如 'x**2 + 2*x + 1' 或 '3 + 5'"
            },
            "operation": {
                "type": "string",
                "description": "操作类型，选项包括 'evaluate'（基本算术），'differentiate'（求导），'integrate'（积分），'sqrt'（平方根）"
            },
            "variable": {
                "type": "string",
                "description": "用于微积分操作的变量，例如 'x' 用于求导或积分",
                "nullable": "True"
            }
        },
        "required": [
            "expression",
            "operation"
        ]
    }
}
在调用上述函数时，请使用 Json 格式表示调用的参数。

## table_tools
{
    "name": "table_tools",
    "description": "动态模拟工具，根据请求执行对应的表操作",
    "parameters": {
        "type": "object",
        "properties": {
            "data": {
                "type": "list",
                "description": "传入的数据表"
            },
            "mission": {
                "type": "string",
                "description": "传入的任务描述"
            }
        },
        "required": [
            "data",
            "mission"
        ]
    }
}
在调用上述函数时，请使用 Json 格式表示调用的参数。

思考流程：
1.首先明确问题属于计算同环比任务类型。
2.接着从指标召回列表中去匹配目标同环比指标。
3.若匹配成功，那就调用查数工具，将查询到的数据记为'result'。
4.若匹配失败，就需要对用户请求进行语义拆分，拆分后分别调用查数工具获取对应的数据'a','b'等。再调用计算器工具基于获取的数据进行计算同环比，得到结果'result'。
5.最后按照要求将最终答案整理成 json 格式呈现。 
上面是整体思考流程和思路
"""

prompt_suffix = """下面是用户问题及召回列表：
[Question]: {{question}}
[Metric]:
{% for m in metrics -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}")
{% endfor -%}
[Dimension]:
{% for m in dimensions -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}", values: {{m.values_to_json}})
{% endfor -%}
"""


def gen_nl2agent_period_on_period(json_prompts: dict):
    sub_json_prompts = {}
    sub_json_prompts[DEFAULT_PROMPT] = gen_basic_json(
        nl2agent_period_on_period_prompt, prompt_suffix
    )
    json_prompts[ParamsExtractStage.PERIOD_ON_PERIOD] = sub_json_prompts
