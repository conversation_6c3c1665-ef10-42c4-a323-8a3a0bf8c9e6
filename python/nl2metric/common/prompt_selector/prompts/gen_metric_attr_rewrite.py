from common.prompt_selector.prompts.common import gen_basic_json
from common.prompt_selector.utils import DEFAULT_PROMPT
from common.types.base import ParamsExtractStage
from nl2metric.few_shots import HUAXIA_PROJECT_NAME

attr_rewrite_prompt = """下面是用户提出的一个归因相关的问题，同时环境中包含了 [Metric], [Dimension], [Hint](Optional) 三个部分。你的任务是遵循[Hint]中的所有提示，这个问题并直接输出新问题。注意，如果[Hint]为空，说明不需要改写问题。
<thought-process>
1. 如果[Hint]为空，说明不需要改写问题，直接按照格式要求返回原问题。
2. 如果[Hint]不为空，请仔细阅读用户问题和[Hint]中所有的要求，并按照要求改写用户问题，并按照格式要求返回新问题。
</thought-process>

<rules>
1. 严格遵守[Hint]的要求，不要做出[Hint]不涉及到的改写。
3. 对于</think>后面的内容，直接输出你修改后的问题即可。
</rules>
"""

prompt_suffix = """[Metric]:
{% for m in metrics -%}
(name: {{ m.label }}, description: "{{m.prompt_description}}"{% if metrics_latest_time %}, latest_time: "{{metrics_latest_time.get(m.name, '')}}"{% endif %})
{% endfor -%}
[Dimension]:
{% for m in dimensions -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}", values: {{m.values_to_json}})
{% endfor -%}
[Hint]: 
{{bi_hint}}

[Question]: {{question}}
"""


def gen_metric_attr_rewrite(json_prompts: dict):
    sub_json_prompts = {}
    sub_json_prompts[DEFAULT_PROMPT] = gen_basic_json(
        attr_rewrite_prompt, prompt_suffix
    )

    json_prompts[ParamsExtractStage.METRIC_ATTR_REWRITE] = sub_json_prompts
