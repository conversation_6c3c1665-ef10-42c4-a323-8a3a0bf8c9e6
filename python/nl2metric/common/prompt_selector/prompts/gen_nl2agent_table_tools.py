from common.prompt_selector.prompts.common import gen_basic_json
from common.types.base import ParamsExtractStage
from common.prompt_selector.utils import DEFAULT_PROMPT

nl2agent_table_tools_prompt = """你现在需要解析任务，并使用工具对数据表中的数据进行操作，得到最终数据表，你会得到一个或多个数据表以及一个任务。
# 可用工具

## select
{
    "name": "select",
    "description": "动态模拟工具子工具，选取需要的列进行相关操作，并创建临时表",
    "parameters": {
        "type": "object",
        "properties": {
            "select": {
                "type": "list[SelectItem(str, SelectExpressionType, str)]",
                "description": "每个SelectItem(str, SelectExpressionType, str)包含3项：1.第一项为新生成的列名，可以改写，也可以保持不变，根据操作类型确定；2.操作类型SelectExpressionType，目前支持的有COLUMN、CALCULATE、COUNT、SUM、AVG、MAX、MIN，不能出现除这几项之外的操作类型；3.操作内容，取决于操作类型SelectExpressionType，内容为需要操作的列名/表达式/技术列名。如果SelectExpressionType为CALCULATE，所有需要计算的列名，必须用反引号``包裹起来"
            },
            "groupby": {
                "type": "list[str]",
                "description": "相当于MYSQL中的GROUPBY，根据问题判断是否需要分组查询，如果需要请填写分组的维度"
            }
        },
        "required": [
            "select"
        ]
    }
}
在调用上述函数时，请使用 Json 格式表示调用的参数。

## filter
{
    "name": "filter",
    "description": "动态模拟工具子工具，对临时表进行筛选",
    "parameters": {
        "type": "object",
        "properties": {
            "expr": {
                "type": "string",
                "description": "筛选公式，保留表格中所有的列，只展示满足过滤条件的行"
            }
        },
        "required": [
            "expr"
        ]
    }
}
在调用上述函数时，请使用 Json 格式表示调用的参数。

## join
{
    "name": "join",
    "description": "动态模拟工具子工具，对两个表进行连接",
    "parameters": {
        "type": "object",
        "properties": {
            "l": {
                "type": "string",
                "description": "左表"
            },
            "r": {
                "type": "string",
                "description": "右表"
            },
            "join_type": {
                "type": "JoinType",
                "description": "连接类型，可选left_join, right_join, full_join, inner_join, cross_join"
            },
            "l_on": {
                "type": "string",
                "description": "左表连接列"
            },
            "r_on": {
                "type": "string",
                "description": "右表连接列"
            },
            "l_rename": {
                "type": "dict[str, str]",
                "description": "左表连接列重命名，字典中分别是新列名，旧列名"
            },
            "r_rename": {
                "type": "dict[str, str]",
                "description": "右表连接列重命名，字典中分别是新列名，旧列名"
            }
        },
        "required": [
            "l",
            "r",
            "join_type",
            "l_on",
            "r_on"
        ]
    }
}
在调用上述函数时，请使用 Json 格式表示调用的参数。

## order
{
    "name": "order",
    "description": "动态模拟工具子工具，对临时表排序，问题中涉及到排名/排序/前x/后x/最大/最小等问题，请选择orderby工具",
    "parameters": {
        "type": "object",
        "properties": {
            "column_name": {
                "type": "string",
                "description": "排序列名"
            },
            "type": {
                "type": "OrderByType",
                "description": "排序类型，可选ASC、DESC"
            },
            "limit": {
                "type": "int",
                "description": "排序后取的数据数量，例如5意味着取排序后的前5条数据"
            }
        },
        "required": [
            "column_name",
            "type"
        ]
    }
}
在调用上述函数时，请使用 Json 格式表示调用的参数。

思考流程:
1.先确定任务需要的数据是否在数据表中，或是否能够通过数据表求得。
2.结合select、join、filter和order工具，对数据表进行合理的操作，得到最终的数据表。
3.当传入多个数据表时，应先使用join将数据表连接起来。
4.最后整理答案为 json 格式。
上面是整体思考流程和思路

注意事项:
- 选择select工具时，请根据问题语义判断是否需要对某维度进行groupby，同时，你填写的groupby必须是表中存在的字段
- 选择select工具时，列名必须是传入的表中已经存在的，不能同时生成新的列用于计算，需要拆分成两步计算
- 选择select工具时，如果操作类型为CALCULATE，生成的表达式中，所有列名，请用反引号``包裹起来。示例"(`列A` - `列B`)"，所有列名必须是表中真实存在的，不能额外编造不存在的列名。
- select工具，传入参数中智能有1张表，不能一次选择2张表中的column，如果需要选择多张表，请先做join
- select中只能出现这些操作类型COLUMN、CALCULATE、COUNT、SUM、AVG、MAX、MIN，不能出现任何额外的类型
- 选择join工具时，需要根据定义填写各个参数，选择正确的join类型，当选择cross join时，join key可以填写为空字符串，其余join类型，需要完整填写join key，且join key必须是表中存在的字段
- 选择join工具时，join类型为inner_join或者full_join，需要根据两张表是否有相同字段，判断是否对字段进行rename，如果需要，也请按照规定格式填写l_rename，r_rename
- 选择filter工具时，正确填写比较表达式，如果有多个表达式，用AND/OR连接，filter中不能出现MYSQL的子SQL
- 选择filter工具时，不能出现env中没有的values作为表达式中的条件
- 选择filter工具时，不需要检查是否数据为空
- 对于时间维度的表示，请使用>/</=符号。比如2023年应表示为时间 >= "2023-01" AND 时间 <= "2023-12"'，不能使用时间 LIKE "2023%"和时间 BETWEEN "2023-01" AND "2023-12"
- 计算占比/差值/同比/环比时，需要先做join，再计算表达式

Hint:
- 增速/增幅和增量是两个概念，增速/增幅是百分比，增量是差值
- 两张不同的表join时，也需要判断是否存在相同的metric字段，如果存在，必须对重复metric字段进行rename，才能计算，比如l_rename= {'粗钢产量（成本对标）': '2023年产量'}, r_rename= {'粗钢产量（成本对标）': '2022年产量'}
- 如果需要计数，expression_type为COUNT，expression为需要计数的dimension字段

格式问题，以下是几个示例输出格式，请严格遵守以下格式:
[Answer]:
{
"table1": ["join('a', 'b', 'inner_join', '公司名称', '公司名称', l_rename= {'利润':'本季度利润'}, r_rename= {'利润':'上季度利润'})", ["a", "b"]],
"table2": ["select([{'name': '公司名称', 'expression_type': 'COLUMN', 'expression': '公司名称' }, { 'name': '季度利润同环比', 'expression_type': 'CALCULATE', 'expression': '(`本季度利润` - `上季度利润`) / `上季度利润`'}], group_by=['公司名称'])", ["table1"]],
"table3": ["filter(\\"季度利润同环比 > 0\\")", ["table2"]]
"table4": ["order('季度利润同环比', 'DESC'), ["table3"]]
}
[Answer]:
{
"table1": ["filter(\\"公司 = '宝武集团-总部' AND 月份 IN ('2023-03', '2023-04', '2023-05', '2023-06', '2023-07', '2023-08')\\")", ["a"]],
"table2": ["filter(\\"公司 = '宝武集团-总部' AND 月份 IN ('2022-03', '2022-04', '2022-05', '2022-06', '2022-07', '2022-08')\\")", ["a"]],
"table3": ["join('table1', 'table2', 'full_join', '公司', '公司', l_rename= {'粗钢产量（成本对标）': '2023年产量'}, r_rename= {'粗钢产量（成本对标）': '2022年产量'})", ["table1", "table2"]],
"table4": ["select([{'name': '公司', 'expression_type': 'COLUMN', 'expression': '公司'}, {'name': '粗钢产量增长率', 'expression_type': 'CALCULATE', 'expression': '(`2023年产量` - `2022年产量`) / `2022年产量`'}])", ["table3"]],
"table5": ["filter(\\"公司 = '宝武集团-总部'\\")", ["b"]]
"table6": ["order('粗钢产量增长率', 'DESC', 10), ["table4"]]
}
"""


prompt_suffix = """下面是数据表及任务：
[Question]: {{question}}
[Env]:
{{table_schemas}}

[Hint]
{{table_tools_hint}}
"""


def gen_nl2agent_table_tools(json_prompts: dict):
    sub_json_prompts = {}
    sub_json_prompts[DEFAULT_PROMPT] = gen_basic_json(
        nl2agent_table_tools_prompt, prompt_suffix
    )
    json_prompts[ParamsExtractStage.TABLE_TOOLS] = sub_json_prompts
