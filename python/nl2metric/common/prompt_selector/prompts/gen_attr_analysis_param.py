from common.prompt_selector.prompts.common import gen_basic_json
from common.prompt_selector.utils import DEFAULT_PROMPT
from common.types.base import ParamsExtractStage
from nl2metric.few_shots import (
    BANK_PROJECT,
    TIANHONG_PROJECT_NAME,
    JINGFEN_BI,
    FENGHUO_PROJECT_NAME,
    DEFAULT_PROJECT_NAME,
)

prompt_prefix = """你是一个SQL能力非常强的数据分析师，你精通利用SQL语言来回答一个业务问题。
给定一个业务问题，解决的第一步是准确定位并提取其中的指标信息，过滤信息，然后才是拼SQL。因此请你分析我的业务问题，结合我给出的指标和维度码值信息，提取出相关的 metric 和 where 参数，并按照指定 JSON 格式返回。
请用中文回答问题。

指标([Metric])和维度码值([Dimension])信息的说明如下:
1. [Metric]指标列表的格式为(name: #指标英文字段, description: #指标中文含义)
2. [Dimension]维度列表的格式为(name: #维度英文字段, description: #维度中文含义, values: #维度码值)

请你按照下面的步骤来提取参数：
1. 结合[Metric]和[Dimension]信息，分析[Question]中哪些是指标，哪些是维度，哪些是码值，并且忽略问题中涉及到的时间信息
2. 从[Metric]指标列表中找出指标中文含义(description)和[Question]中涉及的指标最为接近的一个，提取其括号中的指标英文字段作为 metric 参数
3. 从[Dimension]中找出[Question]涉及到的过滤信息(对应SQL中的where条件)
   - where 条件是一个标准的SQL片段；如果问题涉及过滤信息，请从[Dimension]中提取合适的过滤条件，如果问题不涉及任何过滤信息，则 where 参数为空字符串
   - where 条件可以是复合的条件，可以在合适的位置加上 AND OR 等其他条件语句的连接词
   - where 中用到的过滤信息和[Dimension]维度码值列表严格匹配

注意:
1. metric 参数值是包含**一个**指标英文字段的数组，该数组**不能为空**。你必须从[Metric]中选择出最匹配的指标信息。同时，你不可以捏造[Metric]中不存在的指标。
2. 请忽略问题中的时间信息，不要把时间提取作为 where 参数值
3. 必须严格按照以下JSON格式来输出提参结果:
    ```json
    {
        "metric": #指标英文字段, 
        "where": #过滤信息
    }
    ```
4. 请不要自己修改[Question]、[Metric]和[Dimension]信息，你的提取的必须来 metric 和 where 必须来自[Metric]和[Dimension]，不要自己编造新的指标或维度码值信息

下面是几个示例，每个示例提供的信息中[Question]是问题、[Metric]是指标信息、[Dimension]是维度码值信息，[Think]是思考过程，[Result]是根据思考过程得到的提参结果。请你仔细分析每个示例中的[Think]思考过程，学习如何从用户问题，指标列表和维度列表信息中提取出我想要的参数。

"""

prompt_prefix_jingfen = """你是一个SQL能力非常强的数据分析师，你精通利用SQL语言来回答一个业务问题。
给定一个业务问题，解决的第一步是准确定位并提取其中的指标信息，过滤信息，然后才是拼SQL。因此请你分析我的业务问题，结合我给出的指标和维度码值信息，提取出相关的 metric 和 where 参数，并按照指定 JSON 格式返回。
请用中文回答问题。
============================================================================================
指标([Metric])和维度码值([Dimension])信息的说明如下:
1. [Metric]指标列表的格式为(name: #指标英文字段, description: #指标中文含义)
2. [Dimension]维度列表的格式为(name: #维度英文字段, description: #维度中文含义, values: #维度码值)
============================================================================================
请你按照下面的步骤来提取参数：
1. 结合[Metric]和[Dimension]信息，分析[Question]中哪些是指标，哪些是维度，哪些是码值，并且忽略问题中涉及到的时间信息
2. 从[Metric]指标列表中找出指标中文含义(description)和[Question]中涉及的指标最为接近的一个，提取其括号中的指标英文字段作为 metric 参数
3. 从[Dimension]中找出[Question]涉及到的过滤信息(对应SQL中的where条件)
   - where 条件是一个标准的SQL片段；如果问题涉及过滤信息，请从[Dimension]中提取合适的过滤条件，如果问题不涉及任何过滤信息，则 where 参数为空字符串
   - where 条件可以是复合的条件，可以在合适的位置加上 AND OR 等其他条件语句的连接词
   - where 中用到的过滤信息和[Dimension]维度码值列表严格匹配

注意:
1. metric 参数值是包含**一个**指标英文字段的数组，该数组**不能为空**。你必须从[Metric]中选择出最匹配的指标信息。同时，你不可以捏造[Metric]中不存在的指标。
2. 请忽略问题中的时间信息，不要把时间提取作为 where 参数值
3. 必须严格按照以下JSON格式来输出提参结果:
    ```json
    {
        "metric": #指标英文字段, 
        "where": #过滤信息
    }
    ```
4. 请不要自己修改[Question]、[Metric]和[Dimension]信息，你的提取的必须来 metric 和 where 必须来自[Metric]和[Dimension]，不要自己编造新的指标或维度码值信息
============================================================================================
下面是几个示例，每个示例提供的信息中[Question]是问题、[Metric]是指标信息、[Dimension]是维度码值信息，[Think]是思考过程，[Result]是根据思考过程得到的提参结果。请你仔细分析每个示例中的[Think]思考过程，学习如何从用户问题，指标列表和维度列表信息中提取出我想要的参数。
"""

fewshot_jiaohang = """
Case1
[Question]: 2023年3月份营业净收入比2022年3月份上升的原因是什么
[Metric]: 
(name: I2000_SUM, description: 营业净收入)
(name: I4000_SUM, description: 营业利润)
(name: I6000_SUM, description: 税前净利润)
(name: I8000_SUM, description: 经济利润)
(name: I1000_SUM, description: 营业收入合计)
[Dimension]: 
(name: sap_cnl_des, description: 发卡渠道描述, values: ["网络", "分中心"])
(name: bln_bch_nme, description: 直属父级分行名称, values: ["北京市分行", "上海市分行","广西壮族自治区分行"])
(name: sap_cnl_csf, description: 发卡渠道分类, values: ["分行", "卡中心员工推荐", "网络销售", "分行-网络发卡", "直销-网络发卡"])
[Think]: 让我们来一步一步思考:
    1. 找出时间信息:[Question]中的"2023年3月份"是时间信息，所以忽略，不提取为 where 参数
    2. 匹配指标: [Question]中的"营业净收入"对应[Metric]中的"(name: I2000_SUM, description: 营业净收入)"，所以 metric 参数为["I2000_SUM"]
    3. 匹配过滤信息: 除了步骤1忽略的时间信息以外，[Dimension]中的维度码值没有出现在[Question]中，因此where参数为空
[Result]: 
```json
{
    "metric": ["I2000_SUM"],
    "where": ""
}
```

Case2
[Question]: 2023年北京市分行和上海市分行的资金成本相比2022年骤降的原因是什么
[Metric]: 
(name: C1200_SUM, description: 资金成本)
(name: C2000_SUM, description: 交易成本合计)
(name: I1300_SUM, description: 手续费收入)
(name: I1100_SUM, description: 利息收入)
[Dimension]: 
(name: bln_bch_nme, description: 直属父级分行名称, values: ["北京市分行", "上海市分行", "广西壮族自治区分行"])
(name: sap_cnl_csf, description: 发卡渠道分类, values: ["分行", "卡中心员工推荐", "网络销售", "直销-网络发卡"])
[Think]: 让我们来一步一步思考:
    1. [Question]中的"2023年"和"2022年"是时间信息，不提取为 where 参数
    2. 匹配指标: [Question]中的"资金成本"对应[Metric]中的"(name: C1200_SUM, description: 资金成本)",因此需要把["C1200_SUM"]作为参数 metric 的值
    3. 匹配过滤信息: [Question]中的"北京市分行"和"上海市分行"和[Dimension]中的"bln_bch_nme"和其码值"北京市分行"和"上海市分行"最为匹配，因此 where 参数提取为"bln_bch_nme in ("北京市分行", "上海市分行")"
[Result]:
```json
{
    "metric": ["C1200_SUM"],
    "where": "bln_bch_nme in ('北京市分行', '上海市分行')"
}
```

Case3
[Question]: 2021年第二季度卡中心员工推荐的交易成本合计相比同年第一季度月出现了何种变化
[Metric]: 
(name: C1000_SUM, description: 交易成本合计)
(name: C1501_SUM, description: 手续费收入-维萨卡)
(name: C1502_SUM, description: 手续费收入-万事达卡)
(name: C1507_SUM, description: 手续费收入-万事达货币转换费)
(name: C1509_SUM, description: 手续费收入-其他)
[Dimension]: 
(name: bln_bch_nme, description: 直属父级分行名称, values: ["北京市分行", "上海市分行","广西壮族自治区分行"])
(name: sap_cnl_csf, description: 发卡渠道分类, values: ["分行", "卡中心员工推荐", "网络销售", "分行-网络发卡", "直销-网络发卡"])
(name: sap_cnl_des, description: 发卡渠道描述, values: ["网络", "分中心"])
[Think]: 让我们来一步一步思考:
    1. 找出时间信息: "2021年第二季度"和"同年第一季度"为时间信息，所以不用提取作为 where 参数值
    2. 匹配指标: [Question]中的"交易成本合计"对应[Metric]中的"(name: C1000_SUM, description: 交易成本合计)"，因此需要把["C1000_SUM"]作为参数 metric 的值
    3. 匹配过滤信息: [Question]中的"卡中心员工推荐"和[Dimension]的"sap_cnl_csf"的码值"卡中心员工推荐"最为匹配，因此提取sap_cnl_csf = '卡中心员工推荐'作为where参数值
[Result]:
```json
{
    "metric": ["C1000_SUM"],
    "where": "sap_cnl_csf = '卡中心员工推荐'"
}
```

Case4
[Question]: 为什么今天直销-网络发卡的手续费收入下降那么多
[Metric]: 
(name: C1500_SUM, description: 手续费支出)
(name: C1502_SUM, description: 手续费收入-万事达卡)
(name: C1507_SUM, description: 手续费收入-万事达货币转换费)
(name: C1509_SUM, description: 手续费收入-其他)
(name: I1300_SUM, description: 手续费收入)
(name: I1304_SUM, description: 取现手续费)
[Dimension]: 
(name: bln_bch_nme, description: 直属父级分行名称, values: ["上海市分行", "广东省分行", "广西壮族自治区分行"])
(name: sap_cnl_csf, description: 发卡渠道分类, values: ["分行", "卡中心员工推荐", "网络销售", "分行-网络发卡", "数据库营销", "直销-网络发卡"])
(name: sap_cnl_des, description: 发卡渠道描述, values: ["网络", "分中心"])
[Think]: 让我们来一步一步思考:
    1. 找出时间信息: "今天"是时间过滤信息，所以不用提取为 where 参数
    2. 匹配指标: [Metric]中与[Question]的"手续费收入"相似的指标有很多，但是"手续费收入(I1300_SUM)"是完全匹配的选项，为最接近的一个，所以选择["I1300_SUM"]作为 metric 参数的值
    3. 匹配过滤信息: [Question]中的"直销-网络发卡"是[Dimension]中sap_cnl_csf维度的一个码值，因此提取"sap_cnl_csf = '直销-网络发卡'"作为 where 参数值
[Result]:
```json
{
    "metric": ["I1300_SUM"],
    "where": "sap_cnl_csf = '直销-网络发卡'"
}
```

Case5
[Question]: 2022年8月数据库营销的经济利润比7月激增的原因是什么
[Metric]: 
(name: I8000_SUM, description: 经济利润)
(name: I7000_SUM, description: 经营利润)
(name: I4000_SUM, description: 营业利润)
(name: C1500_SUM, description: 手续费支出)
(name: C1502_SUM, description: 手续费收入-万事达卡)
(name: C1507_SUM, description: 手续费收入-万事达货币转换费)
(name: C1509_SUM, description: 手续费收入-其他)
(name: I1300_SUM, description: 手续费收入)
(name: I1304_SUM, description: 取现手续费)
[Dimension]: 
(name: bln_bch_nme, description: 直属父级分行名称, values: ["上海市分行", "广东省分行", "广西壮族自治区分行"])
(name: sap_cnl_csf, description: 发卡渠道分类, values: ["分行", "卡中心员工推荐", "网络销售", "分行-网络发卡", "直销-网络发卡", "数据库营销"])
(name: sap_cnl_des, description: 发卡渠道描述, values: ["网络", "分中心"])
[Think]: 让我们来一步一步思考:
    1. 找出时间信息: [Question]中"2022年8月"和"7月"都是时间信息，忽略。
    2. 匹配指标: 观察[Metric]中每个指标的description，发现有三个和[Question]中涉及的"经济利润"非常接近的指标：经济利润(I8000_SUM), 经营利润(I7000_SUM)和营业利润(I4000_SUM)，但是经济利润(I8000_SUM)是完全匹配的指标，因此提取["I8000_SUM"]作为 metric 的参数值
    3. 找出过滤信息: [Question]中的"数据库营销"对应[Dimension]中的维度"sap_cnl_csf"和其码值"数据库营销"，因此提取"sap_cnl_csf = '数据库营销'"作为 where 的参数值
[Result]:
```json
{
    "metric": ["I8000_SUM"],
    "where": "sap_cnl_csf = '数据库营销'"
}
```
"""

fewshot_zhongyuan = """
Case1
[Question]: 本月海外分行的入行口径贷款相比上个月上涨的原因是什么
[Metric]: 
(name: A001_SUM, description: 总资产)
(name: A007_SUM, description: 票据)
(name: A010_SUM, description: 入行口径贷款)
(name: A011_SUM, description: 非标)
(name: A012_SUM, description: 非标)
(name: A013_SUM, description: 同业资产)
(name: A014_SUM, description: 准备金)
(name: A030_SUM, description: 账面存贷比)
(name: A031_SUM, description: 同业融入比)
[Dimension]: 
(name: org_name, description: 机构名称, values: ["上海自贸试验区f","海外分行","东区事业部","南区事业部"])
(name: org_name_lv1, description: 总行机构名, values: ["总行"])
(name: org_name_lv2, description: 分行机构名, values: ["信用卡中心","直辖区","虚拟区域","对公-现代物流金融事业部"])
(name: org_name_lv3, description: 支行汇总机构名, values: ["天津分行汇总","西安直管分中心","汽融事业部北京分部本部","汽融事业部成都分部本部"])
[Think]: 让我们来一步一步思考:
    1. 找出时间信息: [Question]中的"本月"和"上月"是时间信息，所以忽略，不提取为where参数
    2. 匹配指标: 分析[Metric]中每一个指标的description，发现[Question]中的"入行口径贷款"对应"(name: A010_SUM, description: 入行口径贷款)"，所以metric参数为["A010_SUM"]
    3. 匹配除时间以外的过滤信息: [Question]中的过滤信息"海外分行"在[Dimension]中出现，对应维度"org_name"和其码值"海外分行"，所以提取org_name = '海外分行'作为where参数值
[Result]: 
```json
{
    "metric": ["A010_SUM"],
    "where": "org_name = '海外分行'"
}
```

Case2
[Question]: 告诉我本月资本充足率变化的原因和其环比增长率
[Metric]: 
(name: A001_SUM, description: 总资产)
(name: A013_SUM, description: 同业资产)
(name: A015_SUM, description: 其他资产)
(name: A017_SUM, description: 公司存款)
(name: A024_SUM, description: 一级资本净额)
(name: A025_SUM, description: 资本净额)
(name: A028_SUM, description: 资本充足率)
[Dimension]: 
(name: org_name, description: 机构名称, values: ["小微事业部汇总","自贸区分账核算单元","资金运营中心（贵金属）"])
(name: org_name_lv1, description: 总行机构名, values: ["总行"])
(name: org_name_lv2, description: 分行机构名, values: ["小微事业部汇总","自贸区分账核算单元"])
[Think]: 让我们来一步一步思考:
    1. 找出时间信息: [Question]中的"本月"是时间信息，所以忽略，不提取为where参数
    2. 匹配指标: 分析[Metric]中每一个指标的description，发现[Question]中的"资本充足率"对应"(name: A028_SUM, description: 资本充足率)"，"环比增长率"没有在[Metric]中出现，意味着它不是一个指标，所以这里只提取资本充足率对应的英文字段(["A028_SUM"])作为metric参数值即可
    3. 匹配除时间以外的过滤信息: [Question]中没有明确的过滤信息，所以where参数为空字符串
[Result]: 
```json
{
    "metric": ["A028_SUM"],
    "where": ""
}
"""

fewshot_tianhong = """
Case1
[Question]: 去年7月份平安基金的机构赎回金额相比6月变化的原因是什么？
[Metric]: 
(name: gr_sell_today_SUM, description: 个人赎回金额)
(name: holder_COUNT_DISTINCT, description: 账户数量)
(name: jg_sell_today_SUM, description: 机构赎回金额)
(name: redemption_SUM, description: 当日赎回金额)
(name: sell_today_MAX, description: 当日最大赎回)
[Dimension]: 
(name: belong_comp, description: 基金公司, values: ["兴银基金", "平安基金"])
(name: first_track, description: 一级赛道, values: ["制造","债券","消费"])
(name: org_short_name, description: 机构简称, values: ["私募","兴全基金"])
(name: second_track, description: 细分赛道, values: ["证券","债券"])
[Think]: 让我们来一步一步思考:
    1. [Question]中的"去年7月份"和"6月"是时间信息，所以忽略，不提取为where参数
    2. 分析[Metric]中每一个指标的description，发现[Question]中的"机构赎回金额"对应"(name: jg_sell_today_SUM, description: 机构赎回金额)"，所以metric参数为["jg_sell_today_SUM"]
    3. [Question]中的过滤信息"平安基金"在[Dimension]中出现，对应维度"belong_comp"和其码值"平安基金"，提取belong_comp = '平安基金'作为where参数值
[Result]: 
```json
{
    "metric": ["jg_sell_today_SUM"],
    "where": "belong_comp = '平安基金'"
}
```

Case2
[Question]: 告诉我今年招利短债的存量比去年的变化原因
[Metric]: 
(name: asset_AVG, description: 平均存量)
(name: asset_MAX, description: 最大存量)
(name: asset_MIN, description: 最小存量)
(name: asset_SUM, description: 存量)
[Dimension]: 
(name: c_agencyname, description: 渠道名称, values: ["长安银行","邮储银行","陆基金","度小满","雪球基金"])
(name: c_pdepart_name, description: 体系名称, values: ["股票","基金","保险","证券"])
(name: c_pfundabbr, description: 产品名称, values: ["优利短债","同利LOF","安利短债","安恒60天","招利短债","安怡30天","弘择"])
(name: c_vog_region_name, description: 区域名称, values: ["三方"])
[Think]: 让我们来一步一步思考:
    1. [Question]中的"今年"和"去年"是时间信息，所以忽略，不提取为where参数
    2. 分析[Metric]中每一个指标的description，发现[Question]中的"存量"和[Metric]中"(name: asset_SUM, description: 存量)"最为接近，所以metric参数为["asset_SUM"]
    3. 分析[Dimension]中的每一个码值和其维度，发现维度"c_pfundabbr"的码值"招利短债"出现在[Question]里，说明他是一个过滤信息，因此提取c_pfundabbr = '招利短债'作为where参数值
[Result]: 
```json
{
    "metric": ["asset_SUM"],
    "where": "c_pfundabbr = '招利短债'"
}
```
"""

fewshot_jingfen = """
Case1
[Question]: 本月中标项目数的变化原因？
[Metric]: 
(name: project_name_COUNT_DISTINCT, description: 项目数量)
(name: bidding_entity_COUNT_DISTINCT, description: 招标单位数量)
(name: winning_amount_SUM_RATIO, description: 中标金额占比)
(name: winning_amount_SUM, description: 中标金额总额)
(name: project_name_COUNT_RATIO, description: 项目数量占比)
[Dimension]: 

[Think]: 让我们来一步一步思考:
    1. 找出时间信息：[Question]中的"本月"是时间信息，所以忽略，不提取为where参数
    2. 匹配指标：分析[Metric]中每一个指标的description，发现[Question]中的"标项目数"对应"(name: project_name_COUNT_DISTINCT, description: 项目数量)"，所以metric参数为["project_name_COUNT_DISTINCT"]
    3. 匹配过滤信息：[Dimension]信息为空，意味着原问题没有匹配到任何码值过滤信息。除了第1步的时间信息以外，原问题不涉及其他任何过滤信息。因此where参数值为空。
[Result]: 
```json
{
    "metric": ["project_name_COUNT_DISTINCT"],
    "where": ""
}
```

Case2
[Question]: 请帮我给4月份对比3月份电信中标金额占比变化原因做一下归因分析
[Metric]: 
(name: project_name_COUNT_DISTINCT, description: 项目数量)
(name: winning_amount_SUM, description: 中标金额总额)
(name: winning_amount_SUM_RATIO, description: 中标金额占比)
(name: bidding_entity_COUNT_DISTINCT, description: 招标单位数量)
[Dimension]: 
(name: operator_label, description: 运营商标签, values: ["电信"])
(name: winning_code, description: 中标编码, values: ["b542a4eb-2024-48f2-92dc-12e5c1456e6a"])
[Think]: 让我们来一步一步思考:
    1. 找出时间信息：[Question]中的"4月份"和"3月份"是时间信息，所以忽略，不提取为where参数
    2. 匹配指标：分析[Metric]中每一个指标的description，发现[Question]中的"中标金额占比"对应"(name: winning_amount_SUM_RATIO, description: 中标金额占比)"，所以metric参数为["winning_amount_SUM_RATIO"]
    3. 匹配过滤信息：分析[Dimension]中的每一个码值和其维度，发现维度"operator_label"的码值"电信"出现在[Question]里，说明他是一个过滤信息，因此提取operator_label = '电信'作为where参数值
[Result]: 
```json
{
    "metric": ["winning_amount_SUM_RATIO"],
    "where": "operator_label = '电信'"
}
```

Case3
[Question]: 去年签约合同收入变化的原因
[Metric]: 
(name: signed_contract_income, description: 签约合同收入)
(name: winning_amount_SUM, description: 中标金额总额)
(name: winning_amount_SUM_RATIO, description: 中标金额占比)
(name: bidding_entity_COUNT_DISTINCT, description: 招标单位数量)
[Dimension]: 
(name: operator_label, description: 运营商标签, values: ["电信"])
(name: bid_type, description: 中标类型, values: ["合同公告"])
(name: winning_entity, description: 中标单位, values: ["不履行合同条款或只履行部","原因天翼数字生活科技有限公司"])
[Think]: 让我们来一步一步思考:
    1. 找出时间信息：[Question]中的"去年"是时间信息，所以忽略，不提取为where参数
    2. 匹配指标：分析[Metric]中每一个指标的description，发现[Question]中的"签约合同收入"对应"(name: signed_contract_income, description: 签约合同收入)"，所以metric参数为["signed_contract_income"]
    3. 匹配过滤信息：分析[Dimension]中的每一个码值和其维度，没有发现与原问题匹配得上的过滤信息。因此没有码值过滤信息。where参数为空
[Result]: 
```json
{
    "metric": ["signed_contract_income"],
    "where": ""
}
```

Case4
[Question]: 2024年2月，中标金额变化原因
[Metric]: 
(name: dianxin01_inner_0715_winning_amount_SUM, description: 中标金额;中标金额总额;签约金额)
(name: dianxin01_inner_0715_bidding_entity_COUNT, description: 招标单位数量)
(name: dianxin01_inner_0715_project_name_COUNT, description: 中标项目数量;商机数量;应标商机数;招标数;项目数量)
(name: MARKET_SHARE_NEW, description: 中标金额占比;中标金额总额占比;市场份额)
[Dimension]: 
(name: winning_entity, description: 中标单位, values: ["拟中标份额1"])
(name: bid_type, description: 中标类型;类型, values: ["中标通知"])
[Think]: 让我们来一步一步思考:
    1. 找出时间信息：[Question]中的"2024年2月"是时间信息，所以忽略，不提取为where参数
    2. 匹配指标：分析[Metric]中每一个指标的description，发现[Question]中的"中标金额"对应"(name: dianxin01_inner_0715_winning_amount_SUM, description: 中标金额;中标金额总额;签约金额)"，所以metric参数为["dianxin01_inner_0715_winning_amount_SUM"]
    3. 匹配过滤信息：分析[Dimension]中的每一个码值和其维度，没有发现与原问题匹配得上的过滤信息。因此没有码值过滤信息。where参数为空
[Result]:
```json
{
    "metric": ["dianxin01_inner_0715_winning_amount_SUM"],
    "where": ""
}
```
============================================================================================
"""

prompt_suffix = """
现在请你分析下面的[Question]、[Metric]和[Dimension]信息，给出你的[Think]和[Result](JSON 提参结果)。注意: 请不要用到示例中的[Metric]和[Dimension]信息。
[Question]: {{question}}
[Metric]: {{metrics}}
[Dimension]: {{dimensions}}
[Think]:
[Result]:
"""


def gen_attr_analysis_param(json_prompts: dict):
    sub_json_prompts = {}

    sub_json_prompts[DEFAULT_PROMPT] = gen_basic_json(
        prompt_prefix + fewshot_jiaohang, prompt_suffix
    )
    sub_json_prompts[TIANHONG_PROJECT_NAME] = gen_basic_json(
        prompt_prefix + fewshot_tianhong, prompt_suffix
    )
    sub_json_prompts[JINGFEN_BI] = gen_basic_json(
        prompt_prefix_jingfen + fewshot_jingfen, prompt_suffix
    )
    sub_json_prompts[BANK_PROJECT] = gen_basic_json(
        prompt_prefix_jingfen + fewshot_jingfen, prompt_suffix
    )
    sub_json_prompts[DEFAULT_PROJECT_NAME] = gen_basic_json(
        prompt_prefix_jingfen + fewshot_jingfen, prompt_suffix
    )
    # 烽火临时使用天弘fewshots
    sub_json_prompts[FENGHUO_PROJECT_NAME] = gen_basic_json(
        prompt_prefix + fewshot_tianhong, prompt_suffix
    )

    json_prompts[ParamsExtractStage.ATTR_ANALYSIS_PARAM] = sub_json_prompts
