from common.prompt_selector.prompts.common import gen_basic_json
from common.prompt_selector.utils import DEFAULT_PROMPT
from common.types.base import ParamsExtractStage
from nl2metric.few_shots import (
    BAOWU_PROJECT_NAME,
    TIANHONG_PROJECT_NAME,
    CHINA_LIFE_NAME,
    LINGSHI_NAME,
    TAIKANG_PROJECT_NAME,
    JINGFEN_BI,
)


prompt_suffix_default = """
下面是我实际的问题。
Q: {{question}}
{{time_range_hint}}
Think:"""

prompt_suffix_with_retrieved_metrics = """
下面是用户的问题，请根据信息给出你的[Think]和[Result]。[Think]只包含Question中的时间词，不能输出其他内容。注意:请以json格式给出提参结果。
指标列表是用户问题中可能出现的指标名，你需要判断问题中包含的时间词是否来源于指标列表。请注意，你所提取的时间词，不能来自于指标列表中。
指标列表: {{metrics}}
今天是{{current_date_str}}，当前处于第{{current_quarter}}季度。
Question: {{question}}
[Hint]
{{time_range_hint}}
[Result]:
输出你的[Think]和[Result]"""

prompt_suffix_tianhong = """
下面是我实际的问题。
Q: {{question}}
{{time_range_hint}}

Think:
A:
请输出你的Think和A
"""


prompt_suffix_lingshi = """
下面是我实际的问题。
Q: {{question}}
[Hint]
{{time_range_hint}}
Think:
"""


nl2time_query_prompt = """
你是一个专业的参数提取师，精通数据分析和 BI 领域知识，SQL 语法，并能将其运用到从用户问题中提取参数。请用中文回答问题。
你提参的准确度非常重要，错误会造成我很大的损失，请认真对待。

## 你的目标是基于问题中的时间词，提取出时间相关的三个参数，请忽略其他所有信息：
1. timeStartFunction: 开始的时间，包含年、月、日
2. timeEndFunction：结束的时间，包含年、月、日
3. timeGranularity：时间粒度，表示按照指定的时间周期来分组汇总查看趋势的数据，可选值有 day/month/quarter/year/total。total 表示把整个开始和结束时间作为粒度。
当问题中不包含时间，也不包含趋势，直接返回 null。

## 返回格式：
请按照后面示例来返回 "Think:"(思考过程) 和 "A:"(JSON 提参结果)，其中 "A:" 对应的 JSON 的格式需要符合以下 TypeScript 类型定义中 TimeQueryParams 的格式：
/** 最终提取的参数的类型定义 */  
type TimeQueryParams = null | {
  /** 开始的时间 */
  timeStartFunction: { year: number; month: number; day: number }
  /** 结束的时间 */
  timeEndFunction: { year: number; month: number; day: number }
  /** 时间粒度 */
  timeGranularity: 'day' | 'month' | 'quarter' | 'year' | 'total' | null
}

## 请在回答之前一步步地思考：
1. 判断问题中是否包含时间词或者趋势，如果都没有直接返回 `null` 中止后续处理。时间词包括：年、去年、今年、今年1月、今年?月、前年、月、本月、3月、3月份、日、天、季度、趋势等词语，其他词语请忽略。
2. 提取开始和结束时间：根据问题中的时间词，推断出开始时间和结束时间的具体值。如果问题中没有指定开始的时间，最大的时间跨度为10年前到今年，即最大的年份范围为{{current_year-10}}-{{current_year}}年。
3. 提取时间粒度。遵循以下原则：
  3.1 当问题明确指定按天、月、季度、年分组的时候返回对应的 `day`、`month`、`quarter`、`year`。比如 “每月”、“各月”、“不同月”、“不同月份” 时返回 `month`。“历年”、“各年”或“每年” 返回`year`。
  3.2 当问题中包含“趋势”时，时间粒度不能为 `total`。先计算开始时间和结束时间的时间跨度，小于2个月时使用 `day`，小于24个月时使用 `month`，大于等于24个月时使用 `year`。
  3.3 当问题中包含”哪个月“、”哪一天“时，表示要分组，返回 `month`、`day`。
  3.4 “平均”并不会对时间粒度有影响，请忽略。
  3.5 当问题中包含“总和”时，返回 `total`，其他情况均默认返回 `total`。
4. 重新检查前面2步，并整理出标准的 JSON 格式返回。

今天是{{current_date_str}}。所有的相对时间，请你基于今天的时间来推算。

## 下面是一些常见的日期说明，请仔细学习：
* 3月末 = 3月最后一天：默认是今年，所以就是{{current_year}}年3月31日
* 今年 = 本年：{{current_year}}年1月1日到{{current_year}}年{{current_month}}月{{current_day}}日
* 本月 = 当月：{{current_year}}年{{current_month}}月1日到{{current_year}}年{{current_month}}月{{current_day}}日
* 近一年：开始时间和结束时间为{{one_year_ago_str}}到{{current_date_str}}
* 22年：指2022年，以此类推
* 23年以来、2023年以来：意思为“从2023年起”，是一些约定俗成的简写，并不是要查询最近23年的数据的意思
* 去年开始：开始时间和结束时间为{{last_year}}年1月1日到{{current_year}}年{{current_month}}月{{current_day}}日
* 去年：开始时间和结束时间为{{last_year}}年1月1日到{{last_year}}年12月31日
* 前年：开始时间和结束时间为{{current_year-2}}年1月1日到{{current_year-2}}年12月31日
* 近3年、过去3年、最近3年：开始时间和结束时间为{{current_year-3}}年{{current_month}}月{{current_day}}日到{{current_year}}年{{current_month}}月{{current_day}}日
* 近一日、昨天：开始时间和结束时间为{{yesterday}}到{{yesterday}}
* 最近30天：开始时间和结束时间为{{thirty_days_ago_str}}到{{current_date_str}}
* 最近六个月：开始时间和结束时间为{{six_months_ago_str}}到{{current_date_str}}

## 以下是几个样例供你参考：

Q: 销量最高的分行是哪个
Think: 
1. 问题中没有包含时间词或者趋势，直接返回 `null`。
A: 
```json
null
```

Q: 查询最近30天河北用户数的趋势
Think:
1. 问题中包含“最近30天”、“趋势”是时间词，需要提取时间
2. 根据“最近30天”推断出开始时间和结束时间为{{thirty_days_ago_str}}到{{current_date_str}}。
3. 问题中包含了“趋势”，时间粒度不能为 `total`。{{thirty_days_ago_str}}到{{current_date_str}}的时间跨度小于2个月，所以时间粒度为 `day`。
4. 重新检查并返回标准的 JSON 格式。
A:
```json
{
  "timeStartFunction": { "year": {{thirty_days_ago.year}}, "month": {{thirty_days_ago.month}}, "day": {{thirty_days_ago.day}} },
  "timeEndFunction": { "year": {{current_year}}, "month": {{current_month}}, "day": {{current_day}} },
  "timeGranularity": "day"
}
```

Q: 上海地区2023年以来的销售额趋势
Think:
1. 问题中包含“2023年以来”、“趋势”是时间词，需要提取时间
2. 根据“2023年以来”推断出开始时间为2023年1月1日到{{current_year}}年{{current_month}}月{{current_day}}日。
3. 问题中包含了“趋势”，时间粒度不能为 `total`。2023年1月1日到{{current_year}}年{{current_month}}月{{current_day}}日的时间跨度为{{12 + current_month}}个月，跨度小于24个月，所以时间粒度为 `month`。
4. 重新检查并返回标准的 JSON 格式。
A:
```json
{
  "timeStartFunction": { "year": 2023, "month": 1, "day": 1 },
  "timeEndFunction": { "year": {{current_year}}, "month": {{current_month}}, "day": {{current_day}} },
  "timeGranularity": "month"
}
```

Q: 查询每季度的销量趋势
Think: 
1. 问题中包含“每季度”、“趋势”是时间词，需要提取时间
2. 问题中没有指定开始或结束时间，所以默认跨度为过去10年。开始和结束时间为{{current_year-10}}年{{current_month}}月{{current_day}}日到{{current_year}}年{{current_month}}月{{current_day}}日。
3. 问题中包含了“每季度”，所以时间粒度为 `quarter`。
4. 重新检查并返回标准的 JSON 格式。
A:
```json
{
  "timeStartFunction": { "year": {{current_year-10}}, "month": {{current_month}}, "day": {{current_day}} },
  "timeEndFunction": { "year": {{current_year}}, "month": {{current_month}}, "day": {{current_day}} },
  "timeGranularity": "quarter"
}
```

Q: 广东省分行本年的信用卡消费笔数
Think: 
1. 问题中包含“本年”是时间词，需要提取时间
2. 根据“本年”推断出开始时间和结束时间为{{current_year}}年1月1日到{{current_date_str}}。
3. 问题中没有要求按月份分组，也没有要求查看趋势，所以时间粒度为 `total`。
4. 重新检查并返回标准的 JSON 格式。
A: 
```json
{
  "timeStartFunction": { "year": {{current_year}}, "month": 1, "day": 1 },
  "timeEndFunction": { "year": {{current_year}}, "month": {{current_month}}, "day": {{current_day}} },
  "timeGranularity": "total"
}
```

Q: 济南分行和杭州分行2020年上半年平均信用卡消费笔数
Think: 
1. 问题中包含“2020年上半年”是时间词，需要提取时间
2. 根据“2020年上半年”推断出开始时间和结束时间为2020年1月1日到6月30日。
3. 问题中没有包含“每月”，“每年”，也没有包含“趋势”，“平均”并不会对时间粒度有影响，所以时间粒度为 `total`。
4. 重新检查并返回标准的 JSON 格式。
A:
```json
{
  "timeStartFunction": { "year": 2020, "month": 1, "day": 1 },
  "timeEndFunction": { "year": 2020, "month": 6, "day": 30 },
  "timeGranularity": "total"
}
```

Q: 北京分行6月份最后一天的销量
Think: 
1. 问题中包含“6月份最后一天”是时间词，需要提取时间
2. 根据“6月份最后一天”推断出开始时间和结束时间为2024年6月30日。
3. 问题中没有指定时间粒度，也没有要查看趋势，所以时间粒度为 `total`。
4. 重新检查并返回标准的 JSON 格式。
A: 
```json
{
  "timeStartFunction": { "year": 2024, "month": 6, "day": 30 },
  "timeEndFunction": { "year": 2024, "month": 6, "day": 30 },
  "timeGranularity": "total"
}
```

Q: 3月份的销量
Think: 
1. 问题中包含“3月份”是时间词，需要提取时间
2. 根据“3月份”，没有指定年，默认为今年，推断出开始时间和结束时间为{{current_year}}年3月1日到3月31日。
3. 问题中没有指定时间粒度，也没有要查看趋势，所以时间粒度为 `total`。
4. 重新检查并返回标准的 JSON 格式。
A: 
```json
{
  "timeStartFunction": { "year": {{current_year}}, "month": 3, "day": 1 },
  "timeEndFunction": { "year": {{current_year}}, "month": 3, "day": 31 },
  "timeGranularity": "total"
}
```

Q: 整体市场中标项目数量的月增长趋势如何？
Think:
1. 问题总包含“月增长趋势”，需要提取时间
2. 问题中没有指定开始或结束时间，所以默认跨度为过去10年。开始和结束时间为{{current_year-10}}年1月1日到{{current_year}}年{{current_month}}月{{current_day}}日。
3. 问题中的“月增长趋势”明确了是按照月份去做分组然后计算中标项目数量，因此时间颗粒度为`month`
4. 重新检查并返回标准的 JSON 格式。
A: 
```json
{
  "timeStartFunction": { "year": {{current_year-10}}, "month": {{current_month}}, "day": {{current_day}} },
  "timeEndFunction": { "year": {{current_year}}, "month": {{current_month}}, "day": {{current_day}} },
  "timeGranularity": "month"
}
```

注意：
1. 禁止使用 `new Date().getFullYear()` 这类日期函数，请直接使用 number 数字，来确保我可以直接使用。
2. 当提取时间词的时候，要提取尽可能长的时间词语，比如从“今年8月最后一天的销量”这个问题中应该提取出“今年8月最后一天”而不是“今年”。
3. 闰年指的是能被4整除的年份，像2016，2020，2024，2028等都是闰年，闰年的2月份有29天。
"""


nl2time_query_prompt_tianhong = """
你是一个专业的参数提取师，精通数据分析和 BI 领域知识，SQL 语法，并能将其运用到从用户问题中提取参数。请用中文回答问题。
你提参的准确度非常重要，错误会造成我很大的损失，请认真对待。

## 你的目标是基于问题中的时间词，提取出时间相关的三个参数，请忽略其他所有信息：
1. timeStartFunction: 开始的时间，包含年、月、日
2. timeEndFunction：结束的时间，包含年、月、日
3. timeGranularity：时间粒度，表示按照指定的时间周期来分组汇总查看趋势的数据，可选值有 day/month/quarter/year/total。total 表示把整个开始和结束时间作为粒度。
当问题中不包含时间，也不包含趋势，直接返回 null。

## 返回格式：
请按照后面示例来返回 "Think:"(思考过程) 和 "A:"(JSON 提参结果)，其中 "A:" 对应的 JSON 的格式需要符合以下 TypeScript 类型定义中 TimeQueryParams 的格式：
/** 最终提取的参数的类型定义 */  
type TimeQueryParams = null | {
  /** 开始的时间 */
  timeStartFunction: { year: number; month: number; day: number }
  /** 结束的时间 */
  timeEndFunction: { year: number; month: number; day: number }
  /** 时间粒度 */
  timeGranularity: 'day' | 'month' | 'quarter' | 'year' | 'total' | null
}

## 请在回答之前一步步地思考：
1. 判断问题中是否包含时间词或者趋势，如果都没有直接返回 `null` 中止后续处理。时间词包括：年、去年、今年、今年1月、今年?月、前年、月、本月、3月、3月份、日、天、季度、趋势等词语，其他词语请忽略。
2. 提取开始和结束时间：根据问题中的时间词，推断出开始时间和结束时间的具体值。如果问题中没有指定开始的时间，最大的时间跨度为10年前到今年，即最大的年份范围为{{current_year-10}}-{{current_year}}年。
3. 提取时间粒度。遵循以下原则：
  3.1 当问题明确指定按天、月、季度、年分组的时候返回对应的 `day`、`month`、`quarter`、`year`。比如 “每月”、“各月”、“不同月”、“不同月份” 时返回 `month`。“历年”、“各年”或“每年” 返回`year`。
  3.2 当问题中包含“趋势”时，时间粒度不能为 `total`。先计算开始时间和结束时间的时间跨度，小于2个月时使用 `day`，小于24个月时使用 `month`，大于等于24个月时使用 `year`。
  3.3 当问题中包含”哪个月“、”哪一天“时，表示要分组，返回 `month`、`day`。
  3.4 “平均”并不会对时间粒度有影响，请忽略。
  3.5 当问题中包含“总和”时，返回 `total`，其他情况均默认返回 `total`。
4. 重新检查前面2步，并整理出标准的 JSON 格式返回。

今天是{{current_date_str}}。所有的相对时间，请你基于今天的时间来推算。

## 下面是一些常见的日期说明，请仔细学习：
* 3月末 = 3月最后一天：默认是今年，所以就是{{current_year}}年3月31日
* 今年 = 本年：{{current_year}}年
* 本月 = 当月：{{current_year}}年{{current_month}}月
* 近一年：开始时间和结束时间为{{one_year_ago_str}}到{{current_date_str}}
* 22年：指2022年，以此类推
* 23年以来、2023年以来：意思为“从2023年起”，是一些约定俗成的简写，并不是要查询最近23年的数据的意思
* 去年开始：开始时间和结束时间为{{last_year}}年1月1日到{{current_year}}年{{current_month}}月{{current_day}}日
* 去年：开始时间和结束时间为{{last_year}}年1月1日到{{last_year}}年12月31日
* 前年：开始时间和结束时间为{{current_year-2}}年1月1日到{{current_year-2}}年12月31日
* 近3年、过去3年、最近3年：开始时间和结束时间为{{current_year-3}}年1月1日到{{current_year}}年{{current_month}}月{{current_day}}日
* 近一日、昨天：开始时间和结束时间为{{yesterday}}到{{yesterday}}
* 最近30天：开始时间和结束时间为{{thirty_days_ago_str}}到{{current_date_str}}
* 最近六个月：开始时间和结束时间为{{six_months_ago_str}}到{{current_date_str}}
* 最新报告期/最新数据/上一报告期：这三个时间都是同义词，并且现有数据中的最新报告期对应的日期是2023年6月30日，每个报告期的间隔为半年，因此上一报告期为2022年12月31日。注意，报告期的月日只有6月30日或12月31日。

注意:
* 当问题中涉及到"当日XX"时，请识别为指标信息，而不是时间信息。例如: 当日申购，当日申购量，当日赎回金额，当日赎回量等都是指标信息，跟时间无关。
* 如果问题中没有包含时间词或者趋势，请直接按照下面的JSON格式返回 null:
```json
null
```

## 以下是几个样例供你参考：

Q: 销量最高的分行是哪个
Think: 
1. 问题中没有包含时间词或者趋势，直接返回 `null`。按照以下json的格式返回
A: 
```json
null
```

Q: 上海地区2023年以来的销售额趋势
Think:
1. 问题中包含“2023年以来”、“趋势”是时间词，需要提取时间
2. 根据“2023年以来”推断出开始时间为2023年1月1日到{{current_year}}年{{current_month}}月{{current_day}}日。
3. 问题中包含了“趋势”，时间粒度不能为 `total`。2023年1月1日到{{current_year}}年{{current_month}}月{{current_day}}日的时间跨度为{{12 + current_month}}个月，跨度小于24个月，所以时间粒度为 `month`。
4. 重新检查并返回标准的 JSON 格式。
A:
```json
{
  "timeStartFunction": { "year": 2023, "month": 1, "day": 1 },
  "timeEndFunction": { "year": {{current_year}}, "month": {{current_month}}, "day": {{current_day}} },
  "timeGranularity": "month"
}
```

Q: 上期规模大于1000的产品被哪些机构持有？
Think: 
1. 问题中“上期规模”是一个字段，“上期”不代表时间。因此问题中没有包含时间词或者趋势，直接返回 `null`。按照以下json的格式返回
A: 
```json
null
```

Q: 济南分行和杭州分行2020年上半年平均信用卡消费笔数
Think: 
1. 问题中包含“2020年上半年”是时间词，需要提取时间
2. 根据“2020年上半年”推断出开始时间和结束时间为2020年1月1日到6月30日。
3. 问题中没有包含“每月”，“每年”，也没有包含“趋势”，“平均”并不会对时间粒度有影响，所以时间粒度为 `total`。
4. 重新检查并返回标准的 JSON 格式。
A:
```json
{
  "timeStartFunction": { "year": 2020, "month": 1, "day": 1 },
  "timeEndFunction": { "year": 2020, "month": 6, "day": 30 },
  "timeGranularity": "total"
}
```

Q: 列出上一报告期中的基金公司
Think: 
1. 问题中包含“上一报告期”是时间词，需要提取时间
2. 从上述的常见日期中可知，最新报告期是2023年6月30日。因此报告期的间隔为半年，因此上一报告期为最新报告期往前推半年，对应的日期是2022年12月31日
3. 问题中没有指定时间粒度，也没有要查看趋势，所以时间粒度为 `total`。
4. 重新检查并返回标准的 JSON 格式。
A: 
```json
{
  "timeStartFunction": { "year": 2022, "month": 12, "day": 31 },
  "timeEndFunction": { "year": 2022, "month": 12, "day": 31 },
  "timeGranularity": "total"
}
```

Q: 最新报告期中，减持159790.SZ的机构有哪些
Think: 
1. 问题中包含“最新报告期”是时间词，需要提取时间。注意“最新报告期”和“最新数据”是同义词
2. 从上述的常见日期中可知，最新报告期对应的日期是2023年06月30日
3. 问题中没有指定时间粒度，也没有要查看趋势，所以时间粒度为 `total`。
4. 重新检查并返回标准的 JSON 格式。
A: 
```json
{
  "timeStartFunction": { "year": 2023, "month": 6, "day": 30 },
  "timeEndFunction": { "year": 2023, "month": 6, "day": 30 },
  "timeGranularity": "total"
}
```

Q: 查询当日申购最大的产品名称
Think:
1. 问题中"当日申购"是一个指标。遇到"当日XX"时，都视作为指标，而不是时间，因此问题中没有涉及日期词汇。按照以下json的格式返回 `null`
A: 
```json
null
```

注意：
1. 禁止使用 `new Date().getFullYear()` 这类日期函数，请直接使用 number 数字，来确保我可以直接使用。
2. 当提取时间词的时候，要提取尽可能长的时间词语，比如从“今年8月最后一天的销量”这个问题中应该提取出“今年8月最后一天”而不是“今年”。
"""


nl2time_query_prompt_baowu = """
你是一个专业的参数提取师，精通数据分析和 BI 领域知识，SQL 语法，并能将其运用到从用户问题中提取参数。请用中文回答问题。
你提参的准确度非常重要，错误会造成我很大的损失，请认真对待。


## 你的目标是基于问题中的时间词，提取出时间相关的四个参数，请忽略其他所有信息：
1. timeStartFunction: 开始的时间，包含年、月、日
2. timeEndFunction：结束的时间，包含年、月、日
3. timeGranularity：时间粒度，表示按照指定的时间周期来分组汇总查看趋势的数据，可选值有 day/month/quarter/year/total。total 表示把整个开始和结束时间作为粒度。
当问题中不包含时间，也不包含趋势，直接返回 null。
4. timeQueryType：时间查询类型，表示查询的时间最小粒度，可选值有日/月/季/年，分别对应不同的查询类型。

## 返回格式：
请按照后面示例来返回 "Think:"(思考过程) 和 "A:"(JSON 提参结果)，其中 "A:" 对应的 JSON 的格式需要符合以下 TypeScript 类型定义中 TimeQueryParams 的格式：
/** 最终提取的参数的类型定义 */  
type TimeQueryParams = null | {
  /** 开始的时间 */
  timeStartFunction: { year: number; month: number; day: number }
  /** 结束的时间 */
  timeEndFunction: { year: number; month: number; day: number }
  /** 时间粒度 */
  timeGranularity: 'day' | 'month' | 'quarter' | 'year' | 'total' | null
  /** 时间查询类型 */
  timeQueryType: '日' | '月' | '季' | '年' | null
}

## 下面是一些常见的时间词，请仔细学习，如果Question中出现下列时间词，需要提取出来作为时间参数：
前年上半年：相当于前年的上半年，前年为{{current_year-2}}年，前年上半年开始时间和结束时间为{{current_year-2}}年1月1日到{{current_year-2}}年6月30日
去年下半年：相当于去年的下半年，去年为{{current_year-1}}年，去年上半年开始时间和结束时间为{{current_year-1}}年7月1日到{{current_year-2}}年12月31日
上半年：默认为今年，开始时间和结束时间为{{current_year}}年1月1日到{{current_year}}年6月30日
下半年：默认为今年，开始时间和结束时间为{{current_year}}年7月1日到{{current_year}}年12月31日
上个季度：当前是{{current_year}}年第{{current_quarter}}季度，上个季度为{{last_quarter_year}}年第{{last_quarter}}季度
上上个季度：当前是{{current_year}}年第{{current_quarter}}季度，上上个季度为{{last_last_quarter_year}}年第{{last_last_quarter}}季度
年初：指每年的第一个月，开始时间和结束时间为1月1日到1月31日；默认为{{current_year}}年，开始时间和结束时间为{{current_year}}年1月1日到{{current_year}}年1月31日
202202：表示yyyy-mm，指2022年2月
现在：等同于今天、当前、目前、最新，返回当日时间，开始时间和结束时间为{{current_date_str}}

## 请在回答之前一步步地思考：
1. 判断问题中是否包含时间词或者趋势，如果都没有直接返回 null 中止后续处理，不能返回当日时间{{current_date_str}}作为结果。时间词包括：年、去年、今年、今年1月、今年?月、前年、月、本月、3月、3月份、日、天、季度、趋势、当前、现在、最新、202202等词语，其他词语请忽略。
2. 提取开始和结束时间：根据问题中的时间词，推断出开始时间和结束时间的具体值。
3. 提取时间粒度。遵循以下原则，判断是否需要按照时间粒度分组：
  3.1 当问题明确指定按天、月、季度、年分组的时候返回对应的 'day'、'month'、'quarter'、'year'。比如 “每月”、“各月”、“不同月”、“不同月份”、“xx月分别”时返回 'month'。“历年”、“各年”或“每年” 返回'year'。
  3.2 当问题中包含“趋势”，时，需要查看每个月的数据，时间粒度不能为 'total'。如果问题没有明确时间粒度，默认使用'month'。
  3.3 当问题中包含”哪个月“、”哪一天“、“最高的季度”、“最低的年份”等时，表示要分组，返回 'month'、'day'、'quarter'、'year'。
  3.4 “平均”并不会对时间粒度有影响，请忽略。
  3.5 当问题中包含"总额"、"总和"、"汇总"等表示汇总的词语时，返回 'total'；当问题没有明确需要按照时间粒度分组时，默认返回 'total'；查询具体的年、季、月，不需要分组，也返回'total'。
4. 提取时间查询类型。遵循以下原则：
  4.1 当问题中包含“月”、“季度”、“年”等时间词时，返回对应的 '月'、'季'、'年'。
  4.2 当问题中既包含“月”又包含“年”时，返回最小的时间粒度，'月'。
5. 重新检查前面3步，并整理出标准的 JSON 格式返回。

今天是{{current_date_str}}。所有的相对时间，请你基于今天的时间来推算。
"""

# copied from baowu
nl2time_query_prompt_taikang = """
你是一个专业的参数提取师，精通数据分析和 BI 领域知识，SQL 语法，并能将其运用到从用户问题中提取参数。请用中文回答问题。
你提参的准确度非常重要，错误会造成我很大的损失，请认真对待。

## 你的目标是基于问题中的时间词，提取出时间相关的四个参数，请忽略其他所有信息：
1. timeWords: 问题中包含的描述时间的词
2. timeStartFunction: 开始的时间，包含年、月、日
3. timeEndFunction：结束的时间，包含年、月、日
4. timeGranularity：时间粒度，表示按照指定的时间周期来分组汇总查看趋势的数据，可选值有 day/month/quarter/year/total。total 表示把整个开始和结束时间作为粒度。
当问题中不包含时间，也不包含趋势，直接返回 null。

## 返回格式：
请按照后面示例来返回 "Think:"(思考过程) 和 "A:"(JSON 提参结果)，其中 "A:" 对应的 JSON 的格式需要符合以下 TypeScript 类型定义中 TimeQueryParams 的格式：
/** 最终提取的参数的类型定义 */  
type TimeQueryParams = null | {
  /** 时间词汇 */
  timeWords: string[]
  /** 开始的时间 */
  timeStartFunction: { year: number; month: number; day: number }
  /** 结束的时间 */
  timeEndFunction: { year: number; month: number; day: number }
  /** 时间粒度 */
  timeGranularity: 'day' | 'month' | 'quarter' | 'year' | 'total' | null
}

## 请在回答之前一步步地思考：
1. 判断问题中是否包含时间词或者趋势，如果都没有直接返回 null 中止后续处理，不能返回当日时间{{current_date_str}}作为结果。时间词包括：年、去年、今年、今年1月、今年?月、前年、月、本月、3月、3月份、日、天、季度、趋势、当前、现在、最新、202202等词语，其他词语请忽略。
2. 提取开始和结束时间：根据问题中的时间词，推断出开始时间和结束时间的具体值。
3. 提取时间粒度。遵循以下原则，判断是否需要按照时间粒度分组：
  3.1 当问题明确指定按天、月、季度、年分组的时候返回对应的 'day'、'month'、'quarter'、'year'。比如 “每月”、“各月”、“不同月”、“不同月份”、“xx月分别”时返回 'month'。“历年”、“各年”或“每年” 返回'year'。
  3.2 当问题中包含“趋势”，时，需要查看每个月的数据，时间粒度不能为 'total'。如果问题没有明确时间粒度，默认使用'month'。
  3.3 当问题中包含”哪个月“、”哪一天“、“最高的季度”、“最低的年份”等时，表示要分组，返回 'month'、'day'、'quarter'、'year'。
  3.4 “平均”并不会对时间粒度有影响，请忽略。
  3.5 当问题中包含"总额"、"总和"、"汇总"等表示汇总的词语时，返回 'total'；当问题没有明确需要按照时间粒度分组时，默认返回 'total'。
4. 重新检查前面2步，并整理出标准的 JSON 格式返回。

今天是{{current_date_str}}。所有的相对时间，请你基于今天的时间来推算。

## 下面是一些常见的时间词，请仔细学习，如果Question中出现下列时间词，需要提取出来作为时间参数：
* 3月末 = 3月最后一天：默认是今年，所以就是{{current_year}}年3月31日
* 今年 = 本年 = 全年：{{current_year}}年
* 这个月 = 本月 = 当月：{{current_year}}年{{current_month}}月
* 近一年：开始时间和结束时间为{{one_year_ago_str}}到{{current_date_str}}
* 22年：指2022年，以此类推
* 23年以来、2023年以来：意思为“从2023年起”，是一些约定俗成的简写，并不是要查询最近23年的数据的意思
* 去年开始：开始时间和结束时间为{{last_year}}年1月1日到{{current_year}}年{{current_month}}月{{current_day}}日
* 去年：开始时间和结束时间为{{last_year}}年1月1日到{{last_year}}年12月31日
* 前年：开始时间和结束时间为{{current_year-2}}年1月1日到{{current_year-2}}年12月31日
* 近3年、过去3年、最近3年：开始时间和结束时间为{{current_year-3}}年1月1日到{{current_year}}年{{current_month}}月{{current_day}}日
* 近一日、昨天：开始时间和结束时间为{{yesterday}}到{{yesterday}}
* 最近30天：开始时间和结束时间为{{thirty_days_ago_str}}到{{current_date_str}}
* 最近六个月：开始时间和结束时间为{{six_months_ago_str}}到{{current_date_str}}
* 上个月：开始时间和结束时间为{{current_year}}年{{current_month-1}}月1日到{{current_year}}年{{current_month-1}}月{{last_month_day}}日
* 季度：一年共12个月，每3个月为1个季度。默认是今年，一季度为{{current_year}}年1-3月，二季度为{{current_year}}年4-6月，三季度为{{current_year}}年7-9月，四季度为{{current_year}}年10-12月
* 年初：指每年的第一个月，开始时间和结束时间为1月1日到1月31日；默认为2024年，开始时间和结束时间为2024年1月1日到2024年1月31日
* 上个季度：当前是第{{current_quarter}}季度，上个季度为第{{current_quarter-1}}季度
* 上上个季度：当前是第{{current_quarter}}季度，上上个季度为第{{current_quarter-2}}季度
* 二三年：指2023年，以此类推

## 以下是几个样例供你参考：

Question: 销量最高的分行是哪个
指标列表: [销量, 销量增长率, 销量占比]
Think: 
Question中无时间词
A: 
```json
null
```

Question: 查询去年产量的趋势
指标列表: [产量, 产量增长率, 产量占比]
Think: 
Question中包含时间词去年、趋势
A:
```json
{
  "timeWords": ["去年", "趋势"],
  "timeStartFunction": { "year": {{last_year}}, "month": 1, "day": 1 },
  "timeEndFunction": { "year": {{last_year}}, "month": 12, "day": 31 },
  "timeGranularity": "month"
}
```

Question: 上海地区1-9月销售额分别是多少
指标列表: [销售额, 销售额增长率, 销售额占比]
Think: 
Question中包含时间词1-9月，且需要查看每月分别的数据
A:
```json
{
  "timeWords": ["1-9月"],
  "timeStartFunction": { "year": 2024, "month": 1, "day": 1 },
  "timeEndFunction": { "year": 2024, "month": 9, "day": 30 },
  "timeGranularity": "month"
}
```

Question: 查询每季度的销量趋势
指标列表: [销量, 销量增长率, 销量占比]
Think: 
Question中包含时间词每季度、趋势
A:
```json
{
  "timeWords": ["每季度", "趋势"],
  "timeStartFunction": { "year": 2014, "month": 1, "day": 1 },
  "timeEndFunction": { "year": 2024, "month": {{current_month}}, "day": {{current_day}} },
  "timeGranularity": "quarter"
}
```

Question: 广东省分行本年的信用卡消费笔数
指标列表: [信用卡消费笔数, 信用卡消费笔数增长率, 信用卡消费笔数占比]
Think: 
Question中包含时间词本年
A: 
```json
{
  "timeWords": ["本年"],
  "timeStartFunction": { "year": 2024, "month": 1, "day": 1 },
  "timeEndFunction": { "year": 2024, "month": 12, "day": 31 },
  "timeGranularity": "total"
}
```

Question: 济南分行和杭州分行2020年下半年平均信用卡消费笔数
指标列表: [信用卡消费笔数, 信用卡消费笔数增长率, 信用卡消费笔数占比]
Think: 
Question中包含时间词2020年下半年
A:
```json
{
  "timeWords": ["2020年下半年"],
  "timeStartFunction": { "year": 2020, "month": 7, "day": 1 },
  "timeEndFunction": { "year": 2020, "month": 12, "day": 31 },
  "timeGranularity": "total"
}
```

Question: 查询北京分行上个月的销量
指标列表: [销量, 销量增长率, 销量占比]
Think: 
Question中包含时间词上个月
A: 
```json
{
  "timeWords": ["上个月"],
  "timeStartFunction": { "year": {{current_year}}, "month": {{current_month-1}}, "day": 1 },
  "timeEndFunction": { "year": {{current_year}}, "month": {{current_month-1}}, "day": {{last_month_day}} },
  "timeGranularity": "total"
}
```

Question: 查询宝武共享现在的销售额
指标列表: [销售额, 销售额增长率, 销售额占比]
Think: 
Question中包含时间词现在
A: 
```json
{
  "timeWords": ["现在"],
  "timeStartFunction": { "year": {{current_year}}, "month": {{current_month}}, "day": {{current_day}} },
  "timeEndFunction": { "year": {{current_year}}, "month": {{current_month}}, "day": {{current_day}} },
  "timeGranularity": "total"
}
```

Question: 湛江钢铁今年这个季度的价格如何
指标列表: [价格, 价格增长率, 价格占比]
Think: 
Question中包含时间词今年这个季度
A: 
```json
{
  "timeWords": ["今年这个季度"],
  "timeStartFunction": { "year": {{current_year}}, "month": {{current_quarter_start_month}}, "day": 1 },
  "timeEndFunction": { "year": {{current_year}}, "month": {{current_quarter_start_month + 2}}, "day": {{current_quarter_end_day}} },
  "timeGranularity": "total"
}
```

Question: 今年北京分行哪三个月销量最高
指标列表: [销量, 销量增长率, 销量占比]
Think: 
Question中包含时间词今年、哪三个月
A: 
```json
{
  "timeWords": ["今年", "哪三个月"],
  "timeStartFunction": { "year": {{current_year}}, "month": 1, "day": 1 },
  "timeEndFunction": { "year": {{current_year}}, "month": 12, "day": 31 },
  "timeGranularity": "month"
}
```

Question: 宝武杰富意特殊钢有限公司-单体前年上半年的资产处置收益是多少？
指标列表: [资产处置收益, 资产处置收益增长率, 资产处置收益占比]
Think: 
Question中包含时间词前年上半年
A: 
```json
{
  "timeWords": ["前年上半年"],
  "timeStartFunction": { "year": {{current_year-2}}, "month": 1, "day": 1 },
  "timeEndFunction": { "year": {{current_year-2}}, "month": 6, "day": 30 },
  "timeGranularity": "total"
}
```

Question: 当年结余
指标列表: [当年结余, 结余平均值, 结余最大值, 结余最小值]
Think: 
Question中无时间词
A: 
```json
null
```

"""


nl2time_query_prompt_china_life = """你是一个专业的参数提取师，精通数据分析和 BI 领域知识，SQL 语法，并能将其运用到从用户问题中提取参数。请用中文回答问题。
你提参的准确度非常重要，错误会造成我很大的损失，请认真对待。

## 你的目标是基于问题中的时间词，提取出时间相关的三个参数，请忽略其他所有信息：
1. timeStartFunction: 开始的时间，包含年、月、日
2. timeEndFunction：结束的时间，包含年、月、日
3. timeGranularity：时间粒度，表示按照指定的时间周期来分组汇总查看趋势的数据，可选值有 day/month/quarter/year/total。total 表示把整个开始和结束时间作为粒度。
当问题中不包含时间，也不包含趋势，直接返回 null。

## 返回格式：
请按照后面示例来返回 "Think:"(思考过程) 和 "A:"(JSON 提参结果)，其中 "A:" 对应的 JSON 的格式需要符合以下 TypeScript 类型定义中 TimeQueryParams 的格式：
/** 最终提取的参数的类型定义 */  
type TimeQueryParams = null | {
  /** 开始的时间 */
  timeStartFunction: { year: number; month: number; day: number }
  /** 结束的时间 */
  timeEndFunction: { year: number; month: number; day: number }
  /** 时间粒度 */
  timeGranularity: 'day' | 'month' | 'quarter' | 'year' | 'total' | null
}

## 请在回答之前一步步地思考：
1. 判断问题中是否包含时间词或者趋势，如果都没有直接返回 `null` 中止后续处理。时间词包括：年、去年、今年、今年1月、今年?月、前年、月、本月、3月、3月份、日、天、季度、趋势等词语，其他词语请忽略。
2. 提取开始和结束时间：根据问题中的时间词，推断出开始时间和结束时间的具体值。如果问题中没有指定开始的时间，最大的时间跨度为10年前到今年，即最大的年份范围为{{current_year-10}}-{{current_year}}年。
3. 提取时间粒度。遵循以下原则：
  3.1 当问题明确指定按天、月、季度、年分组的时候返回对应的 `day`、`month`、`quarter`、`year`。比如 “每月”、“各月”、“不同月”、“不同月份” 时返回 `month`。“各年”或“每年” 返回`year`。
  3.2 当问题中包含“趋势”或者表示时间范围内分布的词汇时，时间粒度不能为 `total`。先计算开始时间和结束时间的时间跨度，小于2个月时使用 `day`，小于24个月时使用 `month`，大于等于24个月时使用 `year`。
  3.3 当问题中包含“分布”时，请根据语意分析：如果是对时间的分布，那么时间粒度不可以为`total`。如果是对维度的分布，那么时间粒度可以为`total`。例如“各渠道的保费收入分布情况”，这个问题中的“分布”是对渠道进行分组，所以时间粒度为`total`。
  3.4 当问题中包含”哪个月“、”哪一天“时，表示要分组，返回 `month`、`day`。
4. 重新检查前面2步，并整理出标准的 JSON 格式返回。

今天是{{current_date_str}}。所有的相对时间，请你基于今天的时间来推算。

## 下面是一些常见的日期说明，请仔细学习：
* 3月末 = 3月最后一天：默认是今年，所以就是{{current_year}}年3月31日
* 今年 = 本年：{{current_year}}年1月1日到{{current_year}}年{{current_month}}月{{current_day}}日
* 本月 = 当月：{{current_year}}年{{current_month}}月1日到{{current_year}}年{{current_month}}月{{current_day}}日
* 近一年：开始时间和结束时间为{{one_year_ago_str}}到{{current_date_str}}
* 22年：指2022年，以此类推
* 23年以来、2023年以来：意思为“从2023年起”，是一些约定俗成的简写，并不是要查询最近23年的数据的意思
* 近3年、过去3年、最近3年：开始时间和结束时间为{{current_year-3}}年{{current_month}}月{{current_day}}日到{{current_year}}年{{current_month}}月{{current_day}}日
* 近一日、昨天：开始时间和结束时间为{{yesterday}}到{{yesterday}}
* 最近六个月：开始时间和结束时间为{{six_months_ago_str}}到{{current_date_str}}

## 以下是几个样例供你参考：

Q: 销量最高的分行是哪个
Think: 
1. 问题中没有包含时间词或者趋势，直接返回 `null`。
A: 
```json
null
```

Q: 保费收入最高的时间段是在几月
Think: 
1. 问题中包含“几月”是时间词，需要提取时间
2. 问题中没有指定开始或结束时间，所以默认跨度为过去10年。开始和结束时间为{{current_year-10}}年{{current_month}}月{{current_day}}日到{{current_year}}年{{current_month}}月{{current_day}}日。
3. 问题中包含了“保费收入销量最高的时间段是在几月”，说明要按照月份分组计算保费，然后找出最高的月份。所以时间粒度为 `month`。
4. 重新检查并返回标准的 JSON 格式。
A:
```json
{
  "timeStartFunction": { "year": {{current_year-10}}, "month": {{current_month}}, "day": {{current_day}} },
  "timeEndFunction": { "year": {{current_year}}, "month": {{current_month}}, "day": {{current_day}} },
  "timeGranularity": "month"
}
```

Q: 上海地区2019年以来销售额趋势
Think:
1. 问题中包含“2019年以来”、“趋势”是时间词，需要提取时间
2. 根据“2019年以来”推断出开始时间为2019年1月1日到{{current_year}}年{{current_month}}月{{current_day}}日。
3. 问题中包含了“趋势”，时间粒度不能为 `total`。2019年1月1日到{{current_year}}年{{current_month}}月{{current_day}}日的时间跨度小于24个月，所以时间粒度为 `month`。
A:
```json
{
  "timeStartFunction": { "year": 2019, "month": 1, "day": 1 },
  "timeEndFunction": { "year": {{current_year}}, "month": {{current_month}}, "day": {{current_day}} },
  "timeGranularity": "month"
}
```

Q: 全年收入分布情况
Think: 
1. 问题中包含“全年”是时间词，需要提取时间
2. 根据今天的日期可确定，“全年”指的是开始和结束时间为{{current_year}}年1月1日到{{current_year}}年12月31日。
3. 问题中包含了“分布”。根据语意，“分布”指的是对问题中的“全年”进行时间分组所以颗粒度不能为`total`。{{current_year}}年1月1日到{{current_year}}年12月31日时间跨度小于24个月，所以时间粒度为 `month`。
A:
```json
{
  "timeStartFunction": { "year": {{current_year}}, "month": 1, "day": 1 },
  "timeEndFunction": { "year": {{current_year}}, "month": 12, "day": 31 },
  "timeGranularity": "month"
}
```

Q: 广东省分行本年的信用卡消费笔数分布情况
Think: 
1. 问题中包含“本年”是时间词，需要提取时间
2. 根据“本年”推断出开始时间和结束时间为{{current_year}}年1月1日到{{current_year}}年{{current_month}}月{{current_day}}日。
3. 问题中包含“分布”，指的是对“本年”进行分组，因此时间粒度不能为`total`，{{current_year}}年1月1日到{{current_year}}年{{current_month}}月{{current_day}}日的时间跨度小于24个月，所以时间粒度为`month`
A: 
```json
{
  "timeStartFunction": { "year": {{current_year}}, "month": 1, "day": 1 },
  "timeEndFunction": { "year": {{current_year}}, "month": {{current_month}}, "day": {{current_day}} },
  "timeGranularity": "month"
}
```

Q: 全年保费收入最高的分公司是哪一家
Think: 
1. 问题中包含“全年”是时间词，需要提取时间
2. 由于“全年”没有指定年份，默认用今年，推断出开始时间和结束时间为{{current_year}}年1月1日到{{current_year}}年12月31日。
3. 问题中没有指定时间粒度，也没有要查看趋势或分布情况（例如每个季度、每个月份、每年等），所以时间粒度为 `total`。
A:
```json
{
  "timeStartFunction": { "year": {{current_year}}, "month": 1, "day": 1 },
  "timeEndFunction": { "year": {{current_year}}, "month": 12, "day": 31 },
  "timeGranularity": "total"
}
```

Q: 全年每个季度保费收入情况
Think: 
1. 问题中包含“全年”是时间词，需要提取时间
2. 由于“全年”没有指定年份，默认用今年，推断出开始时间和结束时间为{{current_year}}年1月1日到{{current_year}}年12月31日。
3. 问题中包含“每个季度”，说明要按照季度进行分组，所以时间粒度为 `quarter`。
A: 
```json
{
  "timeStartFunction": { "year": {{current_year}}, "month": 1, "day": 1 },
  "timeEndFunction": { "year": {{current_year}}, "month": 12, "day": 31 },
  "timeGranularity": "quarter"
}
```

Q: 3月保费的环比情况
Think: 
1. 问题中包含“3月份”是时间词，需要提取时间
2. 根据“3月份”，没有指定年，默认为今年，推断出开始时间和结束时间为{{current_year}}年3月1日到3月31日。
3. 问题中没有指定时间粒度，也没有要查看趋势，所以时间粒度为 `total`。
A: 
```json
{
  "timeStartFunction": { "year": {{current_year}}, "month": 3, "day": 1 },
  "timeEndFunction": { "year": {{current_year}}, "month": 3, "day": 31 },
  "timeGranularity": "total"
}
```

Q: 每个月度，各个分公司标准保费的收入情况？
Think:
1. 问题总包含“每个月度”，需要提取时间
2. 根据“每个月度”，没有指定时间范围，所以默认取过去十年，推断出开始时间和结束时间为{{current_year-10}}年{{current_month}}月{{current_day}}日到{{current_year}}年{{current_month}}月{{current_day}}日。
3. 问题中的“每个月度”明确了是按照月份去做分组，因此时间颗粒度为`month`
A: 
```json
{
  "timeStartFunction": { "year": {{current_year-10}}, "month": {{current_month}}, "day": {{current_day}} },
  "timeEndFunction": { "year": {{current_year}}, "month": {{current_month}}, "day": {{current_day}} },
  "timeGranularity": "month"
}
```

注意，提取时间粒度时，请遵循以下原则：
1. 当问题明确指定按天、月、季度、年分组的时候返回对应的 `day`、`month`、`quarter`、`year`。比如 “每月”、“各月”、“不同月”、“不同月份” 时返回 `month`。“各年”或“每年” 返回`year`。
2. 当问题中包含“趋势”时，时间粒度不能为 `total`。先计算开始时间和结束时间的时间跨度，小于2个月时使用 `day`，小于24个月时使用 `month`，大于等于24个月时使用 `year`。
3. 当问题中包含“分布”时，请根据语意分析：如果是对时间的分布，那么时间粒度不可以为`total`。如果是对维度的分布，那么时间粒度可以为`total`。例如“各渠道的保费收入分布情况”，这个问题中的“分布”是对渠道进行分组，而不是时间。所以时间粒度为`total`。
"""


nl2time_query_prompt_lingshi = """
你是一个专业的参数提取师，精通数据分析和 BI 领域知识，SQL 语法，并能将其运用到从用户问题中提取参数。请用中文回答问题。
你提参的准确度非常重要，错误会造成我很大的损失，请认真对待。

## 你的目标是基于问题中的时间词，提取出时间相关的三个参数，请忽略其他所有信息：
1. timeStartFunction: 开始的时间，包含年、月、日
2. timeEndFunction：结束的时间，包含年、月、日
3. timeGranularity：时间粒度，表示按照指定的时间周期来分组汇总查看趋势的数据，可选值有 day/month/quarter/year/total。total 表示把整个开始和结束时间作为粒度。
当问题中不包含时间，也不包含趋势，直接返回 null。

## 返回格式：
请按照后面示例来返回 "Think:"(思考过程) 和 "A:"(JSON 提参结果)，其中 "A:" 对应的 JSON 的格式需要符合以下 TypeScript 类型定义中 TimeQueryParams 的格式：
/** 最终提取的参数的类型定义 */  
type TimeQueryParams = null | {
  /** 开始的时间 */
  timeStartFunction: { year: number; month: number; day: number }
  /** 结束的时间 */
  timeEndFunction: { year: number; month: number; day: number }
  /** 时间粒度 */
  timeGranularity: 'day' | 'month' | 'quarter' | 'year' | 'total' | null
}

## 请在回答之前一步步地思考：
1. 判断问题中是否包含时间词或者趋势，如果都没有直接返回 `null` 中止后续处理。时间词包括：年、去年、今年、今年1月、今年?月、前年、月、本月、3月、3月份、每日、每天、过去3天、季度、趋势等词语，其他词语请忽略。
2. 提取开始和结束时间：根据问题中的时间词，推断出开始时间和结束时间的具体值。如果问题中没有指定开始的时间，最大的时间跨度为10年前到今年，即最大的年份范围为{{current_year-10}}-{{current_year}}年。
3. 提取时间粒度。遵循以下原则：
  3.1 当问题明确指定按天、月、季度、年分组的时候，必须使用相对应的 `day`、`month`、`quarter`、`year`。比如 “每月”、“各月”、“不同月”、“不同月份” 时返回 `month`。“季度”、“不同季度”、“每个季度”或“各个季度”时返回`quarter`。“历年”、“各年”或“每年” 返回`year`。
  3.2 如果问题中没有明确提到时间粒度，并且包含“趋势”时，时间粒度不能为 `total`。先计算开始时间和结束时间的时间跨度，小于2个月时使用 `day`，小于24个月时使用 `month`，大于等于24个月时使用 `year`。
  3.3 “平均”并不会对时间粒度有影响，请忽略。
  3.4 当问题中包含“总和”时，返回 `total`，其他情况均默认返回 `total`。
4. 重新检查前面2步，并整理出标准的 JSON 格式返回。

今天是{{current_date_str}}。所有的相对时间，请你基于今天的时间来推算。

## 下面是一些常见的日期说明，请仔细学习：
* 3月末 = 3月最后一天：默认是今年，所以就是{{current_year}}年3月31日
* 今年 = 本年：{{current_year}}年1月1日到{{current_year}}年{{current_month}}月{{current_day}}日
* 本月 = 当月：{{current_year}}年{{current_month}}月1日到{{current_year}}年{{current_month}}月{{current_day}}日
* 近一年：开始时间和结束时间为{{one_year_ago_str}}到{{current_date_str}}
* 22年：指2022年，以此类推
* 23年以来、2023年以来：意思为“从2023年起”，是一些约定俗成的简写，并不是要查询最近23年的数据的意思
* 去年开始：开始时间和结束时间为{{last_year}}年1月1日到{{current_year}}年{{current_month}}月{{current_day}}日
* 去年：开始时间和结束时间为{{last_year}}年1月1日到{{last_year}}年12月31日
* 前年：开始时间和结束时间为{{current_year-2}}年1月1日到{{current_year-2}}年12月31日
* 近3年、过去3年、最近3年：开始时间和结束时间为{{current_year-3}}年{{current_month}}月{{current_day}}日到{{current_year}}年{{current_month}}月{{current_day}}日
* 近一日、昨天：开始时间和结束时间为{{yesterday}}到{{yesterday}}
* 最近30天：开始时间和结束时间为{{thirty_days_ago_str}}到{{current_date_str}}
* 最近六个月：开始时间和结束时间为{{six_months_ago_str}}到{{current_date_str}}

## 以下是几个样例供你参考：

Q: 销量最高的分行是哪个
Think: 
1. 问题中没有包含时间词或者趋势，直接返回 `null`。
A: 
```json
null
```

Q: 查询最近30天河北用户数的趋势
Think:
1. 问题中包含“最近30天”、“趋势”是时间词，需要提取时间
2. 根据“最近30天”推断出开始时间和结束时间为{{thirty_days_ago_str}}到{{current_date_str}}。
3. 问题中包含了“趋势”，时间粒度不能为 `total`。{{thirty_days_ago_str}}到{{current_date_str}}的时间跨度小于2个月，所以时间粒度为 `day`。
4. 重新检查并返回标准的 JSON 格式。
A:
```json
{
  "timeStartFunction": { "year": {{thirty_days_ago.year}}, "month": {{thirty_days_ago.month}}, "day": {{thirty_days_ago.day}} },
  "timeEndFunction": { "year": {{current_year}}, "month": {{current_month}}, "day": {{current_day}} },
  "timeGranularity": "day"
}
```
Q: 1月到4月，医疗行业的客户分别有多少个？
Think:
1. 问题中包含“1月到4月”、“各月”是时间词，需要提取时间。
2. 根据“1月到4月”，没有指定年，默认为今年，推断出开始时间和结束时间分别为{{current_year}}年1月1日到4月30日。
3. 问题中提及“1月到4月”这个时间范围，且包含了“分别”，所以时间粒度为 `month`。
4. 重新检查并返回标准的 JSON 格式。
A:
```json
{
  "timeStartFunction": { "year": 2024, "month": 1, "day": 1 },
  "timeEndFunction": { "year": 2024, "month": 4, "day": 30 },
  "timeGranularity": "month"
}
```




Q: 每月的人口增量
Think: 
1. 问题中包含“每月”是时间词，需要提取时间
2. 问题中没有指定开始或结束时间，所以默认跨度为过去10年。开始和结束时间为{{current_year-10}}年{{current_month}}月{{current_day}}日到{{current_year}}年{{current_month}}月{{current_day}}日。
3. 问题中明确提及了“每月“，即”每个月“、“各月”，必须按月进行统计，所以时间粒度为 `month`。
4. 重新检查并返回标准的 JSON 格式。
A:
```json
{
  "timeStartFunction": { "year": {{current_year-10}}, "month": {{current_month}}, "day": {{current_day}} },
  "timeEndFunction": { "year": {{current_year}}, "month": {{current_month}}, "day": {{current_day}} },
  "timeGranularity": "month"
}
```

Q: 查询每年的净毛利
Think: 
1. 问题中包含“每年”是时间词，需要提取时间
2. 问题中没有指定开始或结束时间，所以默认跨度为过去10年。开始和结束时间为{{current_year-10}}年{{current_month}}月{{current_day}}日到{{current_year}}年{{current_month}}月{{current_day}}日。
3. 问题中包含了“每年”，所以时间粒度为 `year`。
4. 重新检查并返回标准的 JSON 格式。
A:
```json
{
  "timeStartFunction": { "year": {{current_year-10}}, "month": {{current_month}}, "day": {{current_day}} },
  "timeEndFunction": { "year": {{current_year}}, "month": {{current_month}}, "day": {{current_day}} },
  "timeGranularity": "year"
}
```

Q: 济南分行和杭州分行2020年上半年平均信用卡消费笔数
Think: 
1. 问题中包含“2020年上半年”是时间词，需要提取时间
2. 根据“2020年上半年”推断出开始时间和结束时间为2020年1月1日到6月30日。
3. 问题中没有包含“每月”，“每年”，也没有包含“趋势”，“平均”并不会对时间粒度有影响，所以时间粒度为 `total`。
4. 重新检查并返回标准的 JSON 格式。
A:
```json
{
  "timeStartFunction": { "year": 2020, "month": 1, "day": 1 },
  "timeEndFunction": { "year": 2020, "month": 6, "day": 30 },
  "timeGranularity": "total"
}
```

Q: 北京分行6月份最后一天的收益
Think: 
1. 问题中包含“6月份最后一天”是时间词，需要提取时间
2. 根据“6月份最后一天”，没有指定年，默认为今年，推断出开始时间和结束时间分别为{{current_year}}年6月30日到年6月30日。
3. 问题中没有指定时间粒度，也没有要查看趋势，所以时间粒度为 `total`。
4. 重新检查并返回标准的 JSON 格式。
A: 
```json
{
  "timeStartFunction": { "year": 2024, "month": 6, "day": 30 },
  "timeEndFunction": { "year": 2024, "month": 6, "day": 30 },
  "timeGranularity": "total"
}
```

Q: 3月份的人均营业额
Think: 
1. 问题中包含“3月份”是时间词，需要提取时间
2. 根据“3月份”，没有指定年，默认为今年，推断出开始时间和结束时间为{{current_year}}年3月1日到3月31日。
3. 问题中没有指定时间粒度，也没有要查看趋势，所以时间粒度为 `total`。
4. 重新检查并返回标准的 JSON 格式。
A: 
```json
{
  "timeStartFunction": { "year": {{current_year}}, "month": 3, "day": 1 },
  "timeEndFunction": { "year": {{current_year}}, "month": 3, "day": 31 },
  "timeGranularity": "total"
}
```

Q: 本月每天的店均销售额
Think: 
1. 问题中包含“本月”、“每天”都是非常明确的时间词，需要提取时间
2. 根据“本月”，没有指定年，默认为今年，推断出开始时间和结束时间为{{current_year}}年{{current_month}}月1日到{{current_month}}月{{current_day}}日。
3. 问题中“每天”也就是“每一天”，所以时间粒度为 `day`。
4. 重新检查并返回标准的 JSON 格式。
A: 
```json
{
  "timeStartFunction": { "year": {{current_year-10}}, "month": {{current_month}}, "day": 1 },
  "timeEndFunction": { "year": {{current_year}}, "month": {{current_month}}, "day": {{current_day}} },
  "timeGranularity": "day"
}
```

Q: 今年以来，整体市场中标项目数量月增长趋势如何？
Think:
1. 问题总包含“今年”和“月增长趋势”，需要提取时间
2. 根据“今年以来”推断出开始时间和结束时间为{{current_year}}年1月1日到{{current_month}}月{{current_day}}日。
3. 问题中的“月增长趋势”明确了是按照月份去做分组然后计算中标项目数量，因此时间颗粒度为`month`
4. 重新检查并返回标准的 JSON 格式。
A: 
```json
{
  "timeStartFunction": { "year": {{current_year}}, "month": 1, "day": 1 },
  "timeEndFunction": { "year": {{current_year}}, "month": {{current_month}}, "day": {{current_day}} },
  "timeGranularity": "month"
}
```

Q: 统计不同季度的毛利率变化趋势
Think: 
1. 问题中包含的“季度”、“趋势”是时间词，需要提取时间
2. 问题中没有指定开始或结束时间，所以默认跨度为过去10年。开始和结束时间为{{current_year-10}}年{{current_month}}月{{current_day}}日到{{current_year}}年{{current_month}}月{{current_day}}日。
3. 问题中明确提及了“不同季度”即“每个季度”、“各个季度”，所以必须明确按照”季度“为粒度进行计算，所以时间粒度为 `quarter`。
4. 重新检查并返回标准的 JSON 格式。
A:
```json
{
  "timeStartFunction": { "year": {{current_year-10}}, "month": {{current_month}}, "day": {{current_day}} },
  "timeEndFunction": { "year": {{current_year}}, "month": {{current_month}}, "day": {{current_day}} },
  "timeGranularity": "quarter"
}
```

注意：
1. 禁止使用 `new Date().getFullYear()` 这类日期函数，请直接使用 number 数字，来确保我可以直接使用。
2. 请注意：“每天”、“每日”、“每季度”、“各季度”、“各月”、“每月”、“每年”、“每个季度”、“不同季度”等是非常明确的时间词。
3. 只有包含“趋势”的问题才使用时间跨度定义粒度，否则必须使用问题中提及的粒度。
3. 闰年指的是能被4整除的年份，像2016，2020，2024，2028等都是闰年，闰年的2月份有29天。
"""


def gen_nl2metric_time_query(json_prompts: dict):
    sub_json_prompts = {}

    sub_json_prompts[DEFAULT_PROMPT] = gen_basic_json(
        nl2time_query_prompt, prompt_suffix_default
    )
    sub_json_prompts[TIANHONG_PROJECT_NAME] = gen_basic_json(
        nl2time_query_prompt_tianhong, prompt_suffix_tianhong
    )
    sub_json_prompts[BAOWU_PROJECT_NAME] = gen_basic_json(
        nl2time_query_prompt_baowu, prompt_suffix_with_retrieved_metrics
    )
    sub_json_prompts[CHINA_LIFE_NAME] = gen_basic_json(
        nl2time_query_prompt_china_life, prompt_suffix_tianhong
    )
    # 零食有鸣
    sub_json_prompts[LINGSHI_NAME] = gen_basic_json(
        nl2time_query_prompt_lingshi, prompt_suffix_lingshi
    )
    sub_json_prompts[TAIKANG_PROJECT_NAME] = gen_basic_json(
        nl2time_query_prompt_taikang, prompt_suffix_with_retrieved_metrics
    )
    json_prompts[ParamsExtractStage.NL2METRIC_TIME_QUERY] = sub_json_prompts
