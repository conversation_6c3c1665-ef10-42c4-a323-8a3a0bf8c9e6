from common.prompt_selector.prompts.common import gen_basic_json
from common.types.base import ParamsExtractStage
from common.prompt_selector.utils import DEFAULT_PROMPT


judge_prompt = """
你是一个query和相关信息匹配系统，任务是根据我提供的不同信息，判断这些信息是否可以作为答案回答query涉及的问题。
我将会给你提供：
1. query: 用户提出的问题
2. file: 一个csv文件，并且我会以markdown的形式把前几行给你打印出来
3. retrieval信息: 根据问题，从本地知识库召回到的相似文本，该信息可以为空

[Hint]
{{judge_hint}}

注意：
1. 如果file显示工具执行失败或空，说明没有任何csv数据可用，无法调用python_code_tool，因为这个工具需要基于数据的输入来编写python代码。
2. 如果file和retrieval不同时为空，且他们的信息可以直接回答用户问题而不需要做进一步的分析或者计算，那么请直接调用early_stop工具来提前终止。
3. 如果file不为空，但我提供的信息依旧不足以回答用户的问题，请明确为了能够回答问题，需要做的下一步任务是什么？如果可以基于当前的数据做进一步分析就能回答用户问题，请调用chat工具。如果需要进一步通过代码计算，并请调用python_code_tool，传入你的新任务让其来编写python代码完成任务，进而得到新的答案来回答问题。
4. 对于chat工具，如果需要汇总多个信息源来回答用户问题（例如retrieval信息和file同时存在），那么需要思考能力，think为true。如果file或retrieval信息只有一个存在，说明不需要汇总多个信息源，那么think为false。
5. 你所有的思考过程请尽量简短，并请用<think>和</think>包起来，最后的工具调用需要放在</think>下面。
"""


judge_prompt_suffix = """
<tools>
{{tools}}
</tools>
<output-format>
对于每个函数调用，返回一个 json 对象，其中包含 <tool_call></tool_call> XML 标记内的函数名称和参数：
<tool_call>
{"name": <function-name>, "arguments": <args-json-object>}
</tool_call>
</output-format>

以下是我提供的信息：
[query]: {{query}}
[file]: {{bi_result}}
[retrieval]: {{other_result}}
"""


def gen_nl2agent_judge(json_prompts: dict):
    sub_json_prompts = {}
    sub_json_prompts[DEFAULT_PROMPT] = gen_basic_json(judge_prompt, judge_prompt_suffix)
    json_prompts[ParamsExtractStage.JUDGE_TOOL] = sub_json_prompts
