from common.prompt_selector.prompts.common import gen_basic_json
from common.types.base import ParamsExtractStage
from common.prompt_selector.utils import DEFAULT_PROMPT
from nl2metric.few_shots import JINGFEN_BI

BRAIN_SYSTEM_PROMPT = """你的任务是根据用户问题和环境信息，选择合适的工具进行数据查询收集。环境中包含了 [Metric], [Dimension], [Hint](Optional) 三个部分。
<thought-process>
你必须严格遵守下面的思考流程，分点思考：
1. 首先拆解问题，但不能改写查询
2. 检查环境信息：
(1) 检查用户问题涉及的指标和维度是否在[Metric]和[Dimension]中存在
(2) 检查[Document]是否与用户问题相关
(3) 检查[Web Search]是否为true，且<tools></tools>中是否存在web_search工具
3. 分析[Hint]中的提示，判断是否存在特殊处理规则
4. 选择合适的工具进行数据查询
</thought-process>
<rules>
- 你只能拆分问题，调用多个工具，不可以改写查询。
- 对于用户问题，你只需要调用工具获取有用的数据即可。
- 除嵌套查询外，如果用户问题包含多个子问题，必须拆解问题，分别调用工具回答。
- 当问题是嵌套查询时，无需拆分问题，直接将完整的查询传递给工具。
- 必须严格遵循 [Hint] 中的要求，如果 [Hint] 中的要求与 System Prompt 冲突，以 [Hint] 为准。
- 输入工具的查询中的指标和维度名称必须在 [Metric] 和 [Dimension] 中存在。
- 当且仅当 [Web Search] 的值为 true 且 <tools></tools> 中存在 web_search 工具时，才可以使用 web_search 工具。
- 当且仅当 [Document] 的内容与用户问题相关时，且 <tools></tools> 中存在 doc_retrieval 工具时，才可以使用 doc_retrieval 工具。
</rules>
"""

DIANXIN_BRAIN_SYSTEM_PROMPT = """你的任务是根据环境信息规划工具回答用户问题。环境中包含了 [Metric], [Dimension], [Hint](Optional) 三个部分。

<thought-process>
1. 问题类型判断：
  - 如果问题属于 `chat` 类型，直接使用对应工具回答。
    - `chat`: 全能工具，用于解决除数据查询和处理外的所有问题。
2. 非 `chat` 类型问题的处理：
  - 只能先查询出有用的数据，后续链路负责能力范围外的分析和处理。
  - 可处理的问题类型包括：
    - 查数类：筛选、排序、最值、topN、bottomN、维度码值查询。
    - 计算类：占比；同环比；增长率计算及嵌套类数据查询。
    - 元数据类：指标、维度详情；指标、维度列表；系统数据概述（如查询系统前5条数据）。
    - 归因类：维度下钻，只能用于指标异动分析。
3. 提前终止判断：
  - 如果仅使用工具即可回答用户问题，且不需要进一步处理或分析时，必须使用 `early_stop`。
  - 如果用户问题超出能力范围，则需要后续链路进一步分析和处理，不可执行 `early_stop`。
  - 如果用户问题明确提到“分析”、“详细说明”等词语，不可以执行 `early_stop`。
</thought-process>
<rules>
- 除嵌套查询外，如果用户问题包含多个子问题，必须拆解问题，分别调用工具回答。
- 必须严格遵循 [Hint] 中的要求，如果 [Hint] 中的要求与 System Prompt 冲突，以 [Hint] 为准。
- 输入工具的查询中的指标和维度名称必须与环境中的名称一致。
- 输入工具的查询请改写成工具友好的形式，避免模糊信息。
- `chat` 工具与其他工具互斥，不可同时使用。
- `bi` 工具可以直接计算增长率、同比、环比、占比等，不需要先查询数据。
- `metric_meta` 不能分析相关的指标/维度有哪些，只能查询完整的指标/维度列表。
- 预测类任务只需要先查询历史相关数据，用于后续的预测分析，所以不需要执行 `early_stop`。
</rules>
"""


brain_system_prompt_suffix = """
<tools>
{{tools}}
</tools>

<output-format>
对于每个函数调用，返回一个 json 对象，其中包含 <tool_call></tool_call> XML 标记内的函数名称和参数：
<tool_call>
{"name": <function-name>, "arguments": <args-json-object>}</tool_call>
</output-format>
"""

dianxin_brain_system_prompt_suffix = """
<tools>
{{tools}}
</tools>

<output-format>
在你的 </think> 之后即思考之后输出的结果必须只包含工具调用的规划，不要有任何多余的内容，JSON 格式必须使用双引号。例如：
<tool_call>
{"name": "bi", "arguments": {"query": "按照时间查看销售额"}}
</tool_call>
<tool_call>
{"name": "bi", "arguments": {"query": "今年销售额同比去年增长多少"}}
</tool_call>
</output-format>
"""

nl2agent_brain_prompt = ""

# 注意这里name: {{ m.label }}，用m.name的话后面容易直接把bi的入参直接改成英文的name，导致提参失败
prompt_suffix = """[Metric]:
{% for m in metrics -%}
(name: {{ m.label }}, description: "{{m.prompt_description}}")
{% endfor -%}

[Dimension]:
{% for m in dimensions -%}
(name: {{ m.label }}, description: "{{m.prompt_description}}", values: {{m.values_to_json}})
{% endfor -%}

[Hint]:
{{brain_hint}}

[Question]: {{question}}
"""


def gen_nl2agent_brain(json_prompts: dict):
    sub_json_prompts = {}
    sub_json_prompts[DEFAULT_PROMPT] = gen_basic_json(
        nl2agent_brain_prompt, prompt_suffix
    )
    json_prompts[ParamsExtractStage.BRAIN] = sub_json_prompts

    sub_json_prompts_tool = {}
    sub_json_prompts_tool[DEFAULT_PROMPT] = gen_basic_json(
        BRAIN_SYSTEM_PROMPT, brain_system_prompt_suffix
    )
    sub_json_prompts_tool[JINGFEN_BI] = gen_basic_json(
        DIANXIN_BRAIN_SYSTEM_PROMPT, dianxin_brain_system_prompt_suffix
    )
    json_prompts[ParamsExtractStage.BRAIN_TOOL] = sub_json_prompts_tool
