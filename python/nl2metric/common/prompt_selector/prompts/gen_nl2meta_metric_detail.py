from common.prompt_selector.prompts.common import gen_basic_json
from common.types.base import ParamsExtractStage
from common.prompt_selector.utils import DEFAULT_PROMPT

nl2meta_metric_detail_prompt = """
你的任务是根据用户问题和指标列表提取问题对应的指标参数
<output-format>
{"metric_param":"METRIC_NAME"}
</output-format>
<rules>
- 你的输出必须是一个json对象，不要包含任何其他多余内容
- 指标参数的值必须是指标列表中一个元素的name值
</rules>
下面是用户问题和指标列表，请根据这些信息提取指标参数：
[Question]: {{question}}
[Metric]:
{% for m in metric_list -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}")
{% endfor -%}
"""


def gen_nl2meta_metric_detail_prompt(json_prompts: dict):
    sub_json_prompts = {}

    sub_json_prompts[DEFAULT_PROMPT] = gen_basic_json(nl2meta_metric_detail_prompt, "")

    json_prompts[ParamsExtractStage.NL2META_METRIC_DETAILS] = sub_json_prompts
