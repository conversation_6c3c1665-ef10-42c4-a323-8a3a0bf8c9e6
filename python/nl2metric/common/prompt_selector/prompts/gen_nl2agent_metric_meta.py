from common.prompt_selector.prompts.common import gen_basic_json
from common.types.base import ParamsExtractStage
from common.prompt_selector.utils import DEFAULT_PROMPT

nl2agent_metric_meta_prompt = """请根据query、metrics、dimensions，输出他的意图intent，意图只包含5种：维度列表、指标列表、数据概述、维度详情、指标详情，其中维度详情和指标详情要多输出一个name值，值为涉及维度或指标的英文名称
"""

prompt_suffix = """[Query]: {query}
[Metric]:
{% for m in metrics -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}")
{% endfor -%}
[Dimension]:
{% for m in dimensions -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}", values: {{m.values_to_json}})
{% endfor -%}
"""


def gen_nl2agent_metric_meta(json_prompts: dict):
    sub_json_prompts = {}
    sub_json_prompts[DEFAULT_PROMPT] = gen_basic_json(
        nl2agent_metric_meta_prompt, prompt_suffix
    )
    json_prompts[ParamsExtractStage.METRIC_META] = sub_json_prompts
