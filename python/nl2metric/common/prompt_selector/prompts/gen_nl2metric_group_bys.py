from common.prompt_selector.prompts.common import gen_fewshot_json, gen_basic_json
from common.prompt_selector.utils import DEFAULT_PROMPT
from common.types.base import ParamsExtractStage
from metastore.base import Metric, Dimension, DimensionValue
from nl2metric.example_selector import SimpleFilterExampleSelector
from nl2metric.few_shots import (
    BAOWU_PROJECT_NAME,
    FEW_SHOT_PROJECT_NAME_KEY,
    JINGFEN_BI,
    CHINA_LIFE_NAME,
)

PROMPT_PREFIX_BAOWU = """
我希望你扮演专业的数据分析师,你精通SQL领域知识,并能将其运用到从用户问题中提取参数。
你的目标是：根据问题的描述,提取出相关的groupBys维度及其groupByLevel层级, 并按照指定 JSON 格式返回,格式类似:
```json
{"groupBysWithLevel": [{"groupBys": "dimension", "groupByLevel": -1}]}
```

我会提供候选的维度([Dimension])信息：
1. [Dimension]维度列表的格式为(name: #维度英文字段, description: #维度中文含义，一个维度可能对应多个中文含义，用;分隔)

"groupBys"数组中的字段对应SQL中的GROUP BY子句,以下两种情况需要将维度加入groupBys:
1. 需要按照维度分组的场景:
- 问题中包含分组关键词:"每个"、"各个"、"按照"、"根据"、"以"、"按"、"分别"等
- 问题需要对维度进行分组统计
2. 维度涉及指标操作的场景:
- 需要对维度下的指标进行排序
- 需要对该维度下的码值进行筛选，展示满足指标过滤条件的码值
- 需要对该维度下的码值进行筛选，统计有多少满足指标过滤条件的码值，展示数量
- 维度需要出现在SELECT子句中
3. groupByLevel: 维度的层级,规则如下:
- 如果问题中明确指出维度的层级(如"一级"、"二级"、"三级"等),则使用指定的层级数字
- 如果问题中没有明确指出层级,则默认为-1
- 层级数字对应关系: 一级=1, 二级=2, 三级=3, 以此类推

你的处理步骤为：
1. 识别[Question]中的时间信息，并忽略掉。
2. 分析[Question]，[Metric]和[Dimension]，识别以下情况：
- 是否需要按照某个维度分组查询数据
- 是否需要查询某个维度的指标，并且对维度按照指标进行排序
- 是否需要查询某个维度，并且只展示满足问题中提出的指标过滤条件的码值
- 是否需要查询某个维度，并且对满足问题中提出的指标过滤条件的码值进行计数
3. 如果出现上述任一情况，且维度名出现在[Dimension]中，将相关维度放入到groupBys中；否则，请忽略
4. 判断[Question]中维度的层级，放入到groupByLevel中
5. 如果问题中没有提到任何[Dimension]，或者只是单纯查询指标值，将结果置为空

特别注意：
1. 对于(name: COMPANY_INNER_CODE_DES, description: 企业;公司))这个维度，查询某公司时，不需要groupby；查询某公司的子公司时，需要groupby
2. 维度仅作为WHERE条件时不需要加入groupby
3. 时间维度不需要加入groupby
4. 指标过滤条件指对问题中指标需要满足的过滤条件，通常为 指标>0 或者 指标<0 等
5. 判断groupby需要忽略指标名中可能包含的维度名带来的影响。同时理解指标和维度，如果需要提取的维度，来自于指标名，和指标名重叠，此时请判断不需要groupby
"""

EXAMPLE_TPL_DEFAULT = """
[Question]: {{question}}

[Dimension]: 
{% for m in dimensions -%}
(name: {{ m.name }}, description: {{m.prompt_description}})
{% endfor -%}
[Think]: 
{{think}}
[Result]:
```json
{{result|pretty_to_json}}
```
"""

PROMPT_SUFFIX_DEFAULT = """
下面是用户的问题,请给出你的[Think]和[Result](JSON 提参结果)。注意:请以json格式给出提参结果,不要附带任何其他信息 
[Question]: {{question}}
[Dimension]:
{% for m in dimensions -%}
(name: {{ m.name }}, description: {{m.prompt_description}}), values: {{m.values_to_json}})
{% endfor -%}
[Think]:
"""

PROMPT_SUFFIX_BAOWU = """
下面是用户的问题，请根据信息给出你的[Think]和[Result]。注意:请以json格式给出提参结果。
[Question]: {{question}}
[Hint]
{{groupby_hint}}
[Metric]:
{% for m in metrics -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}")
{% endfor -%}
[Dimension]:
{% for m in dimensions -%}
(name: {{ m.name }}, description: {{m.prompt_description}}))
{% endfor -%}
[Result]:
输出你的[Think]和[Result]
"""

JINGFEN_EXAMPLES = [
    {
        "question": "查询机构赎回大于个人赎回的产品名称？",
        "metrics": [
            Metric(name="gr_sell_today_SUM", label="个人赎回金额(万)"),
            Metric(name="jg_sell_today_SUM", label="机构赎回金额"),
            Metric(name="redemption_SUM", label="当日赎回金额(万)"),
            Metric(name="subscription_SUM", label="当日申购金额(万)"),
        ],
        "dimensions": [
            Dimension(
                name="c_pfundabbr",
                label="产品名称",
                values=DimensionValue.from_list(["优利短债", "增利短债", "安利短债", "招利短债"]),
            ),
            Dimension(
                name="c_vog_region_name",
                label="区域名称",
                values=DimensionValue.from_list(["辽宁", "西藏", "海南", "广西", "河北"]),
            ),
        ],
        "think": """让我们来一步一步思考:
1. 定位[Question]中的时间信息并忽略: 问题中没有识别到时间信息。
2. 识别[Question]中的实体链接: "机构赎回"对应[Metric]中的"jg_sell_today_SUM"，"个人赎回"对应[Metric]中的"gr_sell_today_SUM"，说明他们都是指标。"产品名称"对应[Dimension]中的"c_pfundabbr"，说明它是一个维度。
3. 分析问题，确定SELECT语句的构成: 问题中的"查询机构赎回大于个人赎回的产品名称？"说明要用"jg_sell_today_SUM > gr_sell_today_SUM"来筛选符合条件的数据，然后需要输出"c_pfundabbr"，因此SELECT语句应该是"SELECT c_pfundabbr"。
4. 拿出SELECT语句中来自[Dimension]的*所有*字段: "SELECT c_pfundabbr"中来自[Dimension]的字段只有"c_pfundabbr"，把它放入groupBys数组中。""".strip(),
        "result": {"groupBys": ["c_pfundabbr"], "notExistGroupBys": []},
    },
    {
        "question": "2024年移动、联通、电信中标金额月增长趋势分别如何？",
        "metrics": [
            Metric(name="winning_amount_SUM", label="中标金额;中标金额总额"),
            Metric(name="MARKET_SHARE", label="中标份额;中标占比;市场份额;市场份额占比"),
            Metric(name="bidding_entity_COUNT", label="招标单位数量"),
        ],
        "dimensions": [
            Dimension(
                name="operator_label",
                label="运营商标签",
                values=DimensionValue.from_list(["电信", "移动", "联通"]),
            ),
            Dimension(
                name="bidding_province",
                label="招标省份;省份",
                values=DimensionValue.from_list([]),
            ),
            Dimension(
                name="forecast_customer_label",
                label="客户标签",
                values=DimensionValue.from_list(["部门", "广电部门", "城管部门", "主管部门", "疾控部门"]),
            ),
            Dimension(
                name="winning_month",
                label="中标时间",
                values=DimensionValue.from_list([]),
            ),
        ],
        "think": """让我们来一步一步思考:
1. 定位[Question]中的时间信息并忽略: 问题中的"2024年"和"月"是时间信息。忽略。
2. 识别[Question]中的实体链接: "中标金额"对应[Metric]中的"winning_amount_SUM"，是一个指标。"移动"、"联通"、"电信"存在于[Dimension]中"operator_label"的values中，说明它们是过滤条件。
3. 分析问题，确定SELECT语句的构成: "移动、联通、电信中标金额月增长趋势分别如何？"中包含关键词"分别"，说明要分别查看"移动"、"联通"和"电信"的中标金额，需要对"operator_label"进行分组（意味着需要把这个维度加入到SELECT语句中）。因此SELECT语句为"SELECT operator_label, winning_amount_SUM"，这样才会返回"移动"、"联通"、"电信"三个码值各自的中标金额趋势。
4. 拿出SELECT语句中来自[Dimension]的*所有*字段: "SELECT operator_label, winning_amount_SUM"中来自[Dimension]的字段只有"operator_label"。
""".strip(),
        "result": {"groupBys": ["operator_label"], "notExistGroupBys": []},
    },
    {
        "question": "联通招标单位数量在2024年以来的变化趋势？",
        "metrics": [
            Metric(name="winning_amount_SUM", label="中标金额;中标金额总额"),
            Metric(name="MARKET_SHARE", label="中标份额;中标占比;市场份额;市场份额占比"),
            Metric(name="bidding_entity_COUNT", label="招标单位数量"),
        ],
        "dimensions": [
            Dimension(
                name="operator_label",
                label="运营商标签",
                values=DimensionValue.from_list(["电信" "联通"]),
            ),
            Dimension(
                name="bidding_province",
                label="招标省份;省份",
                values=DimensionValue.from_list([]),
            ),
            Dimension(
                name="forecast_customer_label",
                label="客户标签",
                values=DimensionValue.from_list(["部门", "广电部门", "城管部门", "主管部门", "疾控部门"]),
            ),
            Dimension(
                name="winning_month",
                label="中标时间",
                values=DimensionValue.from_list([]),
            ),
        ],
        "think": """让我们来一步一步思考:
1. 定位[Question]中的时间信息并忽略: 问题中的"2024年"是时间信息。忽略。
2. 识别[Question]中的实体链接: "中标金额"对应[Metric]中的"winning_amount_SUM"，是一个指标。"联通"存在于[Dimension]中"operator_label"的values中，说明它们是过滤条件。
3. 分析问题，确定SELECT语句的构成: 问题中的"联通招标单位数量在2024年以来的变化趋势？"说明要用"operator_label"来筛选"联通"的数据，然后输出winning_amount_SUM的数值即可。因此SELECT语句为"SELECT winning_amount_SUM"
4. 拿出SELECT语句中来自[Dimension]的*所有*字段: "SELECT winning_amount_SUM"中没有来自[Dimension]的字段，因此groupby为空
""".strip(),
        "result": {"groupBys": [], "notExistGroupBys": []},
    },
    {
        "question": "今年各行业招标项目数量都是多少？",
        "metrics": [
            Metric(name="winning_amount_SUM", label="中标金额;中标金额总额"),
            Metric(name="dianxin_zhaobiao_model_0820_Project_COUNT", label="项目数量"),
            Metric(name="bidding_entity_COUNT", label="招标单位数量"),
        ],
        "dimensions": [
            Dimension(
                name="operator_label",
                label="运营商标签",
                values=DimensionValue.from_list([]),
            ),
            Dimension(
                name="bidding_province",
                label="招标省份;省份",
                values=DimensionValue.from_list([]),
            ),
            Dimension(
                name="Customer_Primary_Industry",
                label="客户一级行业",
                values=DimensionValue.from_list(
                    ["应急行业", "建筑行业", "政务行业", "教育行业", "金融行业"]
                ),
            ),
            Dimension(
                name="Customer_Tertiary_Industry",
                label="客户三级行业",
                values=DimensionValue.from_list(["IT行业"]),
            ),
            Dimension(
                name="Customer_Control_Department",
                label="客户管控部门;行业",
                values=DimensionValue.from_list([]),
            ),
        ],
        "think": """让我们来一步一步思考:
1. 定位[Question]中的时间信息并忽略: 问题中的"今年"是时间信息，忽略。
2. 识别[Question]中的实体链接: "招标项目数量"对应[Metric]中的"dianxin_zhaobiao_model_0820_Project_COUNT"，是一个指标。"行业"是[Dimension]中的维度"Customer_Control_Department"，说明它是一个维度而不是码值。注意，由于问题不是问的一级行业或者二级行业，所以不选择"Customer_Primary_Industry"或者"Customer_Tertiary_Industry"。
3. 分析问题，确定SELECT语句的构成: 问题中的"今年各行业招标项目数量都是多少？"说明要对"Customer_Control_Department"维度进行分组计算"dianxin_zhaobiao_model_0820_Project_COUNT"。因此SELECT语句为"SELECT Customer_Control_Department, dianxin_zhaobiao_model_0820_Project_COUNT"。
4. 拿出SELECT语句中来自[Dimension]的*所有*字段: 把上述SQL中的"Customer_Control_Department"放入到groupBys数组中。
""".strip(),
        "result": {"groupBys": ["Customer_Control_Department"], "notExistGroupBys": []},
    },
]


CHINA_LIFE_EXAMPLES = [
    {
        "question": "全年每个季度规模保费的变化趋势",
        "metrics": [
            Metric(name="VT_CHINA_LIFE_0918_SUM_STAND_PREM", label="标准保费"),
            Metric(name="VT_CHINA_LIFE_0918_SUM_PREM", label="规模保费;销售;销量"),
        ],
        "dimensions": [
            Dimension(
                name="SALE_CHNL_NAME",
                label="销售渠道名称",
                values=DimensionValue.from_list(["个人保险"]),
            ),
            Dimension(
                name="PROVINCE_NAME",
                label="分公司名称",
                values=DimensionValue.from_list([""]),
            ),
        ],
        "think": """让我们来一步一步思考:
1. 定位[Question]中的时间信息并忽略: "全年"、"每个季度"都是时间信息，groupBys提取时不考虑。
2. 识别[Question]中的实体链接: "规模保费"对应[Metric]中的"VT_CHINA_LIFE_0918_SUM_PREM"，是一个指标。
3. 分析问题，确定SELECT语句的构成: "规模保费的变化趋势"说明要计算出"规模保费"的趋势，因此结合步骤1的结果，SELECT语句应该是"SELECT VT_CHINA_LIFE_0918_SUM_PREM"。
4. 拿出SELECT语句中来自[Dimension]的*所有*字段: "SELECT VT_CHINA_LIFE_0918_SUM_PREM"中没有来自[Dimension]的字段，groupBys数组为空。
""".strip(),
        "result": {"groupBys": [""], "notExistGroupBys": []},
    }
]


for example in JINGFEN_EXAMPLES:
    example[SimpleFilterExampleSelector.METADATA_LABELS_KEY] = {
        FEW_SHOT_PROJECT_NAME_KEY: JINGFEN_BI
    }

for example in CHINA_LIFE_EXAMPLES:
    example[SimpleFilterExampleSelector.METADATA_LABELS_KEY] = {
        FEW_SHOT_PROJECT_NAME_KEY: CHINA_LIFE_NAME
    }

examples = JINGFEN_EXAMPLES + CHINA_LIFE_EXAMPLES

nl2metric_gourpby_prompt = """
你的任务是根据用户问题和维度列表提取问题对应的分组条件
<output-format>
{"groupBys":["DIM_NAME"]}
</output-format>
[Hint]
{{groupby_hint}}
<rules>
- 你的输出必须是一个json对象，不要包含任何其他多余内容
- 分组条件的值必须存在于维度列表中
- 当问题没有涉及分组时，直接输出空列表，即 {"groupBys":[]}
- 当问题涉及多个分组时，可以使用逗号连接，格式为{"groupBys":["DIM_NAME1","DIM_NAME2"]}
</rules>
下面是用户问题和环境，请根据这些信息提取分组条件：
[Question]: {{question}}
[Metric]:
{% for m in metrics -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}")
{% endfor -%}
[Dimension]:
{% for m in dimensions -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}", values: {{m.values_to_json}})
{% endfor -%}
"""


def gen_nl2metric_group_bys(json_prompts: dict):
    sub_json_prompts = {}

    sub_json_prompts[DEFAULT_PROMPT] = gen_basic_json(nl2metric_gourpby_prompt, "")

    sub_json_prompts[BAOWU_PROJECT_NAME] = gen_fewshot_json(
        prompt_prefix=PROMPT_PREFIX_BAOWU,
        prompt_suffix=PROMPT_SUFFIX_BAOWU,
        example_tpl=EXAMPLE_TPL_DEFAULT,
    )

    json_prompts[ParamsExtractStage.NL2METRIC_GROUP_BYS] = sub_json_prompts
