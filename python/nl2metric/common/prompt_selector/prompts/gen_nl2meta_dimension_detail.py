from common.prompt_selector.prompts.common import gen_basic_json
from common.types.base import ParamsExtractStage
from common.prompt_selector.utils import DEFAULT_PROMPT

nl2meta_dimension_detail_prompt = """
你的任务是根据用户问题和维度列表提取问题对应的维度参数
<output-format>
{"dimension_param":"DIM_NAME"}
</output-format>
<rules>
- 你的输出必须是一个json对象，不要包含任何其他多余内容
- 维度参数的值必须是维度列表中一个元素的name值
</rules>
下面是用户问题和维度列表，请根据这些信息提取维度参数：
[Question]: {{question}}
[Dimension]:
{% for m in dimension_list -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}", values: {{m.values_to_json}})
{% endfor -%}
"""


def gen_nl2meta_dimension_detail_prompt(json_prompts: dict):
    sub_json_prompts = {}

    sub_json_prompts[DEFAULT_PROMPT] = gen_basic_json(
        nl2meta_dimension_detail_prompt, ""
    )

    json_prompts[ParamsExtractStage.NL2META_DIMENSION_DETAILS] = sub_json_prompts
