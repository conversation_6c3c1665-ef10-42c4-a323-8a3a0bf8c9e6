from common.prompt_selector.prompts.common import gen_basic_json
from common.types.base import ParamsExtractStage
from common.prompt_selector.utils import DEFAULT_PROMPT
from nl2metric.few_shots import (
    CHINA_LIFE_NAME,
    JINGFEN_BI,
    BANK_PROJECT,
    DEFAULT_PROJECT_NAME,
)

prompt_suffix = """
你的任务是根据用户问题和环境时间提取问题对应的时间维度
<output-format>
{"time_dimension":["DIM_NAME"]}
</output-format
<rules>
- 你的输出必须是一个json对象，不要包含任何其他多余内容
- 时间维度的值必须存在于环境中
- 当问题没有涉及时间或时间维度不存在时，直接输出空列表，即 {"time_dimension":[]}
- 当问题涉及多个时间维度时，可以使用逗号连接，格式为{"time_dimension":["DIM_NAME1","DIM_NAME2"]}
</rules>
下面是用户问题和环境时间，请根据这些信息提取时间维度：
[Question]: {{question}}
[Dimension]:
{% for m in dimensions -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}")
{% endfor -%}
[Think]:
[Result]:
"""

nl2time_dimension_glm_prompt = """根据问题的描述，提取出符合MySQL语法的以下参数，你当前的任务是提取time dims时间维度，把条件相关的都提取到time dims时间维度中
以下为用户的问题：
[Dimension]:
{% for m in dimensions -%}
(name: {{ m.name }}, description: "{{m.prompt_description}}")
{% endfor -%}
[Question]: {{question}}
"""


def gen_nl2time_dimension(json_prompts: dict):
    sub_json_prompts = {}

    sub_json_prompts[DEFAULT_PROMPT] = gen_basic_json(prompt_suffix, "")

    sub_json_prompts[CHINA_LIFE_NAME] = gen_basic_json(nl2time_dimension_glm_prompt, "")

    sub_json_prompts[JINGFEN_BI] = gen_basic_json(nl2time_dimension_glm_prompt, "")

    sub_json_prompts[BANK_PROJECT] = gen_basic_json(nl2time_dimension_glm_prompt, "")

    sub_json_prompts[DEFAULT_PROJECT_NAME] = gen_basic_json(prompt_suffix, "")

    json_prompts[ParamsExtractStage.NL2TIME_DIMENSION] = sub_json_prompts
