from common.prompt_selector.prompts.common import gen_basic_json
from common.types.base import ParamsExtractStage
from common.prompt_selector.utils import DEFAULT_PROMPT
from nl2metric.few_shots import (
    CHINA_LIFE_NAME,
    JINGFEN_BI,
    BANK_PROJECT,
    DEFAULT_PROJECT_NAME,
)


nl2attr_time_prompt = """你的任务是根据用户问题和环境时间提取问题对应的归因时间
<output-format>
{"baseTime":BASE_TIME,"compareTime":COMPARE_TIME}
</output-format>
<rules>
- 你的输出必须是一个json对象，不要包含任何其他多余内容
- 当问题没有涉及时间时，直接输出空，即 {}
- 当问题没有明确两个对比时间时，输出格式中之需要包含"compareTime"，即 {"compareTime":COMPARE_TIME}
- 当问题明确了两个对比时间时，输出格式中需要同时包含"basTime"和"compareTime"，即 {"baseTime":BASE_TIME,"compareTime":COMPARE_TIME}
- 当问题涉及“近两年”、“前两年”之类的时间范围时，你需要根据当前时间提取出"basTime"和"compareTime"
- BASE_TIME和COMPARE_TIME都是一个json格式，字段定义如下
  - "type": 时间粒度，可选值包括"specificYear", "specificQuarter", "specificMonth", "specificDate"
  - "year": 年份
  - "quarter": 季度，只有在"quarter"粒度下才有值
  - "month": 月份，只有在"month"和"date"粒度下才有值
  - "day": 日期，只有在"date"粒度下才有值
</rules>
下面是用户问题和环境时间，请根据这些信息提取时间范围：
[Question]: {{question}}
[Current Time]: {{current_date_str}}
"""


def gen_attr_analysis_time(json_prompts: dict):
    sub_json_prompts = {}

    sub_json_prompts[DEFAULT_PROMPT] = gen_basic_json(nl2attr_time_prompt, "")
    json_prompts[ParamsExtractStage.ATTR_ANALYSIS_TIME] = sub_json_prompts
