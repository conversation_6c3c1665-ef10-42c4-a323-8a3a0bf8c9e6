import json

from enum import Enum
from pydantic import BaseModel
from langchain_core.language_models.llms import LLM
from typing import Mapping, List, Any

FAKE_PASSTHROUGH_PROMPTS = "fake_passthrough_prompts"
FAKE_PASSTHROUGH_PROMPTS_MODE = "fake_passthrough_prompts_mode"


class FakePassthroughPromptsMode(Enum):
    single = "single"
    list = "list"
    by_question = "by_question"


class CircularArray(BaseModel):
    arr: List[Any]
    index: int = 0

    def get(self):
        if not self.arr:
            raise RuntimeError(
                f"fake_passthrough_prompts list does not contain any prompt"
            )
        result = self.arr[self.index]
        self.index = (self.index + 1) % len(self.arr)
        return result


def get_fake_passthrough_prompt(fake_prompts, mode, stage, question):
    if not fake_prompts:
        raise RuntimeError("model_type fake_passthrough needs fake_passthrough_prompts")
    if not isinstance(stage, str):
        stage = stage.value

    if not mode:
        mode = FakePassthroughPromptsMode.single
    else:
        mode = FakePassthroughPromptsMode(mode)

    if mode == FakePassthroughPromptsMode.single:
        prompt = fake_prompts[stage]
    elif mode == FakePassthroughPromptsMode.list:
        prompt_list = fake_prompts[stage]
        if isinstance(prompt_list, CircularArray):
            prompt = prompt_list.get()
        else:
            circular_prompt_list = CircularArray(arr=prompt_list)
            prompt = circular_prompt_list.get()
            fake_prompts[stage] = circular_prompt_list
    else:
        prompt = fake_prompts[question][stage]

    if not isinstance(prompt, str):
        return json.dumps(prompt, indent=2, ensure_ascii=False)
    else:
        return prompt


class FakePassthroughLLM(LLM):
    @property
    def _llm_type(self) -> str:
        """Return type of llm."""
        return "fake_passthrough_llm"

    def _call(
        self,
        prompt: str,
        *args: Any,
        **kwargs: Any,
    ) -> str:
        return prompt

    async def _acall(
        self,
        prompt: str,
        *args: Any,
        **kwargs: Any,
    ) -> str:
        return prompt

    @property
    def _identifying_params(self) -> Mapping[str, Any]:
        return {"model_name": "fake_passthrough_llm"}
