from typing import List
from common.logging.logger import get_logger
from common.types.base import CHAIN_META, ChainMeta, ChainRuntime, ParamsExtractStage
from common.llm.general import create_chat_model
from common.prompt_selector.prompt_selector_base import PromptSelectorBase
from langchain_core.runnables import (
    Runnable,
    RunnableLambda,
    RunnableConfig,
)
from langchain_core.messages import ChatMessage
from langchain_core.runnables.utils import Input, Output
from langchain_core.output_parsers import StrOutputParser

logger = get_logger(__name__)


def chitchat_preprocess(input):
    if isinstance(input, dict):
        input = input["question"]
    return [ChatMessage(role="user", content=input)]


def chitchat(model_type: str) -> Runnable[Input, Output]:
    chain = (
        RunnableLambda(
            chitchat_preprocess,
            name="chitchat_preprocess",
        )
        | create_chat_model(model_type)
        | StrOutputParser()
    )
    chain.name = "chitchat"
    return chain
