import functools

from llama_index.core.llms import LLM

from common.llm.general import GeneralLLM
from common.logging.logger import get_logger

logger = get_logger(__name__)


@functools.lru_cache(maxsize=20)
def get_llm(model_type, temperature=0, repetition_penalty=1.0) -> LLM:
    return GeneralLLM(
        model_type=model_type,
        temperature=temperature,
        repetition_penalty=repetition_penalty,
    )
