import json
from typing import Any, <PERSON><PERSON>

from common.logging.logger import get_logger

logger = get_logger(__name__)


def parse_json_from_response(response: str) -> Tuple[str, Any]:
    try:
        split_str = "```json"
        i = response.rindex(split_str)
        end = response.rindex("```")
        if end <= i:
            raise Exception(f"Parse json from response failed: {response}")
        cot = response[:i]
        json_result = response[i + len(split_str) : end]
        return cot, json.loads(json_result)
    except Exception as e:
        logger.exception(f"response invalid format: %s", response)
        raise e
