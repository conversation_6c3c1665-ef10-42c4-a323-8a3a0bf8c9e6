import functools
from typing import Any, Optional, List, Sequence

import requests
from common.llm.fake_passthrough_llm import FakePassthroughLLM
from langchain_community.chat_models import ChatZhipuAI, ChatBaichuan
from langchain_community.llms.baidu_qianfan_endpoint import <PERSON><PERSON><PERSON><PERSON>MEndpoint
from langchain_core.language_models.fake_chat_models import FakeChatModel
from langchain_core.messages import ChatMessage
from langchain_core.output_parsers import Str<PERSON>utputParser
from langchain_core.runnables import RunnableConfig
from langchain_openai import AzureChatOpenAI, ChatOpenAI
from llama_index.core import BasePromptTemplate
from llama_index.core.base.llms.generic_utils import (
    completion_response_to_chat_response,
)
from llama_index.core.base.llms.types import ChatMessage as llama_ChatMessage
from llama_index.core.base.llms.types import (
    ChatResponseAsyncGen,
    ChatResponse,
    MessageRole,
)
from llama_index.core.callbacks import CallbackManager
from llama_index.core.llms import (
    CustomLLM,
    CompletionResponse,
    CompletionResponseGen,
    LLMMetadata,
)
from llama_index.core.llms.llm import astream_chat_response_to_tokens
from llama_index.core.types import TokenAsyncGen
from pydantic import Field, PrivateAttr, BaseModel

from common.llm.poe_api import BotName, get_poe_client
from common.logging.logger import get_logger
from common.types.base import CHAIN_META, ChainMeta, ParamsExtractStage
from common.utils.llm_utils import create_llm_model_by_project_config
from config import app_config
from config.app_config import (
    AZURE_API_KEY,
    AZURE_API_TYPE,
    AZURE_API_VERSION,
    AZURE_DEPLOYMENT_NAME,
    AZURE_ENDPOINT,
    GPT_4_API_KEY,
    GPT_35_API_KEY,
    MODEL_TYPE_AZURE_GPT,
    MODEL_TYPE_GPT4,
    MODEL_TYPE_GPT35,
    MODEL_TYPE_POE_MIXTRAL,
    MODEL_TYPE_QWEN_72B,
    MODEL_TYPE_VLLM_MIXTRAL,
    MODEL_TYPE_YI_34B,
    MODEL_TYPE_ZHIPU_GLM3_32B,
    MODEL_TYPE_ZHIPU_GLM4,
    OPENAI_PROXY,
    VLLM_MIXTRAL_MODEL_NAME,
    YI_34B_MODEL_NAME,
    ZHIPU_API_KEY,
    BAICHUAN_API_KEY,
    BAICHUAN_API_BASE,
    MODEL_TYPE_BAICHUAN,
    MODEL_TYPE_YI_34B_16K,
    QIANFAN_ACCESS_KEY,
    QIANFAN_SECRET_KEY,
    MODEL_TYPE_QIANFAN_ERNIE_SPEED_128K,
    MODEL_TYPE_MOCK,
    MODEL_TYPE_BAOWU_GLM_4_9B,
    MODEL_TYPE_PRE_GLM_4_9B,
    MODEL_TYPE_PRE_GLM_4_9B_AGENT,
    MODEL_TYPE_CHAT_LAW_7B,
    MODEL_TYPE_GLM_4_9b_kxc_ADDRESS,
    MODEL_TYPE_GLM_4_9b_kxc,
    MODEL_TYPE_DEEPSEEK_14B,
    DEEPSEEK_14B_MODEL_NAME,
    DEEPSEEK_14B_ADDRESS,
    DEEPSEEK_R1_671B_MODEL_NAME,
    MODEL_TYPE_DEEPSEEK_R1_671B,
    DEEPSEEK_R1_671B_ADDRESS,
    DEEPSEEK_R1_671B_API_KEY,
    MODEL_TYPE_DEEPSEEK_AGENT_14B,
    MODEL_TYPE_FAKE_PASSTHROUGH,
    MODEL_TYPE_QWQ_32B,
    CODE_MODEL_TYPE_NAME,
    CODE_MODEL_TYPE_TEMPERATURE,
    CODE_MODEL_TYPE_MAX_TOKENS,
    CODE_MODEL_TYPE,
    MODEL_TYPE_O1,
    MODEL_TYPE_QWEN3_8B,
)
from config.project_config import get_project_config

logger = get_logger(__name__)
model_map = {MODEL_TYPE_ZHIPU_GLM4: "glm-4", MODEL_TYPE_ZHIPU_GLM3_32B: "chatglm3_32b"}


def llm_predict(
    msg,
    model_type: str,
    temperature: float = 1e-6,
    timeout: int = 60,
    max_tokens: int = 1024,
):
    if model_type.lower() == MODEL_TYPE_VLLM_MIXTRAL:
        return mixtral_vllm_predict(msg, temperature, timeout, max_tokens=max_tokens)
    if model_type.lower() == MODEL_TYPE_POE_MIXTRAL:
        return get_poe_client().chat(BotName.mixtral_8_7b, msg, timeout)
    if model_type.lower() == MODEL_TYPE_QWEN_72B:
        return get_poe_client().chat(BotName.qwen_72b, msg, timeout)
    if model_type.lower() == MODEL_TYPE_AZURE_GPT:
        return azure_gpt_predict(msg)
    if model_type.lower() == MODEL_TYPE_GPT35:
        return (
            openai_gpt_3_5_chat_model()
            .invoke(
                [ChatMessage(role="user", content=msg)],
            )
            .content
        )
    if model_type.lower() == MODEL_TYPE_GPT4:
        return (
            openai_gpt_4_chat_model()
            .invoke(
                [ChatMessage(role="user", content=msg)],
            )
            .content
        )
    if (
        model_type.lower() == MODEL_TYPE_ZHIPU_GLM4
        or model_type.lower() == MODEL_TYPE_ZHIPU_GLM3_32B
    ):
        return zhipu_chat_predict(msg, model=model_map[model_type])
    if model_type.lower() == MODEL_TYPE_BAICHUAN:
        return baichuan_chat_predict(msg)
    raise ValueError(f"Unexpect model_type: {model_type}")


vllm_chat_model = functools.partial(
    ChatOpenAI,
    openai_api_key="EMPTY",
    request_timeout=60,
    temperature=app_config.VLLM_MODEL_TEMPERATURE,
    logprobs=False,
    top_p=0.01,
    max_tokens=app_config.VLLM_MODEL_MAX_TOKEN,
    extra_body={"repetition_penalty": 1.0},
)

glm_chat_model = functools.partial(
    ChatOpenAI,
    openai_api_key="EMPTY",
    request_timeout=60,
    temperature=app_config.VLLM_MODEL_TEMPERATURE,
    logprobs=False,
    top_p=0.01,
    max_tokens=1024,
    extra_body={"repetition_penalty": 1.0},
)

azure_gpt_chat_model = functools.partial(
    AzureChatOpenAI,
    azure_endpoint=AZURE_ENDPOINT,
    openai_api_version=AZURE_API_VERSION,
    openai_api_type=AZURE_API_TYPE,
    openai_api_key=AZURE_API_KEY,
    deployment_name=AZURE_DEPLOYMENT_NAME,
    openai_proxy=OPENAI_PROXY,
    request_timeout=60,
)

zhipu_chat_model = functools.partial(
    ChatZhipuAI,
    temperature=0.5,
    api_key=ZHIPU_API_KEY,
    model="glm-4",
    top_p=0.7,
    request_timeout=60,
    max_tokens=1024,
    streaming=False,
)

openai_gpt_3_5_chat_model = functools.partial(
    ChatOpenAI,
    model_name=MODEL_TYPE_GPT35,
    openai_api_key=GPT_35_API_KEY,
    max_tokens=1024,
    request_timeout=60,
    temperature=1e-6,
    openai_proxy=app_config.OPENAI_PROXY,
)
openai_O1_chat_model = functools.partial(
    ChatOpenAI,
    model=MODEL_TYPE_O1,
    openai_api_key=app_config.OPENAI_API_KEY,
    temperature=1,  # Set temperature to 1 as required by the 'o1' model
    openai_proxy=app_config.OPENAI_PROXY,  # Include proxy if necessary for your setup
    max_completion_tokens=app_config.VLLM_MODEL_MAX_TOKEN,
)
openai_gpt_4_chat_model = functools.partial(
    ChatOpenAI,
    model_name=MODEL_TYPE_GPT4,
    openai_api_key=GPT_4_API_KEY,
    max_tokens=1024,
    request_timeout=60,
    temperature=1e-6,
    openai_proxy=app_config.OPENAI_PROXY,
)

baichuan_api_model = functools.partial(
    ChatBaichuan,
    baichuan_api_base=BAICHUAN_API_BASE,
    baichuan_api_key=BAICHUAN_API_KEY,
    model=MODEL_TYPE_BAICHUAN,
    temperature=1e-6,
    top_p=0.7,
    request_timeout=60,
    max_tokens=1024,
    streaming=False,
)

qianfan_api_model = functools.partial(
    QianfanLLMEndpoint,
    qianfan_ak=QIANFAN_ACCESS_KEY,
    qianfan_sk=QIANFAN_SECRET_KEY,
    temperature=1e-6,
    top_p=0.7,
    request_timeout=60,
    streaming=False,
)

deepseek_chat_model = functools.partial(
    ChatOpenAI,
    request_timeout=60,
    temperature=app_config.VLLM_MODEL_TEMPERATURE,
    logprobs=False,
    top_p=0.01,
    max_tokens=8192,
    extra_body={"repetition_penalty": 1.0},
)

deepseek_agent_chat_model = functools.partial(
    ChatOpenAI,
    request_timeout=60,
    temperature=0.001,
    top_p=0.001,
    logprobs=False,
    max_tokens=8192,
    extra_body={"repetition_penalty": 1.0},
)


class MockModel:
    def invoke(self, scene_list: List[str]):
        return scene_list


def mock_model():
    return MockModel()


@functools.lru_cache(maxsize=20)
def create_chat_model(
    model_type: str,
    max_tokens: int = 512,
    repetition_penalty: float = 1.0,
    custom_model: bool = False,
    model_url: str = "",
):
    # 新增支持自定义的vllm模型，便于doc的多模型评估
    if custom_model:
        result = vllm_chat_model(
            model_name=model_type,
            openai_api_base=model_url + "/v1",
            max_tokens=max_tokens,
            extra_body={"repetition_penalty": repetition_penalty},
        )
    elif model_type == MODEL_TYPE_VLLM_MIXTRAL:
        result = vllm_chat_model(
            model_name=VLLM_MIXTRAL_MODEL_NAME,
            openai_api_base=app_config.MIXTRAL_VLLM_ADDRESS + "/v1",
            max_tokens=max_tokens,
        )
    elif model_type == MODEL_TYPE_YI_34B:
        result = vllm_chat_model(
            model_name=YI_34B_MODEL_NAME,
            openai_api_base=app_config.YI_34B_ADDRESS + "/v1",
        )
    elif model_type == MODEL_TYPE_YI_34B_16K:
        result = vllm_chat_model(
            model_name=YI_34B_MODEL_NAME,
            openai_api_base=app_config.YI_34B_16K_ADDRESS + "/v1",
        )
    elif model_type == app_config.VLLM_MODEL_NAME:
        openai_api_base_temp = app_config.VLLM_MODEL_URL
        if app_config.VLLM_MODEL_ADD_V1_SUFFIX:
            openai_api_base_temp += "/v1"
        result = vllm_chat_model(
            model_name=app_config.VLLM_MODEL_NAME,
            openai_api_base=openai_api_base_temp,
            extra_body={"repetition_penalty": repetition_penalty},
            openai_api_key=app_config.VLLM_MODEL_API_KEY,
        )
    elif model_type == MODEL_TYPE_AZURE_GPT:
        result = azure_gpt_chat_model()
    elif model_type == MODEL_TYPE_GPT35:
        result = openai_gpt_3_5_chat_model()
    elif model_type == MODEL_TYPE_GPT4:
        result = openai_gpt_4_chat_model()
    elif model_type == MODEL_TYPE_O1:
        result = openai_O1_chat_model()
    elif model_type == MODEL_TYPE_ZHIPU_GLM4 or model_type == MODEL_TYPE_ZHIPU_GLM3_32B:
        result = zhipu_chat_model(model=model_map[model_type])
    elif model_type == MODEL_TYPE_BAICHUAN:
        result = baichuan_api_model()
    elif model_type == MODEL_TYPE_QIANFAN_ERNIE_SPEED_128K:
        result = qianfan_api_model(model=MODEL_TYPE_QIANFAN_ERNIE_SPEED_128K)
    elif model_type == MODEL_TYPE_MOCK:
        result = FakeChatModel()
    elif model_type == MODEL_TYPE_BAOWU_GLM_4_9B:
        result = vllm_chat_model(
            model_name=MODEL_TYPE_BAOWU_GLM_4_9B,
            openai_api_base=app_config.BAOWU_GLM_4_9B_ADDRESS + "/v1",
        )
    elif model_type == MODEL_TYPE_PRE_GLM_4_9B:
        result = glm_chat_model(
            model_name=MODEL_TYPE_PRE_GLM_4_9B,
            openai_api_base=app_config.PRE_GLM_4_9B_ADDRESS + "/v1",
        )
    elif model_type == MODEL_TYPE_PRE_GLM_4_9B_AGENT:
        # agent model need special param
        result = ChatOpenAI(
            openai_api_key="EMPTY",
            model_name=MODEL_TYPE_PRE_GLM_4_9B_AGENT,
            openai_api_base=app_config.PRE_GLM_4_9B_AGENT_ADDRESS + "/v1",
            temperature=0.01,
            top_p=0.01,
        )
    elif model_type == CODE_MODEL_TYPE:
        result = vllm_chat_model(
            model_name=CODE_MODEL_TYPE_NAME,
            openai_api_base=app_config.CODE_MODEL_TYPE_ADDRESS + "/v1",
            temperature=CODE_MODEL_TYPE_TEMPERATURE,
            max_tokens=CODE_MODEL_TYPE_MAX_TOKENS,
        )
    elif model_type == MODEL_TYPE_CHAT_LAW_7B:
        result = vllm_chat_model(
            model_name=app_config.CHAT_LAW_7B_NAME,
            openai_api_base=app_config.CHAT_LAW_7B_ADDRESS + "/v1",
        )
    elif model_type == MODEL_TYPE_GLM_4_9b_kxc:
        result = vllm_chat_model(
            model_name=MODEL_TYPE_GLM_4_9b_kxc,
            openai_api_base=app_config.MODEL_TYPE_GLM_4_9b_kxc_ADDRESS + "/v1",
        )
    elif model_type == MODEL_TYPE_DEEPSEEK_14B:
        result = deepseek_chat_model(
            model_name=DEEPSEEK_14B_MODEL_NAME,
            openai_api_key="EMPTY",
            openai_api_base=app_config.DEEPSEEK_14B_ADDRESS + "/v1",
        )
    elif model_type == MODEL_TYPE_DEEPSEEK_R1_671B:
        result = deepseek_chat_model(
            model_name=DEEPSEEK_R1_671B_MODEL_NAME,
            openai_api_key=DEEPSEEK_R1_671B_API_KEY,
            openai_api_base=app_config.DEEPSEEK_R1_671B_ADDRESS + "/v1",
        )
    elif model_type == MODEL_TYPE_DEEPSEEK_AGENT_14B:
        result = deepseek_agent_chat_model(
            model_name=app_config.DEEPSEEK_AGENT_14B_MODEL_NAME,
            openai_api_key="EMPTY",
            openai_api_base=app_config.DEEPSEEK_AGENT_14B_ADDRESS + "/v1",
        )
    elif model_type == MODEL_TYPE_QWQ_32B:
        result = vllm_chat_model(
            model_name=app_config.QWQ_32B_MODEL_NAME,
            openai_api_base=app_config.QWQ_32B_ADDRESS + "/v1",
        )
    elif model_type == MODEL_TYPE_QWEN3_8B:
        result = vllm_chat_model(
            model_name=app_config.QWEN3_8B_MODEL_NAME,
            openai_api_base=app_config.QWEN3_8B_ADDRESS + "/v1",
        )
    elif model_type == MODEL_TYPE_FAKE_PASSTHROUGH:
        result = FakePassthroughLLM()
    else:
        raise ValueError(f"Unexpected model_type: {model_type}")
    result.name = model_type
    return result


# param_extract chain is cached in router,
# therefore param_extract chain is the same for different project.
# So we need to get different chat model for different project
# in the same cached param_extract chain
def create_chat_model_in_chain(prompt, stage, config: RunnableConfig, do_invoke=True):
    project_name = config[CHAIN_META][ChainMeta.PROJECT_NAME]
    model_name = config[CHAIN_META][ChainMeta.MODEL_NAME]
    model_type = config[CHAIN_META][ChainMeta.MODEL_TYPE]
    project_config = get_project_config(project_name, model_name)
    params = project_config.special_subchain_params(stage)
    if params and model_type != MODEL_TYPE_FAKE_PASSTHROUGH:
        special_model_type = params.get("model_type", None)
        if special_model_type:
            model_type = special_model_type
    chat_model = create_llm_model_by_project_config(model_type, project_config)
    if do_invoke:
        return chat_model.invoke(prompt, config=config)
    else:
        return chat_model


def baichuan_chat_predict(
    msg: str,
    temperature: float = 1e-6,
    timeout: int = 60,
):
    chat = baichuan_api_model(
        temperature=temperature,
        request_timeout=timeout,
    )
    return chat.invoke(
        [ChatMessage(role="user", content=msg)],
    ).content


def zhipu_chat_predict(
    msg: str,
    model=MODEL_TYPE_ZHIPU_GLM4,
    temperature: float = 1e-6,
    timeout: int = 60,
):
    chat = zhipu_chat_model(
        temperature=temperature, request_timeout=timeout, model=model
    )
    return chat.invoke(
        [ChatMessage(role="user", content=msg)],
    ).content


def azure_gpt_predict(
    msg: str,
    temperature: float = 1e-6,
    timeout: int = 60,
):
    chat = azure_gpt_chat_model(
        temperature=temperature,
        request_timeout=timeout,
    )
    return chat.invoke(
        [ChatMessage(role="user", content=msg)],
    ).content


def mixtral_vllm_predict(
    msg,
    temperature: float = 1e-6,
    timeout: int = 60,
    address: str = app_config.MIXTRAL_VLLM_ADDRESS,
    repetition_penalty=1.1,
    max_tokens: int = 1024,
) -> str:
    chat = ChatOpenAI(
        model_name="/model/mixtral-8-7b",
        openai_api_key="EMPTY",
        openai_api_base=address + "/v1",
        temperature=temperature,
        request_timeout=timeout,
        extra_body={"repetition_penalty": repetition_penalty, "max_tokens": max_tokens},
    )
    return chat.invoke(
        [ChatMessage(role="user", content=msg)],
    ).content


def llm_poe_predict(msg, model: str, temperature: float, timeout: int = 60) -> str:
    return get_poe_client().chat(model, msg, timeout)


def llm_base_predict(msg, model_type, temperature: float, timeout: int) -> str:
    headers = {
        "Content-Type": "application/json",
    }

    messages = [{"role": "user", "content": msg}]

    data = {
        "modelType": model_type,
        "format": "text",
        "messages": messages,
        "paramsMap": {"temperature": temperature},
    }

    try:
        response = requests.post(
            app_config.LLM_BASE_URL, headers=headers, json=data, timeout=timeout
        )
        logger.debug("send prompt: %s\nresponse: %s", msg, response.text)
        return response.text
    except Exception as e:
        logger.exception("LLM exception: %s", e)
        raise e


class GeneralLLM(CustomLLM):
    model_type: str = Field(MODEL_TYPE_GPT35)
    temperature: float = 0.0
    max_retries: int = 10
    timeout: int = 60
    repetition_penalty: float = 1.0
    callback_manager: CallbackManager = PrivateAttr()

    def __init__(
        self,
        model_type: str = MODEL_TYPE_GPT35,
        temperature: float = 0.0,
        max_retries: int = 10,
        repetition_penalty: float = 1.0,
        callback_manager: Optional[CallbackManager] = None,
    ) -> None:
        super().__init__()
        self.model_type = model_type
        self.temperature = temperature
        self.max_retries = max_retries
        self.repetition_penalty = repetition_penalty
        self.callback_manager = callback_manager or CallbackManager([])

    @property
    def metadata(self) -> LLMMetadata:
        return LLMMetadata(
            is_chat_model=True,
        )

    def complete(self, prompt: str, **kwargs: Any) -> CompletionResponse:
        chain = create_chat_model(
            self.model_type, repetition_penalty=self.repetition_penalty
        )
        response = chain.invoke([ChatMessage(role="user", content=prompt)])
        return CompletionResponse(
            text=response.content,
            additional_kwargs=response.response_metadata,
        )

    async def achat(
        self,
        messages: Sequence[ChatMessage],
        **kwargs: Any,
    ) -> ChatResponse:
        prompt = self.messages_to_prompt(messages)
        completion_response = await self.acomplete(prompt, formatted=True, **kwargs)
        return completion_response_to_chat_response(completion_response)

    async def acomplete(self, prompt: str, **kwargs: Any) -> CompletionResponse:
        chain = create_chat_model(
            self.model_type, repetition_penalty=self.repetition_penalty
        )
        response = await chain.ainvoke([ChatMessage(role="user", content=prompt)])
        return CompletionResponse(
            text=response.content,
            additional_kwargs=response.response_metadata,
        )

    def stream_complete(self, prompt: str, **kwargs: Any) -> CompletionResponseGen:
        chain = (
            create_chat_model(
                self.model_type, repetition_penalty=self.repetition_penalty
            )
            | StrOutputParser()
        )

        def gen() -> CompletionResponseGen:
            text = ""
            for response in chain.stream([ChatMessage(role="user", content=prompt)]):
                text += response
                yield CompletionResponse(
                    delta=response,
                    text=text,
                )

        return gen()

    async def astream_chat(
        self, messages: Sequence[llama_ChatMessage], **kwargs: Any
    ) -> ChatResponseAsyncGen:
        chain = (
            create_chat_model(
                self.model_type, repetition_penalty=self.repetition_penalty
            )
            | StrOutputParser()
        )
        chain.name = "GeneralLLM"
        prompt = self.messages_to_prompt(messages)

        async def gen() -> ChatResponseAsyncGen:
            text = ""
            async for response in chain.astream(
                [ChatMessage(role="user", content=prompt)], config=kwargs.get("config")
            ):
                text += response
                yield ChatResponse(
                    message=llama_ChatMessage(role=MessageRole.ASSISTANT, content=text),
                    delta=response,
                )

        return gen()

    async def astream(
        self,
        prompt: BasePromptTemplate,
        output_cls: Optional[BaseModel] = None,
        **prompt_args: Any,
    ) -> TokenAsyncGen:
        """Async stream."""
        if output_cls is not None:
            raise NotImplementedError("Streaming with output_cls not supported.")

        if self.metadata.is_chat_model:
            messages = prompt.format_messages(llm=self, **prompt_args)
            messages = self._extend_messages(messages)
            chat_response = await self.astream_chat(messages, **prompt_args)
            stream_tokens = await astream_chat_response_to_tokens(chat_response)
        return stream_tokens

    @classmethod
    def class_name(cls) -> str:
        return "GeneralLLM"
