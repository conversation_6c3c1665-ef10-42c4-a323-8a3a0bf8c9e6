import abc
import json
import os
import threading
import time
from typing import Dict, Sequence

import boto3
import aioboto3
from botocore.client import BaseClient
from common.logging.logger import get_logger
from common.utils.concurrent_utils import do_once
from config.doc_config import (
    default_access_key,
    default_region_name,
    default_secret_key,
    default_endpoint_url,
    S3_ETAGS_FILE,
    default_s3_local_dir,
    default_s3_folder,
    default_bucket_name,
)

logger = get_logger(__name__)

EMPTY_TAG = "empty"


class IStorage(abc.ABC):
    @abc.abstractmethod
    def pull(self, path):
        pass

    @abc.abstractmethod
    def get_local_dir(self) -> str:
        pass

    @abc.abstractmethod
    def refresh(self):
        pass

    @abc.abstractmethod
    def start(self):
        pass

    @abc.abstractmethod
    def push(self, path, prefix_key):
        pass


async def create_async_s3_client(
    access_key: str = default_access_key,
    secret_key: str = default_secret_key,
    region_name: str = default_region_name,
    endpoint_url: str = default_endpoint_url,
):
    session = aioboto3.Session(
        aws_access_key_id=access_key,
        aws_secret_access_key=secret_key,
        region_name=region_name,
    )
    return session.client("s3", endpoint_url=endpoint_url)


def create_s3_client(
    access_key: str = default_access_key,
    secret_key: str = default_secret_key,
    region_name: str = default_region_name,
    endpoint_url: str = default_endpoint_url,
):
    session = boto3.Session(
        aws_access_key_id=access_key,
        aws_secret_access_key=secret_key,
        region_name=region_name,
    )
    return session.client("s3", endpoint_url=endpoint_url)


class S3Storage(IStorage):
    def __init__(
        self,
        local_dir: str,
        remote_dir: str,
        access_key: str,
        secret_key: str,
        region_name: str,
        endpoint_url: str,
        bucket_name: str,
        auto_refresh: bool = True,
    ):
        self._local_dir = local_dir
        self._remote_dir = remote_dir
        self._bucket_name = bucket_name
        self._mutex = threading.RLock()
        self._s3_client: BaseClient = create_s3_client(
            access_key, secret_key, region_name, endpoint_url
        )
        self._loop_thread: threading.Thread = None
        self._loop_started = False
        self._auto_refresh = auto_refresh
        self.refresh()

    def _get_local_etags(self) -> Dict[str, str]:
        self._mutex.acquire()
        try:
            path = os.path.join(self._local_dir, S3_ETAGS_FILE)
            if not os.path.exists(path):
                return {}
            with open(path) as f:
                return json.load(f)
        finally:
            self._mutex.release()

    def _save_local_etags(self, etags: Dict[str, str]):
        with open(os.path.join(self._local_dir, S3_ETAGS_FILE), "w") as f:
            json.dump(etags, f, indent=2)

    def _list_remote_tags_by_prefix(self, prefix: str = "") -> Dict[str, str]:
        response = self._s3_client.list_objects_v2(
            Bucket=self._bucket_name, Prefix=os.path.join(self._remote_dir, prefix)
        )
        return {
            os.path.relpath(obj["Key"], self._remote_dir): obj["ETag"]
            for obj in response.get("Contents", [])
        }

    def _download_file(self, path: str):
        local_path = os.path.join(self._local_dir, path)
        if local_path.endswith("/"):
            os.makedirs(local_path, exist_ok=True)
        else:
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            logger.info(
                f"Downloading '{local_path}' from bucket '{self._bucket_name}' to '{self._local_dir}'"
            )
            self._s3_client.download_file(
                self._bucket_name, os.path.join(self._remote_dir, path), local_path
            )
            logger.info(
                f"Downloaded '{local_path}' from bucket '{self._bucket_name}' to '{self._local_dir}'"
            )

    def _get_remote_tags(self, paths: Sequence[str]) -> Dict[str, str]:
        etags = {}
        if len(paths) > 3:
            for path, etag in self._list_remote_tags_by_prefix().items():
                if any(path.startswith(p) for p in paths):
                    etags[path] = etag
        else:
            for path in paths:
                etags.update(self._list_remote_tags_by_prefix(path))
        return etags

    def _is_subset_dict(self, super_dict: Dict[str, str], sub_dict: Dict[str, str]):
        for k, v in sub_dict.items():
            if super_dict.get(k) != v:
                return False
        return True

    def _download(self, fetch_etags: Dict[str, str]):
        self._mutex.acquire()
        try:
            local_etags = self._get_local_etags()
            # if self._is_subset_dict(local_etags, fetch_etags):
            #    return
            # if not self._auto_refresh:
            #    self._save_local_etags(local_etags)
            #    return
            for path, remote_etag in self._get_remote_tags(
                list(fetch_etags.keys())
            ).items():
                local_etags[path] = remote_etag
                self._download_file(path)
                self._save_local_etags(local_etags)
        finally:
            self._mutex.release()

    def pull(self, path_or_prefix: str):
        if path_or_prefix.startswith(self._local_dir):
            path_or_prefix = os.path.relpath(path_or_prefix, self._local_dir)
        local_etag = self._get_local_etags().get(path_or_prefix, EMPTY_TAG)
        self._download({path_or_prefix: local_etag})

    def pull_all(self):
        local_etags = self._get_local_etags()
        fetch_etags = {}
        for path in self._list_remote_tags_by_prefix().keys():
            fetch_etags[path] = local_etags.get(path, EMPTY_TAG)
        self._download(fetch_etags)

    def refresh(self):
        self._download(self._get_local_etags())

    def get_local_dir(self):
        return self._local_dir

    def _loop_refresh(self):
        while True:
            try:
                self.refresh()
            except Exception as e:
                logger.error("[Storage] refresh failed: %s", e)
            time.sleep(60)

    def start(self):
        self._mutex.acquire()
        try:
            if self._loop_started:
                return
            t = threading.Thread(target=self._loop_refresh, daemon=True)
            self._loop_thread = t
            t.start()
            self._loop_thread = True
        finally:
            self._mutex.release()

    def push(self, path: str, prefix_key: str):
        for root, dirs, files in os.walk(path):
            for file in files:
                local_file_path = os.path.join(root, file)
                s3_key = os.path.join(
                    self._remote_dir, prefix_key, os.path.relpath(local_file_path, path)
                )

                try:
                    self._s3_client.upload_file(
                        local_file_path, self._bucket_name, s3_key
                    )
                    logger.info(f"file upload：{s3_key}")
                except Exception as e:
                    logger.error(f"file upload fail：{e}")


@do_once
def get_storage() -> IStorage:
    return S3Storage(
        local_dir=default_s3_local_dir,
        remote_dir=default_s3_folder,
        bucket_name=default_bucket_name,
        secret_key=default_secret_key,
        region_name=default_region_name,
        endpoint_url=default_endpoint_url,
        access_key=default_access_key,
        auto_refresh=os.environ.get("s3_auto_refresh", True),
    )
