from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.responses import JSONResponse
from pyinstrument import Profiler
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.middleware.base import RequestResponseEndpoint
from datetime import datetime
from pathlib import Path

from common.logging.logger import get_logger
from common.trace import tracer
from config import app_config

accesslog_logger = get_logger(__name__)
# 你需要定义或引入 `tracer`, `Profiler`, `app_config`, `accesslog_logger`


class TracerMiddleware(BaseHTTPMiddleware):
    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        tracer.set_trace_id(request.headers.get(tracer.TRACE_ID_KEY))
        response = await call_next(request)
        tracer.clear_trace_id()
        return response


class ProfilerMiddleware(BaseHTTPMiddleware):
    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        if "profile" in request.query_params or "profile" in request.headers:
            request.state.profiler = Profiler()
            request.state.profiler.start()

        response = await call_next(request)

        if hasattr(request.state, "profiler"):
            request.state.profiler.stop()
            try:
                output_text = request.state.profiler.output_text()
                run_dir = app_config.run_dir
                profile_dir = Path(run_dir) / "profile"
                if not profile_dir.exists():
                    profile_dir.mkdir(parents=True, exist_ok=True)
                timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                with (
                    profile_dir
                    / f"{request.scope['path'].replace('/', '_')}_{timestamp}.txt"
                ).open("w") as f:
                    f.write(output_text)
            except Exception as e:
                accesslog_logger.exception("Profile failed, %s", e)

        return response


class AccessLogMiddleware(BaseHTTPMiddleware):
    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        start_time = datetime.now()
        response = await call_next(request)
        duration = (datetime.now() - start_time).total_seconds() * 1000
        path = request.url.path
        method = request.method
        ip = request.client.host

        accesslog_logger.info(
            f"{method} {path} {ip} {response.status_code} {duration}ms"
        )
        if response.status_code >= 400:
            accesslog_logger.error(
                f"{method} {path} {ip} {response.status_code} {duration}ms"
            )
        return response
