from asyncio import current_task
from datetime import datetime
from typing import List, Iterable, Optional

from sqlalchemy import <PERSON><PERSON><PERSON> as MySQLJSO<PERSON>, create_engine
from sqlalchemy import SMALLINT, TIMESTAMP, String, Text, select
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import (
    DeclarativeBase,
    Mapped,
    mapped_column,
    scoped_session,
    sessionmaker,
    Session,
)
from sqlalchemy.ext.asyncio import async_scoped_session

from config.app_config import AI_DATA_OPERATOR_DATABASE_URL, DATABASE_URL

# 配置驱动为 async pymysql
async_connection_str = DATABASE_URL.replace("mysql://", "mysql+aiomysql://").split("?")[
    0
]

connection_str = DATABASE_URL.replace("mysql://", "mysql+pymysql://").split("?")[0]

async_engine = create_async_engine(async_connection_str, echo=False, pool_recycle=300)

engine = create_engine(connection_str, echo=False, pool_recycle=300)

async_ai_data_operator_connection_str = AI_DATA_OPERATOR_DATABASE_URL.replace(
    "mysql://", "mysql+aiomysql://"
).split("?")[0]

ai_data_operator_connection_str = AI_DATA_OPERATOR_DATABASE_URL.replace(
    "mysql://", "mysql+pymysql://"
).split("?")[0]

async_ai_data_operator_engine = create_async_engine(
    async_ai_data_operator_connection_str, echo=False, pool_recycle=300
)

ai_data_operator_engine = create_engine(
    ai_data_operator_connection_str, echo=False, pool_recycle=300
)

# 创建一个异步Session工厂
async_session_factory = async_sessionmaker(
    async_engine,
    expire_on_commit=False,
)
async_session = async_scoped_session(
    async_session_factory,
    scopefunc=current_task,
)


class Base(DeclarativeBase):
    pass


class SemanticProject(Base):
    __tablename__ = "semantic_projects"
    id: Mapped[str] = mapped_column(String(191), primary_key=True)
    name: Mapped[str] = mapped_column(String(255), primary_key=True)
    create_time: Mapped[datetime] = mapped_column(TIMESTAMP(True), nullable=False)
    update_time: Mapped[datetime] = mapped_column(TIMESTAMP(True), nullable=False)


class SemanticMetric(Base):
    __tablename__ = "semantic_metrics"
    id: Mapped[str] = mapped_column(String(191), primary_key=True)
    name: Mapped[str] = mapped_column(String(255), nullable=False, unique=True)
    label: Mapped[str] = mapped_column(String(255), nullable=False)
    type: Mapped[str] = mapped_column(String(255), nullable=False)
    type_params: Mapped[dict] = mapped_column(MySQLJSON, nullable=False)
    description: Mapped[str] = mapped_column(Text)
    filter: Mapped[str] = mapped_column(Text)
    filter_refs: Mapped[dict] = mapped_column(MySQLJSON)
    config: Mapped[dict] = mapped_column(MySQLJSON)
    meta: Mapped[dict] = mapped_column(MySQLJSON)

    create_time: Mapped[datetime] = mapped_column(TIMESTAMP(True), nullable=False)
    update_time: Mapped[datetime] = mapped_column(TIMESTAMP(True), nullable=False)
    synonyms: Mapped[list] = mapped_column(MySQLJSON, nullable=False)
    semantic_project_id: Mapped[str] = mapped_column(String(191), nullable=True)
    semantic_model_ids: Mapped[List[str]] = mapped_column(MySQLJSON)


class SemanticModel(Base):
    __tablename__ = "semantic_scenes"
    # 使用 Mapped 类型注释和 mapped_column 函数定义列
    id: Mapped[str] = mapped_column(String(191), primary_key=True)
    table_name: Mapped[str] = mapped_column(String(255), nullable=False)
    label: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[str] = mapped_column(Text)
    agg_time_dimension: Mapped[str] = mapped_column(String(191), nullable=False)

    create_time: Mapped[datetime] = mapped_column(TIMESTAMP(True), nullable=False)
    update_time: Mapped[datetime] = mapped_column(TIMESTAMP(True), nullable=False)
    table_meta_id: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
    )
    semantic_project_id: Mapped[str] = mapped_column(String(191), nullable=True)


class ScenesModel(Base):
    __tablename__ = "semantic_scenes_models"
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    scene_id: Mapped[str] = mapped_column(String(255), nullable=False)
    model_name: Mapped[str] = mapped_column(String(255), nullable=False)


class SemanticDimension(Base):
    __tablename__ = "semantic_dimensions"
    id: Mapped[str] = mapped_column(String(191), primary_key=True)
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    type: Mapped[str] = mapped_column(String(255), nullable=False)
    type_params: Mapped[dict] = mapped_column(MySQLJSON, nullable=False)
    description: Mapped[str] = mapped_column(Text)
    expr: Mapped[str] = mapped_column(Text)
    values: Mapped[list] = mapped_column(MySQLJSON)
    semantic_model_id: Mapped[str] = mapped_column(String(255), nullable=False)
    create_time: Mapped[datetime] = mapped_column(TIMESTAMP(True), nullable=False)
    update_time: Mapped[datetime] = mapped_column(TIMESTAMP(True), nullable=False)
    synonyms: Mapped[list] = mapped_column(MySQLJSON, nullable=False)
    label: Mapped[str] = mapped_column(String(255), nullable=False, server_default="")
    semantic_project_id: Mapped[str] = mapped_column(String(191), nullable=True)


class SemanticMeasure(Base):
    __tablename__ = "semantic_measures"
    id: Mapped[str] = mapped_column(String(191), primary_key=True)
    name: Mapped[str] = mapped_column(String(255), nullable=False, unique=True)
    label: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[str] = mapped_column(Text)
    agg: Mapped[str] = mapped_column(String(255), nullable=False)
    create_metric: Mapped[bool] = mapped_column(SMALLINT, default=False)
    expr: Mapped[str] = mapped_column(Text)
    non_additive_dimension: Mapped[dict] = mapped_column(MySQLJSON)
    agg_params: Mapped[dict] = mapped_column(MySQLJSON)
    agg_time_dimension: Mapped[str] = mapped_column(String(191))
    semantic_model_id: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
    )
    create_time: Mapped[datetime] = mapped_column(TIMESTAMP(True), nullable=False)
    update_time: Mapped[datetime] = mapped_column(TIMESTAMP(True), nullable=False)
    synonyms: Mapped[list] = mapped_column(MySQLJSON, nullable=False)
    semantic_project_id: Mapped[str] = mapped_column(String(191), nullable=True)


class SemanticMetricTreeNode(Base):
    __tablename__ = "semantic_metric_tree"
    id: Mapped[str] = mapped_column(String(191), primary_key=True)
    metric_name: Mapped[str] = mapped_column(String(191), nullable=False)
    expr_calc: Mapped[str] = mapped_column(String(191), nullable=True)
    expr_relation: Mapped[str] = mapped_column(String(191), nullable=True)
    children_names: Mapped[list] = mapped_column(MySQLJSON, nullable=False)
    semantic_project_id: Mapped[str] = mapped_column(String(191), nullable=True)


class FewShotModel(Base):
    __tablename__ = "few_shot"
    id: Mapped[str] = mapped_column(primary_key=True)
    metrics: Mapped[str] = mapped_column()
    categories: Mapped[str] = mapped_column()
    dimensions: Mapped[str] = mapped_column()
    where_constraints: Mapped[str] = mapped_column()
    template: Mapped[str] = mapped_column()


class NLMetricFewShotModel(Base):
    __tablename__ = "nl_metric_few_shot"
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    semantic_project_id: Mapped[str] = mapped_column(String(191))
    question: Mapped[str] = mapped_column(String(1024), nullable=False)
    metrics: Mapped[list] = mapped_column(MySQLJSON, nullable=False)
    dimensions: Mapped[list] = mapped_column(MySQLJSON, nullable=False)
    think: Mapped[str] = mapped_column(String(1024), nullable=False)
    result: Mapped[dict] = mapped_column(MySQLJSON, nullable=False)
    labels: Mapped[dict] = mapped_column(MySQLJSON, nullable=False)
    scene: Mapped[str] = mapped_column(String(32), nullable=False)


def get_nl_metric_few_shot_models_by_project_id(
    project_id: str,
) -> List[NLMetricFewShotModel]:
    with Session(ai_data_operator_engine) as session:
        stmt = select(NLMetricFewShotModel).where(
            NLMetricFewShotModel.semantic_project_id == project_id
        )
        return list(session.scalars(stmt))


def save_nl_metric_few_shot_model(models: List[NLMetricFewShotModel]) -> None:
    with Session(ai_data_operator_engine) as session:
        session.add_all(models)
        session.commit()


def get_few_shot_models():
    with Session(engine) as session:
        stmt = select(FewShotModel)
        return list(session.scalars(stmt))


def get_semantic_projects() -> List[SemanticProject]:
    with Session(engine) as session:
        stmt = select(SemanticProject)
        return list(session.scalars(stmt))


def get_semantic_model_by_name(project_id: str, model_name: str) -> SemanticModel:
    with Session(engine) as session:
        stmt = select(SemanticModel).where(
            SemanticModel.semantic_project_id == project_id,
            SemanticModel.table_name == model_name,
        )
        ret = session.scalar(stmt)
        if not ret:
            raise ValueError(
                f"Model not found: project_id={project_id}, name={model_name}"
            )
        return ret


def get_semantic_model_by_names(
    project_id: str, model_names: Iterable[str]
) -> List[SemanticModel]:
    if not project_id:
        raise RuntimeError(
            f"get_semantic_model_by_names must input project_id: project_id={project_id}, model_names={model_names}"
        )
    if not model_names:
        return None
    elif len(model_names) == 1:
        return [
            get_semantic_model_by_name(project_id, next(iter(model_names))),
        ]
    else:
        with Session(engine) as session:
            stmt = select(SemanticModel).where(
                SemanticModel.semantic_project_id == project_id,
                SemanticModel.table_name.in_(model_names),
            )
            ret = list(session.scalars(stmt))
            if not ret:
                raise ValueError(
                    f"Models not found: project_id={project_id}, model_names={model_names}"
                )
            return ret


def get_semantic_project_by_name(name) -> SemanticProject:
    with Session(engine) as session:
        stmt = select(SemanticProject).where(SemanticProject.name == name)
        ret = session.scalar(stmt)
        if not ret:
            raise ValueError(f"Project not found: name={name}")
        return ret


def get_semantic_model(model_id) -> SemanticModel:
    with Session(engine) as session:
        stmt = select(SemanticModel).where(SemanticModel.id == model_id)
        return session.execute(stmt).one()[0]


def get_all_scenes_models() -> List[ScenesModel]:
    with Session(engine) as session:
        stmt = select(ScenesModel)
        return list(session.scalars(stmt))


def get_scenes_model_by_scene_id(scene_id) -> List[ScenesModel]:
    with Session(engine) as session:
        stmt = select(ScenesModel).where(ScenesModel.scene_id == scene_id)
        return list(session.scalars(stmt))


def get_all_semantic_models() -> List[SemanticModel]:
    with Session(engine) as session:
        stmt = select(SemanticModel)
        return list(session.scalars(stmt))


def get_semantic_models(project_id) -> List[SemanticModel]:
    with Session(engine) as session:
        stmt = select(SemanticModel).where(
            SemanticModel.semantic_project_id == project_id
        )
        return list(session.scalars(stmt))


def get_semantic_project_by_id(project_id) -> SemanticProject:
    with Session(engine) as session:
        stmt = select(SemanticProject).where(SemanticProject.id == project_id)
        ret = session.scalar(stmt)
        if not ret:
            raise ValueError(f"Project {project_id} not found")
        return ret


def get_semantic_metrics(project_id) -> List[SemanticMetric]:
    with Session(engine) as session:
        stmt = select(SemanticMetric).where(
            SemanticMetric.semantic_project_id == project_id
        )
        return list(session.scalars(stmt))


async def aget_nl_metric_few_shot_models_by_project_id(
    project_id: str,
) -> List[NLMetricFewShotModel]:
    async with async_session() as session:
        stmt = select(NLMetricFewShotModel).where(
            NLMetricFewShotModel.semantic_project_id == project_id
        )
        result = await session.scalars(stmt)
        return list(result)


async def asave_nl_metric_few_shot_model(models: List[NLMetricFewShotModel]) -> None:
    async with async_session() as session:
        async with session.begin():
            session.add_all(models)
            await session.commit()


async def aget_few_shot_models() -> List[FewShotModel]:
    async with async_session() as session:
        stmt = select(FewShotModel)
        result = await session.scalars(stmt)
        return list(result)


async def aget_semantic_projects() -> List[SemanticProject]:
    async with async_session() as session:
        stmt = select(SemanticProject)
        result = await session.scalars(stmt)
        return list(result)


async def aget_semantic_model_by_name(
    project_id: str, model_name: str
) -> SemanticModel:
    async with async_session() as session:
        stmt = select(SemanticModel).where(
            SemanticModel.semantic_project_id == project_id,
            SemanticModel.table_name == model_name,
        )
        ret = await session.scalar(stmt)
        if not ret:
            raise ValueError(
                f"Model not found: project_id={project_id}, name={model_name}"
            )
        return ret


async def aget_semantic_model_by_names(
    project_id: str, model_names: Iterable[str]
) -> List[SemanticModel]:
    if not project_id:
        raise RuntimeError(
            f"get_semantic_model_by_names must input project_id: project_id={project_id}, model_names={model_names}"
        )
    if not model_names:
        return None
    elif len(model_names) == 1:
        return [await get_semantic_model_by_name(project_id, next(iter(model_names)))]
    else:
        async with async_session() as session:
            stmt = select(SemanticModel).where(
                SemanticModel.semantic_project_id == project_id,
                SemanticModel.table_name.in_(model_names),
            )
            ret = await session.scalars(stmt)
            if not ret:
                raise ValueError(
                    f"Models not found: project_id={project_id}, model_names={model_names}"
                )
            return list(ret)


async def aget_semantic_project_by_name(name: str) -> SemanticProject:
    async with async_session() as session:
        stmt = select(SemanticProject).where(SemanticProject.name == name)
        ret = await session.scalar(stmt)
        if not ret:
            raise ValueError(f"Project not found: name={name}")
        return ret


async def aget_semantic_model(model_id: str) -> SemanticModel:
    async with async_session() as session:
        stmt = select(SemanticModel).where(SemanticModel.id == model_id)
        return (await session.execute(stmt)).one()[0]


async def aget_all_semantic_models() -> List[SemanticModel]:
    async with async_session() as session:
        stmt = select(SemanticModel)
        result = await session.scalars(stmt)
        return list(result)


async def aget_semantic_models(project_id: str) -> List[SemanticModel]:
    async with async_session() as session:
        stmt = select(SemanticModel).where(
            SemanticModel.semantic_project_id == project_id
        )
        result = await session.scalars(stmt)
        return list(result)


async def aget_semantic_project_by_id(project_id: str) -> SemanticProject:
    async with async_session() as session:
        stmt = select(SemanticProject).where(SemanticProject.id == project_id)
        ret = await session.scalar(stmt)
        if not ret:
            raise ValueError(f"Project {project_id} not found")
        return ret


async def aget_semantic_metrics(project_id: str) -> List[SemanticMetric]:
    async with async_session() as session:
        stmt = select(SemanticMetric).where(
            SemanticMetric.semantic_project_id == project_id
        )
        result = await session.scalars(stmt)
        return list(result)


async def aget_semantic_dimensions(
    project_id: str, model_id: Optional[str] = None
) -> List[SemanticDimension]:
    async with async_session() as session:
        stmt = select(SemanticDimension).where(
            SemanticDimension.semantic_project_id == project_id
        )
        if model_id:
            stmt = stmt.where(SemanticDimension.semantic_model_id == model_id)
        result = await session.scalars(stmt)
        return list(result)


async def aget_semantic_metric_tree_nodes(
    project_id: str,
) -> List[SemanticMetricTreeNode]:
    async with async_session() as session:
        stmt = select(SemanticMetricTreeNode).where(
            SemanticMetricTreeNode.semantic_project_id == project_id
        )
        result = await session.scalars(stmt)
        return list(result)


async def aget_semantic_measures(project_id: str) -> List[SemanticMeasure]:
    async with async_session() as session:
        stmt = select(SemanticMeasure).where(
            SemanticMeasure.semantic_project_id == project_id
        )
        result = await session.scalars(stmt)
        return list(result)


def get_semantic_dimensions(project_id, model_id=None) -> List[SemanticDimension]:
    with Session(engine) as session:
        stmt = select(SemanticDimension).where(
            SemanticDimension.semantic_project_id == project_id
        )
        if model_id:
            stmt = stmt.where(SemanticDimension.semantic_model_id == model_id)
        return list(session.scalars(stmt))


def get_semantic_metric_tree_nodes(project_id) -> List[SemanticMetricTreeNode]:
    with Session(engine) as session:
        stmt = select(SemanticMetricTreeNode).where(
            SemanticMetricTreeNode.semantic_project_id == project_id
        )
        return list(session.scalars(stmt))


def get_semantic_measures(project_id) -> List[SemanticMeasure]:
    with Session(engine) as session:
        stmt = select(SemanticMeasure).where(
            SemanticMeasure.semantic_project_id == project_id
        )
        return list(session.scalars(stmt))


async def aget_nl_metric_few_shot_models_by_project_id(
    project_id: str,
) -> List[NLMetricFewShotModel]:
    async with async_session() as session:
        stmt = select(NLMetricFewShotModel).where(
            NLMetricFewShotModel.semantic_project_id == project_id
        )
        result = await session.scalars(stmt)
        return list(result)


async def asave_nl_metric_few_shot_model(models: List[NLMetricFewShotModel]) -> None:
    async with async_session() as session:
        async with session.begin():
            session.add_all(models)
            await session.commit()


async def aget_few_shot_models() -> List[FewShotModel]:
    async with async_session() as session:
        stmt = select(FewShotModel)
        result = await session.scalars(stmt)
        return list(result)


async def aget_semantic_projects() -> List[SemanticProject]:
    async with async_session() as session:
        stmt = select(SemanticProject)
        result = await session.scalars(stmt)
        return list(result)


async def aget_semantic_model_by_name(
    project_id: str, model_name: str
) -> SemanticModel:
    async with async_session() as session:
        stmt = select(SemanticModel).where(
            SemanticModel.semantic_project_id == project_id,
            SemanticModel.table_name == model_name,
        )
        ret = await session.scalar(stmt)
        if not ret:
            raise ValueError(
                f"Model not found: project_id={project_id}, name={model_name}"
            )
        return ret


async def aget_semantic_model_by_names(
    project_id: str, model_names: Iterable[str]
) -> List[SemanticModel]:
    if not project_id:
        raise RuntimeError(
            f"get_semantic_model_by_names must input project_id: project_id={project_id}, model_names={model_names}"
        )
    if not model_names:
        return None
    elif len(model_names) == 1:
        return [await get_semantic_model_by_name(project_id, next(iter(model_names)))]
    else:
        async with async_session() as session:
            stmt = select(SemanticModel).where(
                SemanticModel.semantic_project_id == project_id,
                SemanticModel.table_name.in_(model_names),
            )
            ret = await session.scalars(stmt)
            if not ret:
                raise ValueError(
                    f"Models not found: project_id={project_id}, model_names={model_names}"
                )
            return list(ret)


async def aget_semantic_project_by_name(name: str) -> SemanticProject:
    async with async_session() as session:
        stmt = select(SemanticProject).where(SemanticProject.name == name)
        ret = await session.scalar(stmt)
        if not ret:
            raise ValueError(f"Project not found: name={name}")
        return ret


async def aget_semantic_model(model_id: str) -> SemanticModel:
    async with async_session() as session:
        stmt = select(SemanticModel).where(SemanticModel.id == model_id)
        return (await session.execute(stmt)).one()[0]


async def aget_all_semantic_models() -> List[SemanticModel]:
    async with async_session() as session:
        stmt = select(SemanticModel)
        result = await session.scalars(stmt)
        return list(result)


async def aget_semantic_models(project_id: str) -> List[SemanticModel]:
    async with async_session() as session:
        stmt = select(SemanticModel).where(
            SemanticModel.semantic_project_id == project_id
        )
        result = await session.scalars(stmt)
        return list(result)


async def aget_semantic_project_by_id(project_id: str) -> SemanticProject:
    async with async_session() as session:
        stmt = select(SemanticProject).where(SemanticProject.id == project_id)
        ret = await session.scalar(stmt)
        if not ret:
            raise ValueError(f"Project {project_id} not found")
        return ret


async def aget_semantic_metrics(project_id: str) -> List[SemanticMetric]:
    async with async_session() as session:
        stmt = select(SemanticMetric).where(
            SemanticMetric.semantic_project_id == project_id
        )
        result = await session.scalars(stmt)
        return list(result)


async def aget_semantic_dimensions(
    project_id: str, model_id: Optional[str] = None
) -> List[SemanticDimension]:
    async with async_session() as session:
        stmt = select(SemanticDimension).where(
            SemanticDimension.semantic_project_id == project_id
        )
        if model_id:
            stmt = stmt.where(SemanticDimension.semantic_model_id == model_id)
        result = await session.scalars(stmt)
        return list(result)


async def aget_semantic_metric_tree_nodes(
    project_id: str,
) -> List[SemanticMetricTreeNode]:
    async with async_session() as session:
        stmt = select(SemanticMetricTreeNode).where(
            SemanticMetricTreeNode.semantic_project_id == project_id
        )
        result = await session.scalars(stmt)
        return list(result)


async def aget_semantic_measures(project_id: str) -> List[SemanticMeasure]:
    async with async_session() as session:
        stmt = select(SemanticMeasure).where(
            SemanticMeasure.semantic_project_id == project_id
        )
        result = await session.scalars(stmt)
        return list(result)
