from common.logging.logger import get_logger
from enum import Enum
from typing import Dict, Any, List, Optional
from llama_index.core.vector_stores import MetadataFilters

from config import doc_config
from nl2document.common.msg.docservice import CondenseInfo
from common.utils.string_utils import class_to_dict

logger = get_logger(__name__)


GENERATE_CHART_TITLE_STAGE = "generate_char_title_stage"

CHART_INSIGHT_STAGE = "chart_insight_stage"

MEASURE_DIM_RECOMMENDATION_STAGE = "measure_dim_recommendation_stage"

CB_CLOSE_MARK = "\1\1\close\1\1\1"


class ParamsExtractStage(str, Enum):
    CONDENSE_QUERY = "condense_query"
    CONDENSE_QUERY_V2 = "condense_query_v2"

    NL2INTENT = "nl2intent"
    NL2INTENT_BY_TAG = "nl2intent_by_tag"
    NL2INTENT_BY_TAG_V2 = "nl2intent_by_tag_v2"
    NL2TIME_DIMENSION = "nl2time_dimension"

    # 查数
    NL2METRIC_TIME_QUERY = "nl2metric_time_query"
    NL2METRIC_TIME_QUERY_V2 = "nl2metric_time_query_v2"
    NL2METRIC_METRICS = "nl2metric_metrics"
    NL2METRIC_WHERE = "nl2metric_where"
    NL2METRIC_GROUP_BYS = "nl2metric_group_bys"
    NL2METRIC_ORDER_BYS = "nl2metric_order_bys"

    # 归因
    ATTR_ANALYSIS_TIME = "attr_analysis_time"
    ATTR_ANALYSIS_PARAM = "attr_analysis_param"
    ATTR_ANALYSIS = "attr_analysis"  # no prompt needed
    ATTR_ANALYSIS_METRIC = "attr_analysis_metric"
    ATTR_ANALYSIS_DIMENSION = "attr_analysis_dimension"
    ATTR_ANALYSIS_REPORT = "attr_analysis_report"

    # 其它
    NL2META = "nl2meta"
    NL2META_METRIC_DETAILS = "nl2meta_details"
    NL2META_DIMENSION_DETAILS = "nl2meta_details_dimension"

    TASK_DECOMPOSITION = "task_decomposition"
    NL2MODELS = "nl2models"

    # doc
    NL2DOCUMENT_RETRIEVE_NODES = "nl2document_retrieve_nodes"
    NL2DOCUMENT_SYNTHESIZE_ANSWER = "nl2document_synthesize_answer"

    QUERY_MEETING = "query_meeting"

    NL2MEETING_INTENT = "nl2meeting_intent"
    NL2MEETING_TIME = "nl2meeting_time"
    NL2MEETING_PARAMS = "nl2meeting_params"
    NL2MEETING_QUERY = "nl2meeting_query"
    NL2MEETING_META = "nl2meeting_meta"
    NL2MEETING_CHITCHAT = "nl2meeting_chitchat"

    PERCENTAGE = "percentage"
    PERIOD_ON_PERIOD = "period_on_period"
    TABLE_TOOLS = "table_tools"
    PREDICT = "predict"
    NL2AGENT = "nl2agent"

    WEB_SEARCH = "web_search"
    RESULT_ANALYSIS = "result_analysis"

    # agent
    BRAIN = "brain"
    BRAIN_TOOL = "brain_tool"
    JUDGE_TOOL = "judge_tool"
    INTENT_BI = "intent_bi"
    BI = "bi"
    ANALYZE = "analyze"
    METRIC_META = "metric_meta"
    DOC = "doc"
    FAST_LOOKUP = "fast_lookup"
    AGENT_CHAT = "agent_chat"
    AGENT_DOC_CHAT = "agent_doc_chat"
    AGENT_PY_CODE_TOOL = "agent_py_code_tool"
    AGENT_CONDENSE = "agent_condense"
    AGENT_CONDENSE_NO_HISTORY_RESPONSE = "agent_condense_no_history_response"
    METRIC_ATTR_REWRITE = "metric_attr_rewrite"
    GENERAL_NER_EXTRACT = "general_ner_extract"


STAGE_LIST = {
    ParamsExtractStage.NL2METRIC_WHERE,
    ParamsExtractStage.NL2METRIC_TIME_QUERY,
    ParamsExtractStage.NL2TIME_DIMENSION,
    ParamsExtractStage.NL2METRIC_METRICS,
    ParamsExtractStage.NL2METRIC_GROUP_BYS,
    ParamsExtractStage.NL2METRIC_ORDER_BYS,
    ParamsExtractStage.ATTR_ANALYSIS_PARAM,
    ParamsExtractStage.ATTR_ANALYSIS_TIME,
}

# TODO(bhx): use stage name directly
STAGE_TO_METRIC_FEW_SHOT = {
    ParamsExtractStage.NL2METRIC_METRICS: "metric",
    ParamsExtractStage.NL2METRIC_WHERE: "where",
    ParamsExtractStage.NL2METRIC_GROUP_BYS: "group_by",
    ParamsExtractStage.NL2METRIC_ORDER_BYS: "order_by",
}


CHAIN_META = "metadata"


# langchain metadata key
class ChainMeta(str, Enum):
    JOB_TYPE = "job_type"
    PROJECT_NAME = "project_name"
    PROJECT_ID = "project_id"
    MODEL_NAME = "model_name"
    MODEL_LABEL = "model_label"
    MODEL_ID = "model_id"
    MODEL_TYPE = "model_type"
    LOOKUP_DATA_MODEL_TYPE = "lookup_data_model_type"
    CHAT_MODEL_TYPE = "chat_model_type"
    CODE_MODEL_TYPE = "code_model_type"
    FORCE_EXACT_MATCH = "force_exact_match"
    HISTORY_PARAMS_EXTRACT_DATA = "history_params_extract_data"
    MESSAGES = "messages"
    PROMPT_SELECTOR = "prompt_selector"
    RUN_TIME = "run_time"
    FORCE_INTENT = "force_intent"
    CALCULATOR_PARAM_KEY = "calculator_param_key"
    ADDITIONAL_INFO = "additional_info"
    CSV_MGR = "csv_mgr"
    AGENT_REPORTER = "agent_reporter"
    ENABLE_INTERNET_SEARCH = "enable_internet_search"
    ENABLE_DOC_SEARCH = "enable_doc_search"
    ENABLE_BI = "enable_bi"
    MANUAL_SELECTS_RESULT = "manual_selects_result"

    # nl2document
    DOCUMENT_INDEX_SERVICE = "document_index_service"
    LLAMAINDEX_SERVICE_CONTEXT = "llamaindex_service_ctx"
    DOCUMENT_LIBRARY = "document_library"
    DOCUMENT_IDS = "document_ids"
    DOCUMENT_INDEX_TYPE = "document_index_type"
    DOCUMENT_FILTERS = "document_filters"
    DOC_FILE_IDS = "doc_file_ids"
    DOC_DIR_IDS = "doc_dir_ids"
    COMPANY_ID = "company_id"
    YEAR = "year"
    MONTH = "month"
    QUERY_PARAMS = "query_params"

    # nl2meeting
    NL2MEETING_USER = "nl2meeting_user"
    NL2MEETING_ID = "meeting_id"
    USER_ID = "dipeak_user_id"
    CONDENSE_INFO = "condense_info"


# Meta to exclude from langfuse
META_EXCLUDE_FROM_TRACE = [
    ChainMeta.PROMPT_SELECTOR,
    ChainMeta.DOCUMENT_INDEX_SERVICE,
    ChainMeta.LLAMAINDEX_SERVICE_CONTEXT,
    ChainMeta.CSV_MGR,
    ChainMeta.AGENT_REPORTER,
]


class JobType(str, Enum):
    PARAMS_EXTRACT = "params_extract"
    PROJECT_PARAMS_EXTRACT = "project_params_extract"
    EVALUATE_PARAMS_EXTRACT = "evaluate_params_extract"
    GENERATE_CHART_TITLE = "generate_chart_title"
    CHART_INSIGHT = "chart_insight"
    ATTRIBUTE_ANALYSIS_REPORT = "attribute_analysis_report"
    MEASURE_DIM_RECOMMENDATION = "measure_dim_recommendation"
    QUERY_DOCUMENT = "query_document"
    RETRIEVE_NODES = "retrieve_nodes"
    QUERY_MEETING = "query_meeting"
    CHITCHAT = "chitchat"
    CONDENSE = "condense"
    RESULT_ANALYSIS = "result_analysis"
    AGENT = "agent"


class SharedDict(dict):
    def __deepcopy__(self, memo):
        # deepcopy returns itself
        return self


# key added to ChainMeta during invoke will be thrown away, so we use RUN_TIME instead
class ChainRuntime(str, Enum):
    QUESTION = "SUMMARY_TEMPLATEquestion"
    SUMMARY_TEMPLATE = "summary_template"
    CONDENSE_QUESTION = "condense_question"
    METRICS = "metrics"
    DIMENSIONS = "dimensions"
    RAW_METRICS = "raw_metrics"
    RAW_DIMENSIONS = "raw_dimensions"
    INTENT = "intent"
    META_INTENT = "meta_intent"
    INTENT_TAG = "intent_tags"
    IS_SUB = "is_sub"
    METRIC_NER = "metric_ner"
    WHERE_NER = "where_ner"
    GROUPBYS_WITH_LEVEL = "groupbys_with_level"
    ASK_BI = "askbi"
    ASK_DOC = "askdoc"
    TIME_QUERY_TYPE = "timeQueryType"

    AGENT_BRAIN_MSG = "agent_brain_msg"

    FIRST_RETRIEVED_METRIC = "first_retrieved_metric"
    MAPPING_DIMENSION_NAMES = "mapping_dimension_names"

    LLM_OUTPUT_QUERY_METRIC = "llm_output_query_metric"
    LLM_OUTPUT_QUERY_GROUPBY = "llm_output_query_groupby"
    LLM_OUTPUT_QUERY_WHERE = "llm_output_query_where"
    LLM_OUTPUT_QUERY_ORDERBY = "llm_output_query_orderby"
    LLM_OUTPUT_QUERY_TIME = "llm_output_query_time"

    LLM_INPUT_QUERY_METRIC = "llm_input_query_metric"
    LLM_INPUT_QUERY_GROUPBY = "llm_input_query_groupby"
    LLM_INPUT_QUERY_WHERE = "llm_input_query_where"
    LLM_INPUT_QUERY_ORDERBY = "llm_input_query_orderby"
    LLM_INPUT_QUERY_TIME = "llm_input_query_time"

    NODES_WITH_SCORE = "nodes_with_score"
    RETRIEVE_NODES_WITH_SCORE = "retrieve_nodes_with_score"
    EARLY_STOP = "early_stop"
    MESSAGES_RECORD = "messages_record"
    MESSAGES_IN_RESP = "messages_in_resp"
    PARAMS_EXTRACT_DATA = "params_extract_data"

    MEETING = "meeting"
    MEETING_PERSONNEL = "meeting_personnel"

    # only for langfuse
    OK_LOGS = "ok_logs"
    ERROR_LOGS = "error_logs"

    COLLECTION_NAME = "collection_name"
    HISTORY_MESSAGES = "history_messages"
    ATTR_QUERY_METRIC = "attr_query_metric"
    AGENT_BRAIN_INPUT = "agent_brain_input"
    HINT = "hint"


def gen_simple_chain_meta(
    job_type: str,
    model_type: str,
) -> Dict:
    return {
        ChainMeta.JOB_TYPE: job_type,
        ChainMeta.MODEL_TYPE: model_type,
        ChainMeta.RUN_TIME: SharedDict(
            {ChainRuntime.OK_LOGS: [], ChainRuntime.ERROR_LOGS: []}
        ),
    }


def gen_chain_meta(
    job_type: str,
    project_name: str,
    project_id: str,
    model_name: str,
    model_label: str,
    model_id: str,
    model_type: str,
    prompt_selector: Any,
    force_exact_match: bool = False,
    history_params_extract_data: Optional[Any] = None,
    messages: Optional[List[Any]] = None,
    user_id: Optional[str] = None,
) -> Dict:
    from nl2agent.common.agent_reporter import MockAgentReporter

    return {
        ChainMeta.JOB_TYPE: job_type,
        ChainMeta.PROJECT_NAME: project_name,
        ChainMeta.PROJECT_ID: project_id,
        ChainMeta.MODEL_NAME: model_name,
        ChainMeta.MODEL_LABEL: model_label,
        ChainMeta.MODEL_ID: model_id,
        ChainMeta.MODEL_TYPE: model_type,
        ChainMeta.FORCE_EXACT_MATCH: force_exact_match,
        ChainMeta.HISTORY_PARAMS_EXTRACT_DATA: history_params_extract_data,
        ChainMeta.MESSAGES: messages,
        ChainMeta.PROMPT_SELECTOR: prompt_selector,
        ChainMeta.USER_ID: user_id,
        ChainMeta.RUN_TIME: SharedDict(
            {ChainRuntime.OK_LOGS: [], ChainRuntime.ERROR_LOGS: []}
        ),
        ChainMeta.AGENT_REPORTER: MockAgentReporter(""),
    }


def gen_agent_chain_meta(
    job_type: str,
    project_name: str,
    project_id: str,
    model_name: str,
    model_label: str,
    model_id: str,
    model_type: str,
    chat_model_type: str,
    code_model_type: str,
    prompt_selector: Any,
    question: str,
    trace_id: str,
    task_id: str,
    file_ids: List[str],
    dir_ids: List[int],
    lookup_data_model_type,
    force_exact_match: bool = False,
    history_messages: Optional[List[Any]] = None,
    user_id: Optional[str] = None,
    enable_internet_search=False,
    enable_doc_search=True,
    enable_bi=True,
    additional_info: Dict = {},
) -> Dict:
    from nl2agent.common.csv_mgr import CsvMgr
    from nl2agent.common.agent_reporter import AgentReporter

    return {
        ChainMeta.JOB_TYPE: job_type,
        ChainMeta.PROJECT_NAME: project_name,
        ChainMeta.PROJECT_ID: project_id,
        ChainMeta.MODEL_NAME: model_name,
        ChainMeta.MODEL_LABEL: model_label,
        ChainMeta.MODEL_ID: model_id,
        ChainMeta.MODEL_TYPE: model_type,
        ChainMeta.CHAT_MODEL_TYPE: chat_model_type,
        ChainMeta.CODE_MODEL_TYPE: code_model_type,
        ChainMeta.LOOKUP_DATA_MODEL_TYPE: lookup_data_model_type,
        ChainMeta.FORCE_EXACT_MATCH: force_exact_match,
        ChainMeta.PROMPT_SELECTOR: prompt_selector,
        ChainMeta.USER_ID: user_id,
        ChainMeta.CSV_MGR: CsvMgr(trace_id),
        ChainMeta.AGENT_REPORTER: AgentReporter(task_id),
        ChainMeta.ADDITIONAL_INFO: additional_info,
        ChainMeta.DOC_FILE_IDS: file_ids,
        ChainMeta.DOC_DIR_IDS: dir_ids,
        ChainMeta.ENABLE_INTERNET_SEARCH: enable_internet_search,
        ChainMeta.ENABLE_DOC_SEARCH: enable_doc_search,
        ChainMeta.ENABLE_BI: enable_bi,
        ChainMeta.RUN_TIME: SharedDict(
            {
                ChainRuntime.QUESTION: question,
                ChainRuntime.HISTORY_MESSAGES: history_messages,
                ChainRuntime.MESSAGES_RECORD: [],
                ChainRuntime.MESSAGES_IN_RESP: [],
                ChainRuntime.OK_LOGS: [],
                ChainRuntime.ERROR_LOGS: [],
            }
        ),
    }


def gen_chain_meta_without_model(
    job_type: str,
    project_name: str,
    project_id: str,
    model_type: str,
    prompt_selector: Any,
    force_exact_match: bool = False,
    history_params_extract_data: Optional[Any] = None,
    messages: Optional[List[Any]] = None,
    user_id: Optional[str] = None,
) -> Dict:
    from nl2agent.common.agent_reporter import MockAgentReporter

    return {
        ChainMeta.JOB_TYPE: job_type,
        ChainMeta.PROJECT_NAME: project_name,
        ChainMeta.PROJECT_ID: project_id,
        ChainMeta.MODEL_NAME: None,
        ChainMeta.MODEL_LABEL: None,
        ChainMeta.MODEL_ID: None,
        ChainMeta.MODEL_TYPE: model_type,
        ChainMeta.FORCE_EXACT_MATCH: force_exact_match,
        ChainMeta.PROMPT_SELECTOR: prompt_selector,
        ChainMeta.HISTORY_PARAMS_EXTRACT_DATA: history_params_extract_data,
        ChainMeta.MESSAGES: messages,
        ChainMeta.USER_ID: user_id,
        ChainMeta.RUN_TIME: SharedDict(
            {ChainRuntime.OK_LOGS: [], ChainRuntime.ERROR_LOGS: []}
        ),
        ChainMeta.AGENT_REPORTER: MockAgentReporter(""),
    }


# 注意修改后的config一定要手动传到invoke里面才能生效
def add_model_to_chain_meta(meta_dict, semantic_model):
    meta_dict[ChainMeta.MODEL_NAME] = semantic_model.table_name
    meta_dict[ChainMeta.MODEL_ID] = semantic_model.id
    meta_dict[ChainMeta.MODEL_LABEL] = semantic_model.label


def del_model_from_chain_meta(meta_dict):
    meta_dict[ChainMeta.MODEL_NAME] = None
    meta_dict[ChainMeta.MODEL_ID] = None
    meta_dict[ChainMeta.MODEL_LABEL] = None


def gen_nl2document_chain_meta(
    job_type: str,
    document_index_service: Any,
    llamaindex_service_ctx: Any,
    document_library: str,
    document_ids: List[str],
    model_type: str,
    filters: Optional[MetadataFilters] = None,
):
    return {
        ChainMeta.JOB_TYPE: job_type,
        ChainMeta.DOCUMENT_INDEX_SERVICE: document_index_service,
        ChainMeta.LLAMAINDEX_SERVICE_CONTEXT: llamaindex_service_ctx,
        ChainMeta.DOCUMENT_LIBRARY: document_library,
        ChainMeta.DOCUMENT_IDS: document_ids,
        ChainMeta.MODEL_TYPE: model_type,
        ChainMeta.DOCUMENT_FILTERS: filters,
        ChainMeta.PROJECT_NAME: None,
        ChainMeta.PROJECT_ID: None,
        ChainMeta.MODEL_NAME: None,
        ChainMeta.MODEL_LABEL: None,
        ChainMeta.MODEL_ID: None,
        ChainMeta.RUN_TIME: SharedDict(
            {ChainRuntime.OK_LOGS: [], ChainRuntime.ERROR_LOGS: []}
        ),
        "query_params": {
            "rank_topk": doc_config.rank_topk,
            "ask_doc_similarity_top_k": doc_config.ask_doc_similarity_top_k,
            "score_threshold": doc_config.ask_doc_similarity_threshold,
        },
    }


def gen_nl2meeting_chain_meta(
    job_type: str,
    document_index_service: Any,
    llamaindex_service_ctx: Any,
    user_id: str,
    file_ids: Optional[List[str]],
    model_type: str,
    condense_info: Optional[CondenseInfo] = None,
):
    return {
        ChainMeta.JOB_TYPE: job_type,
        ChainMeta.DOCUMENT_INDEX_SERVICE: document_index_service,
        ChainMeta.LLAMAINDEX_SERVICE_CONTEXT: llamaindex_service_ctx,
        ChainMeta.NL2MEETING_USER: user_id,
        ChainMeta.DOCUMENT_IDS: file_ids,
        ChainMeta.MODEL_TYPE: model_type,
        ChainMeta.PROJECT_NAME: None,
        ChainMeta.PROJECT_ID: None,
        ChainMeta.MODEL_NAME: None,
        ChainMeta.MODEL_LABEL: None,
        ChainMeta.MODEL_ID: None,
        ChainMeta.RUN_TIME: SharedDict(
            {ChainRuntime.OK_LOGS: [], ChainRuntime.ERROR_LOGS: []}
        ),
        "query_params": {
            "rank_topk": doc_config.rank_topk,
            "ask_doc_similarity_top_k": doc_config.ask_doc_similarity_top_k,
            "score_threshold": doc_config.ask_doc_similarity_threshold,
        },
        ChainMeta.CONDENSE_INFO: class_to_dict(condense_info),
    }


class SharedServiceStage(Enum):
    BOOTING = 1
    RUNNING = 2
