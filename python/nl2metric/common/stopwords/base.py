import copy
from pathlib import Path

dipeak_stopwords_path = Path(__file__).parent / "dipeak_stopwords.txt"


def read_stop_words(stop_words_path):
    stop_words = set()
    single_stop_words = set()
    content = open(stop_words_path, "rb").read().decode("utf-8")
    for line in content.splitlines():
        if not line:
            continue
        stop_words.add(line)
        if len(line) == 1:
            single_stop_words.add(line)

    return stop_words, single_stop_words


dipeak_stopwords, dipeak_single_stopwords = read_stop_words(dipeak_stopwords_path)
