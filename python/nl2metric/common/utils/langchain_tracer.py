import functools
from langchain_core.runnables import RunnableLambda
from langchain_core.runnables.utils import accepts_config


# this is used to trace fn in langfuse
def trace_by_chain(chain_name=None):
    def decorator(fn):
        @functools.wraps(fn)
        def wrapper(input, config):
            name = chain_name if chain_name else fn.__name__
            chain = RunnableLambda(fn, name=name)
            # 这里不能让invoke自动填入config，必须手动填入config才是最稳妥的
            # 出过一个问题：add_model_to_chain_meta生成的新config不能在invoke(input)的时候自动生效
            return chain.invoke(input, config=config)

        wrapper.direct_run = fn
        return wrapper

    return decorator


def trace_class_method_by_chain(chain_name=None):
    def decorator(fn):
        @functools.wraps(fn)
        def wrapper(self_bound, input, config):
            name = chain_name if chain_name else fn.__name__
            chain = RunnableLambda(lambda x: fn(self_bound, x), name=name)
            # 这里不能让invoke自动填入config，必须手动填入config才是最稳妥的
            # 出过一个问题：add_model_to_chain_meta生成的新config不能在invoke(input)的时候自动生效
            return chain.invoke(input, config=config)

        wrapper.direct_run = fn
        return wrapper

    return decorator
