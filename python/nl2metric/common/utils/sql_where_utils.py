"""对sql where 子句的过滤操作的工具函数"""
import re
import sqlparse
from sqlparse.sql import Comparison, Identifier, Where, Token


def extract_conditions_from_where(sql_where):
    # 使用 and or 把 sql_where 切割成不同的部分
    conditions = re.split(r"\s+(AND|OR)\s+", sql_where, flags=re.IGNORECASE)
    return conditions


def remove_condition_by_expression(sql_where, expression):
    """使用正则表达式 expression 来删除符合条件的 where 子句"""

    conditions = extract_conditions_from_where(sql_where)
    # 如果 conditions 为空或者只有1个空字符串，那么直接返回空字符串
    if not conditions or (len(conditions) == 1 and not conditions[0].strip()):
        return ""
    modified_conditions = []
    for condition in conditions:
        if re.search(expression, condition, flags=re.IGNORECASE):
            modified_condition = re.sub(expression, "", condition, flags=re.IGNORECASE)
            # 如果 modified_conditions 的长度大于 1，且前面是 and 或者 or，那么就去掉前面的 and 或者 or，也就是 modified_conditions 的最后一个元素
            if len(modified_conditions) > 0 and modified_conditions[
                -1
            ].strip().lower() in ["and", "or"]:
                modified_conditions = modified_conditions[:-1]
            modified_conditions.append(modified_condition)
        else:
            modified_conditions.append(condition)
    # 去掉空的条件
    modified_conditions = [
        condition for condition in modified_conditions if condition.strip()
    ]
    # 去掉第一个条件的 and 或者 or
    if len(modified_conditions) > 0 and modified_conditions[0].strip().lower() in [
        "and",
        "or",
    ]:
        modified_conditions = modified_conditions[1:]
    modified_sql = " ".join(modified_conditions)
    return modified_sql


# 移除 [column] is not null 的子句
def remove_is_not_null_condition(sql_where):
    return remove_condition_by_expression(sql_where, r"\b\w+\s+is\s+not\s+null\b")


# 移除只有左值没有右值的子句，比如 "where bch_nme"
def remove_no_right_condition(sql_where):
    if re.search(r"^\w+$", sql_where):
        return ""
    return sql_where


# 移除指标过滤
# this is no longer needed, same rule is applied in filter_where
def remove_metrics_condition(sql_where, fields_to_remove):
    fields_to_remove = [i.name for i in fields_to_remove]
    sql = "SELECT * FROM table WHERE " + sql_where

    parsed = sqlparse.parse(sql)[0]

    where_clause = None
    for item in parsed.tokens:
        if isinstance(item, Where):
            where_clause = item
            break

    if where_clause is None:
        return sql

    new_conditions = []

    for condition in where_clause.tokens:
        if isinstance(condition, Token) and (
            condition.value.upper() == "WHERE" or condition.value == " "
        ):
            continue
        if isinstance(condition, Comparison):
            left = condition.left
            if (
                isinstance(left, Identifier)
                and left.get_real_name() in fields_to_remove
            ):
                if new_conditions and new_conditions[-1].value.upper() in (
                    "AND",
                    "OR",
                ):  # NOT?
                    new_conditions.pop()
                    continue

        new_conditions.append(condition)

    if not new_conditions:
        return ""
    else:
        new_conditions = [i.value for i in new_conditions]
        return " ".join(new_conditions)
