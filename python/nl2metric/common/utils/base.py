import os
import re
import time
import shutil

from collections import Counter
from common.logging.logger import get_logger
from common.types import Message
from langchain_core.messages import ChatMessage
from typing import List

logger = get_logger(__name__)


def check_condition(result, message: str):
    if not result:
        raise ValueError(message)


def json_equal_without_list_order(json1, json2):
    """
    Recursively compare two JSON, ignoring the order of list.
    """
    if isinstance(json1, dict) and isinstance(json2, dict):
        if set(json1.keys()) != set(json2.keys()):
            return False
        return all(json_equal_without_list_order(json1[k], json2[k]) for k in json1)

    elif (
        isinstance(json1, list)
        and isinstance(json2, list)
        and all(isinstance(item, str) for item in json1)
        and all(isinstance(item, str) for item in json2)
    ):
        return Counter(json1) == Counter(json2)

    else:
        return json1 == json2


def convert_messages(input: List[Message]):
    result = []
    for m in input:
        content = str(m.content)
        if m.role == "assistant":
            pattern = r"<think>.*?</think>"
            content = re.sub(pattern, "", content, flags=re.DOTALL).strip()
            result.append(ChatMessage(role=m.role, content=content))
        else:
            result.append(ChatMessage(role=m.role, content=content))
    return result


def clean_dir_by_time(directory, ttl):
    """
    删除指定目录下超过指定天数的子目录和文件。
    子目录的时间判断基于目录本身的修改时间，不关心其内容的时间。

    :param directory: 要清理的目录路径
    :param days: 时间阈值（天），默认为7天
    """
    if not os.path.exists(directory):
        return
    now = time.time()
    cutoff = now - ttl  # 计算截止时间戳

    for entry in os.scandir(directory):
        try:
            if entry.is_file():
                # 处理文件：修改时间早于截止时间则删除
                if entry.stat().st_mtime < cutoff:
                    os.remove(entry.path)
                    logger.info(f"Deleted file: {entry.path}")
            elif entry.is_dir():
                # 处理子目录：修改时间早于截止时间则删除整个目录
                if entry.stat().st_mtime < cutoff:
                    shutil.rmtree(entry.path)
                    logger.info(f"Deleted directory: {entry.path}")
        except Exception as e:
            print(f"Error processing {entry.path}: {e}")
