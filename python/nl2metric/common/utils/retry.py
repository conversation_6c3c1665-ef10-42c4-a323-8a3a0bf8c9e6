import asyncio
import time
import functools
from typing import Callable, Tuple, Type, Optional, Union
from functools import wraps

from common.logging.logger import get_logger

logger = get_logger(__name__)


def retry_async(retries: int = 3, delay: float = 0.5, exceptions: tuple = (Exception,)):
    """
    异步重试装饰器。

    Parameters
    ----------
    retries: int
        最大重试次数，默认是3次。
    delay: float
        每次重试之间的等待时间（秒），默认是2秒。
    exceptions: tuple
        需要捕获并重试的异常类型，默认是InternalError。

    Returns
    -------
    Decorator
        用于装饰异步函数的装饰器。
    """

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            for attempt in range(retries):
                try:
                    return await func(*args, **kwargs)
                except exceptions as e:
                    if attempt < retries - 1:
                        logger.info(
                            f"Attempt {attempt + 1} failed with error: {e}. Retrying in {delay} seconds..."
                        )
                        await asyncio.sleep(delay)
                    else:
                        logger.exception(
                            f"All {retries} attempts failed. Raising error."
                        )
                        raise e

        return wrapper

    return decorator


def retry(
    retries: int = 3,
    delay: Union[float, Callable[[int], float]] = 1,
    exceptions: Tuple[Type[Exception], ...] = (Exception,),
    on_retry: Optional[Callable[[Exception, int, float], None]] = None,
) -> Callable:
    """
    重试装饰器，默认按照固定间隔，可以自定义间隔函数
    @retry(retires=3, delay=2, exceptions=(Exception,))
    @retry(retires=3, delay=lambda attempt: 5 * (2 ** (attempt - 1)), exceptions=(Exception,))

    :param retries: 最大重试次数（含首次尝试）
    :param delay: 重试间隔（秒）或返回间隔时间的函数(当前尝试次数 -> 秒)
    :param exceptions: 需要捕获的异常类型
    :param on_retry: 重试时的回调函数（参数：异常实例，当前尝试次数，等待时间）
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            max_attempts = retries + 1  # 含首次尝试
            for attempt in range(1, max_attempts + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    if attempt == max_attempts:
                        raise  # 最后一次尝试仍失败，抛出异常

                    # 计算等待时间
                    if not callable(delay):
                        wait_time = delay
                    else:
                        wait_time = delay(attempt)

                    # 执行回调
                    if on_retry:
                        on_retry(e, attempt, wait_time)

                    time.sleep(wait_time)
            return func(*args, **kwargs)  # 实际不会执行到这里，保持语法正确

        return wrapper

    return decorator
