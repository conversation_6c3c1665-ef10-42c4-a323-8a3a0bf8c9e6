import httpx
import time

from common.logging.logger import get_logger
from common.types.base import SharedServiceStage
from config import app_config

logger = get_logger(__name__)


def wait_for_embedding_service():
    if app_config.embedding_model != "embedding_service_api":
        return

    url = (
        f"http://127.0.0.1:{app_config.EMBEDDING_SERVICE_PORT}/embedding_service/health"
    )
    try_cnt = 0
    last_msg = ""
    while True:
        if try_cnt <= 30:
            time.sleep(2)
        elif try_cnt <= 60:
            time.sleep(5)
        else:
            time.sleep(60)
            logger.info(f"waiting for embedding_service to start, last_msg: {last_msg}")
        try_cnt += 1
        try:
            with httpx.Client() as client:
                resp = client.get(url=url)
                if not resp.is_success:
                    last_msg = f"Request embedding_service stage failed with status {resp.status_code}: {resp.text}"
                    continue
                health = resp.json()["health"]
                if health:
                    break
                last_msg = f"embedding_service health {health}"
        except Exception as e:
            last_msg = f"Request embedding_service_api stage failed exception {e}"
            continue
