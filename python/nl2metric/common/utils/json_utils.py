import ast
import json
import re
from typing import Any, Optional, TypeVar

from common.logging.logger import get_logger

logger = get_logger(__name__)

T = TypeVar("T")


def remove_special_token(s: str) -> str:
    s = s.replace("\n", "")
    s = s.replace("\_", "_")
    return s


def extract_json_from_string(s: str, job_name="") -> Optional[T]:
    """
    从字符串中提取出 JSON，用于从 LLM 结果中提取 JSON，是否返回 null。
    注意，只能提取出 {}，不能提取出 []
    搜索字符串，找到第一个 { 符号和对应的闭合 } 符号，以此来提取 JSON 对象。
    """
    # 兼容 comment 注释的场景，去掉每一行 // 和之后的内容，有的时候会在 JSON 中添加注释
    s = re.sub(r"//.*?$", "", s, flags=re.MULTILINE)
    s = remove_special_token(s)
    # 首先匹配 ```json {} ``` 格式的 JSON
    json_regex = re.compile(r"```\s*json\s*([\s\S]*?)```", re.IGNORECASE)
    match = json_regex.search(s)
    if match:
        try:
            # We get this result sometimes: json\n{\n    \"metric\": [\"I1000_SUM\"],\n    \"where\": ''\n}\n
            # TODO(bhx): json_string.replace("''", '""') this might cause other err. eg: {    "metric": ["I1000_SUM"],    "where": "''"}
            return json.loads(match.group(1))  # type: ignore
        except json.JSONDecodeError as error:
            logger.error(f"Job {job_name}: error parsing JSON: %{s}, error: {error}")
            return None

    # 如果没有匹配到 ```json {} ``` 格式的 JSON，再尝试匹配普通的 JSON
    stack = []
    in_string = False
    escape = False
    json_str = ""
    start = -1

    for i, char in enumerate(s):
        if in_string:
            if escape:
                escape = False
            elif char == "\\":
                escape = True
            elif char == '"':
                in_string = False
        else:
            if char == '"':
                in_string = True
            elif char == "{":
                stack.append("{")
                if start == -1:
                    start = i
            elif char == "}":
                stack.pop()
                if not stack:
                    json_str = s[start : i + 1]
                    break

    if json_str:
        # 尝试解析 JSON，如果失败则返回 null
        # HACK: 兼容单引号的场景，判断的标准是以 `{'` 开头，大括号和单引号中间可以有空格或换行
        pattern = r"^\{\s*'"
        if bool(re.match(pattern, json_str)):
            try:
                obj = ast.literal_eval(json_str)
            except Exception:
                # process when redundant ' or " in json_str
                # todo: remove when llm will not cause this error
                json_str = re.sub(r"(\d)'(\s*[}])", r"\1\2", json_str)
                obj = ast.literal_eval(json_str)
            return obj
        try:
            return json.loads(json_str)  # type: ignore
        except json.JSONDecodeError as error:
            logger.error(
                f"Job {job_name}: failed to parsing JSON: {json_str}, err={error}"
            )
            raise Exception(
                f"Job {job_name}: failed to parsing JSON: {json_str}, err={error}"
            )
    raise Exception(f"Job {job_name}: failed to parsing JSON: {s}")
