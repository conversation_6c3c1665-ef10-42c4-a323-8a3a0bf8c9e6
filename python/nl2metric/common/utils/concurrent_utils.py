import functools
import multiprocessing
import multiprocessing.context
import pickle
import threading
from abc import abstractmethod
from concurrent.futures import ThreadPoolExecutor
from typing import Any, Callable, Dict, List
from collections import namedtuple
import inspect

from common.logging.logger import get_logger

logger = get_logger(__name__)

_lazy_vars: Dict[int, Any] = {}
_locks: Dict[int, threading.Lock] = {}


_do_once_overall_lock: threading.Lock = threading.Lock()


def do_once(fn):
    @functools.wraps(fn)
    def wrapper():
        key = str(id(fn))

        # 确保每个函数都有一个独立的锁
        if key not in _locks:
            with _do_once_overall_lock:  # 这个锁确保_locks字典的线程安全
                if key not in _locks:  # 再次检查以避免竞态条件
                    _locks[key] = threading.Lock()

        lock = _locks[key]
        with lock:  # 使用该函数专属的锁
            if key in _lazy_vars:
                return _lazy_vars[key]
            _lazy_vars[key] = fn()
            return _lazy_vars[key]

    return wrapper


def run_concurrently(
    tasks: List[Callable], max_workers: int = 2, timeout_seconds=60
) -> list:
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务。注意：我们使用lambda来传递不同参数给函数。
        futures = [executor.submit(task) for task in tasks]

        # 等待所有任务完成，并按顺序收集结果
        results = [future.result(timeout_seconds) for future in futures]

    return results


def multiprocessing_worker(arg):
    user_fn, user_fn_arg, tmp_lazy_vars, tmp_cache_holder = arg
    global _lazy_vars
    global cache_holder
    _lazy_vars = tmp_lazy_vars
    cache_holder = tmp_cache_holder
    return user_fn(user_fn_arg)


# TODO(bhx): test this
def run_multiprocessing(user_fn, user_fn_args):
    if not user_fn_args:
        raise RuntimeError(f"run_multiprocessing needs args")

    multiprocessing_worker_args = []
    for user_fn_arg in user_fn_args:
        multiprocessing_worker_args.append(
            (user_fn, user_fn_arg, _lazy_vars, cache_holder)
        )

    processor_num = len(user_fn_args)
    with multiprocessing.Pool(processes=processor_num) as pool:
        return pool.map(multiprocessing_worker, multiprocessing_worker_args)


class LoopRunnable(threading.Thread):
    def __init__(self, interval):
        super().__init__()
        self.daemon = True
        self.interval = interval
        self.suspend = False
        self.closed = False
        self.bell = threading.Condition()

    @abstractmethod
    def run_unthrowable(self):
        raise NotImplementedError

    def waiting(self):
        with self.bell:
            self.bell.wait(self.interval)

    def run(self):
        logger.info("LoopRunnable start")
        while not self.closed:
            with self.bell:
                try:
                    if self.suspend:
                        self.bell.wait(self.interval)
                    else:
                        self.run_unthrowable()
                        self.waiting()
                except Exception as e:
                    logger.exception("LoopRunnable meet unexpect error: %s", e)
                    self.waiting()
        logger.info("LoopRunnable exit")

    def close(self):
        logger.info("LoopRunnable close")
        with self.bell:
            self.closed = True
            self.bell.notify_all()

    def suspend(self):
        logger.info("LoopRunnable suspend")
        self.suspend = True

    def resume(self):
        logger.info("LoopRunnable resume")
        with self.bell:
            self.suspend = False
            self.bell.notify_all()

    def wakeup(self):
        # 唤醒线程，立即执行runUnthrowable
        logger.info("LoopRunnable wakeup")
        with self.bell:
            self.bell.notify_all()


def return_on_failure(value):
    def decorate(f):
        def applicator(*args, **kwargs):
            try:
                return f(*args, **kwargs)
            except Exception as e:
                logger.exception(f"Execute failed, return fallback value {value}")
                return value

        return applicator

    return decorate


# 定义缓存信息结构
CacheInfo = namedtuple("CacheInfo", ["hits", "misses", "maxsize", "currsize"])


# 线程安全缓存装饰器
def synchronized_lru_cache(maxsize=128):
    def decorator(func):
        lock = threading.Lock()
        cache = {}
        queue = []
        hits = 0
        misses = 0
        sig = inspect.signature(func)

        def _make_key(args, kwargs):
            def normalize(value):
                if isinstance(value, (int, float, str, bytes, bool, type(None))):
                    return value
                elif isinstance(value, dict):
                    return tuple(
                        sorted((normalize(k), normalize(v)) for k, v in value.items())
                    )
                elif isinstance(value, (list, set, frozenset)):
                    return tuple(normalize(v) for v in value)
                elif isinstance(value, tuple):
                    return tuple(normalize(v) for v in value)
                else:
                    # 对不可哈希对象使用序列化哈希
                    try:
                        return hash(value)
                    except TypeError:
                        try:
                            return hash(pickle.dumps(value))
                        except Exception:
                            # 最终fallback方案
                            return id(value)

            # 绑定参数到函数签名
            bound = sig.bind(*args, **kwargs)
            bound.apply_defaults()

            # 生成规范化参数元组
            normalized_args = tuple(
                (name, normalize(value))
                for name, value in sorted(bound.arguments.items())
            )

            return hash(normalized_args)

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            nonlocal hits, misses
            key = _make_key(args, kwargs)

            with lock:
                if key in cache:
                    hits += 1
                    # 更新LRU顺序
                    queue.remove(key)
                    queue.append(key)
                    return cache[key]

                misses += 1
                result = func(*args, **kwargs)

                if len(queue) >= maxsize:
                    oldest = queue.pop(0)
                    del cache[oldest]

                cache[key] = result
                queue.append(key)
                return result

        def cache_info():
            # 返回命名元组代替字符串
            return CacheInfo(hits, misses, maxsize, len(cache))

        def cache_clear():
            nonlocal hits, misses
            with lock:
                cache.clear()
                queue.clear()
                hits = 0
                misses = 0

        wrapper.cache_info = cache_info
        wrapper.cache_clear = cache_clear
        return wrapper

    return decorator
