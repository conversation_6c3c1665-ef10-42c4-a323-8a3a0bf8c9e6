import ast
import json
from typing import List

from langchain_core.exceptions import OutputParserException
from langchain_core.output_parsers import JsonOutputPars<PERSON>, PydanticOutputParser
from langchain_core.output_parsers.pydantic import TBaseModel
from langchain_core.outputs import Generation

from common.utils.string_utils import trim_escape_char


def parse_json(s):
    s = trim_escape_char(s)
    try:
        return json.loads(s)
    except json.JSONDecodeError as error:
        pass
    try:
        return ast.literal_eval(s)
    except Exception as error:
        result_error = error
    if result_error:
        raise Exception("Failed to parsing JSON: %s, err=%s", s, result_error)


class CustomPydanticOutputParser(PydanticOutputParser):
    def parse_result(
        self, result: List[Generation], *, partial: bool = False
    ) -> TBaseModel:
        try:
            return super().parse_result(result)
        except OutputParserException as e:
            pass
        return self._parse_first_dict(result[0].text)

    def _parse_first_dict(self, input_str):
        open_brackets = 0
        start_index = -1

        for i, char in enumerate(input_str):
            if char == "{":
                open_brackets += 1
                if start_index == -1:
                    start_index = i
            elif char == "}":
                open_brackets -= 1

            if open_brackets == 0 and start_index != -1:
                json_object = parse_json(input_str[start_index : i + 1])
                return self.pydantic_object.parse_obj(json_object)
        return self.pydantic_object.parse_obj({})
