import json
import requests

from common.types import QueryMetricResult
from common.types.exceptions import QueryMetricFailed
from common.utils.langchain_tracer import trace_by_chain
from config import app_config
from urllib.parse import urljoin


@trace_by_chain()
def query_frontend_sql(input):
    param_key = input["param_key"]
    model_id = input["model_id"]
    query_metric_json = input["query_metric_json"]
    model_type = input["model_type"]
    url = urljoin(app_config.ASK_BI_HOST, f"api/chats/metric-query")

    user_id = input["user_id"]
    # for ut/debug
    if model_type == app_config.MODEL_TYPE_FAKE_PASSTHROUGH:
        model_type = app_config.MODEL_TYPE_YI_34B_16K

    headers = {
        "content-type": "application/json",
    }
    data = json.dumps(
        {
            "queryParamsVerified": {
                "originalQueryParams": query_metric_json,
                "queryParams": query_metric_json,
                "extraParams": {
                    "extraMetricNames": [],
                    "extraGroupBys": [],
                    "extraOrderBys": [],
                },
            },
            "llmType": model_type,
            "sceneId": model_id,
            "disable_mom_yoy": True,
            "user_id": user_id,
        },
        ensure_ascii=False,
    ).encode("utf-8")

    # for some werid reason, json=data_json will fail
    response = requests.post(url, data=data, headers=headers)
    try:
        response = response.json()
    except Exception as e:
        raise QueryMetricFailed(
            param_key=param_key,
            metric_names=list(query_metric_json["metricNames"]),
            query_metric=query_metric_json,
            row=None,
            message=f"query_metric_data query failed, resp is not json",
        )
    data = response.get("rows", [])
    if not data and response.get("sqlColumnName", None) is None:
        raise QueryMetricFailed(
            param_key=param_key,
            metric_names=list(query_metric_json["metricNames"]),
            query_metric=query_metric_json,
            row=None,
            message=f"cannot find rows for metric {query_metric_json['metricNames']}",
            metric_query_resp=response,
        )
    return data, response
