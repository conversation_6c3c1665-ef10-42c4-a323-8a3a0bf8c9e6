#! coding: utf-8

import logging
import os
from datetime import datetime
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
from typing import Dict

from pythonjsonlogger import jsonlogger

from common.trace.tracer import <PERSON>Filter
from config import app_config

backup_count = 7
max_bytes = 250 * 1024 * 1024


class ContextFilter(logging.Filter):
    def filter(self, record):
        record.class_name = record.funcName + "." + record.module
        return True


class LoggerScope:
    def __init__(
        self,
        log_dir: str,
        enable_stdout: bool,
        enable_error_log: bool,
        default_log_name: str = None,
        error_log_name: str = None,
    ):
        self._log_dir = log_dir
        if not os.path.exists(self._log_dir):
            os.makedirs(self._log_dir, exist_ok=True)

        context_filter = ContextFilter()
        tracer_filter = TraceFilter()

        filters = [context_filter, tracer_filter]

        json_formatter = jsonlogger.JsonFormatter(
            "%(asctime)s %(levelname)s %(class_name)s %(message)s",
            json_ensure_ascii=False,
        )

        stdout_formatter = logging.Formatter(
            "%(asctime)s [%(process)-5d] [%(threadName)-10s] %(levelname)s %(Traceid)s %(class_name)s %(message)s"
        )
        formatter = (
            stdout_formatter if app_config.disable_json_logger else json_formatter
        )

        if not default_log_name:
            default_log_name = "common-default.log"
        default_log_path = os.path.join(self._log_dir, default_log_name)
        common_file_handler = _create_common_file_handler(default_log_path, formatter)
        self._handlers = [
            common_file_handler,
        ]

        if enable_error_log:
            if not error_log_name:
                error_log_name = "common-error.log"
            error_log_path = os.path.join(self._log_dir, error_log_name)
            error_log_handler = _create_error_file_handler(error_log_path, formatter)
            self._handlers.append(error_log_handler)

        if enable_stdout:
            stdout_handler = _create_stdout_handler(stdout_formatter)
            self._handlers.append(stdout_handler)

        for handler in self._handlers:
            for f in filters:
                handler.addFilter(f)

    def create_logger(self, name):
        logger = logging.getLogger(name)
        logger.propagate = False
        for handler in self._handlers:
            logger.addHandler(handler)
        logger.setLevel(logging.INFO)
        return logger


class LevelFilter(object):
    def __init__(self, level):
        self.level = level

    def filter(self, record):
        return record.levelno != self.level


def _create_common_file_handler(path, formatter):
    handler = TimedRotatingFileHandler(
        path, when="midnight", interval=1, backupCount=backup_count
    )
    handler.setFormatter(formatter)
    # common log should include err log
    # handler.addFilter(LevelFilter(logging.ERROR))
    handler.setLevel(logging.DEBUG)
    return handler


def _create_error_file_handler(path, formatter):
    handler = RotatingFileHandler(path, maxBytes=max_bytes, backupCount=backup_count)

    handler.setFormatter(formatter)
    handler.setLevel(logging.ERROR)
    return handler


def _create_stdout_handler(formatter):
    handler = logging.StreamHandler()

    handler.setFormatter(formatter)
    return handler


_logger_scopes: Dict[str, LoggerScope] = {}
_elk_logger: logging.Logger = None


def _get_or_create_logger_scope(
    log_dir: str,
    enable_stdout: bool,
    enable_error_log: bool,
) -> LoggerScope:
    logger_scope = _logger_scopes.get(log_dir)
    if not logger_scope:
        logger_scope = LoggerScope(
            log_dir=log_dir,
            enable_stdout=enable_stdout,
            enable_error_log=enable_error_log,
        )
        _logger_scopes[log_dir] = logger_scope
    return logger_scope


def get_logger(name: str, enable_stdout=True, enable_error_log=True):
    return _get_or_create_logger_scope(
        log_dir=app_config.log_root,
        enable_stdout=enable_stdout,
        enable_error_log=enable_error_log,
    ).create_logger(name)


def get_langchain_debug_logger(trace_name):
    now = datetime.now()
    formatted_time = now.strftime("%m%d_%H:%M:%S")
    default_log_name = f"{formatted_time}_{trace_name}.log"
    default_log_name = default_log_name.replace(" ", "#")
    log_dir = os.path.join(app_config.log_root, "langchain/")

    logger_scope = LoggerScope(
        log_dir=log_dir,
        enable_stdout=False,
        enable_error_log=False,
        default_log_name=default_log_name,
    )
    return logger_scope.create_logger("langchain")


def get_elk_logger():
    global _elk_logger
    if not _elk_logger:
        log_dir = app_config.log_root
        if not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
        default_log_path = os.path.join(log_dir, "elk.log")
        json_formatter = jsonlogger.JsonFormatter(
            "%(message)s",
            json_ensure_ascii=False,
        )

        handler = TimedRotatingFileHandler(
            default_log_path, when="midnight", interval=1, backupCount=backup_count
        )
        handler.setFormatter(json_formatter)
        handler.setLevel(logging.DEBUG)

        logger = logging.getLogger("elk_logger")
        logger.propagate = False
        logger.addHandler(handler)

        logger.setLevel(logging.INFO)
        _elk_logger = logger
        return logger
    else:
        return _elk_logger
