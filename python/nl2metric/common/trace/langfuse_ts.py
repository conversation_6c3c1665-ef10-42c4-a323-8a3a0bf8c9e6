import inspect

from config import app_config
from langfuse.callback import <PERSON><PERSON><PERSON><PERSON><PERSON>
from langfuse.client import StatefulClient
from typing import Dict, List, Any, Optional, Union
from uuid import UUID


class LangchainTsHolder:
    def __init__(self):
        self.times = None

    def set_times(self, times):
        self.times = times


class StatefulClientWrapper:
    def __init__(self, cb_times, orig_client: StatefulClient):
        self.cb_times = cb_times
        self.orig_client = orig_client

    def __getattr__(self, name):
        return getattr(self.orig_client, name)

    def _run_update_time(self, orig_fn, args, kwargs):
        start_time, end_time = self.cb_times.times
        if inspect.signature(orig_fn).parameters.get("start_time") is not None:
            kwargs["start_time"] = start_time
        if inspect.signature(orig_fn).parameters.get("end_time") is not None:
            kwargs["end_time"] = end_time
        result = orig_fn(*args, **kwargs)
        if isinstance(result, StatefulClient):
            return StatefulClientWrapper(cb_times=self.cb_times, orig_client=result)
        else:
            return result

    def update(self, *args, **kwargs):
        return self._run_update_time(self.orig_client.update, args, kwargs)

    def end(self, *args, **kwargs):
        return self._run_update_time(self.orig_client.end, args, kwargs)

    def span(self, *args, **kwargs):
        return self._run_update_time(self.orig_client.span, args, kwargs)

    def generation(self, *args, **kwargs):
        return self._run_update_time(self.orig_client.generation, args, kwargs)


class LangchainTsCallbackHandler(CallbackHandler):
    def __init__(self, *args, **kwargs):
        self.cb_times = LangchainTsHolder()
        super().__init__(*args, **kwargs)

    def set_times(self, times):
        self.cb_times.set_times(times)

    def _LangchainCallbackHandler__generate_trace_and_parent(
        self,
        serialized: Dict[str, Any],
        inputs: Union[Dict[str, Any], List[str], str, None],
        *,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ):
        try:
            class_name = self.get_langchain_run_name(serialized, **kwargs)

            # on a new invocation, and not user provided root, we want to initialise a new traceo
            # parent_run_id is None when we are at the root of a langchain execution
            if (
                self.trace is not None
                and parent_run_id is None
                and self.langfuse is not None
            ):
                self.trace = None

            if (
                self.trace is not None
                and parent_run_id is None  # We are at the root of a langchain execution
                and self.langfuse is None  # StatefulClient was provided by user
                and self.update_stateful_client
            ):
                params = {
                    "name": self.trace_name
                    if self.trace_name is not None
                    else class_name,
                    "metadata": self._LangchainCallbackHandler__join_tags_and_metadata(
                        tags, metadata, trace_metadata=self.metadata
                    ),
                    "version": self.version,
                    "session_id": self.session_id,
                    "user_id": self.user_id,
                    "tags": self.tags,
                    "input": inputs,
                }

                if self.root_span:
                    self.root_span.update(**params)
                else:
                    self.trace.update(**params)

            # if we are at a root, but langfuse exists, it means we do not have a
            # root provided by a user. Initialise it by creating a trace and root span.
            if self.trace is None and self.langfuse is not None:
                trace = self.langfuse.trace(
                    id=str(run_id),
                    name=self.trace_name if self.trace_name is not None else class_name,
                    metadata=self._LangchainCallbackHandler__join_tags_and_metadata(
                        tags, metadata, trace_metadata=self.metadata
                    ),
                    version=self.version,
                    session_id=self.session_id,
                    user_id=self.user_id,
                    tags=self.tags,
                    input=inputs,
                )

                # ATTATION: we only changed here
                self.trace = StatefulClientWrapper(
                    cb_times=self.cb_times, orig_client=trace
                )

                if parent_run_id is not None and parent_run_id in self.runs:
                    self.runs[run_id] = self.trace.span(
                        id=self.next_span_id,
                        trace_id=self.trace.id,
                        name=class_name,
                        metadata=self._LangchainCallbackHandler__join_tags_and_metadata(
                            tags, metadata
                        ),
                        input=inputs,
                        version=self.version,
                    )

                return

        except Exception as e:
            self.log.exception(e)


def get_ts_langfuse_callback(trace_id: str, trace_name: str, user_id: str = None):
    if app_config.ENABLE_LANGFUSE:
        return LangchainTsCallbackHandler(
            public_key=app_config.LANGFUSE_PUBLIC_KEY,
            secret_key=app_config.LANGFUSE_SECRET_KEY,
            host=app_config.LANGFUSE_HOST,
            session_id=trace_id,
            user_id=app_config.CLUSTER_ID
            if not user_id
            else app_config.CLUSTER_ID + ":" + user_id,
            trace_name=trace_name,
        )
    else:
        return None
