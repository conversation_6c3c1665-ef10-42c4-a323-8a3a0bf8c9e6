import asyncio
import functools
import time
from typing import Callable, Optional
from contextlib import contextmanager

from common.logging.logger import get_logger

logger = get_logger(__name__)


def performance_monitor(
    name: Optional[str] = None,
    log_args: bool = True,
    log_result: bool = False,
    log_level: str = "info",
):
    """
    性能监控装饰器

    Args:
        name: 自定义监控名称，默认使用函数名
        log_args: 是否记录函数参数
        log_result: 是否记录函数返回值
        log_level: 日志级别，支持 "info", "debug", "warning"
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            func_name = name or func.__name__

            # 记录函数调用信息
            if log_args:
                args_info = []
                if args:
                    args_info.extend([str(arg) for arg in args])
                if kwargs:
                    args_info.extend([f"{k}={v}" for k, v in kwargs.items()])
                args_str = ", ".join(args_info)
                logger.info(f"开始执行 {func_name}, 参数: {args_str}")
            else:
                logger.info(f"开始执行 {func_name}")

            try:
                result = await func(*args, **kwargs)
                end_time = time.time()
                execution_time = end_time - start_time

                # 根据日志级别记录执行时间
                log_func = getattr(logger, log_level)
                if log_result:
                    log_func(
                        f"函数 {func_name} 执行完成, 耗时: {execution_time:.2f}秒, 返回值: {result}"
                    )
                else:
                    log_func(f"函数 {func_name} 执行完成, 耗时: {execution_time:.2f}秒")

                return result
            except Exception as e:
                end_time = time.time()
                execution_time = end_time - start_time
                logger.error(
                    f"函数 {func_name} 执行失败, 耗时: {execution_time:.2f}秒, 错误: {str(e)}"
                )
                raise

        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            func_name = name or func.__name__

            # 记录函数调用信息
            if log_args:
                args_info = []
                if args:
                    args_info.extend([str(arg) for arg in args])
                if kwargs:
                    args_info.extend([f"{k}={v}" for k, v in kwargs.items()])
                args_str = ", ".join(args_info)
                logger.info(f"开始执行 {func_name}, 参数: {args_str}")
            else:
                logger.info(f"开始执行 {func_name}")

            try:
                result = func(*args, **kwargs)
                end_time = time.time()
                execution_time = end_time - start_time

                # 根据日志级别记录执行时间
                log_func = getattr(logger, log_level)
                if log_result:
                    log_func(
                        f"函数 {func_name} 执行完成, 耗时: {execution_time:.2f}秒, 返回值: {result}"
                    )
                else:
                    log_func(f"函数 {func_name} 执行完成, 耗时: {execution_time:.2f}秒")

                return result
            except Exception as e:
                end_time = time.time()
                execution_time = end_time - start_time
                logger.error(
                    f"函数 {func_name} 执行失败, 耗时: {execution_time:.2f}秒, 错误: {str(e)}"
                )
                raise

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

    return decorator


@contextmanager
def performance_context(name: str, log_level: str = "info"):
    """
    性能监控上下文管理器

    Args:
        name: 监控名称
        log_level: 日志级别
    """
    start_time = time.time()
    log_func = getattr(logger, log_level)
    log_func(f"开始执行 {name}")
    try:
        yield
    except Exception as e:
        end_time = time.time()
        execution_time = end_time - start_time
        logger.error(f"{name} 执行失败, 耗时: {execution_time:.2f}秒, 错误: {str(e)}")
        raise
    else:
        end_time = time.time()
        execution_time = end_time - start_time
        log_func(f"{name} 执行完成, 耗时: {execution_time:.2f}秒")
