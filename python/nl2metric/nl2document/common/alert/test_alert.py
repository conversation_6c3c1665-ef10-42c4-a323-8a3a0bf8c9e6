import json
from datetime import datetime

from nl2document.common.alert.alert import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def test_alert():
    alert = FeishuAlert(
        webhook_url="https://open.feishu.cn/open-apis/bot/v2/hook/57088c33-9af5-45f0-9abf-d4c118a628db"
    )
    # alert.send_alert("test send alert msg, no need care")
    now = datetime.now()
    extra = {
        "timestamp": now.strftime("%Y-%m-%d %H:%M:%S"),
        "duration": str(datetime.now() - now),
        "module_type": "index_builder",
        "file_id": "111111",
        "input": {
            "source_path": "11111",
        },
        "output": {
            "exception": "1111",
        },
    }
    alert.send_alert(
        f"(测试，无需关注)索引构建失败，请手动处理，详细内容:\n{json.dumps(extra, ensure_ascii=False, indent=4)} "
    )
