from abc import ABC, abstractmethod

import requests


class AlertBase(ABC):
    def __init__(self):
        pass

    @abstractmethod
    def send_alert(self, message):
        pass


class <PERSON><PERSON>uAlert(AlertBase, ABC):
    def __init__(self, webhook_url):
        super().__init__()
        # https://open.feishu.cn/open-apis/bot/v2/hook/57088c33-9af5-45f0-9abf-d4c118a628db
        self.webhook_url = webhook_url

    def send_alert(self, message):
        """
        curl -X POST -H "Content-Type: application/json" \
        -d '{"msg_type":"text","content":{"text":"request example"}}' \
        https://open.feishu.cn/open-apis/bot/v2/hook/****
        """
        data = {"msg_type": "text", "content": {"text": message}}
        requests.post(self.webhook_url, json=data)
