import os
from typing import List

from markdown import markdown

from nl2document.common.base.const import BLACK_VALUE


def save_to_docx(file_name: str, report: str):
    from docx import Document

    # 创建一个DOCX文件
    doc = Document()

    # 将报告按行分割, 并处理每一行
    for line in report.split("\n"):
        stripped_line = line.strip()
        if stripped_line.startswith("####"):
            # 添加一个4级标题
            doc.add_heading(stripped_line[4:].strip(), level=4)
        elif stripped_line.startswith("###"):
            # 添加一个3级标题
            doc.add_heading(stripped_line[3:].strip(), level=3)
        elif stripped_line:
            # 添加一个段落
            doc.add_paragraph(stripped_line)

    # 保存DOCX文件
    doc.save(file_name)


def save_to_pdf(file_name: str, report: str):
    import markdown  # 确保导入 markdown 库及其扩展

    # 启用表格和其他常用扩展
    html_content = markdown.markdown(report, extensions=["tables", "fenced_code"])

    with open("temp.html", "w") as temp_file:
        complete_html = f"""
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
            body {{
                font-family: 'WenQuanYi Zen Hei', sans-serif;
                width: 100%;
                margin: 0;
                padding: 20px;  /* 页面内边距 */
                box-sizing: border-box;
            }}
            /* 新增颜色和居中样式 */
            h1, h2 {{
                color: #000080; /* 标题颜色 */
                text-align: center; /* 标题居中 */
            }}
            h3, h4, h5, h6 {{
                color: #00008b; /* 标题颜色 */
                text-align: left; 
            }}
            img {{
                max-width: 100% !important;  /* 强制图片不超过容器宽度 */
                height: auto !important;     /* 保持原始宽高比 */
                display: block;              /* 避免行内空白 */
                margin: 10px auto;           /* 上下10px，水平居中 */
            }}
                table {{
                    width: 100%;
                    border-collapse: collapse;
                }}
                th, td {{
                    border: 1px solid #dddddd;
                    text-align: left;
                    padding: 8px;
                    word-break: break-all;
                }}
                th {{
                    background-color: #f2f2f2;
                }}
                
            /* 自定义居中容器 */
            .center-block {{
                text-align: center;
                margin: 20px 0;
            }}
            .risk-warning {{
            color: #FFD700;  /* 黄色 */
            text-align: left;
            font-weight: bold;
            background-color: rgba(255,215,0,0.1);  /* 浅黄色背景 */
            padding: 8px;
            border-radius: 4px;
        }}
        .risk-safe {{
            color: #008000;  /* 绿色 */
            text-align: center;
            font-weight: bold;
            background-color: rgba(0,128,0,0.1);  /* 浅绿色背景 */
            padding: 8px;
            border-radius: 4px;
        }}
            </style>
        </head>
        <body>
            {html_content}
        </body>
        </html>
        """
        temp_file.write(complete_html)
        temp_file_path = os.path.abspath(temp_file.name)

    import pdfkit

    options = {"enable-local-file-access": True, "quiet": ""}

    # 可选：配置 pdfkit 以使用特定的 CSS 或选项
    pdfkit.from_file(temp_file_path, file_name, options=options)
    os.remove(temp_file_path)
    return


def truncate_to_bytes(s: str, max_bytes: int) -> str:
    encoded = s.encode("utf-8")
    if len(encoded) <= max_bytes:
        return s

    # 逐步截断直到满足字节限制
    while len(encoded) > max_bytes:
        s = s[:-1]
        encoded = s.encode("utf-8")
    return s


import re
from typing import List, Tuple, Optional


def extract_company_and_date(
    folder_name: List[str], file_name: str
) -> Tuple[Optional[str], Optional[str], Optional[str]]:
    company_id = None
    year = None
    month = None

    # Step 1: Extract company ID and date from folder names (from the deepest level)
    for folder in reversed(folder_name):
        # Pattern for folder with date info, e.g., "1052-202407 纳税申报资料"
        match_folder_with_date = re.search(r"(\d{4})-(\d{4})(\d{2})", folder)
        if match_folder_with_date:
            company_id = match_folder_with_date.group(1)
            year = match_folder_with_date.group(2)
            month = match_folder_with_date.group(3)
            break  # Use the first match (from the deepest folder)

        # Pattern for folder starting with a 4-digit company ID followed by a non-digit,
        # e.g., "1000中国宝武..."
        match_id = re.match(r"(\d{4})\D", folder)
        if match_id and not company_id:
            candidate = match_id.group(1)
            # Check if candidate is not a plausible year (e.g. 1000–1899)
            if not (1900 <= int(candidate) <= 2100):
                company_id = candidate

        # Pattern for a folder that is exactly a 4-digit number.
        match_exact = re.match(r"^(\d{4})$", folder)
        if match_exact:
            candidate = match_exact.group(1)
            # Only assign to year if the candidate is in a plausible year range;
            # otherwise (e.g. "1052"), assign it as company ID if not already set.
            if 1900 <= int(candidate) <= 2100:
                # If year is not set yet, use it as the year
                if not year:
                    year = candidate
            else:
                if not company_id:
                    company_id = candidate

    # Step 2: Extract year and month from the file name.
    # Use file date patterns to override any folder-extracted date.
    # Try matching "YYYYMM" first.
    match_yyyymm = re.search(r"(\d{4})(\d{2})", file_name)
    if match_yyyymm:
        file_year = match_yyyymm.group(1)
        file_month = match_yyyymm.group(2)
        # Override folder's year and month if found in file name
        year = file_year
        month = file_month

    # Next, try matching "YYYY年MM月" which allows for a one-digit month.
    match_year_month = re.search(r"(\d{4})年\s*(\d{1,2})月", file_name)
    if match_year_month:
        file_year = match_year_month.group(1)
        file_month = match_year_month.group(2).zfill(2)
        year = file_year
        month = file_month

    # Finally, if month is still not set, try to extract it from a pattern like "04.pdf" at the end.
    if not month:
        match_month = re.search(r"(\d{2})\.pdf$", file_name)
        if match_month:
            month = match_month.group(1)

    # Step 3: If company_id is still not set, look again in the folder list
    # for any folder that is a 4-digit number and does not represent a valid year.
    if not company_id:
        for folder in reversed(folder_name):
            match_id = re.match(r"^(\d{4})$", folder)
            if match_id:
                candidate = match_id.group(1)
                if not (1900 <= int(candidate) <= 2100):  # Exclude valid years
                    company_id = candidate
                    break

    return company_id or BLACK_VALUE, year or BLACK_VALUE, month or BLACK_VALUE


# 测试代码
if __name__ == "__main__":
    # 定义测试用例和期望结果
    test_cases = [
        (["1052", "1纳税申报资料"], "某简报202301.pdf", ("1052", "2023", "01")),
        (["1052"], "某简报202301.pdf", ("1052", "2023", "01")),
        (["公司财务数据"], "2024年2月...分析报告.pdf", (BLACK_VALUE, "2024", "02")),
        (["未知目录"], "不含日期的信息.pdf", (BLACK_VALUE, BLACK_VALUE, BLACK_VALUE)),
        (
            ["1000中国宝武钢铁集团有限公司", "1001宝山钢铁股份有限公司", "1001宝钢股份销售简报", "2024"],
            "销售结算信息简报202404-宝钢股份.pdf",
            ("1001", "2024", "04"),
        ),
        (["1052", "2024"], "销售报告.pdf", ("1052", "2024", BLACK_VALUE)),
        (["1052"], "销售报告2024年4月.pdf", ("1052", "2024", "04")),
        (["1052", "2024"], "销售报告2023年4月.pdf", ("1052", "2023", "04")),
        (["1052", "2024"], "销售报告04.pdf", ("1052", "2024", "04")),
        (["公司财务数据"], "202404.pdf", (BLACK_VALUE, "2024", "04")),
        (["1000中国宝武", "1001宝山钢铁"], "销售报告202404.pdf", ("1001", "2024", "04")),
        (["1052", "2024"], "销售报告.pdf", ("1052", "2024", BLACK_VALUE)),
        (["2024"], "销售报告.pdf", (BLACK_VALUE, "2024", BLACK_VALUE)),
        (["1052"], "销售报告04.pdf", ("1052", BLACK_VALUE, "04")),
        (["1052", "财务数据"], "2024年10月报告.pdf", ("1052", "2024", "10")),
        (["1000中国企业", "2023"], "报告.pdf", ("1000", "2023", BLACK_VALUE)),
        (["未知目录", "数据"], "2024年3月.pdf", (BLACK_VALUE, "2024", "03")),
        (["1052", "2024"], "销售报告202403.pdf", ("1052", "2024", "03")),
        (["1052"], "销售报告.pdf", ("1052", BLACK_VALUE, BLACK_VALUE)),
        (["1001宝山", "2024", "销售数据"], "报告2023年5月.pdf", ("1001", "2023", "05")),
        (
            ["1052", "1052-202407 纳税申报资料"],
            "1052 账套《增...人适用》.pdf",
            ("1052", "2024", "07"),
        ),
        (["1052"], "2024年 07月...有限公司.xls", ("1052", "2024", "07")),
        (["1001", "2023"], "采购简报 202301.pdf", ("1001", "2023", "01")),
        (["1000", "1001", "财税政策"], "2023年2月宝武集团经营综合分析报告.pdf", ("1001", "2023", "02")),
    ]

    # 运行测试并对比结果
    for i, (folders, file, expected) in enumerate(test_cases, 1):
        result = extract_company_and_date(folders, file)
        is_correct = result == expected
        print(f"Test Case {i}:")
        print(f"Folders: {folders}, File: {file}")
        print(f"Expected: {expected}")
        print(f"Result:   {result}")
        print(f"Status:   {'✅ Correct' if is_correct else '❌ Incorrect'}\n")
