from typing import List, Optional

from pydantic import BaseModel


class OutlineNode(BaseModel):
    id: int
    title: str
    content: str
    allowChildren: bool
    maxChildrenCount: int
    children: Optional[List["OutlineNode"]] = None
    dependsOn: Optional[List[int]] = None


class TemplateUnit(BaseModel):
    id: int
    name: str
    thumbnailPath: Optional[str] = None
    createUser: str
    createAt: str
    outline: Optional[List[OutlineNode]] = None


class TemplateListData(BaseModel):
    templateList: List[TemplateUnit]


class CodeName(BaseModel):
    code: str
    name: str


class ColumnClassifyRequest(BaseModel):
    modelName: str


class ColumnClassifyData(BaseModel):
    metrics: List[CodeName]
    dimensions: List[CodeName]
    timeDimensions: List[CodeName]


class ListColumnCodeValueRequest(BaseModel):
    modelName: str
    columnCode: str
    page: Optional[int] = 0
    pageSize: Optional[int] = 0
    searchValue: Optional[str] = None


class ListDataOperatorColumnCodeValueRequest(BaseModel):
    modelName: str
    columnCode: str
    templateId: int


class ListColumnCodeValueData(BaseModel):
    total: int
    valueList: List[str]


class KeyValue(BaseModel):
    key: str
    value: str


class DataFilterOperatorData(BaseModel):
    opList: List[KeyValue]


class ColumnFilterValues(BaseModel):
    """业务筛选口径"""

    """列名"""
    columnName: str
    columnCode: str
    """运算符"""
    operator: str
    """过滤值列表"""
    values: List[str]


class DataTimeParams(BaseModel):
    """数据时间配置"""

    """时间列"""
    timeColumn: str = "默认时间维度"
    """时间戳"""
    timeRangeEnd: str = "2024-03-31"
    """时间戳"""
    timeRangeStart: str = "2020-01-01"


class CreateOrSaveOutlineRequest(BaseModel):
    """CreateOrSaveOutlineRequest"""

    dataFilterParams: Optional[List[ColumnFilterValues]] = None
    dataTimeParams: Optional[DataTimeParams] = None
    modelName: str
    reportTitle: str
    reportIntention: str
    templateId: int
    sceneId: str
    creator: str
    reportId: Optional[int] = None
    languageStyle: Optional[str] = None


class SaveTemplateRequest(BaseModel):
    dataFilterParams: Optional[List[ColumnFilterValues]] = None
    dataTimeParams: Optional[DataTimeParams] = None
    modelName: str
    templateId: int
    templateTitle: str
    templateIntention: str
    sceneId: str
    creator: str


class CreateOrSaveOutlineData(BaseModel):
    """CreateOrSaveOutlineData"""

    outline: List[OutlineNode]


class UpdateTemplateOutlineRequest(BaseModel):
    outline: List[OutlineNode]
    templateId: int


class UpdateTemplateSectionRequest(BaseModel):
    minWordLen: Optional[int] = None
    maxWordLen: Optional[int] = None
    sectionIntention: Optional[str] = None
    sectionId: int
    templateId: int
    dataOpList: Optional[List] = None
    textOpList: Optional[List] = None
    isPreview: Optional[bool] = False


class UpdateTemplateSectionData(BaseModel):
    templateId: int
    sectionId: int
    content: str


class ReNameReportSection(BaseModel):
    sectionId: int
    reportId: int
    name: str


class SaveReportRequest(BaseModel):
    """SaveReportRequest"""

    reportId: int


class ReportMetaInfo(BaseModel):
    reportId: int
    reportTitle: str
    reportIntention: str
    modelName: str
    templateId: int
    sceneId: str
    creator: str
    createAt: str
    updateAt: str
    status: str


class TemplateMetaInfo(BaseModel):
    templateId: int
    templateTitle: str
    templateIntention: str
    modelName: str
    sceneId: str
    creator: str
    createAt: str
    updateAt: str
    status: str


class ReportDetailInfo(BaseModel):
    outlineNodes: List[OutlineNode]
    dataFilterParams: List[ColumnFilterValues]
    dataTimeParams: Optional[DataTimeParams] = None
    reportInfo: ReportMetaInfo


class TemplateDetailInfo(BaseModel):
    outlineNodes: Optional[List[OutlineNode]] = None
    dataFilterParams: Optional[List[ColumnFilterValues]] = None
    dataTimeParams: Optional[DataTimeParams] = None
    templateInfo: Optional[TemplateMetaInfo] = None


class GetReportDetailInfoRequest(BaseModel):
    reportId: int


class GetTemplateDetailInfoRequest(BaseModel):
    templateId: int


class GetReportSectionConfigRequest(BaseModel):
    reportId: int
    sectionId: int


class GetTemplateSectionConfigRequest(BaseModel):
    templateId: int
    sectionId: int


class TemplateDataOpClass(BaseModel):
    sectionId: int
    templateId: int
    dataOpId: Optional[int] = None
    name: str
    computeType: str
    operator: str
    operatorDesc: Optional[str] = None
    metric: str
    groupBy: Optional[str] = None
    dataFilter: Optional[List[dict]] = None
    outputDataSectionParams: Optional[List[float]] = None
    outputOrderBy: Optional[str] = None
    outputLimit: Optional[int] = None
    dataDescTemplate: Optional[str] = None
    timeGranularity: str
    timeRangeStart: str
    timeRangeEnd: str
    timeColumn: str
    segmentationOptions: Optional[str] = None
    enumOrder: Optional[str] = None
    result: Optional[str] = None


class CreateOrUpdateDataOpRequest(BaseModel):
    dataOp: TemplateDataOpClass
    result: Optional[str] = None


class TemplateTextOpClass(BaseModel):
    sectionId: int
    templateId: int
    textOpId: Optional[int] = None
    name: str
    prompt: str
    type: str
    inputDataOpIds: Optional[List[int]] = None
    inputSectionIds: Optional[List[int]] = None
    result: Optional[str] = None


class CreateOrUpdateTextOpRequest(BaseModel):
    textOp: TemplateTextOpClass
    resultText: Optional[str] = None


class CreateNewTemplateRequest(BaseModel):
    name: str
    createUser: str


class DeleteTemplateRequest(BaseModel):
    templateId: int


class ReportDataOpClass(BaseModel):
    reportId: int
    sectionId: int
    dataOpId: int
    name: str
    computeType: str
    operator: str
    operatorDesc: str
    metric: str
    groupBy: str
    dataFilter: List[dict]
    outputDataSectionParams: List[float]
    outputOrderBy: str
    outputLimit: int
    dataDescTemplate: str
    timeGranularity: str
    timeRangeStart: str
    timeRangeEnd: str
    timeColumn: str
    segmentationOptions: str
    enumOrder: str


class ReportTextOpClass(BaseModel):
    reportId: int
    sectionId: int
    textOpId: int
    name: str
    prompt: str
    type: str
    inputDataOpIds: Optional[List[int]] = None
    inputSectionIds: Optional[List[int]] = None


class ReportSectionConfigData(BaseModel):
    reportId: int
    sectionId: int
    dataOp: List[ReportDataOpClass]
    maxWordLen: int
    minWordLen: int
    sectionIntention: str
    textOp: List[ReportTextOpClass]


class TemplateSectionConfigData(BaseModel):
    templateId: int
    sectionId: int
    dataOp: List[TemplateDataOpClass]
    maxWordLen: int
    minWordLen: int
    sectionIntention: str
    textOp: List[TemplateTextOpClass]


class TemplateSectionOperatorsData(BaseModel):
    dataOp: Optional[List[TemplateDataOpClass]]
    textOp: Optional[List[TemplateTextOpClass]]


class GetReportListRequest(BaseModel):
    page: int = 1
    pageSize: int = 10
    searchName: str = ""
    orderByColumn: Optional[str] = "update_at"
    orderByType: Optional[str] = "desc"
    templateId: Optional[int] = None


class GetReportListData(BaseModel):
    total: int
    reportList: List[ReportMetaInfo]


class ExportReportRequest(BaseModel):
    reportId: int


class ExportReportData(BaseModel):
    pdfUrl: str
    wordUrl: str


class DeleteReportRequest(BaseModel):
    reportId: int


class RefreshModelCacheRequest(BaseModel):
    modelName: str


class GetComputeTypeList(BaseModel):
    computeTypeList: List[KeyValue]


class GetTextOperatorTypeList(BaseModel):
    textOperatorTypeList: List[KeyValue]


class GetDataOperatorTypeList(BaseModel):
    dataOperatorTypeList: List[KeyValue]


class GetTimeGranularityList(BaseModel):
    timeGranularityList: List[KeyValue]


class CreateAuthTemplateReportRequest(BaseModel):
    """CreateOrSaveOutlineRequest"""

    dataTimeParams: Optional[DataTimeParams] = None
    reportTitle: str
    reportIntention: str
    creator: str
    department: Optional[str] = None
    province: Optional[str] = None
    reportId: Optional[int] = None
