from typing import Optional, List

from pydantic import BaseModel

from nl2document.common.msg.report_generate_msg import DataTimeParams


class GeneratePPTRequest(BaseModel):
    templateId: int
    timeStr: str
    province: Optional[str] = None


class GeneratePPTResponse(BaseModel):
    pdfUrl: str
    pptUrl: str


class PPTTemplateUnit(BaseModel):
    id: int
    name: str
    thumbnailPath: Optional[str] = None
    previewPath: Optional[str] = None


class PPTTemplateListData(BaseModel):
    pptTemplateList: List[PPTTemplateUnit]


class PPTContentUnit(BaseModel):
    id: int
    type: str
    title: Optional[str] = None
    inputDataOpIds: Optional[List[int]] = None
    subTitles: Optional[List[str]] = None
    inputTextOpIds: Optional[List[int]] = None


class PPTSlide(BaseModel):
    id: int
    slide_master_id: int
    contents: List[PPTContentUnit]


class PPTTemplateData(BaseModel):
    slides: List[PPTSlide]
