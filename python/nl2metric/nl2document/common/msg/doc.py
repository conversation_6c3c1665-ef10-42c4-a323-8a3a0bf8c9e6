from typing import List, Any, Optional, Union
from enum import Enum
from pydantic import BaseModel


class DocumentFilePb(BaseModel):
    id: Optional[int] = None
    folderId: Optional[str] = None
    name: Optional[str] = None
    createdTime: Optional[int] = None
    updatedTime: Optional[int] = None
    mimeType: Optional[str] = None
    size: Optional[int] = None
    thumbnailUrl: Optional[str] = None
    sourceUrl: Optional[str] = None
    overview: Optional[str] = None
    searched: Optional[bool] = None
    fileStatus: Optional[str] = None
    creator: Optional[str] = None
    source: Optional[str] = None
    sceneIdList: Optional[List[str]] = None


class DocumentFolderPb(BaseModel):
    id: Optional[int] = None
    parentFolderId: Optional[int] = None
    name: Optional[str] = None
    createdTime: Optional[int] = None
    updatedTime: Optional[int] = None
    folders: Optional[List[Any]] = None
    files: Optional[List[DocumentFilePb]] = None
    searched: Optional[bool] = None
    folderStatus: Optional[str] = None


class TextNodePb(BaseModel):
    fileId: str = ""
    content: str = ""
    page: int = ""
    folderId: str = ""
    fileName: str = ""
    partName: str = ""
    nodeId: str = ""

    chapter_title: str = ""

    columnIndex: bool = False

    def get_content(self):
        result = ""
        if len(self.fileName) > 0:
            result += f"文件名：{self.fileName}\n"
        if len(self.chapter_title) > 0:
            result += f"章节：{self.chapter_title}\n"
        if len(self.partName) > 0:
            result += f"其他相关信息：{self.partName}\n"
        if len(self.content) > 0:
            result += f"内容：{self.content}\n"
        return result


class ImageNodePb(BaseModel):
    fileId: str
    content: str
    page: int
    url: str
    folderId: str
    fileName: str
    partName: str
    nodeId: str


class NodeWithScorePb(BaseModel):
    score: Optional[float] = None
    textNode: Optional[TextNodePb] = None
    imageNode: Optional[ImageNodePb] = None
    # TODO(bhx)
    # oneof node:
    #   TextNodePb textNode
    #   ImageNodePb imageNode


class FileStatus:
    #
    Pending = 0
    # 文档上传完成
    Ready = 1
    # 文档索引构建完成
    Done = 2
    # 文档索引构建失败
    Fail = 3
    # 文档索引构建中
    INDEX_BUILDING = 4

    Deleted = 5


file_status_name = {0: "Pending", 1: "Ready", 2: "Done", 3: "Fail", 4: "INDEX_BUILDING"}


def get_file_status_name(status: int):
    return file_status_name[status]


class CodeNameUnit(BaseModel):
    code: str
    name: str


class ColumnClassifyPb(BaseModel):
    metrics: List[CodeNameUnit]
    dimensions: List[CodeNameUnit]
    timeDimensions: List[CodeNameUnit]


class GenerateReportRequestInfo(BaseModel):
    fileId: str
    # 长度不大于5
    focusMetrics: List[str]
    focusDimensions: List[str]
    # 毫秒时间戳
    timeRangeStart: str
    # 毫秒时间戳
    timeRangeEnd: str
    timeDimension: str
    userIntent: str
    fileUrl: str
    metrics: List[str]
    dimensions: List[str]
    timeDimensions: List[str]
