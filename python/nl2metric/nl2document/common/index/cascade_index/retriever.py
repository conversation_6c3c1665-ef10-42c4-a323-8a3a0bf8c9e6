import time
from typing import Any, List, Optional

from llama_index.core import VectorStoreIndex, ServiceContext
from llama_index.core.indices.base_retriever import BaseRetriever
from llama_index.core.indices.query.schema import QueryBundle
from llama_index.core.schema import NodeWithScore
from llama_index.core.vector_stores import MetadataFilters
from llama_index.core.vector_stores.types import VectorStoreQuery

from common.logging.logger import get_logger

logger = get_logger(__name__)


class NodeScoreAccumulator:
    def __init__(self, node):
        self.node = node
        self.total_score = 0
        self.count = 0

    def add_score(self, score):
        use_decay = False
        if use_decay:
            self.count += 1
            decay_factor = 1 / (1 + self.count)  # 衰减因子
            self.total_score += score * decay_factor
        else:
            if score > self.total_score:
                self.total_score = score

    def get_score(self):
        return self.total_score

    def get_final_node_with_score(self):
        return NodeWithScore(node=self.node, score=self.get_score())


class CascadeIndexEmbeddingRetriever(BaseRetriever):
    def __init__(
        self,
        index: VectorStoreIndex,
        service_context: ServiceContext,
        similarity_ref_top_k: int = 1,
        node_ids: Optional[List[str]] = None,
        filters: Optional[MetadataFilters] = None,
        name: str = "",
        **kwargs: Any,
    ) -> None:
        """Init params."""
        self._name = name
        self._node_ids = node_ids
        self._index = index
        self._vector_store = self._index._vector_store
        self._service_context = service_context or self._index.service_context
        self._similarity_ref_top_k = similarity_ref_top_k
        self._filters = filters

    def _retrieve(
        self,
        query_bundle: QueryBundle,
    ) -> List[NodeWithScore]:
        return self._retrieve_nodes_sync(query_bundle)

    async def _aretrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
        """异步检索节点。"""
        start = time.time()
        ret = await self._retrieve_nodes_async(query_bundle)
        logger.info(f"retrieve cost time: {time.time() - start}")
        return ret

    def _retrieve_nodes_sync(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
        if self._vector_store.is_embedding_query:
            if query_bundle.embedding is None:
                query_bundle.embedding = (
                    self._service_context.embed_model.get_agg_embedding_from_queries(
                        query_bundle.embedding_strs
                    )
                )
        query = VectorStoreQuery(
            query_embedding=query_bundle.embedding,
            similarity_top_k=self._similarity_ref_top_k,
            node_ids=self._node_ids,
            filters=self._filters,
        )
        query_result = self._vector_store.query(query)
        node_ids = []
        for qn in query_result.nodes:
            parent_node = qn.metadata.get("parent_node")
            if parent_node:
                node_ids.append(parent_node)
            else:
                node_ids.append(qn.node_id)
        if len(node_ids) == 0:
            return []
        parent_nodes = self._vector_store.get_nodes(node_ids=node_ids)
        parent_nodes_dict = {n.node_id: n for n in parent_nodes}
        results = {}
        for index, node in enumerate(query_result.nodes):
            score = query_result.similarities[index]
            parent_node_id = node.metadata.get("parent_node")
            if parent_node_id and parent_node_id in parent_nodes_dict:
                parent_node = parent_nodes_dict[parent_node_id]
            else:
                parent_node = node
                parent_node_id = node.node_id
            if parent_node_id not in results:
                results[parent_node_id] = NodeScoreAccumulator(parent_node)
            results[parent_node_id].add_score(score)

        final_list = [
            accumulator.get_final_node_with_score() for accumulator in results.values()
        ]
        final_list_sorted = sorted(final_list, key=lambda x: x.score, reverse=True)
        return final_list_sorted

    async def _retrieve_nodes_async(
        self, query_bundle: QueryBundle
    ) -> List[NodeWithScore]:
        if self._vector_store.is_embedding_query:
            if query_bundle.embedding is None:
                start = time.time()
                query_bundle.embedding = await self._service_context.embed_model.aget_agg_embedding_from_queries(
                    query_bundle.embedding_strs
                )
                logger.info(f"embedding cost time: {time.time() - start}")
        query = VectorStoreQuery(
            query_embedding=query_bundle.embedding,
            similarity_top_k=self._similarity_ref_top_k,
            node_ids=self._node_ids,
            filters=self._filters,
        )
        start = time.time()
        query_result = await self._vector_store.aquery(query)
        logger.info(f"vector_store query cost time: {time.time() - start}")
        logger.info(f"len of query_result.nodes: {len(query_result.nodes)}")
        node_ids = []
        for qn in query_result.nodes:
            parent_node = qn.metadata.get("parent_node")
            if parent_node:
                node_ids.append(parent_node)
            else:
                node_ids.append(qn.node_id)
        if len(node_ids) == 0:
            return []
        node_ids = list(set(node_ids))
        logger.info(f"len of node_ids: {len(node_ids)}")
        parent_nodes = await self._vector_store.aget_nodes(node_ids=node_ids)
        parent_nodes_dict = {n.node_id: n for n in parent_nodes}
        results = {}
        for index, node in enumerate(query_result.nodes):
            score = query_result.similarities[index]
            parent_node_id = node.metadata.get("parent_node")
            if parent_node_id and parent_node_id in parent_nodes_dict:
                parent_node = parent_nodes_dict[parent_node_id]
            else:
                parent_node = node
                parent_node_id = node.node_id
            if parent_node_id not in results:
                results[parent_node_id] = NodeScoreAccumulator(parent_node)
            results[parent_node_id].add_score(score)

        final_list = [
            accumulator.get_final_node_with_score() for accumulator in results.values()
        ]
        final_list_sorted = sorted(final_list, key=lambda x: x.score, reverse=True)
        return final_list_sorted
