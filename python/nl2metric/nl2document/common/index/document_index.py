import os
import random
import re
from typing import List
from common.fs.fs import get_s3_file_system
from common.logging.logger import get_logger
from nl2document.common.vector.vector_store import get_vector_store
from nl2document.common.index.cascade_index.question import QuestionCascadeIndex
from nl2document.common.index.cascade_index.sentence import SentenceCascadeIndex
from nl2document.common.index.cascade_index.summary import SummaryCascadeIndex

from llama_index.core import (
    VectorStoreIndex,
    StorageContext,
    ServiceContext,
    load_indices_from_storage,
)
from config import doc_config

from nl2document.common.base.const import QUESTION_META_KEY

logger = get_logger(__name__)

askdoc_doc_index_s3_dir = os.path.join(doc_config.ASK_DOC_S3_BUCKET_NAME, "doc-index")
askdoc_doc_index_dir = os.path.join(doc_config.ask_doc_dir, "doc-index")
askdoc_doc_resource_s3_dir = os.path.join(
    doc_config.ASK_DOC_S3_BUCKET_NAME, "doc-resource"
)
askdoc_meta_data_db = os.path.join(doc_config.ask_doc_dir, "meta.sqlite")


class LoadIndexException(Exception):
    pass


class DocumentIndex:
    def __init__(
        self,
        vector_index: VectorStoreIndex = None,
        question_index: QuestionCascadeIndex = None,
        sentence_index: SentenceCascadeIndex = None,
        summary_index: SummaryCascadeIndex = None,
        storage_context: StorageContext = None,
    ):
        self.vector_index = vector_index
        self.question_index = question_index
        self.sentence_index = sentence_index
        self.summary_index = summary_index
        self._storage_context = storage_context

    @classmethod
    def _from_storage_context(
        cls, storage_context: StorageContext, service_context: ServiceContext
    ):
        indices = load_indices_from_storage(
            storage_context, service_context=service_context
        )
        vector_indices = [
            index for index in indices if isinstance(index, VectorStoreIndex)
        ]
        question_indices = [
            index for index in indices if isinstance(index, QuestionCascadeIndex)
        ]
        sentence_indices = [
            index for index in indices if isinstance(index, SentenceCascadeIndex)
        ]
        sentence_indices[0].index_struct.print_ref_ids()
        summary_indices = [
            index for index in indices if isinstance(index, SummaryCascadeIndex)
        ]
        logger.info(
            f"load vector index size {len(vector_indices)}, "
            f" question index size {len(question_indices)}, "
            f" sentence index size {len(sentence_indices)}, "
            f" summary index size {len(summary_indices)}"
        )
        # if not vector_indices:
        #     raise LoadIndexException("VectorIndex not found")
        return DocumentIndex(
            vector_index=vector_indices[0] if vector_indices else None,
            question_index=question_indices[0] if question_indices else None,
            sentence_index=sentence_indices[0] if sentence_indices else None,
            summary_index=summary_indices[0] if summary_indices else None,
            storage_context=storage_context,
        )

    @classmethod
    def from_persist_path(cls, persist_dir: str, service_context: ServiceContext):
        storage_context = StorageContext.from_defaults(
            persist_dir=persist_dir, vector_store=get_vector_store()
        )
        return cls._from_storage_context(
            storage_context, service_context=service_context
        )

    @property
    def docstore(self):
        return self._storage_context.docstore

    @property
    def text_node_ids(self):
        if self.sentence_index != None:
            return list(self.sentence_index.index_struct.node_id_to_ref_node_ids.keys())
        elif self.vector_index != None:
            return list(self.vector_index.index_struct.nodes_dict.keys())
        return None

    @property
    def text_nodes(self):
        return self._storage_context.docstore.get_nodes(self.text_node_ids)

    def get_node(self, node_id: str):
        return self._storage_context.docstore.get_node(node_id)

    def get_suggestions_questions(self, limit=10) -> List[str]:
        ret_questions = []
        text_node_ids = self.text_node_ids
        if not text_node_ids:
            return None
        node_ids = list(text_node_ids)
        random.shuffle(node_ids)
        for id in node_ids:
            n = self.docstore.get_node(id)
            question = n.metadata.get(QUESTION_META_KEY, "")
            questions = question.split("\n")
            questions = [re.sub(r"^\d+\.", "", q).strip() for q in questions if q]
            ret_questions.extend(questions)
            if len(ret_questions) >= limit:
                break
        return ret_questions[:limit]
