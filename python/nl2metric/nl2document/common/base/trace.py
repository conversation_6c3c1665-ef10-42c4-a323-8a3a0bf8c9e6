import functools
import json
import os
from collections import OrderedDict
from dataclasses import dataclass
from datetime import datetime
from typing import Dict, List, Optional

from jinja2 import Environment
from llama_index.core import QueryBundle
from llama_index.core.callbacks import (
    CallbackManager,
    CBEvent,
    CBEventType,
    EventPayload,
    LlamaDebugHandler,
)
from llama_index.core.callbacks.schema import TIMESTAMP_FORMAT
from llama_index.core.indices.base_retriever import BaseRetriever
from llama_index.core.postprocessor.types import BaseNodePostprocessor
from llama_index.core.schema import NodeWithScore, TextNode, QueryType, IndexNode

from common.utils.string_utils import ObjectEncoder
from config import doc_config
from langchain_core.runnables import RunnableLambda
from nl2document.index.retrievers.retrievers import CustomRetriever

EventPayloadComponentName = "component_name"
EventPayloadComponentType = "component_type"
EventPayloadComponentTypeRetriever = "retriever"
EventPayloadComponentTypePostprocessor = "post_processor"
EventPayloadInputNodes = "input_nodes"


class RetrieverTracer(BaseRetriever):
    def __init__(
        self,
        retriever: BaseRetriever,
        name: str = "",
        callback_manager: Optional[CallbackManager] = None,
        object_map: Optional[Dict] = None,
        objects: Optional[List[IndexNode]] = None,
        verbose: bool = False,
    ):
        self.__dict__["_retriever"] = retriever
        self.retriever_name = name or getattr(
            self._retriever, "_name", self._retriever.__class__.__name__
        )
        super().__init__(
            callback_manager=callback_manager,
            object_map=object_map,
            objects=objects,
            verbose=verbose,
        )

    def _retrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
        # trace it with langchain
        chain = RunnableLambda(self._retriever.retrieve, name=self.retriever_name)
        return chain.invoke(query_bundle)

    async def _aretrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
        chain = RunnableLambda(self._retriever.aretrieve, name=self.retriever_name)
        return await chain.ainvoke(query_bundle)

    def __getattr__(self, attr):
        return getattr(self._retriever, attr)

    def __setattr__(self, attr, value):
        setattr(self._retriever, attr, value)


class PostprocessorTracer:
    def __init__(
        self,
        processor: BaseNodePostprocessor,
    ):
        self.__dict__["_postprocessor"] = processor

    def postprocess_nodes(
        self,
        nodes: List[NodeWithScore],
        query_bundle: Optional[QueryBundle] = None,
    ) -> List[NodeWithScore]:
        postprocessor_name = getattr(
            self._postprocessor, "_name", self._postprocessor.__class__.__name__
        )
        # trace it with langchain
        chain = RunnableLambda(
            lambda x: self._postprocessor.postprocess_nodes(
                nodes=x["nodes"], query_bundle=x["query_bundle"]
            ),
            name=postprocessor_name,
        )
        return chain.invoke({"nodes": nodes, "query_bundle": query_bundle})

    def __getattr__(self, attr):
        return getattr(self._postprocessor, attr)

    def __setattr__(self, attr, value):
        setattr(self._postprocessor, attr, value)


@dataclass
class DebugInfoTime:
    start_time: datetime
    end_time: datetime

    @classmethod
    def from_events(cls, events: List[CBEvent]):
        times = [datetime.strptime(e.time, TIMESTAMP_FORMAT) for e in events]
        times = sorted(times)
        return cls(times[0], times[-1])

    def format(self):
        return "%s ~ %s" % (
            self.start_time.strftime("%Y-%m-%dT%H:%M:%S.%f"),
            self.end_time.strftime("%Y-%m-%dT%H:%M:%S.%f"),
        )


@dataclass
class DebugInfoNode:
    id: str
    text: str
    metadata: Dict[str, str]
    parent: str
    children: List[str]
    type: str
    score: float

    @classmethod
    def from_node(cls, node_score: NodeWithScore):
        node = node_score.node
        score = node_score.score
        return cls(
            id=node.node_id,
            text=node.get_text() if isinstance(node, TextNode) else "",
            metadata={k: str(v) for k, v in node.metadata.items()},
            parent=node.parent_node.node_id if node.parent_node else -1,
            children=[n.node_id for n in node.child_nodes] if node.child_nodes else [],
            type=node.__class__.__name__,
            score=score,
        )

    @classmethod
    def from_nodes(cls, nodes: List[NodeWithScore]) -> List:
        return [cls.from_node(n) for n in nodes]


@dataclass
class DebugInfoQuery:
    query: str
    response: str
    time: DebugInfoTime


@dataclass
class DebugInfoRetriever:
    name: str
    output_nodes: List[DebugInfoNode]
    query: str
    time: DebugInfoTime


@dataclass
class DebugInfoPostProcessor:
    name: str
    input_nodes: List[DebugInfoNode]
    output_nodes: List[DebugInfoNode]
    time: DebugInfoTime


@dataclass
class DebugInfoSynthesize:
    query: str
    response: str
    time: DebugInfoTime


def find_payload_in_events(events: List[CBEvent], key: str):
    for event in events:
        if not event.payload:
            continue
        if key not in event.payload:
            continue
        return event.payload[key]


@dataclass
class DebugInfo:
    queries: List[DebugInfoQuery]
    retrievers: List[DebugInfoRetriever]
    postprocessors: List[DebugInfoPostProcessor]
    synthesizes: List[DebugInfoSynthesize]

    def __init__(self, callback_manager: Optional[CallbackManager]):
        self.queries = []
        self.retrievers = []
        self.postprocessors = []
        self.synthesizes = []
        if not callback_manager:
            return

        handlers = callback_manager.handlers
        debugger = next(
            filter(lambda h: isinstance(h, LlamaDebugHandler), handlers), None
        )
        all_events: List[CBEvent] = debugger.get_events()
        event_map = OrderedDict()
        for event in all_events:
            events: List[CBEvent] = event_map.get(event.id_)
            if not events:
                events = []
                event_map[event.id_] = events
            events.append(event)
        for events in event_map.values():
            first = events[0]
            if first.event_type == CBEventType.RETRIEVE:
                component_type = first.payload.get(EventPayloadComponentType)
                if component_type == EventPayloadComponentTypeRetriever:
                    self.retrievers.append(self._create_retriever(events))
                elif component_type == EventPayloadComponentTypePostprocessor:
                    self.postprocessors.append(self._create_postprocessor(events))
                if not self.queries:
                    self.queries.append(self._create_query(events))
            elif first.event_type == CBEventType.SYNTHESIZE:
                self.synthesizes.append(self._create_synthesize(events))

    def _create_postprocessor(self, events: List[CBEvent]) -> DebugInfoPostProcessor:
        name = find_payload_in_events(events, EventPayloadComponentName)
        input_nodes = find_payload_in_events(events, EventPayloadInputNodes)
        nodes = find_payload_in_events(events, EventPayload.NODES)
        return DebugInfoPostProcessor(
            name,
            DebugInfoNode.from_nodes(input_nodes),
            DebugInfoNode.from_nodes(nodes),
            DebugInfoTime.from_events(events),
        )

    def _create_retriever(self, events: List[CBEvent]) -> DebugInfoRetriever:
        name = find_payload_in_events(events, EventPayloadComponentName)
        nodes = find_payload_in_events(events, EventPayload.NODES)
        query = find_payload_in_events(events, EventPayload.QUERY_STR)
        return DebugInfoRetriever(
            name,
            DebugInfoNode.from_nodes(nodes),
            query,
            DebugInfoTime.from_events(events),
        )

    def _create_synthesize(self, events: List[CBEvent]) -> DebugInfoSynthesize:
        query = find_payload_in_events(events, EventPayload.QUERY_STR)
        response = find_payload_in_events(events, EventPayload.RESPONSE).response
        return DebugInfoSynthesize(query, response, DebugInfoTime.from_events(events))

    def _create_query(self, events: List[CBEvent]) -> DebugInfoQuery:
        query = find_payload_in_events(events, EventPayload.QUERY_STR)
        return DebugInfoQuery(query, "", DebugInfoTime.from_events(events))

    def _to_json(self) -> str:
        return json.dumps(self, cls=ObjectEncoder, ensure_ascii=False, indent=2)

    def _to_html(self) -> str:
        with open(
            os.path.join(os.path.dirname(__file__), "trace_template.html"),
            encoding="utf-8",
        ) as f:
            environment = Environment()
            environment.filters["tojson"] = lambda x: json.dumps(
                x, cls=ObjectEncoder, ensure_ascii=False, indent=2
            )
            tpl = f.read()
            t = environment.from_string(tpl)
            ret = t.render(
                query=self.queries[0],
                retrievers=[
                    r for r in self.retrievers if r.name != CustomRetriever.__name__
                ]
                + [r for r in self.retrievers if r.name == CustomRetriever.__name__],
                post_processors=self.postprocessors,
            )
            return ret

    def dump(self):
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        dir = os.path.join(doc_config.ask_doc_dir, "output/chat_traces")
        if not os.path.exists(dir):
            os.makedirs(dir, exist_ok=True)
        json_file = os.path.join(
            dir, f"{timestamp}_{self._get_query().replace('/', '_')}.json"
        )
        html_file = os.path.join(
            dir, f"{timestamp}_{self._get_query().replace('/', '_')}.html"
        )
        with open(json_file, "w", encoding="utf-8") as f:
            f.write(self._to_json())
        with open(html_file, "w", encoding="utf-8") as f:
            f.write(self._to_html())

    def _get_query(self):
        if self.queries:
            return self.queries[0].query
        return "empty"
