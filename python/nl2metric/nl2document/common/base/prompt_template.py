# TODO(chenying): switch between En and Chs
# TODO(chenying): get the refs that is actually relevant, like pdfGPT
# TODO(bhx): mv to prompt selector

# CHS_CONDENSE_QUESTION_TMPL
"""\
我们首先会给出Human与Assistant之间的聊天记录，然后会给出一个Human提的问题。\
请根据聊天记录中的相关内容，抽取Human的问题的关键词，并总结重写为一个独立问题。当Human使用全英文提问时，请将Human提的问题总结重写为一个英文的独立问题。当Human的问题中存在中文时，请将Human提的问题总结重写为一个中文的独立问题\
请注意：识别Human问题的语言，如果问题中存在中文时，在重写后的问题中说明"请问中文回答以下问题"。\
请注意：识别Human问题的语言，如果问题为全英文，在重写后的问题中说明"please answer in english"。\
举例：
<Human提的问题> 文档中有没有"权利和免责声明"相关信息
<重写后的独立问题> 请问中文回答以下问题:权利和免责声明是什么
<Human提的问题> what is "权利和免责声明"?
<重写后的独立问题> 请问中文回答以下问题:权利和免责声明是什么
<Human提的问题> 追索业务处理分几种情况？
<重写后的独立问题> 请问中文回答以下问题:追索业务处理分为哪些？
<Human提的问题> 什么情况下可以撤票？
<重写后的独立问题> 请问中文回答以下问题:什么情况下可以办理票据的未用退回？
<Human提的问题> 被追索人不同意清偿时如何处理？
<重写后的独立问题> 请问中文回答以下问题:被追索人拒绝清偿的流程是什么？
<Human提的问题> How to sign up a bill account
<重写后的独立问题> please answer in english:What is the signing process for a bill account
<Human提的问题> 如何签约票据account
<重写后的独立问题> 请问中文回答以下问题:票据账户的签约流程是什么？
<Human提的问题> appendix是什么
<重写后的独立问题> 请问中文回答以下问题:附录是什么，附录的内容有哪些
<Human提的问题> 如何开通电票业务
<重写后的独立问题> 请问中文回答以下问题:如何开通电子汇票业务
<Human提的问题> How to open electronic bill business at Hankou Bank?
<重写后的独立问题> please answer in english:How to open electronic bill business at Hankou Bank?
<Human提的问题> 什么是电子汇票？
<重写后的独立问题> 请问中文回答以下问题:什么是电子商业汇票？

<聊天记录> 
{chat_history}

<Human提的问题>
{question}

<重写后的独立问题>
"""

CHS_CONDENSE_QUESTION_TMPL = """\
我们首先会给出Human与Assistant之间的聊天记录，然后会给出一个Human提的问题。
请根据聊天记录中的相关内容，将Human提的问题重写为一个中文的独立问题。
请注意，重写后的问题需为中文。

<聊天记录> 
{chat_history}

<Human提的问题>
{question}

<重写后的中文独立问题>
"""


CH_TEXT_QA_PROMPT_TMPL = (
    "上下文信息如下.\n"
    "---------------------\n"
    "{context_str}\n"
    "---------------------\n"
    "请注意：在回答时仅依据给出的上下文即可，不要依据你已有的知识来回答。\n"
    "请注意：在回答时仅依据给出的上下文即可，不要依据你已有的知识来回答。\n"
    "请注意：在回答时仅依据给出的上下文即可，不要依据你已有的知识来回答。\n"
    "如果你对答案不自信的话，请回答我不知道。\n"
    "请用中文回答以下问题: {query_str}\n"
)


EN_SUGGESSTION_TEMPLATE = """\
我们会给出Human与Assistant之间的聊天记录，\
请根据聊天记录中的相关内容，生成{suggesstions_num}个Human可能会问的问题。\
请注意:
1. 生成的问题需为中文
2. 生成的问题需以问号结束
3. 仅生成问题即可，不需要回答问题

<聊天记录> 
{chat_history}

<Human可能会问的问题>
"""


CH_SUMMARY_EXTRACT_TEMPLATE = """\
请总结以下文档，并尽可能多的包含待总结的文档中的关键信息。
请注意：在回答时仅依据给出的内容即可，不要依据你已有的知识。
请注意：在回答时仅依据给出的内容即可，不要依据你已有的知识。
请注意：在回答时仅依据给出的内容即可，不要依据你已有的知识。

<待总结的文档>
{context_str}


<上述文档的总结>"""

# without "to which are unlikely to be found elsewhere", the question generated might be really common
CH_QUESTION_EXTRACT_TEMPLATE = """\
基于给出的文档，请生成{num_questions}个该文档可以回答的问题。
请注意：生成的问题要针对文档的具体内容，且只有该文档可以解答。

<给出的文档>
{context_str}

<生成的问题列表>"""

CH_KEYWORD_EXTRACT_TEMPLATE = """\
基于给出的文档，请找出{keywords_num}个关键词。
请注意：在回答时仅依据给出的内容即可，不要依据你已有的知识。关键词返回务必使用逗号分隔，不要携带序号，不要携带序号，不要携带序号。
请注意：在回答时仅依据给出的内容即可，不要依据你已有的知识。关键词返回务必使用逗号分隔，不要携带序号，不要携带序号，不要携带序号。
请注意：在回答时仅依据给出的内容即可，不要依据你已有的知识。关键词返回务必使用逗号分隔，不要携带序号，不要携带序号，不要携带序号。

<给出的文档>
{context_str}

<上述文档的关键词>"""

CH_CHOICE_SELECT_PROMPT_TMPL = (
    "以下显示了一系列文档。每个文档旁边都有一个数字，"
    "以及文档的摘要。也提供了一个问题。\n"
    "请回答你认为应该参考哪些文档来回答这个问题，按照相关性的顺序，\n"
    "并给出相关性的评分。相关性评分是一个1-10的数字，"
    "根据你认为文档对问题的相关性进行评定。\n"
    "不要包含任何与问题无关的文档。\n"
    "示例格式：\n"
    "Document 1:\n<文档1的摘要>\n\n"
    "Document 2:\n<文档2的摘要>\n\n"
    "...\n\n"
    "Document 10:\n<文档10的摘要>\n\n"
    "问题：<问题>\n"
    "答案：\n"
    "Doc: 9, Relevance: 7\n"
    "Doc: 3, Relevance: 4\n"
    "Doc: 7, Relevance: 3\n\n"
    "现在我们试试：\n\n"
    "{context_str}\n"
    "问题：{query_str}\n"
    "答案：\n"
)

CH_TREE_SUMMARIZE_TMPL = (
    "假设你是一个AI会议助手，你的任务是根据召回的会议记录内容，回答用户的一个查询问题。以下是召回的会议记录内容：\n"
    "---------------------\n"
    "{context_str}\n"
    "---------------------\n"
    # "回答过程：\n1.请你判断会议内容是否能回答问题，如果可以，请总结会议内容并给出答案。注意，直接给出答案即可。不要使用例如“答案：”等前置词。\n2.在会议内容跟问题无关的情况下，请以“未找到相关会议内容，以下是基于大模型自身理解回答的内容：”开头，围绕问题中的相关概念进行适度扩展解释。\n"
    "回答过程：\n1.请你判断会议内容是否能回答问题，如果可以，请总结会议内容并给出答案。注意，直接给出答案即可。不要使用例如“答案：”等前置词。\n"
    "---------------------\n"
    "现在请回答下列问题，如果有重复内容请过滤。\n"
    "问题：{query_str}\n"
    "答案："
)

WITHOUT_RETRIVAL_CONTENT_TMPL = (
    "你是一个通用的百科助手，能回答任何问题。\n"
    "现在请你回答这个问题：{query_str}\n"
    # "请以“未找到相关会议内容，以下是基于大模型自身理解回答的内容：”开头，基于你的内生知识给出完整的回答。\n"
)

BJ_TELECOM_SUMMARIZE_TMPL = (
    "假设你是一个AI助手，你的任务是理解用户的一个问题，从我给出的信息片段中提取能回答用户问题的答案返回给用户。\n"
    "---------------------\n"
    "用户问题是：{query_str}\n"
    "---------------------\n"
    "以下是信息片段：\n"
    "{context_str}\n"
    "---------------------\n"
    "请根据上述用户问题和信息片段，生成一个连贯、准确且全面的答案。确保答案简洁明了直接回应用户的问题，并且包含所有相关信息片段中的关键信息。答案应该清晰、简洁，并且易于理解。请直接输出你的答案即可。\n"
    "** 注意事项： **\n"
    "- 产品的区分：可依据文件名来进行判别\n"
    "- 若用户问题明确与某一特定产品相关，则回答应聚焦于该产品，剔除其他不相关产品（依据文件名来进行判别）的内容。\n"
    "- 倘若用户问题未提及具体的某种产品，回答时需按照不同产品（依据文件名来进行判别）分类分别进行阐述。\n"
    "- 请你从信息片段中提炼出能回答用户问题的内容，总结概括并给出你的答案。\n"
    "- 不同信息片段可能存在重复信息，请甄别重复信息并去重，不要让重复信息出现在你的答案里。\n"
)

# askbot common
ASKBOT_COMMON_CH_TREE_SUMMARIZE_TMPL = (
    "以下是来自多个来源的上下文信息。\n"
    "---------------------\n"
    "{context_str}\n"
    "---------------------\n"
    "根据来自多个来源的信息，而非先前的知识，回答尽量完整，不要遗漏列表内容\n"
    "回答下列查询。\n"
    "查询：{query_str}\n"
    "答案："
)

ASKBOT_THEME_TREE_SUMMARIZE_TMPL = (
    "以下是来自多个来源的上下文信息。\n"
    "---------------------\n"
    "{context_str}\n"
    "---------------------\n"
    "根据提供的上下文信息，而非先前的知识，提取与以下文档相关的主要主题和关键词。要求：\n"
    "1. 严格基于文本内容，不依赖外部知识\n"
    "2. 使用项目符号列表格式呈现\n"
    "3. 包含所有重要内容，不遗漏关键点\n"
    "4. 主题需概括中心思想，关键词需精准匹配原文\n"
    "5. 若存在多个相关领域，需分类整理\n"
    "要求：{query_str}\n"
    "答案：\n\n"
)


CONDENSE_TMPL = """\
我们首先会给出Human与Assistant之间的上一轮聊天记录，然后会给出一个Human新提出的问题。
请根据聊天记录中的相关内容，将Human新提出的问题重写为一个中文的独立问题。
请注意，重写后的问题需为中文。
以下是你需要分析的几部分信息：
1. [上一轮Human提问]
2. [上一轮Assistant答案]
3. [新上传的文件名称]，该文件是上一轮对话结束之后，本轮对话开始之前Human上传的文件，多个文件用"，"分隔。如果Human新提出的问题中涉及指代信息涉及到该文件，请在重写独立问题时，把文件名称信息包含进去。如果该信息为空，说明用户没有上传文件。
4. [Human新提出的问题]
注意，当新上传的文件名称有多个（例如"A.txt， B.docx"，），并且Human新提出的问题中有明确指代这两个文件（例如"帮我总结一下这两个文件都说了什么"），你需要在新的问题中做指代消除（例如"帮我总结一下"A"和"B"两个文档分别都说了什么"）。
如果你认为本轮提问和上一轮历史对话信息不相关，说明Human新提出的问题为独立问题，不需要做改写，请原封不动地输出Human新提出的问题即可。

以下是几个示例：
case1
[上一轮Human提问]: 
[上一轮Assistant答案]: 
[新上传的文件名称]: 智能问答设计方案.pdf
[Human新提出的问题]: aaa
说明: 上一轮的历史对话为空，说明这是第一次对话。aaa和是无法理解的合法问题，和上传的文件无关，所以不需要重写问题。直接输出"aaa"

case2
[上一轮Human提问]: 
[上一轮Assistant答案]: 
[新上传的文件名称]: 智能问答设计方案.pdf
[Human新提出的问题]: 这个文档中都提到了哪些设计方案？
说明: 新提出问题中的"这个"指代新上传的文件，所以重写问题为"智能问答设计方案.pdf中都提到了哪些设计方案？"

case3:
[上一轮Human提问]: 需求评审会议中都有哪些todo事项？
[上一轮Assistant答案]: 以下是该会议中提到的代办事项： 1. 明确需求 2. 明确开发周期 3. 确定预算
[新上传的文件名称]: 
[Human新提出的问题]: 详细展开以下这三点代办事项
说明: 新提出问题中的"这三点"指代上一轮Assistant答案中的三点代办事项，所以重写问题为"详细展开说以下需求评审会议中提到的明确需求、明确开发周期、确定预算这三点代办事项"

case4:
[上一轮Human提问]: a a a
[上一轮Assistant答案]: 会议内容中没有找到相关信息。3-王雪刚-计算机科学与技术.pdf: 在2021年9月至2024年6月期间，该学生主修数据结构、程序设计、数据库系统概论等课程，并掌握了Python、Java、C语言等多种编程语言及深度学习框架。
[新上传的文件名称]: 3-王雪刚-计算机科学与技术.pdf
[Human新提出的问题]: 123897879**！@&#&%
说明: Human新提出的问题和上一轮的对话历史不相关，所以不需要重写问题。输出"123897879**！@&#&%"

请你通过示例学习如何进行问题改写，并根据以下对话历史对新问题进行改写。
<聊天记录> 
[上一轮Human提问]: <pre_query>
[上一轮Assistant答案]: <pre_answer>
[新上传的文件名称]: <file_name>
[Human新提出的问题]: <cur_question>

请你讲Human新提出的问题重写为一个新的提问，并按照以下json格式返回你的结果：
```json
{"query": <你的答案>}
```
"""

SIMILARITY_TMPL = """
###请你基于用户的一个提问，给根据该问题召回到的相关信息片段打一个相似度评分。
###评分标准如下：
    - 0分：无法从信息片段中提取出任何信息来回答用户问题，信息片段和用户问题完全无关。
    - 1分：信息片段包含少量和用户问题相关的信息，但是单依赖该信息片段的信息无法完整回答用户问题。
    - 2分：信息片段可以直接回答用户问题，无需其他额外信息补充。

###这是召回到的信息片段：
<node>

###这是用户问题：<question>

###现在请你根据评分标准，对召回的信息片段和用户问题进行相似度打分，并按照以下json格式返回你的答案：
```json
{"score": <你的分数>}
```
"""
