import hashlib
import json
import os
import re
import string
from datetime import datetime
from typing import Type
from llama_index.core.data_structs.data_structs import IndexStruct
from llama_index.core.data_structs.registry import (
    INDEX_STRUCT_TYPE_TO_INDEX_STRUCT_CLASS,
)

from llama_index.core.prompts.base import Prompt
from llama_index.core.response_synthesizers.factory import get_response_synthesizer

from nl2document.common.base.prompt_template import CH_TEXT_QA_PROMPT_TMPL
from common.logging.logger import get_logger
from config import app_config
from llama_index.core.indices.registry import INDEX_STRUCT_TYPE_TO_INDEX_CLASS
from llama_index.core.indices.base import BaseIndex

en_punctuation_string = string.punctuation
from zhon.hanzi import punctuation as ch_punctuation_string

logger = get_logger(__name__)


def get_md5(file_path):
    if not os.path.isfile(file_path):
        raise RuntimeError("{} is not a file".format(file_path))
    md5 = hashlib.md5()
    with open(file_path, "rb") as f:
        while True:
            data = f.read(1024)
            if not data:
                break
            md5.update(data)
    return md5.hexdigest()


def get_index_summary(folderid, docid, index):
    response_synthesizer = get_response_synthesizer(
        text_qa_template=Prompt(CH_TEXT_QA_PROMPT_TMPL),
    )
    engine = index.as_query_engine(
        response_mode="tree_summarize",
        verbose=True,
        similarity_top_k=app_config.ask_doc_similarity_top_k,
        response_synthesizer=response_synthesizer,
    )
    resp = engine.query("请总结本文档")
    if resp.response is None:
        raise RuntimeError(
            "folder {} doc {} faild to get summary".format(folderid, docid)
        )
    summary = resp.response.strip()
    if len(summary) == 0:
        raise RuntimeError(
            "folder {} doc {} faild to get summary".format(folderid, docid)
        )
    logger.info(
        "folder {} doc {} get summary succeed: {}".format(folderid, docid, summary)
    )
    return summary


def get_summary(service_context, summary_prompt, summarys):
    if len(summarys) == 0:
        raise RuntimeError("got zero summaries to summarize")
    elif len(summarys) == 1:
        return summarys[0]
    truncated_summaries = service_context.prompt_helper.truncate(
        prompt=summary_prompt, text_chunks=summarys
    )
    text_chunk = "\n".join(truncated_summaries)
    new_summary = service_context.llm_predictor.predict(
        summary_prompt, context_str=text_chunk
    )
    return new_summary


def rm_en_punctuation(raw_str: str) -> str:
    # rm en punctuation
    return re.sub("[{}]".format(en_punctuation_string), "", raw_str)


def rm_ch_punctuation(raw_str: str) -> str:
    # rm ch punctuation
    return re.sub("[{}]".format(ch_punctuation_string), "", raw_str)


def rm_blank(raw_str: str) -> str:
    # rm blank
    return re.sub("[\s]", "", raw_str)


def rm_num(raw_str: str) -> str:
    # rm blank
    return re.sub("[0-9]", "", raw_str)


def rm_seq(raw_str: str) -> str:
    # rm pattern <seq blank> or <seq dot>
    return re.sub(r"^\d+\.|\d+\s", "", raw_str)


def simplify_by_rm_combo(
    raw_str: str,
    rm_seq_enable=True,
    rm_en_punctuation_enable=True,
    rm_ch_punctuation_enable=True,
    rm_blank_enable=True,
) -> str:
    res = raw_str
    # 0. rm seq on prefix
    if rm_seq_enable:
        res = rm_seq(res)
    # 1.rm en punctuation
    if rm_en_punctuation_enable:
        res = rm_en_punctuation(res)
    # 2.rm ch punctuation
    if rm_ch_punctuation_enable:
        res = rm_ch_punctuation(res)
    # 3.rm blank
    if rm_blank_enable:
        res = rm_blank(res)
    return res


def keywords_split(raw_keywords_text: str, separator="，|,|\n") -> list:
    keywords = re.split(separator, raw_keywords_text)
    return list(filter(None, [simplify_by_rm_combo(i) for i in keywords if i]))


class ObjectEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.strftime("%Y-%m-%d %H:%M:%S")
        return obj.__dict__


def register_index_type(index_struct_type_name: str, index_type: Type[BaseIndex]):
    INDEX_STRUCT_TYPE_TO_INDEX_CLASS[index_struct_type_name] = index_type


def register_index_struct_type(
    index_struct_type_name: str, index_struct_type: Type[IndexStruct]
):
    INDEX_STRUCT_TYPE_TO_INDEX_STRUCT_CLASS[index_struct_type_name] = index_struct_type


def delete_number(title: str) -> str:
    if re.match(r"^[0-9]", title):
        title = re.sub(
            "^[(（]?[0-9]+[.、-]?[0-9]*[.-]?[0-9]*[.-]?[0-9]*[.-]?[0-9]*[.-]?[0-9]*\.?[)）]?",
            "",
            title,
        ).strip()
    return title
