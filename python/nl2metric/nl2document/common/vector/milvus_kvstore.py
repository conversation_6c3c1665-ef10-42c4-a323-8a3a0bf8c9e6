import json
from typing import List, Tu<PERSON>, Dict, Optional

from llama_index.core.storage.docstore.keyval_docstore import (
    KVDocumentStore,
    DEFAULT_NAMESPACE,
)
from llama_index.core.storage.kvstore.types import BaseKVStore
from llama_index.core.utils import iter_batch
from pymilvus import DataType, MilvusClient

DEFAULT_VEC = [0.1, 0.2]
DEFAULT_COLLECTION = "MilvusKVStore"
DEFAULT_BATCH_SIZE = 200


class MilvusKVStore(BaseKVStore):
    def __init__(self, uri: str, token: str = "", overwrite: bool = False, **kwargs):
        super().__init__()
        self._milvusclient = MilvusClient(
            uri=uri,
            token=token,
            **kwargs,  # pass additional arguments such as server_pem_path
        )
        self.overwrite = overwrite

    @property
    def client(self):
        return self._milvusclient

    def create_collection_if_not_exists(self, collection_name):
        # Check if the collection exists and create it if not
        if collection_name in self.client.list_collections():
            if self.overwrite:
                self._milvusclient.drop_collection(collection_name)
            else:
                return
        schema = MilvusClient.create_schema(
            auto_id=False,
        )
        schema.add_field(
            field_name="key", datatype=DataType.VARCHAR, max_length=64, is_primary=True
        )
        schema.add_field(field_name="vector", datatype=DataType.FLOAT_VECTOR, dim=2)
        schema.add_field(
            field_name="value", datatype=DataType.JSON, description="Value field"
        ),

        index_params = MilvusClient.prepare_index_params()
        index_params.add_index(
            field_name="vector",
            metric_type="COSINE",
            index_type="FLAT",
            index_name="vector_index",
            params={"nlist": 128},
        )
        index_params.add_index(
            field_name="key",  # Name of the scalar field to be indexed
            index_type="",  # Type of index to be created. For auto indexing, leave it empty or omit this parameter.
            index_name="primary_index",  # Name of the index to be created
        )
        self._milvusclient.create_collection(
            collection_name=collection_name,
            schema=schema,
            index_params=index_params,
            consistency_level="Strong",
        )

    def put(self, key: str, val: dict, collection: str = DEFAULT_COLLECTION) -> None:
        self._milvusclient.upsert(
            collection, [{"key": key, "value": json.dumps(val), "vector": DEFAULT_VEC}]
        )

    async def aput(
        self, key: str, val: dict, collection: str = DEFAULT_COLLECTION
    ) -> None:
        self.put(key, val, collection=collection)

    def put_all(
        self,
        kv_pairs: List[Tuple[str, dict]],
        collection: str = DEFAULT_COLLECTION,
        batch_size: int = DEFAULT_BATCH_SIZE,
    ) -> None:
        entities = [
            {"key": k, "value": json.dumps(v), "vector": DEFAULT_VEC}
            for k, v in kv_pairs
        ]
        for insert_batch in iter_batch(entities, batch_size):
            self._milvusclient.insert(collection_name=collection, data=insert_batch)

    async def aput_all(
        self,
        kv_pairs: List[Tuple[str, dict]],
        collection: str = DEFAULT_COLLECTION,
        batch_size: int = DEFAULT_BATCH_SIZE,
    ) -> None:
        return self.put_all(kv_pairs, collection=collection, batch_size=batch_size)

    def get(self, key: str, collection: str = DEFAULT_COLLECTION) -> Optional[dict]:
        result = self._milvusclient.query(
            collection, filter=f'key == "{key}"', output_fields=["value"]
        )
        if result:
            return json.loads(result[0]["value"])
        return None

    async def aget(
        self, key: str, collection: str = DEFAULT_COLLECTION
    ) -> Optional[dict]:
        return self.get(key, collection=collection)

    def get_all(self, collection: str = DEFAULT_COLLECTION) -> Dict[str, dict]:
        results = self._milvusclient.query(collection, output_fields=["key", "value"])
        return {r["key"]: json.loads(r["value"]) for r in results}

    async def aget_all(self, collection: str = DEFAULT_COLLECTION) -> Dict[str, dict]:
        return self.get_all(collection=collection)

    def delete(self, key: str, collection: str = DEFAULT_COLLECTION) -> bool:
        status = self._milvusclient.delete(collection, filter=f'key == "{key}"')
        return status.get("delete_count") > 0

    async def adelete(self, key: str, collection: str = DEFAULT_COLLECTION) -> bool:
        return self.delete(key, collection=collection)


class MilvusDocumentStore(KVDocumentStore):
    def __init__(
        self,
        kvstore: MilvusKVStore,
        namespace: Optional[str] = DEFAULT_NAMESPACE,
        batch_size: int = DEFAULT_BATCH_SIZE,
        node_collection_suffix: Optional[str] = "_data",
        ref_doc_collection_suffix: Optional[str] = "_ref_doc_info",
        metadata_collection_suffix: Optional[str] = "_metadata",
    ) -> None:
        super().__init__(
            kvstore,
            namespace=namespace,
            batch_size=batch_size,
            node_collection_suffix=node_collection_suffix,
            ref_doc_collection_suffix=ref_doc_collection_suffix,
            metadata_collection_suffix=metadata_collection_suffix,
        )
        kvstore.create_collection_if_not_exists(f"{namespace}{node_collection_suffix}")
        kvstore.create_collection_if_not_exists(
            f"{namespace}{ref_doc_collection_suffix}"
        )
        kvstore.create_collection_if_not_exists(
            f"{namespace}{metadata_collection_suffix}"
        )


if __name__ == "__main__":
    milvus_uri = "http://192.168.110.27:31870"

    # 创建MilvusKVStore实例
    kv_store = MilvusKVStore(uri=milvus_uri)

    # 测试put和get方法
    key = "test_key"
    val = {"test_value": "hello world"}

    # 存储键值对
    kv_store.put(key, val)

    # 读取键值对
    retrieved_val = kv_store.get(key)

    # 输出结果
    print(f"Retrieved value: {retrieved_val}")

    # 清理：删除测试键
    kv_store.delete(key)
