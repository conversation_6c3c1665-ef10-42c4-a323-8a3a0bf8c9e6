from time import sleep

from common.logging.logger import get_logger
from config import doc_config
from nl2document.builder.index_builder import IndexBuilderBase
from nl2document.common.models.base_model import get_need_build_index_documents

logger = get_logger(__name__)


class TaskManager:
    """
    1. 获取所有状态为待处理的文件夹列表（1: 待处理, 2: 已处理）
    2. 获取文件夹下所有待处理的文件
    3. 基于文件夹+文件列表开始构建索引
    """

    def __init__(self, index_builder: IndexBuilderBase = IndexBuilderBase()):
        self.index_builder = index_builder

    def _run_build_tasks_document(self):
        doc_list = get_need_build_index_documents()
        if len(doc_list) != 0:
            logger.info(f"doc_list: {doc_list}")
            self.index_builder.exec_documents_task(list(doc_list))

    # 定时执行
    def run(self):
        logger.info("doc builder task manager start")
        while True:
            try:
                self._run_build_tasks_document()
                sleep(doc_config.doc_builder_loop_gap)
            except Exception as e:
                print(e)
