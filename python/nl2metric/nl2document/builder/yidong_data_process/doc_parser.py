import re
import os
from docx import Document
from docx.oxml.text.paragraph import CT_P
from docx.text.paragraph import Paragraph
import json
from datetime import datetime, timedelta


# 会议文档doc解析
class DocParser:
    def parse(
        self, path, platform: str = "", output_path: str = None
    ):  # platform 标记(maxhub, web)
        file_name = os.path.basename(path)
        if not path.lower().endswith(".docx") and not path.lower().endswith(".doc"):
            raise RuntimeError("{} not docx document".format(file_name))
        if platform == "suirui":
            print("app会议")
            doc_result = get_app_conference_result(path)
        elif platform == "maxhub":
            print("大屏")
            doc_result = get_daping_conference_result(path)
        else:
            return None

        if output_path:
            parse_output_file = os.path.join(
                output_path, f"{doc_result['file_name']}.json"
            )
            with open(parse_output_file, "w") as f:
                json.dump(doc_result, f, indent=4, ensure_ascii=False)
            print("parse_output_dir path is >>> ", parse_output_file)
        return doc_result


def get_seconds(accumulated_time: str) -> int:
    hour_minute_second = accumulated_time.split(":")
    seconds = 0
    for index, time in enumerate(hour_minute_second):
        if index < len(hour_minute_second) - 1:
            seconds += 60 * (seconds + int(time))
        else:
            seconds += int(time)
    return seconds


def get_daping_conference_result(path: str = "") -> dict:
    doc_result = {}
    file_name = os.path.basename(path)
    doc = Document(path)
    conference_theme_time_date_time = ""
    doc_result["file_name"] = file_name
    conference_theme = ""
    conference_promoter = ""
    conference_start_time = ""
    conference_over_accumulated_time = ""
    conference_over_time = ""
    conference_persons = ""
    conference_records = []
    conference_record = {}
    conference_summarys = []
    conference_summary = {}
    chapters = []
    chapter_start_time = ""
    chapter_end_time = ""
    chapter = {}
    conference_to_deal = ""
    content_type = ""  # conference_record 转写记录 conference_summary 会议纪要 conference_to_deal 会议待办 chapters 章节 conference_summary_speaker 发言人总结

    for block in doc.element.body:
        if isinstance(block, CT_P):
            p = Paragraph(block, doc)
            if p.text.strip():
                line = p.text.strip()
                if re.match(r"^[一二三四五六七八九十]{1,3}、原文", line):
                    content_type = "conference_record"
                    continue
                if re.match(r"^[一二三四五六七八九十]{1,3}、纪要", line) or re.match(r"^会议纪要", line):
                    content_type = "conference_summary"
                    continue
                if re.match(r"^[一二三四五六七八九十]{1,3}、章节", line) or re.match(r"^章节", line):
                    content_type = "chapters"
                    continue
                if re.match(r"^[一二三四五六七八九十]{1,3}、待办", line) or re.match(r"^会议待办", line):
                    content_type = "conference_to_deal"
                    continue
                if re.match(r"^发言人总结", line):
                    content_type = "conference_summary_speaker"
                    continue
                if not content_type:
                    conference_theme = line
                if content_type == "conference_record":
                    record_txts = line.split("\n")
                    for record_txt in record_txts:
                        if re.match(r"\d{1,2}[:：]\d{2}", record_txt):
                            time = record_txt.strip().replace("：", ":")
                            if conference_record:
                                conference_record["end_time"] = time
                                conference_records.append(conference_record)
                                conference_over_accumulated_time = time
                            conference_record = {
                                "speaker": "",
                                "content": "",
                                "start_time": time,
                                "end_time": time,
                            }
                        else:
                            conference_record["content"] += record_txt
                if content_type == "conference_summary":
                    if not re.search(r"[.。；;?？！!]", line):  # 不含标点符号 是标题
                        if conference_summary:
                            conference_summarys.append(conference_summary)
                        conference_summary = {
                            "title": line,
                            "content": "",
                            "speaker": "",
                        }
                    else:
                        conference_summary["content"] = (
                            conference_summary["content"] + line
                            if "content" in conference_summary
                            else line
                        )
                if content_type == "conference_summary_speaker":
                    if not re.search(r"[.。；;?？！!]", line):  # 不含标点符号 是发言人
                        if conference_summary:
                            conference_summarys.append(conference_summary)
                        conference_summary = {
                            "title": "",
                            "content": "",
                            "speaker": line,
                        }
                    else:
                        conference_summary["content"] = (
                            conference_summary["content"] + line
                            if "content" in conference_summary
                            else line
                        )
                if content_type == "chapters":
                    if line == "关键词":
                        chapter = {
                            "title": line,
                            "content": "",
                            "start_time": "",
                            "end_time": "",
                        }
                    elif re.match(
                        "^\d{1,2}[:：]\d{1,2}\s*[~|-]\s*\d{1,2}[:：]\d{1,2}.*[\u4e00-\u9fff]+",
                        line,
                    ):
                        line_time = (
                            re.match(
                                r"^\d{1,2}[:：]\d{1,2}[~|-]\d{1,2}[:：]\d{1,2}", line
                            )
                            .group()
                            .strip()
                            .replace("：", ":")
                        )
                        line_title = re.sub(
                            r"^\d{1,2}[:：]\d{1,2}[~|-]\d{1,2}[:：]\d{1,2}", "", line
                        ).strip()
                        chapter_start_time = re.split(r"~|-", line_time)[0].strip()
                        chapter_end_time = re.split(r"~|-", line_time)[1].strip()
                        if chapter:
                            chapters.append(chapter)
                            conference_over_accumulated_time = chapter_end_time
                        chapter = {
                            "title": line_title,
                            "content": "",
                            "start_time": chapter_start_time,
                            "end_time": chapter_end_time,
                        }
                    elif re.match(
                        "^\d{1,2}[:：]\d{1,2}\s*[~|-]\s*\d{1,2}[:：]\d{1,2}$", line
                    ):
                        chapter_start_time = (
                            re.split(r"~|-", line)[0].strip().replace("：", ":")
                        )
                        chapter_end_time = (
                            re.split(r"~|-", line)[1].strip().replace("：", ":")
                        )
                        if chapter:
                            chapters.append(chapter)
                            conference_over_accumulated_time = chapter_end_time
                        chapter = {
                            "title": "",
                            "content": "",
                            "start_time": chapter_start_time,
                            "end_time": chapter_end_time,
                        }
                    elif (
                        not re.search(r"[.。；;?？！!]", line)
                        and chapter_start_time
                        and chapter_end_time
                    ):  # 不含标点符号 是标题
                        chapter["title"] = line
                    else:
                        chapter["content"] += line
                if content_type == "conference_to_deal":
                    conference_to_deal += line
    if conference_record:
        conference_records.append(conference_record)
        if conference_record["end_time"]:
            conference_over_accumulated_time = conference_record["end_time"]
    if conference_summary:
        conference_summarys.append(conference_summary)
    if chapter:
        chapters.append(chapter)
        if chapter["end_time"]:
            conference_over_accumulated_time = chapter["end_time"]

    doc_result["conference_theme"] = conference_theme
    if re.search(r"\d{8}[-_]\d{4}", conference_theme):
        conference_theme_time = re.search(r"\d{8}[-_]\d{4}", conference_theme).group()
        conference_theme_time_year = conference_theme_time[0:4]
        conference_theme_time_month = conference_theme_time[4:6]
        conference_theme_time_day = conference_theme_time[6:8]
        conference_theme_time_day_hour = conference_theme_time[9:11]
        conference_theme_time_day_minute = conference_theme_time[11:]
        conference_theme_time_date_time = "{}-{}-{} {}:{}:00".format(
            conference_theme_time_year,
            conference_theme_time_month,
            conference_theme_time_day,
            conference_theme_time_day_hour,
            conference_theme_time_day_minute,
        )
        conference_start_time = "{}年{}月{}日 {}:{}".format(
            conference_theme_time_year,
            conference_theme_time_month,
            conference_theme_time_day,
            conference_theme_time_day_hour,
            conference_theme_time_day_minute,
        )
    if conference_theme_time_date_time:
        for conference_record in conference_records:
            if conference_record.get("start_time"):
                seconds = get_seconds(conference_record.get("start_time"))
                new_start_time = (
                    datetime.strptime(
                        conference_theme_time_date_time, "%Y-%m-%d %H:%M:%S"
                    )
                    + timedelta(seconds=seconds)
                ).strftime("%H:%M")
                conference_record["start_time"] = new_start_time
            if conference_record.get("end_time"):
                seconds = get_seconds(conference_record.get("end_time"))
                if get_seconds(conference_over_accumulated_time) < seconds:
                    conference_over_accumulated_time = conference_record.get("end_time")
                new_end_time = (
                    datetime.strptime(
                        conference_theme_time_date_time, "%Y-%m-%d %H:%M:%S"
                    )
                    + timedelta(seconds=seconds)
                ).strftime("%H:%M")
                conference_record["end_time"] = new_end_time
        for chapter in chapters:
            if chapter.get("start_time"):
                seconds = get_seconds(chapter.get("start_time"))
                new_start_time = (
                    datetime.strptime(
                        conference_theme_time_date_time, "%Y-%m-%d %H:%M:%S"
                    )
                    + timedelta(seconds=seconds)
                ).strftime("%H:%M")
                chapter["start_time"] = new_start_time
            if chapter.get("end_time"):
                seconds = get_seconds(chapter.get("end_time"))
                if get_seconds(conference_over_accumulated_time) < seconds:
                    conference_over_accumulated_time = chapter.get("end_time")
                new_end_time = (
                    datetime.strptime(
                        conference_theme_time_date_time, "%Y-%m-%d %H:%M:%S"
                    )
                    + timedelta(seconds=seconds)
                ).strftime("%H:%M")
                chapter["end_time"] = new_end_time
    if conference_theme_time_date_time and conference_over_accumulated_time:
        conference_over_time = (
            datetime.strptime(conference_theme_time_date_time, "%Y-%m-%d %H:%M:%S")
            + timedelta(seconds=get_seconds(conference_over_accumulated_time))
        ).strftime("%Y年%m月%d日 %H:%M")
    doc_result["conference_promoter"] = conference_promoter
    doc_result["conference_start_time"] = conference_start_time
    doc_result["conference_over_time"] = conference_over_time
    doc_result["conference_persons"] = conference_persons
    doc_result["conference_records"] = conference_records
    doc_result["summary"] = conference_summarys
    doc_result["chapters"] = chapters
    doc_result["to_do"] = conference_to_deal
    return doc_result


def get_app_conference_result(path: str = "") -> dict:
    doc_result = {}
    file_name = os.path.basename(path)
    doc = Document(path)
    doc_result["file_name"] = file_name
    conference_theme = ""
    conference_promoter = ""
    conference_start_time = ""
    conference_over_time = ""
    conference_persons = ""
    conference_records = []
    conference_record = {}
    conference_summarys = []
    conference_summary = {}
    conference_to_deal = ""
    content_type = (
        ""  # conference_record 转写记录 conference_summary 会议纪要 conference_to_deal 会议待办
    )
    for block in doc.element.body:
        if isinstance(block, CT_P):
            p = Paragraph(block, doc)
            print(p.text.strip())
            if p.text.strip():
                line = p.text.strip()
                if re.match(r"^会议主题[:：]", line):
                    conference_theme = line.replace(":", "：").split("会议主题：")[1]
                    if re.match(r"^.+(发起|發起)的.*(会议|會議)", conference_theme):
                        conference_promoter = (
                            conference_theme.replace("發起", "发起")
                            .replace("會議", "会议")
                            .split("发起的")[0]
                        )
                if re.match(r"^会议开始时间[：:]", line):
                    conference_start_time = line.replace(":", "：").split("会议开始时间：")[1]
                if re.match(r"^会议日期与时间[：:]", line):
                    conference_start_time = line.replace(":", "：").split("会议日期与时间：")[1]
                if re.match(r"^会议结束时间[：:]", line):
                    conference_over_time = line.replace(":", "：").split("会议结束时间：")[1]
                if re.match(r"^参会人员[：:]", line):
                    conference_persons = line.replace(":", "：").split("参会人员：")[1]
                if re.match(r"^转写记录[：:]", line):
                    content_type = "conference_record"
                    continue
                if (
                    not content_type
                    and "会议开始时间：" not in line
                    and "会议开始时间:" not in line
                    and "会议结束时间：" not in line
                    and "会议结束时间:" not in line
                    and "会议日期与时间：" not in line
                    and "会议日期与时间:" not in line
                    and re.match(r".*\d{1,2}[:：]\d{2}$", line)
                ):
                    content_type = "conference_record"
                if re.match(r"^会议纪要[:：-]?", line):
                    content_type = "conference_summary"
                    continue
                if re.match(r"^(会议待办|待办事项)[:：-]?", line):
                    content_type = "conference_to_deal"
                    continue
                if content_type == "conference_record":
                    if re.match(r".*\d{1,2}[:：]\d{2}$", line):
                        person = re.sub(r"\s*\d{1,2}[:：]\d{2}$", "", line)
                        time = (
                            re.search(r"\d{1,2}[:：]\d{2}$", line)
                            .group()
                            .replace("：", ":")
                        )
                        if conference_record:
                            if not person == conference_record["speaker"]:  # 不是同一个人发言
                                conference_record["end_time"] = time
                                conference_records.append(conference_record)
                                conference_record = {
                                    "speaker": person,
                                    "content": "",
                                    "start_time": time,
                                    "end_time": time,
                                }
                            else:
                                conference_record["end_time"] = time
                        else:
                            conference_record = {
                                "speaker": person,
                                "content": "",
                                "start_time": time,
                                "end_time": time,
                            }
                    else:
                        conference_record["content"] += line
                if content_type == "conference_summary":
                    if not re.search(r"[。；;?？！!]", line):  # 不含标点符号 是标题
                        if conference_summary:
                            conference_summarys.append(conference_summary)
                        if re.match(r"^发言人[:：]", line):
                            conference_summary = {
                                "title": "",
                                "content": "",
                                "speaker": line.replace(":", "：").split("发言人：")[1],
                            }
                        else:
                            conference_summary = {
                                "title": line,
                                "content": "",
                                "speaker": "",
                            }
                    else:
                        conference_summary["content"] = (
                            conference_summary["content"] + line
                            if "content" in conference_summary
                            else line
                        )
                if content_type == "conference_to_deal":
                    conference_to_deal += line
    if conference_record:
        conference_records.append(conference_record)
    if conference_summary:
        conference_summarys.append(conference_summary)

    doc_result["conference_theme"] = conference_theme
    for speaker in conference_persons.split(" "):
        if speaker in conference_promoter and speaker.strip():
            conference_promoter = speaker
            break
    if not conference_promoter in conference_persons:
        conference_promoter = ""
    doc_result["conference_promoter"] = conference_promoter
    doc_result["conference_start_time"] = conference_start_time
    doc_result["conference_over_time"] = conference_over_time
    doc_result["conference_persons"] = conference_persons
    doc_result["conference_records"] = conference_records
    doc_result["summary"] = conference_summarys
    doc_result["chapters"] = []
    doc_result["to_do"] = conference_to_deal
    return doc_result


if __name__ == "__main__":
    file_path = "/Users/<USER>/Desktop/工作内容/移动/documents_0903/原版会议.docx"
    DocParser().parse(
        file_path, "suirui", "/Users/<USER>/Desktop/工作内容/移动/documents_0903"
    )
