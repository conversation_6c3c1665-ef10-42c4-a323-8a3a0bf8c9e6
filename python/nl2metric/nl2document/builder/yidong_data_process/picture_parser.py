from zhipuai import ZhipuAI
import json
import os
import re

from common.logging.logger import get_logger
from config.app_config import ZHIPU_API_KEY

client = ZhipuAI(api_key=ZHIPU_API_KEY)  # 请填写您自己的APIKey

logger = get_logger(__name__)


class PictureParser:
    def parse(self, path, output_path: str = None) -> dict:
        # eg : path = "https://ysx-dev-cloud-record.eos-guangzhou-1.cmecloud.cn/14249216955457372168/1234567890/d29416e706a948499c8175385d881d94?AWSAccessKeyId=6AT6QCAV794I87E2MW48&Expires=1723085468&response-content-disposition=attachment%3B%20filename%3D1280X1280.png&Signature=58aWkxRk2AlPswf4Po4rjSTOYOk%3D"
        logger.info("image http url = {}: meeting image Parser.".format(path))
        picture_result = {}
        file_name = re.sub(r"&Signature.*", "", re.sub(r".*filename%", "", path))
        if not file_name or not file_name.split(".")[-1].lower() in ["png", "jpeg"]:
            raise RuntimeError("{} not image".format(file_name))
        # image_url = "https://dipeak.oss-cn-hangzhou.aliyuncs.com/20240729-140535.jpeg"
        picture_result["file_name"] = file_name
        picture_result["summary"] = get_image_summary_content(path)
        picture_result["conference_theme"] = get_image_theme_content(path)

        conference_start_time = ""
        if re.search(r"\d{8}[-_]\d{4}", file_name):
            conference_theme_time = re.search(r"\d{8}[-_]\d{4}", file_name).group()
            conference_theme_time_year = conference_theme_time[0:4]
            conference_theme_time_month = conference_theme_time[4:6]
            conference_theme_time_day = conference_theme_time[6:8]
            conference_theme_time_day_hour = conference_theme_time[9:11]
            conference_theme_time_day_minute = conference_theme_time[11:]

            conference_start_time = "{}年{}月{}日 {}:{}".format(
                conference_theme_time_year,
                conference_theme_time_month,
                conference_theme_time_day,
                conference_theme_time_day_hour,
                conference_theme_time_day_minute,
            )
        picture_result["conference_promoter"] = ""
        picture_result["conference_start_time"] = conference_start_time
        picture_result["conference_over_time"] = ""
        picture_result["conference_persons"] = ""
        picture_result["conference_records"] = []
        picture_result["chapters"] = []
        picture_result["to_do"] = get_image_to_do_content(path)

        if output_path:
            parse_output_file = os.path.join(
                output_path, f"{picture_result['file_name']}.json"
            )
            with open(parse_output_file, "w") as f:
                json.dump(picture_result, f, indent=4, ensure_ascii=False)
            logger.info(f"parse_output_dir path is >>>  {parse_output_file}")
        return picture_result


def get_image_summary_content(image_url: str) -> []:
    conference_summary = []
    response = client.chat.completions.create(
        model="glm-4v",  # 填写需要调用的模型名称
        messages=[
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "你是一个图片内容理解专家，该图片是来自于白板会议记录，通过解读图片的内容，请输出会议主题和会议内容，会议主题和会议内容要一一对应紧密关联, 会议主题的数量要尽可能的少，各个会议主题的内容区别要明显。\n\n---------------------\n会议主题：排除图片里面明确指明为待办事项的文字内容，从剩余内容里面解读后精炼简要概括出的议题，一定不能虚构；没有则返回“无”。\n会议内容：排除图片里面明确指明为待办事项的文字内容，解读出来的同会议主题紧密关联的一段内容，如果是文字就尽量保持内容原样；如果是流程图、结构图、架构图、表图就进一步解读，并且保持结构一体和完整，描述尽量合理详尽，一定不能虚构；如果是普通图像，就具体描述图片的内容；什么都没有则返回“无”。\n\n---------------------\n返回结果以json的格式返回，例如：[{会议主题: <主题1>, 会议内容: <内容1>},{会议主题: <主题2>, 会议内容: <内容2>}]。",
                    },
                    {"type": "image_url", "image_url": {"url": image_url}},
                ],
            },
        ],
    )
    print(response.choices[0].message.content.strip("```")[4:-1])
    answer = response.choices[0].message.content.strip("```")[4:-1]
    try:
        image_result = json.loads(answer)
    except:
        raise RuntimeError(
            "image url = {}. Getting summary content of single meeting picture. Multi modal model response not json needed. response: {}".format(
                image_url, answer
            )
        )

    if isinstance(image_result, list):
        for summary in image_result:
            if isinstance(summary, dict) and "会议主题" in summary and "会议内容" in summary:
                if isinstance(summary.get("会议内容"), list):
                    contents = "。".join(summary.get("会议内容"))
                else:
                    contents = summary.get("会议内容")
                conference_summary.append(
                    {
                        "title": summary.get("会议主题", ""),  # 可能不是string 而是list
                        "content": contents,
                        "speaker": "",
                    }
                )
    elif (
        isinstance(image_result, dict)
        and "会议主题" in image_result
        and "会议内容" in image_result
    ):
        if isinstance(image_result.get("会议内容"), list):
            contents = "。".join(image_result.get("会议内容"))
        else:
            contents = image_result.get("会议内容")
        conference_summary.append(
            {
                "title": image_result.get("会议主题", ""),  # 可能不是string 而是list
                "content": contents,
                "speaker": "",
            }
        )
    return conference_summary


def get_image_theme_content(image_url: str) -> str:
    conference_theme = ""
    response = client.chat.completions.create(
        model="glm-4v",  # 填写需要调用的模型名称
        messages=[
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "你是一个图片内容理解专家，该图片是来自于白板会议记录，通过解读图片的内容，请简要概括出会议主题。\n\n---------------------\n会议主题：排除图片里面明确指明为待办事项的文字内容，对图片剩余内容总结出一个整体的议题，一定不能虚构，如果有则简明概括输出结果，没有则返回“无”。\n\n---------------------\n返回结果以json的格式返回，例如：{会议主题: <主题>}。",
                    },
                    {"type": "image_url", "image_url": {"url": image_url}},
                ],
            },
        ],
    )
    print(response.choices[0].message.content.strip("```")[4:-1])
    answer = response.choices[0].message.content.strip("```")[4:-1]
    try:
        image_result = json.loads(answer)
    except:
        raise RuntimeError(
            "image url = {}. Getting conference theme of single meeting picture. Multi modal model response not json needed. response: {}".format(
                image_url, answer
            )
        )

    if isinstance(image_result, dict) and "会议主题" in image_result:
        if image_result.get("会议主题") != "无":
            conference_theme = image_result.get("会议主题")
    return conference_theme


def get_image_to_do_content(image_url: str) -> str:
    conference_to_do = ""
    response = client.chat.completions.create(
        model="glm-4v",  # 填写需要调用的模型名称
        messages=[
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "你是一个图片内容理解专家，该图片是来自于白板会议记录，请输出其中记录的待办事项。\n\n---------------------\n待办事项是图片内明确指明为待办事项的文字内容。如果有的话就按照原文将其输出，不要带markdown格式；没有则返回“无”。",
                    },
                    {"type": "image_url", "image_url": {"url": image_url}},
                ],
            },
        ],
    )
    print(response.choices[0].message.content)
    answer = response.choices[0].message.content

    if answer != "无":
        conference_to_do = answer

    return conference_to_do


if __name__ == "__main__":
    file_path = "https://ysx-dev-cloud-record.eos-guangzhou-1.cmecloud.cn/14249216955457372168/1234567890/d29416e706a948499c8175385d881d94?AWSAccessKeyId=6AT6QCAV794I87E2MW48&Expires=1723092303&response-content-disposition=attachment%3B%20filename%3D1280X1280.png&Signature=fjnkXbyebAU4kCu36k%2Ff9Pa5%2BWM%3D"
    PictureParser().parse(file_path, "/Users/<USER>/Desktop/工作内容/移动/documents")
