from zhipuai import ZhipuAI
import json
import os
import re
from pdf2image import convert_from_path

from common.logging.logger import get_logger, get_elk_logger
from config.app_config import ZHIPU_API_KEY

logger = get_logger(__name__)
elk_logger = get_elk_logger()

from nl2document.builder.yidong_data_process.picture_parser import (
    get_image_summary_content,
    get_image_theme_content,
    get_image_to_do_content,
)
from nl2document.builder.figure_process.figure_upload_yidongyun import (
    put_object,
    get_temporary_accessible_address,
)

BUCKET_NAME = "dipeak-bkt1"
OBJECT = "meeting_pdf_images"

client = ZhipuAI(api_key=ZHIPU_API_KEY)  # 请填写您自己的APIKey

image_type = "PNG"


class MeetingPdfParser:  # 会议图片pdf分页处理
    def parse(self, path, output_path: str = None, file_id: str = "") -> dict:
        logger.info(
            "pdf file url = {}: meeting pdf Parser. pdf file file_id: {}".format(
                path, file_id
            )
        )
        pdf_result = {}
        file_name = os.path.basename(path)
        if not file_name or not file_name.split(".")[-1].lower() == "pdf":
            raise RuntimeError("{} not pdf file".format(file_name))
        pdf_result["file_name"] = file_name

        images_path = []
        images = convert_from_path(path)
        for i, image in enumerate(images):
            image_name = f"{os.path.splitext(file_name)[0]}_page_{i + 1}.png"
            object_path = os.path.join(os.path.join(OBJECT, file_name), image_name)
            local_image_path = os.path.join(os.path.dirname(path), image_name)
            image.save(local_image_path, image_type)
            put_object(BUCKET_NAME, object_path, local_image_path, file_id)
            temp_image_path = get_temporary_accessible_address(BUCKET_NAME, object_path)
            images_path.append(temp_image_path)
            if os.path.exists(local_image_path):
                os.remove(local_image_path)

        pdf_result["summary"] = get_pdf_summary(images_path)
        pdf_result["conference_theme"] = get_pdf_conference_theme(
            file_name, images_path
        )

        conference_start_time = ""
        if re.search(r"\d{8}[-_]\d{4}", file_name):
            conference_theme_time = re.search(r"\d{8}[-_]\d{4}", file_name).group()
            conference_theme_time_year = conference_theme_time[0:4]
            conference_theme_time_month = conference_theme_time[4:6]
            conference_theme_time_day = conference_theme_time[6:8]
            conference_theme_time_day_hour = conference_theme_time[9:11]
            conference_theme_time_day_minute = conference_theme_time[11:]

            conference_start_time = "{}年{}月{}日 {}:{}".format(
                conference_theme_time_year,
                conference_theme_time_month,
                conference_theme_time_day,
                conference_theme_time_day_hour,
                conference_theme_time_day_minute,
            )
        pdf_result["conference_promoter"] = ""
        pdf_result["conference_start_time"] = conference_start_time
        pdf_result["conference_over_time"] = ""
        pdf_result["conference_persons"] = ""
        pdf_result["conference_records"] = []
        pdf_result["chapters"] = []
        pdf_result["to_do"] = get_pdf_conference_to_do(images_path)

        if output_path:
            parse_output_file = os.path.join(
                output_path, f"{pdf_result['file_name']}.json"
            )
            with open(parse_output_file, "w") as f:
                json.dump(pdf_result, f, indent=4, ensure_ascii=False)
            logger.info(f"parse_output_dir path is >>>  {parse_output_file}")
        return pdf_result


def get_pdf_summary(images_path: []) -> []:
    conference_summary = []
    for i, image_path in enumerate(images_path):
        conference_summary_image = get_image_summary_content(image_path)
        conference_summary.extend(conference_summary_image)
    return conference_summary


def get_pdf_conference_theme(file_name: str, images_path: []) -> str:
    pdf_conference_theme = ""
    conference_themes = ""
    for i, image_path in enumerate(images_path):
        conference_theme_image = get_image_theme_content(image_path)
        if conference_theme_image:
            conference_themes = (
                conference_themes + "\n" + conference_theme_image
                if conference_themes
                else conference_theme_image
            )

    # 主题整合
    response = client.chat.completions.create(
        model="glm-4v",  # 填写需要调用的模型名称
        messages=[
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": f"你是一个会议专家，请依据下面给你的会议内容，用一句话概括出更精简恰当的会议主题，不要带markdown格式，没有则返回“无”。\n\n---------------------\n会议内容：{conference_themes}\n\n---------------------\n返回结果以json的格式返回，"
                        + "例如：{会议主题: <主题>}",
                    }
                ],
            },
        ],
    )
    print(response.choices[0].message.content.strip("```")[4:-1])
    answer = response.choices[0].message.content.strip("```")[4:-1]
    try:
        theme_result = json.loads(answer)
    except:
        raise RuntimeError(
            "file_name = {}. Getting conference theme of meeting pdf pictures. Multi modal model response not json needed. response: {}".format(
                file_name, answer
            )
        )

    if isinstance(theme_result, dict) and "会议主题" in theme_result:
        if theme_result.get("会议主题") != "无":
            pdf_conference_theme = theme_result.get("会议主题")
    return pdf_conference_theme


def get_pdf_conference_to_do(images_path: []) -> str:
    conference_to_do = ""
    for i, image_path in enumerate(images_path):
        conference_to_do_image = get_image_to_do_content(image_path)
        print(f"image {i}  待办事项：{conference_to_do_image}")
        if conference_to_do_image:
            if conference_to_do:
                conference_to_do += "。" + conference_to_do_image
            else:
                conference_to_do = conference_to_do_image
    return conference_to_do


if __name__ == "__main__":
    file_path = (
        "/Users/<USER>/Desktop/工作内容/移动/MAXHUB白板导出的文件用于AI问答/白板文件20240814-21252123.pdf"
    )
    MeetingPdfParser().parse(
        file_path, "/Users/<USER>/Desktop/工作内容/移动/MAXHUB白板导出的文件用于AI问答"
    )
