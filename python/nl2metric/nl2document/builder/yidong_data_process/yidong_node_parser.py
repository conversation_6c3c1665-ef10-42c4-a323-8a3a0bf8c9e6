import json
from typing import Dict, <PERSON><PERSON>, List
import re

from llama_index.core.node_parser import TokenTextSplitter

from common.logging.logger import get_logger, get_elk_logger
from config.doc_config import ask_doc_index_temp_file_dir

from llama_index.core.schema import (
    BaseNode,
    NodeRelationship,
    TextNode,
)

from nl2document.builder.parser.node_parser import Tokenized


from nl2document.common.base.const import (
    LLAMA_INDEX_FILE_NAME,
    LLAMA_INDEX_FILE_ID,
    LLAMA_INDEX_FOLDER_ID,
    LLAMA_INDEX_CONFERENCE_THEME,
    LLAMA_INDEX_CONFERENCE_PERSONS,
    LLAMA_INDEX_CONFERENCE_PROMOTER,
    LLAMA_INDEX_CONFERENCE_START_TIME,
    LLAMA_INDEX_CONFERENCE_OVER_TIME,
    LLAMA_INDEX_CONFERENCE_SUMMARY_TITLE,
    LLAMA_INDEX_CONFERENCE_SPEAKER,
    LLAM<PERSON>_INDEX_CONFERENCE_SPEAKERS,
    LLAMA_INDEX_CONFERENCE_SPEAKER_START_TIME,
    LLAMA_INDEX_CONFERENCE_SPEAKER_END_TIME,
    LLAMA_INDEX_CONFERENCE_CHAPTER_TITLE,
    LLAMA_INDEX_CONFERENCE_CHAPTER_START_TIME,
    LLAMA_INDEX_CONFERENCE_CHAPTER_END_TIME,
    LLAMA_INDEX_CONFERENCE_CONTENT_TYPE,
)

logger = get_logger(__name__)
elk_logger = get_elk_logger()


splitter = TokenTextSplitter(tokenizer=Tokenized(), chunk_size=500, chunk_overlap=50)

smaller_splitter = TokenTextSplitter(
    tokenizer=Tokenized(), chunk_size=400, chunk_overlap=10
)


class YiDongNodeParser:
    def __init__(self):
        self.chunk_sort_id: int = 0

    def get_nodes_from_json(
        self, json_datas: Dict, file_id: str, folder_id: str
    ) -> Tuple[List[BaseNode], List[BaseNode]]:
        if "conference_theme" not in json_datas:
            extra_info = {"file_id": file_id}
            logger.exception(
                msg="json file structure not suit yidong_node_parser", extra=extra_info
            )
            elk_logger.info(
                msg="json file structure not suit yidong_node_parser", extra=extra_info
            )
            raise RuntimeError(
                "file_name: {} file_id: {} it's json file structure not suit yidong_node_parser".format(
                    json_datas.get("file_name", ""), file_id
                )
            )

        root_node = TextNode()
        root_node.relationships = {}
        root_node.metadata = {}

        root_node.metadata[LLAMA_INDEX_FILE_NAME] = json_datas.get("file_name", "")
        root_node.metadata[LLAMA_INDEX_FILE_ID] = file_id
        root_node.metadata[LLAMA_INDEX_FOLDER_ID] = folder_id
        root_node.metadata[LLAMA_INDEX_CONFERENCE_THEME] = (
            "会议主题：" + json_datas.get("conference_theme", "")
            if json_datas.get("conference_theme", "")
            else ""
        )
        root_node.metadata[LLAMA_INDEX_CONFERENCE_PERSONS] = (
            "会议参与人员：" + json_datas.get("conference_persons", "")
            if json_datas.get("conference_persons", "")
            else ""
        )
        root_node.metadata[LLAMA_INDEX_CONFERENCE_PROMOTER] = (
            "会议发起人：" + json_datas.get("conference_promoter", "")
            if json_datas.get("conference_promoter", "")
            else ""
        )
        root_node.metadata[LLAMA_INDEX_CONFERENCE_START_TIME] = (
            "会议开始时间：" + json_datas.get("conference_start_time", "")
            if json_datas.get("conference_start_time", "")
            else ""
        )
        root_node.metadata[LLAMA_INDEX_CONFERENCE_OVER_TIME] = (
            "会议结束时间：" + json_datas.get("conference_over_time", "")
            if json_datas.get("conference_over_time", "")
            else ""
        )
        speakers = self.get_conference_record_speakers(
            json_datas.get("conference_records")
        )
        root_node.metadata[LLAMA_INDEX_CONFERENCE_SPEAKERS] = speakers

        node_list = []
        self.get_conference_record_nodes(
            root_node, node_list, json_datas.get("conference_records")
        )
        self.get_conference_summary_nodes(
            root_node, node_list, json_datas.get("summary")
        )
        self.get_conference_chapter_nodes(
            root_node, node_list, json_datas.get("chapters")
        )
        self.get_conference_to_do_nodes(root_node, node_list, json_datas.get("to_do"))

        self.save_nodes(json_datas.get("file_name", ""), node_list)

        return [root_node], node_list

    def save_nodes(self, file_name: str, nodes):
        if ask_doc_index_temp_file_dir:
            file_path = ask_doc_index_temp_file_dir + "/" + file_name + ".nodes.json"
            nodes_info = []
            for node in nodes:
                node_info = {
                    "node_id": node.node_id,
                    "content": node.get_content(),
                    "metadata": node.get_metadata_str(),
                }
                nodes_info.append(node_info)

            with open(file_path, "w", encoding="utf-8") as file:
                json.dump(nodes_info, file, indent=4, ensure_ascii=False)

    def split_texts(self, text: str, metadata_info: {}) -> []:  # 使用句号分割
        speaker_info = ""
        if (
            LLAMA_INDEX_CONFERENCE_SPEAKER in metadata_info
            and metadata_info[LLAMA_INDEX_CONFERENCE_SPEAKER]
            and metadata_info[LLAMA_INDEX_CONFERENCE_CONTENT_TYPE]
            == "conference_record"
        ):
            speaker_info = (
                re.sub(r"^发言人：", "", metadata_info[LLAMA_INDEX_CONFERENCE_SPEAKER])
                + "说："
            )
        elif (
            metadata_info[LLAMA_INDEX_CONFERENCE_CONTENT_TYPE] == "conference_to_do"
            and text
        ):
            speaker_info = "待办事项："
        metadata_info_str = json.dumps(metadata_info, ensure_ascii=False)
        contents = text.split("。")
        texts = []
        tmp_content = ""
        for content_index, content in enumerate(contents):
            if content:
                pre_tmp_content = tmp_content
                if not pre_tmp_content:
                    if content_index == len(contents) - 1:
                        pre_tmp_content = content
                    else:
                        pre_tmp_content = content + "。"
                else:
                    if content_index == len(contents) - 1:
                        pre_tmp_content = pre_tmp_content + content
                    else:
                        pre_tmp_content = pre_tmp_content + content + "。"
                length = len(
                    splitter._tokenizer(
                        speaker_info + pre_tmp_content + metadata_info_str
                    )
                )
                if length >= splitter.chunk_size and tmp_content:
                    tmp_texts = splitter.split_text(tmp_content)
                    for tmp_text in tmp_texts:
                        if (
                            len(
                                splitter._tokenizer(
                                    speaker_info + tmp_text + metadata_info_str
                                )
                            )
                            >= splitter.chunk_size
                        ):
                            smaller_tmp_texts = self.split_texts_comma(
                                tmp_text, speaker_info, metadata_info_str
                            )
                            texts.extend(smaller_tmp_texts)
                        else:
                            texts.append(speaker_info + tmp_text)
                    if content_index == len(contents) - 1:
                        tmp_content = content
                    else:
                        tmp_content = content + "。"
                else:
                    tmp_content = pre_tmp_content
        if tmp_content:
            tmp_texts = splitter.split_text(tmp_content)
            for tmp_text in tmp_texts:
                if (
                    len(
                        splitter._tokenizer(speaker_info + tmp_text + metadata_info_str)
                    )
                    >= splitter.chunk_size
                ):
                    smaller_tmp_texts = self.split_texts_comma(
                        tmp_text, speaker_info, metadata_info_str
                    )
                    texts.extend(smaller_tmp_texts)
                else:
                    texts.append(speaker_info + tmp_text)
        return texts

    def split_texts_comma(
        self, text: str, speaker_info: str, metadata_info_str: str
    ) -> []:  # 使用逗号分割
        texts = []
        contents = text.split("，")
        tmp_content = ""
        for content_index, content in enumerate(contents):
            if content:
                pre_tmp_content = tmp_content
                if not pre_tmp_content:
                    if content_index == len(contents) - 1:
                        pre_tmp_content = content
                    else:
                        pre_tmp_content = content + "，"
                else:
                    if content_index == len(contents) - 1:
                        pre_tmp_content = pre_tmp_content + content
                    else:
                        pre_tmp_content = pre_tmp_content + content + "，"
                length = len(
                    splitter._tokenizer(
                        speaker_info + pre_tmp_content + metadata_info_str
                    )
                )
                if length >= smaller_splitter.chunk_size and tmp_content:
                    tmp_texts = splitter.split_text(tmp_content)
                    for tmp_text in tmp_texts:
                        if (
                            len(
                                splitter._tokenizer(
                                    speaker_info + tmp_text + metadata_info_str
                                )
                            )
                            >= smaller_splitter.chunk_size
                        ):
                            smaller_tmp_texts = smaller_splitter.split_text(tmp_text)
                            for smaller_tmp_text in smaller_tmp_texts:
                                texts.append(speaker_info + smaller_tmp_text)
                        else:
                            texts.append(speaker_info + tmp_text)
                    if content_index == len(contents) - 1:
                        tmp_content = content
                    else:
                        tmp_content = content + "，"
                else:
                    tmp_content = pre_tmp_content
        if tmp_content:
            tmp_texts = splitter.split_text(tmp_content)
            for tmp_text in tmp_texts:
                if (
                    len(
                        splitter._tokenizer(speaker_info + tmp_text + metadata_info_str)
                    )
                    >= smaller_splitter.chunk_size
                ):
                    smaller_tmp_texts = smaller_splitter.split_text(tmp_text)
                    for smaller_tmp_text in smaller_tmp_texts:
                        texts.append(speaker_info + smaller_tmp_text)
                else:
                    texts.append(speaker_info + tmp_text)
        return texts

    def get_conference_record_speakers(self, conference_records: List):
        speakers = []
        for conference_record in conference_records:
            speaker = conference_record.get("speaker", "")
            if speaker and speaker not in speakers:
                speakers.append(speaker)
        return speakers

    def get_conference_record_nodes(
        self, root_node: TextNode, node_list: List, conference_records: List
    ):
        all_split_contents = []
        for conference_record in conference_records:
            speaker = conference_record.get("speaker", "")
            content = conference_record.get("content", "")
            speaker_start_time = conference_record.get("start_time", "")
            speaker_end_time = conference_record.get("end_time", "")

            metadata_info = {}
            metadata_info[LLAMA_INDEX_FILE_NAME] = root_node.metadata["file_name"]
            metadata_info[LLAMA_INDEX_FILE_ID] = root_node.metadata[LLAMA_INDEX_FILE_ID]
            metadata_info[LLAMA_INDEX_FOLDER_ID] = root_node.metadata[
                LLAMA_INDEX_FOLDER_ID
            ]
            metadata_info[LLAMA_INDEX_CONFERENCE_CONTENT_TYPE] = "conference_record"
            metadata_info[LLAMA_INDEX_CONFERENCE_SPEAKER] = (
                "发言人：" + speaker if speaker else ""
            )
            metadata_info[LLAMA_INDEX_CONFERENCE_SPEAKER_START_TIME] = (
                "发言开始时间：" + speaker_start_time if speaker_start_time else ""
            )
            metadata_info[LLAMA_INDEX_CONFERENCE_SPEAKER_END_TIME] = (
                "发言结束时间：" + speaker_end_time if speaker_end_time else ""
            )

            split_contents = self.split_texts(content, metadata_info)
            for split_content in split_contents:
                all_split_contents.append(
                    {"content": split_content, "metadata_info": metadata_info}
                )
        # 合并处理
        all_merged_split_contents = []
        for split_content in all_split_contents:
            split_content_text = split_content.get("content")
            split_content_metadata_info = split_content.get("metadata_info")
            split_content_speaker = split_content_metadata_info[
                LLAMA_INDEX_CONFERENCE_SPEAKER
            ]
            split_content_end_time = split_content_metadata_info[
                LLAMA_INDEX_CONFERENCE_SPEAKER_END_TIME
            ]
            if all_merged_split_contents:
                final_merged_split_content = all_merged_split_contents[-1]
                final_merged_split_content_text = final_merged_split_content.get(
                    "content"
                )
                final_merged_split_content_metadata_info = (
                    final_merged_split_content.get("metadata_info")
                )
                final_merged_split_content_speaker = (
                    final_merged_split_content_metadata_info[
                        LLAMA_INDEX_CONFERENCE_SPEAKER
                    ]
                )
                final_merged_split_content_end_time = (
                    final_merged_split_content_metadata_info[
                        LLAMA_INDEX_CONFERENCE_SPEAKER_END_TIME
                    ]
                )
                final_merged_split_content_speaker_new = (
                    final_merged_split_content_speaker
                )
                if (
                    split_content_speaker
                    and re.sub(r"^发言人：", "", split_content_speaker)
                    not in final_merged_split_content_speaker_new
                ):
                    final_merged_split_content_speaker_new += (
                        " " + re.sub(r"^发言人：", "", split_content_speaker)
                        if final_merged_split_content_speaker_new
                        else split_content_speaker
                    )
                if (
                    final_merged_split_content_speaker_new
                    != final_merged_split_content_speaker
                ):
                    final_merged_split_content_metadata_info[
                        LLAMA_INDEX_CONFERENCE_SPEAKER
                    ] = final_merged_split_content_speaker_new
                final_merged_split_content_metadata_info_str = json.dumps(
                    final_merged_split_content_metadata_info, ensure_ascii=False
                )
                if (
                    len(
                        splitter._tokenizer(
                            final_merged_split_content_text
                            + "\n"
                            + split_content_text
                            + final_merged_split_content_metadata_info_str
                        )
                    )
                    < splitter.chunk_size
                ):
                    final_merged_split_content["content"] += "\n" + split_content_text
                    if final_merged_split_content_end_time != split_content_end_time:
                        final_merged_split_content["metadata_info"][
                            LLAMA_INDEX_CONFERENCE_SPEAKER_END_TIME
                        ] = split_content_end_time
                    continue
                else:
                    final_merged_split_content["metadata_info"][
                        LLAMA_INDEX_CONFERENCE_SPEAKER
                    ] = final_merged_split_content_speaker
            all_merged_split_contents.append(
                {
                    "content": split_content_text,
                    "metadata_info": split_content_metadata_info,
                }
            )

        content_nodes = []
        for merged_split_content in all_merged_split_contents:
            content_node = TextNode()
            content_node.text = merged_split_content.get("content")
            content_node.relationships = {}

            content_node.metadata = merged_split_content.get("metadata_info")

            content_nodes.append(content_node)

        # add relationship
        for index, content_node in enumerate(content_nodes):
            if index != 0:
                content_node.relationships[NodeRelationship.PREVIOUS] = content_nodes[
                    index - 1
                ].as_related_node_info()
            if index != len(content_nodes) - 1:
                content_node.relationships[NodeRelationship.NEXT] = content_nodes[
                    index + 1
                ].as_related_node_info()
            node_list.append(content_node)

    def get_conference_summary_nodes(
        self, root_node: TextNode, node_list: List, conference_summarys: List
    ):
        content_nodes = []
        for conference_summary in conference_summarys:
            speaker = conference_summary.get("speaker", "")
            content = conference_summary.get("content", "")
            summary_title = conference_summary.get("title", "")

            metadata_info = {}
            metadata_info[LLAMA_INDEX_FILE_NAME] = root_node.metadata["file_name"]
            metadata_info[LLAMA_INDEX_FILE_ID] = root_node.metadata[LLAMA_INDEX_FILE_ID]
            metadata_info[LLAMA_INDEX_FOLDER_ID] = root_node.metadata[
                LLAMA_INDEX_FOLDER_ID
            ]
            metadata_info[LLAMA_INDEX_CONFERENCE_CONTENT_TYPE] = "conference_summary"
            if not summary_title and speaker:  # 会议纪要 按发言人
                metadata_info[LLAMA_INDEX_CONFERENCE_SPEAKER] = (
                    "总结发言人：" + speaker if speaker else ""
                )
            elif summary_title and not speaker:  # 会议纪要 按章节
                metadata_info[LLAMA_INDEX_CONFERENCE_SUMMARY_TITLE] = (
                    "会议纪要：" + summary_title if summary_title else ""
                )
            metadata_info[LLAMA_INDEX_CONFERENCE_THEME] = root_node.metadata[
                LLAMA_INDEX_CONFERENCE_THEME
            ]
            metadata_info[LLAMA_INDEX_CONFERENCE_PERSONS] = root_node.metadata[
                LLAMA_INDEX_CONFERENCE_PERSONS
            ]
            metadata_info[LLAMA_INDEX_CONFERENCE_PROMOTER] = root_node.metadata[
                LLAMA_INDEX_CONFERENCE_PROMOTER
            ]
            metadata_info[LLAMA_INDEX_CONFERENCE_START_TIME] = root_node.metadata[
                LLAMA_INDEX_CONFERENCE_START_TIME
            ]
            # metadata_info[LLAMA_INDEX_CONFERENCE_OVER_TIME] = root_node.metadata[LLAMA_INDEX_CONFERENCE_OVER_TIME]

            split_contents = self.split_texts(content, metadata_info)

            for split_content in split_contents:
                content_node = TextNode()
                content_node.text = split_content
                content_node.relationships = {}

                content_node.metadata = metadata_info

                content_nodes.append(content_node)

        # add relationship
        for index, content_node in enumerate(content_nodes):
            if index != 0:
                content_node.relationships[NodeRelationship.PREVIOUS] = content_nodes[
                    index - 1
                ].as_related_node_info()
            if index != len(content_nodes) - 1:
                content_node.relationships[NodeRelationship.NEXT] = content_nodes[
                    index + 1
                ].as_related_node_info()
            node_list.append(content_node)

    def get_conference_chapter_nodes(
        self, root_node: TextNode, node_list: List, conference_chapters: List
    ):
        content_nodes = []
        for conference_chapter in conference_chapters:
            chapter_title = conference_chapter.get("title", "")
            content = conference_chapter.get("content", "")
            chapter_start_time = conference_chapter.get("start_time", "")
            chapter_end_time = conference_chapter.get("end_time", "")

            metadata_info = {}
            metadata_info[LLAMA_INDEX_FILE_NAME] = root_node.metadata["file_name"]
            metadata_info[LLAMA_INDEX_FILE_ID] = root_node.metadata[LLAMA_INDEX_FILE_ID]
            metadata_info[LLAMA_INDEX_FOLDER_ID] = root_node.metadata[
                LLAMA_INDEX_FOLDER_ID
            ]
            metadata_info[LLAMA_INDEX_CONFERENCE_CHAPTER_TITLE] = (
                "章节主题：" + chapter_title if chapter_title else ""
            )
            metadata_info[LLAMA_INDEX_CONFERENCE_CONTENT_TYPE] = "conference_chapter"
            metadata_info[LLAMA_INDEX_CONFERENCE_CHAPTER_START_TIME] = (
                "章节开始时间：" + chapter_start_time if chapter_start_time else ""
            )
            metadata_info[LLAMA_INDEX_CONFERENCE_CHAPTER_END_TIME] = (
                "章节结束时间：" + chapter_end_time if chapter_end_time else ""
            )

            metadata_info[LLAMA_INDEX_CONFERENCE_THEME] = root_node.metadata[
                LLAMA_INDEX_CONFERENCE_THEME
            ]
            metadata_info[LLAMA_INDEX_CONFERENCE_PERSONS] = root_node.metadata[
                LLAMA_INDEX_CONFERENCE_PERSONS
            ]
            metadata_info[LLAMA_INDEX_CONFERENCE_PROMOTER] = root_node.metadata[
                LLAMA_INDEX_CONFERENCE_PROMOTER
            ]
            metadata_info[LLAMA_INDEX_CONFERENCE_START_TIME] = root_node.metadata[
                LLAMA_INDEX_CONFERENCE_START_TIME
            ]
            # metadata_info[LLAMA_INDEX_CONFERENCE_OVER_TIME] = root_node.metadata[LLAMA_INDEX_CONFERENCE_OVER_TIME]

            split_contents = self.split_texts(content, metadata_info)

            for split_content in split_contents:
                content_node = TextNode()
                content_node.text = split_content
                content_node.relationships = {}

                content_node.metadata = metadata_info

                content_nodes.append(content_node)

        # add relationship
        for index, content_node in enumerate(content_nodes):
            if index != 0:
                content_node.relationships[NodeRelationship.PREVIOUS] = content_nodes[
                    index - 1
                ].as_related_node_info()
            if index != len(content_nodes) - 1:
                content_node.relationships[NodeRelationship.NEXT] = content_nodes[
                    index + 1
                ].as_related_node_info()
            node_list.append(content_node)

    def get_conference_to_do_nodes(
        self, root_node: TextNode, node_list: List, to_do: str = ""
    ):
        if to_do:
            metadata_info = {}
            metadata_info[LLAMA_INDEX_FILE_NAME] = root_node.metadata["file_name"]
            metadata_info[LLAMA_INDEX_FILE_ID] = root_node.metadata[LLAMA_INDEX_FILE_ID]
            metadata_info[LLAMA_INDEX_FOLDER_ID] = root_node.metadata[
                LLAMA_INDEX_FOLDER_ID
            ]
            metadata_info[LLAMA_INDEX_CONFERENCE_CONTENT_TYPE] = "conference_to_do"

            metadata_info[LLAMA_INDEX_CONFERENCE_THEME] = root_node.metadata[
                LLAMA_INDEX_CONFERENCE_THEME
            ]
            metadata_info[LLAMA_INDEX_CONFERENCE_PERSONS] = root_node.metadata[
                LLAMA_INDEX_CONFERENCE_PERSONS
            ]
            metadata_info[LLAMA_INDEX_CONFERENCE_PROMOTER] = root_node.metadata[
                LLAMA_INDEX_CONFERENCE_PROMOTER
            ]
            metadata_info[LLAMA_INDEX_CONFERENCE_START_TIME] = root_node.metadata[
                LLAMA_INDEX_CONFERENCE_START_TIME
            ]
            # metadata_info[LLAMA_INDEX_CONFERENCE_OVER_TIME] = root_node.metadata[LLAMA_INDEX_CONFERENCE_OVER_TIME]

            split_contents = self.split_texts(to_do, metadata_info)

            content_nodes = []
            for split_content in split_contents:
                content_node = TextNode()
                content_node.text = split_content
                content_node.relationships = {}

                content_node.metadata = metadata_info

                content_nodes.append(content_node)
            # add relationship
            for index, content_node in enumerate(content_nodes):
                if index != 0:
                    content_node.relationships[
                        NodeRelationship.PREVIOUS
                    ] = content_nodes[index - 1].as_related_node_info()
                if index != len(content_nodes) - 1:
                    content_node.relationships[NodeRelationship.NEXT] = content_nodes[
                        index + 1
                    ].as_related_node_info()
                node_list.append(content_node)


if __name__ == "__main__":
    nodeParser = YiDongNodeParser()
    with open("/Users/<USER>/Desktop/工作内容/移动/documents/3D1280X1280.png.json", "r") as f:
        json_datas = json.load(f)
    root_nodes, nodes = nodeParser.get_nodes_from_json(json_datas, 79, 14)
