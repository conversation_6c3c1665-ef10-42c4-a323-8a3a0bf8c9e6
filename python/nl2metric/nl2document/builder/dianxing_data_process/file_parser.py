import os
import pdfplumber
import re
import json
import sys
import multiprocessing

from config.doc_config import ask_doc_index_temp_file_dir
from nl2document.builder.dianxing_data_process.pdf_parser3 import PdfParser
from nl2document.builder.dianxing_data_process.ppt_parser import <PERSON><PERSON><PERSON>arser
from nl2document.builder.parser.doc_parser import DocParser
from nl2document.builder.parser.html_parser import HtmlParser
from nl2document.builder.parser.file_model_parser import single_ppt_pdf_parse
from common.logging.logger import get_logger, get_elk_logger
from common.trace.perfomance_monitor import performance_context

logger = get_logger(__name__)
elk_logger = get_elk_logger()


class FileParser:
    def check_path(self, file_path):
        if not os.path.exists(file_path):
            raise RuntimeError("{} not exist".format(file_path))
        if not os.path.isfile(file_path):
            raise RuntimeError("{} is not a file".format(file_path))
        return True

    # 文档务必要先分类
    # 分类树：
    # project_folder:
    # -- language: chinese/english // 按语言分类，语言模型不一样
    #    -- type: text/files // 按文件类型分类，暂定纯文本和其他
    def parse(self, path: str, file_type: str, language: str, file_id: str = ""):
        self.check_path(path)

        suffix = path.split(".")[-1].lower()
        if suffix in ["pdf", "PDF"]:
            logger.info(f"start parse pdf document, file_id: {file_id}")
            if file_type == "text":
                return PdfParser().parser_pdf_by_rule(path, ask_doc_index_temp_file_dir)
            else:
                if language == "chinese":
                    language = "ch"
                return single_ppt_pdf_parse(path, language, ask_doc_index_temp_file_dir)
        elif suffix in ["pptx"]:
            print("pptx文档解析")
            logger.info(f"start parse pptx document, file_id: {file_id}")
            if language == "chinese":
                language = "ch"
            return single_ppt_pdf_parse(path, language, ask_doc_index_temp_file_dir)
        elif suffix in ["docx", "doc"]:
            logger.info(f"start parse docx document, file_id: {file_id}")
            return DocParser().parse(path, ask_doc_index_temp_file_dir, file_id)
        elif suffix in ["html"]:
            logger.info(f"start parse html document, file_id: {file_id}")
            return HtmlParser().parse(path, ask_doc_index_temp_file_dir, file_id)
        else:
            logger.exception(
                msg="{} not support".format(suffix),
                extra={
                    "file_path": path,
                    "file_type": file_type,
                    "file_id": file_id,
                },
            )
            elk_logger.info(
                msg="{} not support".format(suffix),
                extra={
                    "file_path": path,
                    "file_type": file_type,
                    "file_id": file_id,
                },
            )
            raise RuntimeError("{} not support".format(suffix))

    def parse_and_save(self, file_path, resource_dir, json_file_path):
        res = self.parse(file_path, resource_dir)
        f = open(json_file_path, "w", encoding="utf-8")
        json.dump(res, f, ensure_ascii=False)
        f.close()

    def parse_dir_documents(self, source_dir, target_dir):
        if not os.path.exists(source_dir):
            raise RuntimeError("{} not exist".format(source_dir))
        if not os.path.isdir(source_dir):
            raise RuntimeError("{} is not a dir".format(source_dir))
        if not os.path.exists(target_dir):
            os.makedirs(target_dir)

        workers = 6
        process_pool = multiprocessing.Pool(workers)
        tasks = []
        for file_name in os.listdir(source_dir):
            if file_name.split(".")[-1] not in ["pdf", "PDF", "pptx"]:
                continue
            file_path = os.path.join(source_dir, file_name)
            path = os.path.join(target_dir, file_name) + ".json"
            tasks.append(
                process_pool.apply_async(
                    self.parse_and_save, args=(file_path, "", path)
                )
            )
        process_pool.close()
        process_pool.join()
        print("解析结束")

    def get_pdf_file_type(self, file_path: str):
        try:
            pdf = pdfplumber.open(file_path)
        except Exception as e:
            return "file_error"
        flag = 0
        file_type = ""
        for p in pdf.pages:
            try:
                t = p.extract_text()
            except Exception as e:
                file_type = "picture"
                flag = 3
                break
            if re.match(r"\(cid:\d{1,5}\)", t):
                file_type = "unreadable_codes"
                flag = 0
                break
            if t.strip():
                # 有扫描全能王等字为扫描件
                if t.find("扫") != -1 and t.find("描") != -1:
                    flag = 0
                    break
                else:
                    file_type = "non_scanned_file"
                    flag = 1
                    break
        if flag == 0:
            file_type = "scanned_file"
        return file_type


if __name__ == "__main__":
    file_parser = FileParser()
    if len(sys.argv) != 4:
        print("Usage: python xxx.py <file|dir> <document_path> <result_dir>")
        sys.exit(1)
    if sys.argv[1] == "file":
        file_path = sys.argv[2]
        result_dir = sys.argv[3]
        file_parser.check_path(file_path)
        if not os.path.exists(result_dir):
            os.makedirs(result_dir)

        file_name = os.path.basename(file_path)
        res = file_parser.parse(file_path, "")
        path = os.path.join(result_dir, file_name) + ".json"
        f = open(path, "w", encoding="utf-8")
        json.dump(res, f, ensure_ascii=False)
        f.close()
    elif sys.argv[1] == "dir":
        source_file_dir = sys.argv[2]
        result_json_file_dir = sys.argv[3]
        file_parser.parse_dir_documents(source_file_dir, result_json_file_dir)

    # res = FileParser().parse("/Users/<USER>/Desktop/工作内容/电信政企知识库/卫健/pdf/扫描_575/1592424629874483205_核酸检测样本采集系统服务器迁移项目-合同.pdf", "")
