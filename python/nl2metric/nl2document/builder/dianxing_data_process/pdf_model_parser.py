import json
import math
import os
from multiprocessing import Process, set_start_method
import torch
import re
from typing import Dict

from llama_index.core.node_parser import TokenTextSplitter
import cv2
import numpy as np
import pdfplumber
import pytesseract as pt
from PIL import Image

from detectron2.config import get_cfg
from nl2document.builder.layoutlm.ditod.config import add_vit_config
from transformers import AutoTokenizer

import detectron2.data.transforms as T
from detectron2.checkpoint import DetectionCheckpointer
from nl2document.builder.layoutlm.ditod.mytrainer import MyTrainer

cfg = get_cfg()
add_vit_config(cfg)
base_directory = os.path.dirname(os.path.realpath(__file__))
cfg.merge_from_file(str(base_directory) + "/config.yml")

tokenizer = AutoTokenizer.from_pretrained(
    os.path.join(base_directory, "baichuan_tokens"), trust_remote_code=True
)

splitter = TokenTextSplitter(chunk_size=1024, tokenizer=tokenizer.encode)


cfg.MODEL.WEIGHTS = "/home/<USER>/projects/unilm/layoutlmv3/examples/object_detection/zq.output/model_final.pth"
paragraphs_key = "paragraphs"


def clean_str(string):
    return re.sub(r" +", " ", string.strip())


def split_texts(text_accum: str, current_texts: []):
    t = []
    contents = text_accum.split("\n")

    texts = []
    tmp_content = ""
    for content in contents:
        if content:
            pre_tmp_content = tmp_content
            if not pre_tmp_content:
                pre_tmp_content = content
            else:
                pre_tmp_content = pre_tmp_content + "\n" + content
            length = len(splitter.tokenizer(pre_tmp_content))
            if length >= splitter.chunk_size and tmp_content:
                tmp_texts = splitter.split_text(tmp_content)
                texts.extend(tmp_texts)
                tmp_content = content
            else:
                tmp_content = pre_tmp_content
    if tmp_content:
        tmp_texts = splitter.split_text(tmp_content)
        texts.extend(tmp_texts)
    for i in range(len(texts)):
        page_num = get_chunk_page_num(texts[i], current_texts)
        t.append({"text": texts[i], "id": i + 1, "page": page_num})
    return t


# 获取chunk内容所在页面的页码数
def get_chunk_page_num(chunk_texts: str, current_texts: []):
    chunk_text_list = chunk_texts.split("\n")
    for i in range(len(current_texts)):
        if clean_str(current_texts[i]["text"]) == chunk_text_list[0]:
            if len(chunk_text_list) > 1:  # chunk里面有多个段落 就比较最前面两个段落就可以了
                if clean_str(current_texts[i + 1]["text"]) == chunk_text_list[1]:
                    return current_texts[i]["page"]
            else:
                return current_texts[i]["page"]
    # chunk是从一个大长段落里面拆分出来的
    return current_texts[0]["page"]


def pdf_json_cleansing(pdf_json):
    paragraphs = pdf_json["paragraphs"]
    json_file_name = clean_str(pdf_json["file_name"])
    final_json = {
        "file_name": json_file_name,
        "file_type": "pdf",
    }
    parts = []

    if len(paragraphs) == 0:
        final_json["parts"] = parts
        return final_json

    pre_title = clean_str(paragraphs[0]["title"])
    part_sort = 1
    text_accum = ""
    current_texts = []
    for i, para in enumerate(paragraphs):
        cur_title = clean_str(para["title"])
        text = clean_str(para["texts"][0]["text"])

        if text == "":
            continue

        if cur_title != pre_title and cur_title != "":
            contents = split_texts(text_accum.strip(), current_texts)
            text_accum = ""
            parts.append(
                {
                    "part_name": pre_title,
                    "part_level": 1,
                    "part_sort": part_sort,
                    "contents": contents,
                }
            )
            part_sort += 1
            pre_title = cur_title
            current_texts = []
        text_accum += "\n" + text
        current_texts.append(para["texts"][0])
    if text_accum.strip():
        parts.append(
            {
                "part_name": pre_title,
                "part_level": 1,
                "part_sort": part_sort,
                "contents": split_texts(text_accum.strip(), current_texts),
            }
        )
    final_json["parts"] = parts
    return final_json


class PdfModelParser:
    categories = [
        "title",
        "paragraph",
        "catalog",
        "picture",
        "footer",
        "second-title",
        "table",
        "third-title",
        "header",
        "margin",
        "caption",
        "fourth-title",
        "source",
    ]

    def init_model(self, cuda_device):
        torch.cuda.set_device(cuda_device)

        self.model = MyTrainer.build_model(cfg)
        self.model.eval()
        checkpointer = DetectionCheckpointer(self.model)
        checkpointer.load(cfg.MODEL.WEIGHTS)
        self.aug = T.ResizeShortestEdge(
            [cfg.INPUT.MIN_SIZE_TEST, cfg.INPUT.MIN_SIZE_TEST], cfg.INPUT.MAX_SIZE_TEST
        )

    def inference(self, img):
        with torch.no_grad():
            if cfg.INPUT.FORMAT == "RGB":
                img = img[:, :, ::-1]

            height, width = img.shape[:2]
            img = self.aug.get_transform(img).apply_image(img)
            img = torch.as_tensor(img.astype("float32").transpose(2, 0, 1))

            inputs = {"image": img, "height": height, "width": width}
            predictions = self.model([inputs])[0]
            return predictions

    def __init__(self, cuda_device):
        self.id = 0
        self.res = {}
        self.init_model(cuda_device)

    def get_ocr_str(img):
        """
        img = Image.read(img_file)
        """
        context = pt.image_to_string(img, lang="chi_sim")
        return context.replace("\n", "")

    def crop_image(image, scope):
        """将原图片按照scope进行切割，image为被截取的图片，
        scope为需要截取的轮廓的范围，形式为[x1,x2,y1,y2],(x1,y1)为包裹轮廓的直矩形的左上点，（x2,y2）为右下点"""
        x1 = scope[0]
        x2 = scope[2]
        y1 = scope[1]
        y2 = scope[3]
        img = image[int(y1) : int(y2), int(x1) : int(x2)]
        return img

    def process_model_output(self, img, is_ocr_img=False):
        # 模型预测
        predict = self.inference(img)
        if predict.get("instances").get("pred_boxes").tensor.size()[0] == 0:
            return []
        # filter
        arr = np.hstack(
            (
                predict.get("instances").get("pred_boxes").tensor.tolist(),
                np.transpose([predict.get("instances").get("pred_classes").tolist()]),
                np.transpose([predict.get("instances").get("scores").tolist()]),
            )
        )
        arr = arr[np.where(arr[:, 5] > 0.5)]

        ocr_res = []
        labels = []
        for item in arr:
            ocr_img = PdfModelParser.crop_image(img, item)
            image = Image.fromarray(cv2.cvtColor(ocr_img, cv2.COLOR_BGR2RGB))

            label = PdfModelParser.categories[int(item[4])]
            ocr_str = None
            if label != "picture":
                ocr_str = PdfModelParser.get_ocr_str(image)
            # image.show(ocr_str)

            ocr_res.append(ocr_str)
            labels.append(label)
        arr = np.hstack((arr, np.transpose([labels]), np.transpose([ocr_res])))
        arr = arr[np.lexsort((arr[:, 1].astype(float),))]
        left_arr = arr[np.where(arr[:, 0].astype(float) < img.shape[0] / 2)]
        right_arr = arr[np.where(arr[:, 0].astype(float) > img.shape[0] / 2)]
        arr = np.vstack((left_arr, right_arr))
        return arr

    def process_pages(self, pdf, start_page=0, page_number=None, scale=2):
        paragraphs = []
        if not page_number:
            pages = pdf.pages[start_page:]
        else:
            pages = pdf.pages[start_page : start_page + page_number]
        titles = ["title", "second-title", "third-title", "fourth-title"]
        for page in pages:
            print(f"processing page {page.page_number}")
            im = page.to_image(resolution=72 * scale).original
            cv_image = cv2.cvtColor(np.array(im), cv2.COLOR_RGB2BGR)
            predict = self.process_model_output(cv_image)
            # print('predict>>>>>>>> :  ',predict)
            # print('==================')
            if len(predict) == 0:
                continue
            cur_title = ""
            for item in predict[:, [6, 7]]:
                if item[0] in ["header", "footer", "source", "caption"]:
                    continue
                elif item[0] in titles:
                    cur_title = item[1]
                elif item[1]:
                    paragraphs.append(
                        {
                            "title": cur_title,
                            "texts": [
                                {
                                    "text": item[1],
                                    "page": page.page_number,
                                    "id": self.id,
                                }
                            ],
                        }
                    )
                    self.id += 1
        return {"paragraphs": paragraphs}

    def save_result(self, file_path: str, res: Dict) -> None:
        name = os.path.basename(file_path)
        folder_path = os.path.dirname(os.path.dirname(file_path)) + "/" + "扫描件pdf解析结果"
        print(folder_path)
        if not os.path.exists(folder_path):
            os.mkdir(folder_path)
        path = folder_path + "/" + name + ".json"
        f = open(path, "w", encoding="utf-8")
        json.dump(res, f, ensure_ascii=False)
        f.close()

    def parse(self, path: str, start_page=0, page_number=None, scale=2) -> dict:
        pdf = pdfplumber.open(path)
        file_name = path.split("/")[-1]
        res = self.process_pages(pdf, start_page=start_page, page_number=page_number)

        res["file_name"] = file_name
        if pdf.metadata.__contains__("Author"):
            res["author"] = pdf.metadata.get("Author")
        if pdf.metadata.__contains__("ModDate"):
            res["update_time"] = pdf.metadata.get("ModDate")
        if pdf.metadata.__contains__("CreationDate"):
            res["create_time"] = pdf.metadata.get("CreationDate")

        return res

    def process_pdf(self, file_path: str):
        res = self.parse(file_path)
        file_name = res["file_name"]
        if len(res["paragraphs"]) == 0:
            raise RuntimeError("{} has empty paragraphs".format(file_name))
        final_json = pdf_json_cleansing(res)
        # self.save_result(file_path, final_json)
        return final_json


def parse_pdfs(pdf_dir, output_dir):
    all_pdfs = []
    for pdf_name in os.listdir(pdf_dir):
        if pdf_name.endswith(".pdf") or pdf_name.endswith(".PDF"):
            all_pdfs.append((pdf_name, os.path.join(pdf_dir, pdf_name), output_dir))

    set_start_method("spawn")
    os.environ["CUDA_VISIBLE_DEVICES"] = "0,1,2"

    world_size = 9
    cuda_devices = [
        "cuda:0",
        "cuda:1",
        "cuda:2",
        "cuda:0",
        "cuda:1",
        "cuda:2",
        "cuda:0",
        "cuda:1",
        "cuda:2",
    ]

    split_size = math.ceil(len(all_pdfs) / world_size)
    processes = [
        Process(
            target=process,
            args=(all_pdfs[i * split_size : (i + 1) * split_size], cuda_devices[i]),
        )
        for i in range(world_size)
    ]
    for p in processes:
        p.start()
    for p in processes:
        p.join()


def process(pdfs, cuda_device):
    print(f"Process {os.getpid()} started on cuda {cuda_device}")
    for name, src, output_dir in pdfs:
        print(f"Processing {src}")

        parser = PdfModelParser(cuda_device)
        final_json = pdf_json_cleansing(parser.parse(src))
        with open(f"{output_dir}/{name}.json", "w") as f:
            json.dump(final_json, f, ensure_ascii=False)

    print(f"Process {os.getpid()} finished on cuda {cuda_device}")


if __name__ == "__main__":
    os.environ["CUDA_VISIBLE_DEVICES"] = "0,1,2"
    cuda_device = "cuda:0"
    PdfModelParser(cuda_device).process_pdf(
        "/home/<USER>/documents/261272555597332480_重庆市合川区区委办公室.pdf"
    )
