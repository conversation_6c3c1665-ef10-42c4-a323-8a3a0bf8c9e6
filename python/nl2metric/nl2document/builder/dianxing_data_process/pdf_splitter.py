import os
from enum import Enum
from typing import Optional

from llama_index.core.node_parser import TokenTextSplitter


# class SplitterStrategy(Enum):
class SplitHandler:
    def __init__(
        self,
        name: str,
        chunk_size: int,
        chunk_overlap: int,
        splitter: TokenTextSplitter,
        auto_split: Optional[bool] = False,
    ):
        self.name = name
        self.auto_split = auto_split
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.splitter = splitter

    def __str__(self):
        return f"{self.name} (Chunk Size: {self.chunk_size}, Chunk Overlap: {self.chunk_overlap})"

    def split_texts(self, text: str, titles: {}, current_paragraphs: []) -> []:
        t = []

        max_level = 0  # 目录最大等级
        for value in titles.values():
            max_level = value if value > max_level else max_level
        chunk_size = self.splitter.chunk_size
        texts = self.split_texts_some(text, titles, 1, chunk_size, max_level)

        for i in range(len(texts)):
            page_num = self.get_chunk_page_num(texts[i], current_paragraphs)
            t.append({"text": texts[i], "id": i + 1, "page": page_num})
        return t

    def split_texts_some(
        self, text: str, titles: {}, level, chunk_size, max_level
    ) -> []:
        texts = [text]
        if len(self.splitter.tokenizer(text)) >= chunk_size:
            texts = []
            contents = text.split("\n")
            pre_contents = ""
            texts_str = []
            for content in contents:
                if (
                    titles.get(content.replace(" ", ""), -2) == level
                ):  # level比i小的标题 标题等级为0-max_level 其中0为一级标题 最大
                    if pre_contents:
                        texts_str.append(pre_contents)
                    pre_contents = content
                else:
                    if pre_contents:
                        pre_contents = pre_contents + "\n" + content
                    else:
                        pre_contents = content
            if pre_contents:
                texts_str.append(pre_contents)
            # 按level级标题拆分后
            upper_level_text = ""
            for i, text_str in enumerate(texts_str):
                # 首段内容
                first_str = text_str.split("\n")[0]
                # 判断是否为该level下的内容
                if titles.get(first_str.replace(" ", ""), -2) == level:
                    if upper_level_text:
                        text_str = upper_level_text + "\n" + text_str
                        upper_level_text = ""
                    if (
                        (i >= len(texts_str) - 1)
                        or (
                            len(self.splitter.tokenizer(texts_str[i + 1]))
                            >= chunk_size / 4
                            and len(self.splitter.tokenizer(text_str)) >= chunk_size / 4
                        )
                        or len(
                            self.splitter.tokenizer(text_str + "\n" + texts_str[i + 1])
                        )
                        >= chunk_size
                    ):
                        last_text = texts[-1] + "\n" if len(texts) > 0 else ""
                        if (
                            i == len(texts_str) - 1
                            and len(self.splitter.tokenizer(text_str)) <= 100
                            and len(self.splitter.tokenizer(last_text + text_str))
                            < chunk_size
                        ):  # 最后一个level级标题下内容非常少 可以的话和上一个同级标题下内容合并
                            if len(texts) > 0:
                                texts[-1] = last_text + text_str
                            else:
                                texts.append(last_text + text_str)
                        else:
                            texts.extend(
                                self.split_texts_some(
                                    text_str, titles, level + 1, chunk_size, max_level
                                )
                            )
                    else:
                        upper_level_text = text_str
                else:  # 这里最先处理 是texts_str[0] level-1级标题下内容
                    if (
                        len(self.splitter.tokenizer(text_str)) >= 60
                        or len(texts_str) < 2
                    ):
                        texts.extend(self.split_texts_only(text_str))
                    else:
                        upper_level_text = text_str

        return texts

    # 获取chunk内容所在页面的页码数
    def get_chunk_page_num(self, chunk_texts: str, current_paragraphs: []):
        chunk_text_list = chunk_texts.split("\n")
        for i in range(len(current_paragraphs)):
            if current_paragraphs[i]["text"] == chunk_text_list[0]:
                if len(chunk_text_list) > 1:  # chunk里面有多个段落 就比较最前面两个段落就可以了
                    if current_paragraphs[i + 1]["text"] == chunk_text_list[1]:
                        return current_paragraphs[i]["page"]
                else:
                    return current_paragraphs[i]["page"]
        # chunk是从一个大长段落里面拆分出来的
        return current_paragraphs[0]["page"]

    def split_texts_only(self, text: str) -> []:
        contents = text.split("\n")
        texts = []
        tmp_content = ""
        for content in contents:
            if content:
                pre_tmp_content = tmp_content
                if not pre_tmp_content:
                    pre_tmp_content = content
                else:
                    pre_tmp_content = pre_tmp_content + "\n" + content
                length = len(self.splitter.tokenizer(pre_tmp_content))
                if length >= self.splitter.chunk_size and tmp_content:
                    tmp_texts = self.splitter.split_text(tmp_content)
                    texts.extend(tmp_texts)
                    tmp_content = content
                else:
                    tmp_content = pre_tmp_content
        if tmp_content:
            tmp_texts = self.splitter.split_text(tmp_content)
            texts.extend(tmp_texts)
        return texts

    # todo split
    def split_page_texts_rule(self, text: str, current_paragraphs: []) -> []:
        t = []
        texts = self.split_texts_only(text)
        for i in range(len(texts)):
            page_num = self.get_chunk_page_num(texts[i], current_paragraphs)
            t.append({"text": texts[i], "id": i + 1, "page": page_num})
        return t

    def split_page_texts(self, text: str, page_num) -> []:
        t = []
        texts = self.split_texts_only(text)
        for i in range(len(texts)):
            t.append({"text": texts[i], "id": i + 1, "page": page_num})
        return t
