import json
import math
import multiprocessing
import os, re, pdfplumber
from typing import Dict, List, Optional

from llama_index.core.node_parser import TokenTextSplitter
from pdfminer.layout import LTChar
from pdfplumber import PDF
from pdfplumber._typing import T_obj, T_bbox
from pdfplumber.page import Page
from pyinstrument import Profiler

from nl2document.builder.dianxing_data_process.pdf_splitter import SplitHandler
from nl2document.builder.parser.node_parser import Tokenized
from nl2document.builder.parser.parsed_document import ContentType


splitHandler = SplitHandler(
    name="dianxin",
    splitter=TokenTextSplitter(tokenizer=Tokenized(), chunk_size=500, chunk_overlap=50),
    chunk_size=500,
    chunk_overlap=50,
)


class PdfParser:
    catalog_pattern = r"^[\s\S]*[-.·…． ]{10,}([- ]{0,2}\d{1,4}[- ]{0,2}|错误!未定义书签。)$"
    catalog_pattern1 = r"^[\s\S]*[ ]{10,}\s*(\d{1,4}|错误!未定义书签。)"
    catalog_pattern2 = r"^[\s\S]*[.]{10,}\s*(\d{1,4}|错误!未定义书签。)"
    catalog_pattern3 = r"^[\s\S]*[-]{10,}\s*(\d{1,4}|错误!未定义书签。)"
    catalog_pattern4 = r"^[\s\S]*[·]{10,}\s*(\d{1,4}|错误!未定义书签。)"
    page_num_pattern = r"(^\s*\d+\s*$)|(^\s*([-––—第]*\s*\d{1,4}\s*[-––—页]+)\s*$)|(^([-––—第]*\s*\d{1,4}\s*[-––—页]*$))|(^\s*\d+\s*/\s*\d+\s*$)|(^[ -––—第]*[XVIL]{1,8}[-––—页 ]*$)"
    title_label_patterns = r"^[\d一二三四五六七八九十]{1,3}[\.、]"

    def getPdfContent(self, pdf):
        # pdf = pdfplumber.open(file_name)
        start_page = -1
        end_page = 0
        is_ocr_pdf = True
        for i in range(0, len(pdf.pages)):
            pattern = r"\s*[. ]{10,}\s*\d{1,3}\s*"
            pageObj = pdf.pages[i]

            extractedText = pageObj.extract_text()
            if extractedText:
                is_ocr_pdf = False
            if re.search(pattern, extractedText) is not None:
                if end_page == 0:
                    start_page = i + 1
                end_page = i + 1
            else:
                if end_page > 0:
                    break
        return [start_page, end_page], is_ocr_pdf

    def catalog_determine(self, catalogss: [], paragraphs: List) -> bool:
        titles = []
        catalogs_titles = []
        not_parsed_title_num = 0
        for p in paragraphs:
            if re.match(r"^[一二三四五六七八九十]{1,3}[、.]", p["text"]):
                titles.append(p["text"].replace(" ", ""))
        for catalog in catalogss:
            catalogs_titles.append(catalog["text"])
        for title in titles:
            if title not in catalogs_titles:
                not_parsed_title_num += 1
        titles_num = len(titles)
        if titles_num > 0 and not_parsed_title_num / titles_num >= 0.4:
            return False
        else:
            return True

    def get_catalogs_level(self, catalogs, title_text):
        for catalog in catalogs:
            if catalog["text"].replace(" ", "") == title_text.replace(" ", ""):
                return catalog["level"]
        return -1

    def parse_catalog_extra(
        self, catalogs: [], paragraphs: []
    ):  # 对目录进行补充 识别更细小的标题 或者是无目录文档通过正则匹配识别标题
        if catalogs:
            title_formats = [
                r"^第[0-9一二三四五六七八九十]{1,3}[章|节|章节]",
                r"^[一二三四五六七八九十]{1,3}([.、]|\s+)",
                r"^[\(\（]?[一二三四五六七八九十]{1,3}[\)\）]\s*[\d\D]+",
                r"^\d+[.]\s*[^-.\d]\D+",
                r"^\d+[.-]\d+\s*[^-.\d]\D+",
                r"^\d+[.-]\d+[.-]\d*\s*[^-.\d]\D+",
                r"^\d+[.-]\d+[.-]\d+[.-]\d*\s*[^-.\d]\D+",
                r"^\d+[.-]\d+[.-]\d+[.-]\d+[.-]\d*\s*[^-.\d]\D+",
                r"^\d+[.-]\d+[.-]\d+[.-]\d+[.-]\d+[.-]\d*\s*[^-.\d]\D+",
                # r"^[\(\（]\d+[\)\）]\s*[\d\D]+",
                # r"^\d+[\)\）]\s*[\d\D]+",
            ]
            for i in range(len(title_formats)):
                occurrence_level = -1
                char_height = 0  # 字体大小
                first_char_x = -1  # 段落首字符的横坐标
                title_format = title_formats[i]
                maybe_smallest = False
                if title_format in [
                    r"^[\(\（]\d+[\)\）]\s*[\d\D]+",
                    r"^\d+[\)\）]\s*[\d\D]+",
                ]:
                    maybe_smallest = True  # 可能是最小的段落
                pre_title = ""
                for p_num, p in enumerate(paragraphs):
                    if re.search(
                        r"(^[-]+$)|([.„]{6,})|(\D+[._]{1,}\s*[\d]+\s*(/\s*\d+)?\s*$)",
                        p["text"].replace(" ", ""),
                    ):
                        continue
                    if self.get_catalogs_level(catalogs, p["text"]) != -1:
                        occurrence_level = self.get_catalogs_level(catalogs, p["text"])
                        pre_title = p["text"]
                        continue
                    if re.match(r"^\d+[.]\s*[^-.\d]\D+", p["text"]) and re.findall(
                        r"[。;；,，:：]", p["text"]
                    ):  # 处理 1. xxx
                        continue
                    if (
                        re.match(title_format, p["text"])
                        and not re.findall(r"[。;；]", p["text"])
                        and (
                            int(p["first_char"]["height"]) == int(char_height)
                            or char_height == 0
                        )
                        and (
                            abs(p["first_char"]["bbox"][0] - first_char_x)
                            <= p["first_char"]["height"] / 2
                            or first_char_x == -1
                        )
                        and (
                            not maybe_smallest
                            or (
                                (
                                    (p_num >= len(paragraphs) - 1)
                                    or not re.match(
                                        title_format, paragraphs[p_num + 1]["text"]
                                    )
                                )
                                and (
                                    p_num == 0
                                    or not re.match(
                                        title_format, paragraphs[p_num - 1]["text"]
                                    )
                                )
                            )
                        )
                    ):  # 同级别标题字体大小相同
                        char_height = p["first_char"]["height"]
                        first_char_x = p["first_char"]["bbox"][0]
                        level = (
                            occurrence_level + 1
                            if not re.match(title_format, pre_title.replace(" ", ""))
                            else occurrence_level
                        )
                        for catalog in catalogs:
                            if re.match(title_format, catalog["text"]):
                                level = catalog["level"]
                                break
                        catalogs.append(
                            {
                                "text": p["text"],
                                "page": p["page"],
                                "level": level,
                            }
                        )
            return catalogs
        else:
            catalogs = []

            title_formats = [
                r"^第[0-9一二三四五六七八九十]{1,3}[章|节|章节]",
                r"^第[0-9一二三四五六七八九十]{1,3}部分",
                r"^第[0-9一二三四五六七八九十]{1,3}条",
                r"^[一二三四五六七八九十]{1,3}([.、]|\s+)",
                r"^[\(\（]?[一二三四五六七八九十]{1,3}[\)\）]\s*[\d\D]+",
                r"^\d+[.-]\d+\s*[^-.\d]\D+",
                r"^\d+[.-]\d+[.-]\d*\s*[^-.\d]\D+",
                r"^\d+[.-]\d+[.-]\d+[.-]\d*\s*[^-.\d]\D+",
                r"^\d+[.-]\d+[.-]\d+[.-]\d+[.-]\d*\s*[^-.\d]\D+",
                r"^\d+[.-]\d+[.-]\d+[.-]\d+[.-]\d+[.-]\d*\s*[^-.\d]\D+",
                # r"^[\(\（]\d+[\)\）]\s*[\d\D]+",
                # r"^\d+[\)\）]\s*[\d\D]+",
            ]
            for i in range(len(title_formats)):
                occurrence_level = -1
                char_height = 0  # 字体大小
                first_char_x = -1  # 段落首字符的横坐标
                title_format = title_formats[i]
                maybe_smallest = False
                if title_format in [
                    r"^[\(\（]\d+[\)\）]\s*[\d\D]+",
                    r"^\d+[\)\）]\s*[\d\D]+",
                ]:
                    maybe_smallest = True  # 可能是最小的段落
                for p_num, p in enumerate(paragraphs):
                    if re.search(
                        r"(^[-]+$)|([.„]{6,})|(\D+[._]{1,}\s*[\d]+\s*(/\s*\d+)?\s*$)",
                        p["text"].replace(" ", ""),
                    ):
                        continue
                    if p.get("level", -1) != -1:
                        occurrence_level = p["level"]
                    if (
                        re.match(title_format, p["text"])
                        and not re.findall(r"[。;；]", p["text"])
                        and (
                            int(p["first_char"]["height"]) == int(char_height)
                            or char_height == 0
                        )
                        and (
                            abs(p["first_char"]["bbox"][0] - first_char_x)
                            <= p["first_char"]["height"] / 2
                            or first_char_x == -1
                        )
                        and (
                            not maybe_smallest
                            or (
                                (
                                    (p_num >= len(paragraphs) - 1)
                                    or not re.match(
                                        title_format, paragraphs[p_num + 1]["text"]
                                    )
                                )
                                and (
                                    p_num == 0
                                    or not re.match(
                                        title_format, paragraphs[p_num - 1]["text"]
                                    )
                                )
                            )
                        )
                    ):  # 同级别标题字体大小相同
                        char_height = p["first_char"]["height"]
                        first_char_x = p["first_char"]["bbox"][0]
                        p["level"] = occurrence_level + 1
                        catalogs.append(
                            {
                                "text": p["text"],
                                "page": p["page"],
                                "level": occurrence_level + 1,
                            }
                        )
            return catalogs

    def parse_catalog(self, pdf, paragraphs: List):
        catalogs = []
        try:
            for outline in pdf.doc.get_outlines():
                level = outline[0] - 1
                title = outline[1].replace(" ", "")
                objid = outline[2][0].objid
                if re.match("PO_", title):
                    raise Exception()
                if not re.match(r"^[(（]?\d+[)）、]\s*[^-.\d]\D+", title):
                    catalogs.append({"text": title, "level": level, "objid": objid})
            if not self.catalog_determine(catalogs, paragraphs):
                catalogs = []
            if not catalogs:
                raise Exception()
            return catalogs
        except Exception as e:
            catalogs = []
            catalog_paragraphs = [
                p for p in paragraphs if p.get("tag", "") == "catalog"
            ]
            notation = " "
            if catalog_paragraphs:
                if re.match(PdfParser.catalog_pattern2, catalog_paragraphs[-1]["text"]):
                    notation = "."
                elif re.match(
                    PdfParser.catalog_pattern3, catalog_paragraphs[-1]["text"]
                ):
                    notation = "-"
                elif re.match(
                    PdfParser.catalog_pattern4, catalog_paragraphs[-1]["text"]
                ):
                    notation = "·"
                for p in catalog_paragraphs:
                    r = [i for i in p["text"].split(notation * 5) if i != ""]
                    if r and len(r) < 2:
                        continue
                    title = r[0]
                    match_page_num = re.findall(r"\d+", r[1])
                    page_num = -1
                    # if page_num_str.find("错误!未定义书签。") == -1:
                    if match_page_num:
                        page_num = int(match_page_num[0])
                    catalogs.append(
                        {
                            "text": title,
                            "page": page_num,
                            "x": p["first_char"]["bbox"][0],
                        }
                    )
                sorted_index = sorted(
                    range(len(catalogs)), key=lambda j: catalogs[j]["x"]
                )
                level = 0
                for i, index in enumerate(sorted_index):
                    if (
                        i != 0
                        and abs(
                            catalogs[index]["x"] - catalogs[sorted_index[i - 1]]["x"]
                        )
                        > 1
                    ):
                        level = level + 1
                    catalogs[index]["level"] = level
            else:
                for i, p in enumerate(paragraphs):
                    if p["text"].replace(" ", "") == "目录":
                        for item in paragraphs[i + 1 :]:
                            if item["page"] == p["page"]:
                                if not re.match(
                                    PdfParser.page_num_pattern, item["text"]
                                ):
                                    catalogs.append(
                                        {
                                            "text": item["text"],
                                            "x": item["first_char"]["bbox"][0],
                                            "level": 0,
                                        }
                                    )
                            else:
                                return catalogs
            return catalogs

    def filter_paragraphs(self, paragraphs: [], titles, multi_occurrences, file_title):
        filter_paragraphs = []
        filter_paragraphs_pre = []
        already_titles = []  # 出现过的标题
        just_catalog_titles = []  # 只是目录
        # 去除目录及不需要的段落内容
        for i, p in enumerate(paragraphs):
            # 乱码替换
            # p["text"] = re.sub(r"\(cid:\d{1,5}\)", "", p["text"])
            title = p["text"].replace(" ", "")
            if title in titles:
                if title not in already_titles:
                    already_titles.append(title)
                else:
                    if title not in just_catalog_titles:
                        just_catalog_titles.append(title)
            if (
                p["text"].replace(" ", "") == "目录"
                or re.search(
                    r"(^[-]+$)|([.„]{6,})|(\D+[._]{1,}\s*[\d]+\s*(/\s*\d+)?\s*$)",
                    p["text"].replace(" ", ""),
                )
                or re.sub(r"\d|[a-zA-Z]", "", p["text"]) in multi_occurrences
                or (file_title and i == 0)  # 去除 文件标题的段落
            ):
                continue
            filter_paragraphs_pre.append(p)
        for filter_paragraph in filter_paragraphs_pre:
            title = filter_paragraph["text"].replace(" ", "")
            if title in just_catalog_titles:
                just_catalog_titles.remove(title)
                continue
            filter_paragraphs.append(filter_paragraph)
        return filter_paragraphs

    def get_paragraphs_contents_by_page(self, current_paragraphs: []) -> []:
        contents = []
        if not current_paragraphs:
            return contents
        content_text = ""
        content_page = current_paragraphs[0]["page"]
        for p in current_paragraphs:
            if p["page"] != content_page:
                if content_text:
                    contents.append(
                        {
                            "content_type": ContentType.TEXT.name,
                            "text": content_text,
                            "images": [],
                            "tables": [],
                            "extra_info": {
                                "page_header": "",
                                "page_footer": "",
                                "page_num": "",
                                "real_page_num": content_page,
                            },
                        }
                    )
                content_text = p["text"]
                content_page = p["page"]
            else:
                if content_text:
                    content_text = content_text + "\n" + p["text"]
                else:
                    content_text = p["text"]
        if content_text:
            contents.append(
                {
                    "content_type": ContentType.TEXT.name,
                    "text": content_text,
                    "images": [],
                    "tables": [],
                    "extra_info": {
                        "page_header": "",
                        "page_footer": "",
                        "page_num": "",
                        "real_page_num": content_page,
                    },
                }
            )
        return contents

    def get_part_catalog_by_level(
        self, total_title: str, titles: {}, level: int, has_catalogs: bool
    ) -> {}:
        catalogs = {}
        if level == 0:  # 取整个目录
            if not has_catalogs:
                if total_title:
                    return {"chapter_title": total_title}
                else:
                    return catalogs
            else:
                catalogs = titles
                chapter_title = "目录" if not total_title else total_title
        else:
            chapter_title = total_title.replace(" ", "")
            is_catalog_part = False
            for title, title_level in titles.items():
                if level - 1 == title_level:
                    is_catalog_part = False
                if title == total_title.replace(" ", "") and (level - 1 == title_level):
                    is_catalog_part = True
                if is_catalog_part and title_level >= level:
                    catalogs[title] = title_level
        part_catalog = {
            "chapter_title": chapter_title,
        }
        sub_catalogs = self.get_sub_catalogs(catalogs, level)
        if sub_catalogs and level == 0:
            part_catalog["sub_catalogs"] = sub_catalogs
        return part_catalog

    def get_sub_catalogs(self, catalogs: {}, max_level: int) -> []:
        sub_catalogs = []
        if not catalogs:
            return sub_catalogs

        sub_catalog = {}
        sub_sub_catalogs = {}
        pre_chapter_title = ""
        for title, level in catalogs.items():
            if level == max_level:
                if pre_chapter_title:
                    sub_catalog["chapter_title"] = pre_chapter_title
                    tmp_catalogs = self.get_sub_catalogs(
                        sub_sub_catalogs, max_level + 1
                    )
                    if tmp_catalogs:
                        sub_catalog["sub_catalogs"] = tmp_catalogs
                    pre_chapter_title = title
                    sub_catalogs.append(sub_catalog)
                    sub_catalog = {}
                    sub_sub_catalogs = {}
                else:
                    pre_chapter_title = title
            else:
                sub_sub_catalogs[title] = level
        if pre_chapter_title:
            sub_catalog["chapter_title"] = pre_chapter_title
            tmp_catalogs = self.get_sub_catalogs(sub_sub_catalogs, max_level + 1)
            if tmp_catalogs:
                sub_catalog["sub_catalogs"] = tmp_catalogs
            sub_catalogs.append(sub_catalog)
        return sub_catalogs

    def process_title_paragraph_level(
        self,
        total_title: str,
        titles: {},
        paragraphs: [],
        level: int,
        has_catalogs: bool,
    ) -> []:
        pre_title = total_title  # 总标题
        is_next = False
        current_paragraphs, output = [], []
        output.append(
            {
                "cover": {},
                "preface": {},
                "quote": {},
                "appendix": {},
                "catalog": self.get_part_catalog_by_level(
                    pre_title, titles, level, has_catalogs
                ),
                "contents": [],
                "chapters": [],
            }
        )
        for p in paragraphs:
            title = p["text"].replace(" ", "")
            if titles.get(title, -2) == level:
                if not is_next:
                    output[0]["contents"] = self.get_paragraphs_contents_by_page(
                        current_paragraphs
                    )
                else:
                    output_part = self.process_title_paragraph_level(
                        pre_title, titles, current_paragraphs, level + 1, has_catalogs
                    )
                    output[0]["chapters"].append(output_part[0])
                is_next = True
                pre_title = p["text"]
                current_paragraphs = []
            else:
                current_paragraphs.append(p)
        if not is_next:
            output[0]["contents"] = self.get_paragraphs_contents_by_page(
                current_paragraphs
            )
        else:
            output_part = self.process_title_paragraph_level(
                pre_title, titles, current_paragraphs, level + 1, has_catalogs
            )
            output[0]["chapters"].append(output_part[0])
        return output

    def process_title_paragraph(
        self, res: {}, catalogs: [], paragraphs: [], has_catalogs: bool
    ) -> []:
        multi_occurrences = self.get_multi_occurrences(paragraphs)
        file_title = res["file_title"]
        res["catalogs"] = catalogs
        titles = {}
        for catalog in catalogs:
            if not re.match(r"^\d+[.]?\d*$", catalog["text"].replace(" ", "")):
                titles[catalog["text"].replace(" ", "")] = catalog["level"]
        # 去除掉paragraphs里面不需要的段落
        filter_paragraphs = self.filter_paragraphs(
            paragraphs, titles, multi_occurrences, file_title
        )
        titles = self.sort_titles(titles, filter_paragraphs)
        r = self.process_title_paragraph_level(
            file_title, titles, filter_paragraphs, 0, has_catalogs
        )
        return r

    def sort_titles(self, titles: {}, paragraphs: []) -> {}:
        sorted_titles = {}
        for p in paragraphs:
            title = p["text"].replace(" ", "")
            if title in titles and title not in sorted_titles:
                sorted_titles[title] = titles[title]
        return sorted_titles

    def has_catalogs(self, paragraphs: []) -> bool:
        for p in paragraphs:
            if "目录" == p["text"].replace(" ", ""):
                return True
        return False

    def parser_pdf_by_page(self, path: str):
        print("正在处理{}", path)
        pdf = pdfplumber.open(path)
        file_name = os.path.basename(path)
        res = {}
        parts = []
        for i, page in enumerate(pdf.pages, 1):
            # text = re.sub(r"\\u[0-9a-z]{4,4}",r"\n",repr(page.extract_text()))
            text = re.sub(r"\(cid:\d{1,5}\)", "", page.extract_text())
            text = "\n".join(
                [
                    k
                    for k in text.split("\n")
                    if k and len(k) > 1 and not re.match(r"^\d+$", k)
                ]
            )
            # 52316_20220104【天翼云】渭南政务云拓展案例分享 -（陕西）
            text = re.sub(" +", " ", text)
            text = re.sub("\n+", "\n", text)
            text.replace("训传\n培外\n企止\n政禁\n省，", "")
            text.replace("\n政禁\n省，", "")
            text.replace("训传\n，", "")
            text.replace("培外\n，", "")
            text.replace("企止\n，", "")
            if text == "\n":
                text = ""
            if len(text) > 1 and text[-1] == "\n":
                text = text[:-1]
            if len(text) > 1 and text[0] == "\n":
                text = text[1:]
            if (text.find("目录") != -1 and text.find("目录") < 4) or not text:
                continue
            part = {
                "part_name": "",
                "part_level": 1,
                "part_sort": len(parts) + 1,
                "contents": splitHandler.split_page_texts(text, i),
                "parts": [],
            }
            parts.append(part)
        res["file_name"] = file_name
        res["parts"] = parts
        res["file_title"] = ""
        res["file_type"] = "pdf"
        pdf.close()
        return res

    def parser_pdf_by_rule(self, path: str, result_path: Optional[str] = None) -> Dict:
        print("正在处理{}", path)
        pdf = pdfplumber.open(path)
        file_name = os.path.basename(path)
        res, tables = {}, {}
        # start_page, is_orc_file = self.getPdfContent(pdf)
        for page in pdf.pages:
            tables[page.page_number] = self.handle_tables(page)
        subscripts = self.get_subscripts(pdf)
        paragraphs = self.process_page_content(pdf, tables, subscripts)
        res["file_title"] = get_file_title(paragraphs)
        catalogs = self.parse_catalog(pdf, paragraphs)
        has_catalogs = self.has_catalogs(paragraphs)  # 是否有目录内容
        catalogs = self.parse_catalog_extra(catalogs, paragraphs)
        print(catalogs)
        outline = self.process_title_paragraph(res, catalogs, paragraphs, has_catalogs)

        res["chapters"] = outline
        # res["paragraphs"] = paragraphs
        # res["contents"] = paragraphs
        res.pop("file_title", 0)
        res["file_name"] = file_name
        res.pop("catalogs", 0)
        if not res["chapters"]:
            bad_files.append(file_name)
        if result_path:
            result_path = result_path + "/" + file_name + ".json"
            self.save_result(result_path, res)
        pdf.close()
        return res

    def parser(self, dir):
        if os.path.isdir(dir):
            workers = 6
            process_pool = multiprocessing.Pool(workers)
            pdf_files = []
            profiler = Profiler()
            profiler.start()
            tasks = []
            count = 0
            for file in os.listdir(dir):
                if file in already_files:
                    continue
                if (
                    os.path.splitext(file)[1] == ".pdf"
                    or os.path.splitext(file)[1] == ".PDF"
                ):
                    count += 1
                    # '奉贤区政务云二期网签合同.pdf', '（5）前向投标文件-广东省邮政行业安全监控平台及配套服务建设工程项目软件服务项目.pdf',
                    pdf_files.append(os.path.join(dir, file))
                    # self.parser_pdf_by_rule(os.path.join(dir,"奉贤区政务云二期网签合同.pdf"))
                    # self.parser_pdf_by_rule(os.path.join(dir,file))
                    tasks.append(
                        process_pool.apply_async(
                            self.parser_pdf_by_rule, args=(os.path.join(dir, file),)
                        )
                    )
            process_pool.close()
            process_pool.join()
            print(f"count{count}")
            profiler.stop()
            profiler.print()
            for task in tasks:
                task.get()

    def get_tables_and_images_for_outline(
        self,
        obj: LTChar,
        image_map: Dict,
        table_map: Dict,
        pre_page_num: int,
        page_num: int,
    ):
        images, tables = [], []
        for i in range(pre_page_num, page_num):
            while image_map.get(i) and len(image_map.get(i)) > 0:
                image = image_map.get(i).pop(0)
                images.append(image)
            while table_map.get(i) and len(table_map.get(i)) > 0:
                table = table_map.get(i).pop(0)
                tables.append(table)
        # bbox:
        # 0 x0：从页面左侧到框左边缘的距离。
        # 1 y0：从页面底部到框的下边缘的距离。
        # 2 x1：从页面左侧到方框右边缘的距离。
        # 3 y1：从页面底部到框的上边缘的距离
        while (
            image_map
            and image_map.get(page_num)
            and len(image_map.get(page_num)) > 0
            and (obj["x0"], obj["y0"], obj["x0"], obj["x1"])[3]
            < image_map.get(page_num)[0]["bbox"][1]
        ):
            image = image_map.get(page_num).pop(0)
            images.append(image)
        while (
            table_map
            and image_map.get(page_num)
            and len(table_map.get(page_num)) > 0
            and (obj["x0"], obj["y0"], obj["x0"], obj["x1"])[3]
            < table_map.get(page_num)[0]["bbox"][1]
        ):
            table = table_map.get(page_num).pop(0)
            tables.append(table)
        return images, tables

    def build_paragraph(
        self, lines: List = [], page: object = {}, pre_page_line=None
    ) -> List:
        if not lines or len(lines) == 0:
            return []
        paragraphs, left_paragraphs, middle_paragraphs = [], [], []
        lines = list(filter(lambda x: len(x["text"]) > 1, lines))
        lines = sorted(lines, key=lambda x: x["first_char"]["bbox"][1], reverse=True)
        if pre_page_line:
            lines.insert(0, pre_page_line)
        # 去掉页脚
        for line in lines:
            if re.match(PdfParser.page_num_pattern, line["text"]):
                lines.remove(line)
        right_paragraphs = list(
            filter(
                lambda x: x["first_char"]["bbox"][0] > 0.5 * page.get("width", 1000),
                lines,
            )
        )
        if len(right_paragraphs) > math.ceil(len(lines) * 0.2):
            left_paragraphs = list(
                filter(
                    lambda x: x["end_char"]["bbox"][0] < 0.5 * page.get("width", 1000)
                    and x not in right_paragraphs,
                    lines,
                )
            )
            middle_paragraphs = list(
                filter(
                    lambda x: x not in left_paragraphs and x not in right_paragraphs,
                    lines,
                )
            )
        else:
            left_paragraphs = lines
            right_paragraphs = []
        if left_paragraphs:
            paragraphs.extend(self.do_build_paragraph(left_paragraphs, page))
        if middle_paragraphs:
            merge_middle_paragraphs = self.do_build_paragraph(middle_paragraphs, page)
            for merge_middle_paragraph in merge_middle_paragraphs:
                index = len(paragraphs)
                for i, p in enumerate(paragraphs):
                    if (
                        merge_middle_paragraph["first_char"]["bbox"][1]
                        > p["first_char"]["bbox"][1]
                    ):
                        index = i
                        break
                paragraphs.insert(index, merge_middle_paragraph)
        if right_paragraphs:
            paragraphs.extend(self.do_build_paragraph(right_paragraphs, page))
        return paragraphs

    def do_build_paragraph(self, lines: List = [], page: object = {}) -> List:
        paragraph = ""
        paragraphs = []
        count = 0
        pre_item = {"first_char": {"bbox": []}, "end_char": {"bbox": [], "width": 0}}
        filter_lines = list(filter(lambda x: x["page"] == page["page"], lines))
        min_line_x = (
            min(filter_lines, key=lambda x: (x["first_char"]["bbox"][0]))["first_char"][
                "bbox"
            ][0]
            if len(filter_lines) > 0
            else float("inf")
        )
        max_line_x = (
            max(filter_lines, key=lambda x: x["end_char"]["bbox"][0])["end_char"][
                "bbox"
            ][0]
            if len(filter_lines) > 0
            else float("-inf")
        )
        for i, line in enumerate(lines):
            if paragraph != "":
                if re.match(PdfParser.catalog_pattern, paragraph):
                    paragraphs.append(
                        {
                            "text": str(re.sub(r"[-.·… ]{10,}", "." * 12, paragraph)),
                            "page": lines[i - count]["first_char"]["page"],
                            "last_line": pre_item,
                            "first_char": lines[i - count]["first_char"],
                            "end_char": lines[i - 1]["end_char"],
                            "tag": "catalog",
                        }
                    )
                    paragraph = ""
                    count = 0
                # 当前行为标题
                elif re.match(
                    PdfParser.title_label_patterns, line["text"]
                ) and not re.match("\d+[.]{0,1}\d+%", line["text"]):
                    paragraphs.append(
                        {
                            "text": str(paragraph),
                            "page": lines[i - count]["first_char"]["page"],
                            "last_line": pre_item,
                            "first_char": lines[i - count]["first_char"],
                            "end_char": lines[i - 1]["end_char"],
                        }
                    )
                    paragraph = ""
                    count = 0
                # 以特殊字符开始
                elif line["text"][0] in [
                    "\uf0b7",
                    " \uf0bc",
                    "\uf0ba",
                    "\uf0b8",
                    "\uf0d8",
                    "\uf075",
                    "\uf06c",
                    "●",
                    "\uf06e",
                ]:
                    paragraphs.append(
                        {
                            "text": str(paragraph),
                            "page": lines[i - count]["first_char"]["page"],
                            "last_line": pre_item,
                            "first_char": lines[i - count]["first_char"],
                            "end_char": lines[i - 1]["end_char"],
                        }
                    )
                    paragraph = ""
                    count = 0
                # 处理跨页
                elif pre_item["page"] == page["page"] - 1:
                    if (
                        pre_item["last_line"]["end_char"]["bbox"][2]
                        + pre_item["end_char"]["height"] / 2
                        < max_line_x
                        or pre_item["last_line"]["end_char"]["bbox"][1] > 100
                        or line["first_char"]["bbox"][3] < page["height"] - 100
                        or line["first_char"]["bbox"][0] - line["first_char"]["height"]
                        > min_line_x
                    ):
                        paragraphs.append(
                            {
                                "text": str(paragraph),
                                "page": lines[i - count]["first_char"]["page"],
                                "last_line": pre_item,
                                "first_char": lines[i - count]["first_char"],
                                "end_char": lines[i - 1]["end_char"],
                            }
                        )
                        paragraph = ""
                        count = 0
                # 上下行距离
                # elif (pre_item["end_char"]['bbox'][1] - line["first_char"]["bbox"][3]) > 1.65 * line["first_char"][
                #     "height"]:
                #     paragraphs.append(
                #         {"text": str(paragraph), "page": lines[i - count]["first_char"]["page"], "last_line": pre_item,
                #          "first_char": lines[i - count]["first_char"],
                #          "end_char": lines[i - 1]["end_char"]})
                #     paragraph = ""
                #     count = 0
                # 当前行和上一行字体大小不相近(取整后相等)
                elif int(line["first_char"]["height"]) != int(
                    pre_item["first_char"]["height"]
                ):
                    paragraphs.append(
                        {
                            "text": str(paragraph),
                            "page": lines[i - count]["first_char"]["page"],
                            "last_line": pre_item,
                            "first_char": lines[i - count]["first_char"],
                            "end_char": lines[i - 1]["end_char"],
                        }
                    )
                    paragraph = ""
                    count = 0
                # "chars":line_chars
                elif (
                    pre_item["text"][0] in ["\uf0b7", "\uf06c", "\uf075", "•", "●"]
                    or (paragraph[0] in ["\uf0b7", "\uf06c", "\uf075", "•", "●"])
                    or re.match("^(\(\d\)).", pre_item["text"])
                    or re.match("^(\(\d\)).", line["text"])
                ):
                    # 上一行没有填满
                    if (
                        pre_item["end_char"]["bbox"][2]
                        + pre_item["end_char"]["height"] / 2
                        < max_line_x
                    ):
                        paragraphs.append(
                            {
                                "text": str(paragraph),
                                "page": lines[i - count]["first_char"]["page"],
                                "last_line": pre_item,
                                "first_char": lines[i - count]["first_char"],
                                "end_char": lines[i - 1]["end_char"],
                            }
                        )
                        paragraph = ""
                        count = 0

                # 当前行有缩进的情况
                elif (line["first_char"]["bbox"][0] - min_line_x) > line["first_char"][
                    "height"
                ]:
                    # 当前行在上一行左面并且上一行占满 就合并
                    if not (
                        (
                            pre_item["first_char"]["bbox"][0]
                            - line["first_char"]["bbox"][0]
                            > line["first_char"]["height"]
                            or pre_item["first_char"]["bbox"][0]
                            - line["first_char"]["bbox"][0]
                            < 2
                        )
                        and (
                            pre_item["end_char"]["bbox"][2]
                            + pre_item["end_char"]["height"] / 2
                            >= max_line_x
                        )
                    ):
                        paragraphs.append(
                            {
                                "text": str(paragraph),
                                "page": lines[i - count]["first_char"]["page"],
                                "last_line": pre_item,
                                "first_char": lines[i - count]["first_char"],
                                "end_char": lines[i - 1]["end_char"],
                            }
                        )
                        paragraph = ""
                        count = 0
                # 上一行 明显没有填满 或 以句号分号结尾
                elif (
                    pre_item["end_char"]["bbox"][2] + pre_item["end_char"]["height"] * 2
                    < max_line_x
                ) or pre_item["text"][-1] in [".", "。", ";", "；"]:
                    paragraphs.append(
                        {
                            "text": str(paragraph),
                            "page": lines[i - count]["first_char"]["page"],
                            "last_line": pre_item,
                            "first_char": lines[i - count]["first_char"],
                            "end_char": lines[i - 1]["end_char"],
                        }
                    )
                    paragraph = ""
                    count = 0
                # 上一行和下一行都有冒号且段首缩进一致
                elif (
                    re.search(r"[:：]", paragraph)
                    and re.search(r"[:：]", line["text"])
                    and (
                        abs(
                            lines[i - count]["first_char"]["bbox"][0]
                            - line["first_char"]["bbox"][0]
                        )
                        < line["first_char"]["height"]
                    )
                ):
                    paragraphs.append(
                        {
                            "text": str(paragraph),
                            "page": lines[i - count]["first_char"]["page"],
                            "last_line": pre_item,
                            "first_char": lines[i - count]["first_char"],
                            "end_char": lines[i - 1]["end_char"],
                        }
                    )
                    paragraph = ""
                    count = 0
            paragraph += line.get("text")
            pre_item = line
            pre_item["last_line"] = line
            count += 1
        if paragraph != "":
            paragraphs.append(
                {
                    "text": str(paragraph),
                    "page": pre_item["first_char"]["page"],
                    "last_line": pre_item,
                    "first_char": lines[len(lines) - count]["first_char"],
                    "end_char": lines[len(lines) - 1]["end_char"],
                }
            )
        return paragraphs

    def filter_char_in_table(page_chars, page_tables):
        def char_in_bbox(char: T_obj, bbox: T_bbox) -> bool:
            v_mid = (char["top"] + char["bottom"]) / 2
            h_mid = (char["x0"] + char["x1"]) / 2
            x0, top, x1, bottom = bbox
            return bool(
                (h_mid >= x0) and (h_mid < x1) and (v_mid >= top) and (v_mid < bottom)
            )

        if not page_tables or not page_chars:
            return page_chars
        char_not_in_table = []
        for table in page_tables:
            for char in page_chars:
                if not char_in_bbox(char, bbox=table["bbox"]):
                    char_not_in_table.append(char)
            page_chars = char_not_in_table
            char_not_in_table = []
        return page_chars

    def get_subscripts(self, pdf: PDF):
        if not pdf.pages or len(pdf.pages) <= 0:
            return []
        subscripts, line_chars = [], []
        line = ""
        page_lines, pre_first_char, pre_line_last_char = {}, {}, {}
        page_line_count = 5
        for i, page in enumerate(pdf.pages[:10]):
            page_num = page.page_number
            page = page.dedupe_chars(tolerance=1)
            pre_line_y = 0.0
            lines = []
            for obj in page.chars:
                if len(lines) > page_line_count:
                    break
                if 6.2 < abs(obj["matrix"][5] - pre_line_y):
                    pre_line_y = obj["matrix"][5]
                    if (
                        line.strip()
                        and re.match(r"[\．\.\ ]*$", line) is None
                        and line.strip() not in ["\uf076", "\uf043"]
                    ):
                        line = line.strip().replace("\uf076", "")
                        lines.append(
                            {
                                "text": str(line),
                                "page": pre_first_char["page"]
                                if pre_first_char != {}
                                else 1,
                                "first_char": pre_first_char,
                                "end_char": pre_line_last_char,
                            }
                        )
                    pre_first_char = {
                        "bbox": (obj["x0"], obj["y0"], obj["x1"], obj["y1"]),
                        "char": obj["text"],
                        "width": obj["width"],
                        "height": obj["height"],
                        "page": page_num,
                    }
                    line = ""
                    line_chars = []
                if obj["text"] != " ":
                    pre_line_last_char = {
                        "bbox": (obj["x0"], obj["y0"], obj["x1"], obj["y1"]),
                        "char": obj["text"],
                        "width": obj["width"],
                        "height": obj["height"],
                        "page": page_num,
                    }
                line += obj["text"]
                line_chars.append(obj)
            if re.match(r"[\．\.\ ]*$", line) is None and line.strip() not in [
                "\uf076",
                "\uf043",
            ]:
                line = line.strip().replace("\uf076", "")
                lines.append(
                    {
                        "text": str(line),
                        "page": pre_first_char["page"] if pre_first_char != {} else 1,
                        "first_char": pre_first_char,
                        "end_char": pre_line_last_char,
                    }
                )
                line = ""
                line_chars = []
            page_lines[i] = lines
        for index in range(page_line_count):
            statistics_line = {}
            for i in range(len(page_lines.keys())):
                values = page_lines.get(i)
                if index < len(values) and values[index]["text"]:
                    statistics_line[values[index]["text"]] = (
                        statistics_line.get(values[index]["text"], 0) + 1
                    )
            subscript = ""
            occurrence_num = 0
            for key, value in statistics_line.items():
                if value > occurrence_num:
                    occurrence_num = value
                    subscript = key
            if subscript and statistics_line[subscript] >= 0.5 * 10:  # 同样的位置重复超6次
                subscripts.append(subscript.replace(" ", ""))
        return subscripts

    def process_page_content(self, pdf: PDF, tables, subscripts: []) -> List:
        if not pdf.pages or len(pdf.pages) <= 0:
            return []
        line = ""
        line_chars, lines, tmp = [], [], []
        pre_first_char, pre_line_last_char = {}, {}
        for i, page in enumerate(pdf.pages, 1):
            page_num = page.page_number
            page = page.dedupe_chars(tolerance=1)
            page_info = {
                "width": page.width,
                "height": page.height,
                "page": page.page_number,
            }
            pre_line_y = 0.0
            filter_chars = PdfParser.filter_char_in_table(
                page.chars, tables[page.page_number]
            )
            if not pre_first_char and filter_chars:
                first_obj = filter_chars[0]
                pre_first_char = {
                    "bbox": (
                        first_obj["x0"],
                        first_obj["y0"],
                        first_obj["x1"],
                        first_obj["y1"],
                    ),
                    "char": first_obj["text"],
                    "width": first_obj["width"],
                    "height": first_obj["height"],
                    "page": page_num,
                }
            for obj in filter_chars:
                if 6.2 < abs(obj["matrix"][5] - pre_line_y):
                    pre_line_y = obj["matrix"][5]

                    if (
                        line.strip()
                        and re.match(r"[\．\.\ ]*$", line) is None
                        and line.strip() not in ["\uf076", "\uf043"]
                        and line.replace(" ", "") not in subscripts
                    ):
                        line = line.strip().replace("\uf076", "")
                        lines.append(
                            {
                                "text": str(line),
                                "page": pre_first_char["page"]
                                if pre_first_char != {}
                                else 1,
                                "chars": line_chars,
                                "first_char": pre_first_char,
                                "end_char": pre_line_last_char,
                            }
                        )
                    pre_first_char = {
                        "bbox": (obj["x0"], obj["y0"], obj["x1"], obj["y1"]),
                        "char": obj["text"],
                        "width": obj["width"],
                        "height": obj["height"],
                        "page": page_num,
                    }
                    line = ""
                    line_chars = []
                if obj["text"] != " ":
                    pre_line_last_char = {
                        "bbox": (obj["x0"], obj["y0"], obj["x1"], obj["y1"]),
                        "char": obj["text"],
                        "width": obj["width"],
                        "height": obj["height"],
                        "page": page_num,
                    }
                line += obj["text"]
                line_chars.append(obj)
            if (
                re.match(r"[\．\.\ ]*$", line) is None
                and line.strip() not in ["\uf076", "\uf043"]
                and line.replace(" ", "") not in subscripts
            ):
                line = line.strip().replace("\uf076", "")
                lines.append(
                    {
                        "text": str(line),
                        "page": pre_first_char["page"] if pre_first_char != {} else 1,
                        "chars": line_chars,
                        "first_char": pre_first_char,
                        "end_char": pre_line_last_char,
                    }
                )
                line = ""
                line_chars = []
            if len(lines) > 0:
                # 考虑上一页的段落有可能跨页
                pre_page_line = None
                if len(tmp) > 0 and tmp[-1]["page"] == page_num - 1:
                    pre_page_line = tmp.pop(-1)
                new_paragraph = self.build_paragraph(lines, page_info, pre_page_line)
                tmp.extend(new_paragraph)
                lines = []
        # 合并一些行只有几个字，最后一个字是句号，可以和上一行合并 去除单字一行，有可能是水印和页码
        res = []
        for item in tmp:
            if len(item["text"]) > 2 or item["text"] == "目录":
                res.append(item)
        return res

    def handle_tables(self, page: Page) -> List:
        page = page.dedupe_chars()
        tables = []

        def check_table(table, bbox):
            for i in range(len(table)):
                if table[i] < bbox[i % 2] or table[i] > bbox[i % 2 + 2]:
                    return False
            return True

        if page.find_tables():
            for table in page.find_tables(
                table_settings={
                    "intersection_tolerance": 5,
                    "explicit_horizontal_lines": [min(x["top"] for x in page.edges)],
                }
            ):
                # if not check_table(table.bbox, page.bbox):
                #     continue
                data = table.extract()
                is_table = False
                for row in data:
                    if len(row) > 1:
                        is_table = True
                if not is_table:
                    continue
                new_list = []
                for row in data:
                    for c in row:
                        if c and len(c.strip()):
                            new_list.append(row)
                            break
                tables.append(
                    {
                        "bbox": [
                            table.bbox[0],
                            table.bbox[1],
                            table.bbox[2],
                            table.bbox[3],
                        ],
                        "data": new_list,
                    }
                )
        return tables

    def save_result(self, file_path: str, res: Dict) -> None:
        f = open(file_path, "w", encoding="utf-8")
        json.dump(res, f, ensure_ascii=False)
        f.close()

    def get_multi_occurrences(self, paragraphs) -> []:  # 获取文档脚标题内容 或重复出现的干扰内容
        multi_occurrences = []
        if not paragraphs:
            return multi_occurrences

        some_page_paragraphs = {}
        for paragraph in paragraphs:
            page_num = paragraph["page"]
            if page_num in some_page_paragraphs.keys():
                some_page_paragraphs[page_num].append(paragraph)
            else:
                some_page_paragraphs[page_num] = [paragraph]
        for page_num, page_paragraphs in some_page_paragraphs.items():
            some_page_paragraphs[page_num] = sorted(
                page_paragraphs, key=lambda x: x["first_char"]["bbox"][1], reverse=True
            )
        for i in range(3):
            occurrence_statistics = {}  # {text:occurrence_num} 从头到尾的统计
            last_occurrence_statistics = {}  # 从尾到头的统计
            for key, value in some_page_paragraphs.items():
                if i < len(value):
                    occurrence_text = re.sub(r"\d|[a-zA-Z]", "", value[i]["text"])
                    if occurrence_statistics.get(occurrence_text, -1) != -1:
                        occurrence_statistics[occurrence_text] = (
                            occurrence_statistics[occurrence_text] + 1
                        )
                    else:
                        occurrence_statistics[occurrence_text] = 1
                if (len(value) - 1 - i) >= 0:
                    last_occurrence_text = re.sub(
                        r"\d|[a-zA-Z]", "", value[len(value) - 1 - i]["text"]
                    )
                    if last_occurrence_statistics.get(last_occurrence_text, -1) != -1:
                        last_occurrence_statistics[last_occurrence_text] = (
                            last_occurrence_statistics[last_occurrence_text] + 1
                        )
                    else:
                        last_occurrence_statistics[last_occurrence_text] = 1

            for key, value in occurrence_statistics.items():
                if (value >= 10) and key and (key not in multi_occurrences):
                    multi_occurrences.append(key)  # 同样的位置重复超10次
            for key, value in last_occurrence_statistics.items():
                if (value >= 10) and key and (key not in multi_occurrences):
                    multi_occurrences.append(key)  # 同样的位置重复超10次
        return multi_occurrences


def get_file_title(paragraphs: []) -> str:
    if len(paragraphs) == 0:
        return ""
    file_title_origin = paragraphs[0].get("text", "").replace(" ", "")
    if file_title_origin != "":
        if (
            re.match(r"^(\d+|[一二三四五六七八九十]{1,3}[、.])", file_title_origin)
            or file_title_origin == "目录"
            or re.match(PdfParser.page_num_pattern, file_title_origin)
            or re.findall(r"[。，,:：]", file_title_origin)
        ):
            file_title_origin = ""
    return file_title_origin


if __name__ == "__main__":
    bad_files, already_files = [], []
    pdf_parser = PdfParser()
    pdf_parser.parser_pdf_by_rule(
        path="../parser/data_files/chinese/text/ningboquanjiekuai.pdf",
        result_path="../parser/data_result",
    )
