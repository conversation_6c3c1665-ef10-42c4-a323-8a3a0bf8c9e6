import json
import os
from typing import Dict

from llama_index.core import StorageContext, ServiceContext
from llama_index.core.service_context_elements.llm_predictor import LLMPredictor

from common.llm.embedding import get_embedding_model
from common.llm.llama_llm import get_llm
from nl2document.common.vector.vector_store import get_vector_store
from config.doc_config import ask_doc_dir
from nl2document.builder.document_index_builder import DocumentIndexBuilder
from nl2document.builder.data_process.file_parser import FileParser
from nl2document.builder.parser.node_parser import GeneralNodeParser

llm_cache_dir = os.path.join(ask_doc_dir, "output/llm_cache")


def load_document_json(file_path: str):
    with open(file_path, "r") as f:
        json_datas = json.load(f)
    return json_datas


def parse_document(file_path: str, file_type: str, language: str):
    file_parser = FileParser()
    return file_parser.parse(file_path, file_type, language)


def node_parser(document_json: Dict, file_id: int, folder_id: int):
    node_parser = GeneralNodeParser()
    return node_parser.get_nodes_from_json(document_json, file_id, folder_id)


def build_index(nodes):
    embed_model, tokenizer, dim = get_embedding_model()
    llm_predictor = LLMPredictor(
        get_llm(),
    )
    vector_store = get_vector_store()
    storage_context = StorageContext.from_defaults(vector_store=vector_store)
    service_context = ServiceContext.from_defaults(
        embed_model=embed_model,
        llm_predictor=llm_predictor,
    )
    index = DocumentIndexBuilder(storage_context, service_context).build(
        all_nodes=nodes
    )


if __name__ == "__main__":
    # file_path = "../parser/data_result/taiyang_2022.pdf.json"
    file_path = "../parser/data_files/chinese/ppt/ABBqicai.pdf"
    res = parse_document(file_path, "ppt", "chinese")
    # res = load_document_json(file_path)
    file_id = 90
    folder_id = 18
    root_nodes, nodes = node_parser(res, file_id, folder_id)
    document_index = build_index(nodes)
