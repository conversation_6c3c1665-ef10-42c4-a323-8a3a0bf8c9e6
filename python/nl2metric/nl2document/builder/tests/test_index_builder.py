from pathlib import Path

from nl2document.builder.index_builder import (
    DocumentAddRequest,
    IndexBuilder,
)
from nl2document.builder.dianxing_data_process.file_parser import FileParser
from index.manager.document_manager import DEFAULT_LIBRARY_NAME


from common.models.model import (
    get_library,
    get_folder_tree,
    set_folder_suggestion_questions,
)


def test_dianxin_parse_document():
    file_parser = FileParser()
    source_dir = "./data"
    output_dir = "./data_json"
    file_parser.parse_dir_documents(source_dir, output_dir)


def test_dianxin_index_build():
    builder = IndexBuilder()
    source_dir = Path("../parser/data_files/")
    doc1 = DocumentAddRequest(
        source_url="local://" + str(source_dir) + "/chinese/files/taiyang_2022.pdf"
    )
    doc2 = DocumentAddRequest(
        source_url="local://" + str(source_dir) + "/chinese/text/ningbo.pdf"
    )
    doc3 = DocumentAddRequest(
        source_url="local://"
        + str(source_dir)
        + "/english/files/FY2022-Q4-IP-Earnings_Release.pdf"
    )
    builder.add_default_documents("jinguang", [doc1, doc2, doc3])


def test_index_build():
    builder = IndexBuilder()
    source_dir = Path("/data2/lixudong/daneng-docs")
    doc1 = DocumentAddRequest(
        source_url="local://" + str(source_dir / "01. 蕴荟基本信息.pdf")
    )
    doc2 = DocumentAddRequest(source_url="local://" + str(source_dir / "02. 蕴荟FAQ.pdf"))
    doc3 = DocumentAddRequest(
        source_url="local://" + str(source_dir / "03. 蕴荟Product Story解析.pdf")
    )
    doc4 = DocumentAddRequest(
        source_url="local://" + str(source_dir / "04. 蕴荟场景话术.pdf")
    )
    doc5 = DocumentAddRequest(
        source_url="local://" + str(source_dir / "05. 蕴荟竞品分析 .xlsx")
    )

    builder.add_default_documents("蕴荟基本信息", [doc1])
    builder.add_default_documents("蕴荟FAQ", [doc2])
    builder.add_default_documents("蕴荟Product Story解析", [doc3])
    builder.add_default_documents("蕴荟场景话术", [doc4])
    builder.add_default_documents("蕴荟竞品分析", [doc5])
    builder.add_default_documents("蕴荟5合1", [doc1, doc2, doc3, doc4, doc5])


def test_insert_suggestion_question():
    library_name = DEFAULT_LIBRARY_NAME
    folder_questions = {
        "蕴荟5合1": [
            "产品中包含哪些类型的植物油？",
            "产品是否有某些过敏原?",
            "每100g产品的亚油酸含量是多少",
            "该产品关注儿童生长和发育的关键营养成分有哪些？",
            "产品的奶源是如何选择的？" "为什么选择草饲奶源带牛乳供给而不是其他奶源",
            "蕴荟中添加的NOPOeTM天然结构脂对于宝宝肠道健康有什么用？",
            "诺优能3蕴荟中提到的“50%更高含量的维生素D”是相对于哪个产品而言的？",
            "蕴荟奶粉”为什么相对难以溶解？",
            "如何处理冲泡时产生的奶粉颗粒和泡沫？",
        ],
        "蕴荟竞品分析": [
            "在脑眼发育方面，蕴荟相对于其他竞品的优势是什么？",
            "蕴荟在其沟通重点中最强调的信息是什么？",
            "蕴荟在吸收力方面，和飞鹤星飞帆相比有什么区别？",
            "通过对各竞品的营养成分进行比较，蕴荟在哪些方面相对其他产品具有优势或特色？",
        ],
        "蕴荟基本信息": [
            "产品中包含哪些类型的植物油？",
            "产品是否有某些过敏原?",
            "每100g产品的亚油酸含量是多少",
            "如何提高“诺优能3蕴荟”奶粉的溶解度？",
        ],
        "蕴荟场景话术": [
            "该产品关注儿童生长和发育的关键营养成分有哪些？",
            "产品的奶源是如何选择的？" "为什么选择草饲奶源带牛乳供给而不是其他奶源",
            "诺优能蕴荟奶粉是如何提高宝宝对钙的吸收的？",
        ],
        "蕴荟Product Story解析": [
            "蕴荟中添加的NOPOeTM天然结构脂对于宝宝肠道健康有什么用？",
            "诺优能3蕴荟中提到的“50%更高含量的维生素D”是相对于哪个产品而言的？",
            "维生素D在宝宝身体中的主要功能是什么？",
            "维生素C在诺优能3蕴荟中的作用是什么？",
        ],
        "蕴荟FAQ": [
            "如何提高“诺优能3蕴荟”奶粉的溶解度？",
            "蕴荟奶粉”为什么相对难以溶解？",
            "如何处理冲泡时产生的奶粉颗粒和泡沫？",
            "转奶后宝宝出现便秘、腹泻、过敏等情况，如何处理？",
            "如何有效地帮助宝宝适应蕴荟奶粉的清淡口味，并减少由于口味不适导致的奶量减少？",
        ],
    }
    library = get_library(library_name)
    for folder_path, questions in folder_questions.items():
        folder = get_folder_tree(library.id, folder_path)
        set_folder_suggestion_questions(library.id, folder.id, questions)
