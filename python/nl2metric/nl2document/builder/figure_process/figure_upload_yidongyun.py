import os
from boto3.session import Session
import warnings

warnings.filterwarnings("ignore", category=UnicodeWarning)

from common.logging.logger import get_logger, get_elk_logger

logger = get_logger(__name__)
elk_logger = get_elk_logger()

yidong_access_key = "QZG0OHBA4VUUD0M2J0WZ"
yidong_secret_key = "fYsf0pmY44QNt50F7ICsgZ0BEvWM1JpXJv3JI5M6"
yidong_url = "http://eos-beijing-1.cmecloud.cn"  # such as 'http://10.254.3.68:7480'
session = Session(yidong_access_key, yidong_secret_key)
s3_client = session.client("s3", endpoint_url=yidong_url)


# 上传图片
def put_object(bucket: str, object_path: str, image_path: str, file_id: str = ""):
    picture_name = os.path.basename(image_path)
    try:
        with open(image_path, "rb") as data:
            s3_client.put_object(Bucket=bucket, Key=object_path, Body=data)
    except Exception as e:
        logger.exception(
            msg=f"上传会议图片  {picture_name}  时发生错误: {e}", extra={"file_id": file_id}
        )
        elk_logger.info(
            msg=f"上传会议图片  {picture_name}  时发生错误: {e}", extra={"file_id": file_id}
        )
        raise RuntimeError(f"上传会议图片  {picture_name}  时发生错误: {e}")


# 获取图片的临时可访问地址
def get_temporary_accessible_address(bucket: str, object_path: str) -> str:
    return s3_client.generate_presigned_url(
        ClientMethod="get_object",
        Params={"Bucket": bucket, "Key": object_path},
        ExpiresIn=3600,  # URL有效期为1小时（以秒为单位）
    )


#  删除图片
def delete_object(bucket: str, object_path: str, file_id: str = ""):
    try:
        s3_client.delete_object(Bucket=bucket, Key=object_path)
        print("成功删除")
    except Exception as e:
        raise RuntimeError(f"删除会议图片时发生错误: {e}")
