absl-py==2.0.0
accelerate==1.5.1
aiobotocore==2.7.0
aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.11.13
aioitertools==0.12.0
aiolimiter==1.2.1
aiomysql==0.2.0
aiosignal==1.3.1
#albucore==0.0.13
albumentations==2.0.5
alibabacloud_credentials==0.3.6
alibabacloud_endpoint_util==0.0.3
alibabacloud_gateway_spi==0.0.3
alibabacloud-iqs20241111==1.1.5
alibabacloud_openapi_util==0.2.2
alibabacloud_tea==0.4.2
alibabacloud_tea_openapi==0.3.13
alibabacloud_tea_util==0.3.13
alibabacloud_tea_xml==0.0.2
annotated-types==0.7.0
anyio==4.8.0
apex==0.1
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
arrow==1.3.0
astor==0.8.1
astroid==2.15.8
asttokens==2.4.0
astunparse==1.6.3
async-timeout==4.0.3
attrs==23.1.0
audioread==3.0.1
backcall==0.2.0
backoff==2.2.1
bce-python-sdk==0.9.29
beautifulsoup4==4.13.3
black==23.3.0
bleach==6.0.0
blinker==1.7.0
blis==0.7.11
bm25s==0.1.10
boto3==1.28.64
botocore==1.31.64
cachetools==5.3.1
catalogue==2.0.10
certifi==2025.1.31
cffi==1.16.0
chardet==5.2.0
charset-normalizer==3.2.0
chinese_calendar==1.10.0
click==8.1.8
cloudpathlib==0.15.1
cloudpickle==2.2.1
cmake==3.27.6
colorama==0.4.6
colorlog==6.9.0
comm==0.1.4
confection==0.1.3
contourpy==1.1.1
cryptography==44.0.2
cssselect==1.3.0
cssutils==2.11.1
cubinlinker==0.3.0+2.gce0680b
cuda-python==12.2.0rc5+5.g84845d1
cugraph==23.8.0
cugraph-dgl==23.8.0
cugraph-service-client==23.8.0
cugraph-service-server==23.8.0
cuml==23.8.0
cupy-cuda12x==12.1.0
cycler==0.12.1
cymem==2.0.8
Cython==3.0.3
dask==2023.7.1
dask-cuda==23.8.0
dataclasses-json==0.6.7
datasets==2.19.2
#dbus-python==1.2.18
debugpy==1.8.0
decorator==5.1.1
decord==0.6.0
deepdiff==8.3.0
deepspeed==0.12.6
defusedxml==0.7.1
Deprecated==1.2.18
dill==0.3.8
dirtyjson==1.0.8
distributed==2023.7.1
distro==1.9.0
dm-tree==0.1.8
doclayout_yolo==0.0.3
duckduckgo_search==7.5.2
einops==0.7.0
emoji==2.14.1
environs==9.5.0
et_xmlfile==2.0.0
eval_type_backport==0.2.2
exceptiongroup==1.1.3
execnet==2.0.2
executing==2.0.0
expecttest==0.1.3
faiss-cpu==1.7.4
fastapi==0.112.2
fastjsonschema==2.18.1
fastrlock==0.8.1
filelock==3.12.4
filetype==1.2.0
fire==0.7.0
FlagEmbedding==1.2.5
flake8==7.0.0
flash-attention==1.0.0
flash-attn==2.0.4
flashtext==2.7
Flask==2.3.3
fonttools==4.43.1
fqdn==1.5.1
frozenlist==1.4.0
fsspec==2023.12.2
ftfy==6.3.1
future==1.0.0
gast==0.5.4
google-auth==2.23.2
google-auth-oauthlib==0.4.6
googleapis-common-protos==1.59.1
GPUtil==1.4.0
graphsurgeon==0.4.6
greenlet==3.1.1
grpcio==1.59.0
h11==0.14 
hanlp==2.1.1
hanlp-common==0.0.23
hanlp-downloader==0.0.25
hanlp-trie==0.0.5
hjson==3.1.0
html5lib==1.1
httpcore==1.0.7
httptools==0.6.1
httpx==0.27.0
httpx-sse==0.4.0
huggingface-hub==0.29.3
hypothesis==5.35.1
idna==3.10
imageio==2.37.0
imagesize==1.4.1
imgaug==0.4.0
importlib-metadata==6.8.0
importlib_resources==6.4.5
iniconfig==2.0.0
intel-openmp==2021.4.0
ipykernel==6.25.2
ipython==8.16.1
ipython-genutils==0.2.0
isoduration==20.11.0
isort==5.13.2
itsdangerous==2.2.0
jedi==0.19.1
jieba==0.42.1
Jinja2==3.1.6
jiter==0.9.0
jmespath==1.0.1
joblib==1.3.2
json5==0.9.14
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.19.1
jsonschema-specifications==2023.7.1
jupyter_client==8.6.3
jupyter_core==5.7.2
jupyter-events==0.12.0
jupyter-kernel-gateway==3.0.1
jupyter_server==2.15.0
jupyter_server_terminals==0.5.3
jupyter-tensorboard==0.2.0
jupyterlab==2.3.2
jupyterlab-pygments==0.2.2
jupyterlab-server==1.2.0
jupytext==1.15.2
kiwisolver==1.4.5
langchain==0.2.17
langchain-community==0.2.17
langchain-core==0.2.43
langchain-openai==0.1.25
langchain-text-splitters==0.2.4
langcodes==3.3.0
langdetect==1.0.9
langfuse==2.59.7
langsmith==0.1.147
lazy_loader==0.4
lazy-object-proxy==1.10.0
librosa==0.9.2
llama-cloud==0.1.14
llama-index==0.10.58
llama-index-agent-openai==0.2.9
llama-index-cli==0.1.13
#llama-index-core==0.10.68.post1
llama-index-embeddings-langchain==0.1.2
llama-index-embeddings-openai==0.1.11
llama-index-indices-managed-llama-cloud==0.2.7
llama-index-legacy==0.9.48.post4
llama-index-llms-openai==0.1.27
llama-index-multi-modal-llms-openai==0.1.9
llama-index-program-openai==0.1.7
llama-index-question-gen-openai==0.1.3
llama-index-readers-file==0.1.33
llama-index-readers-llama-parse==0.1.6
llama-index-retrievers-bm25==0.2.2
llama-index-vector-stores-faiss==0.1.2
llama-index-vector-stores-milvus==0.1.20
llama-parse==0.4.9
llamaindex-py-client==0.1.19
llvmlite==0.40.1
lmdb==1.6.2
locket==1.0.0
loguru==0.7.3
lxml==5.3.1
Markdown==3.5.2
markdown-it-py==2.2.0
markdownify==1.1.0
MarkupSafe==2.1.3
marshmallow==3.26.1
matplotlib==3.8.0
matplotlib-inline==0.1.6
mccabe==0.7.0
mdit-py-plugins==0.4.0
mdurl==0.1.2
milvus-lite==2.4.11
mistune==3.0.2
mkl==2021.1.1
mkl-devel==2021.1.1
mkl-include==2021.1.1
mock==5.1.0
modelscope==1.22.3
more-itertools==10.6.0
mpmath==1.3.0
msgpack==1.0.5
multidict==6.0.4
multiprocess==0.70.16
murmurhash==1.0.10
mypy==1.8.0
mypy-extensions==1.0.0
nbclient==0.8.0
nbconvert==7.9.2
nbformat==5.9.2
nest-asyncio==1.6.0
networkx==3.4.2
ninja==********
nltk==3.9.1
notebook==6.4.10
numba==0.57.1+1.g5fba9aa8f
numpy==1.24.4
#nvfuser==0.0.20+gitunknown
nvidia-cublas-cu11==*********
nvidia-cuda-cupti-cu11==11.8.87
nvidia-cuda-nvrtc-cu11==11.8.89
nvidia-cuda-runtime-cu11==11.8.89
nvidia-cudnn-cu11==********
nvidia-cufft-cu11==*********
nvidia-curand-cu11==*********
nvidia-cusolver-cu11==*********
nvidia-cusparse-cu11==*********
nvidia-dali-cuda120==1.30.0
nvidia-ml-py==12.570.86
nvidia-nccl-cu11==2.19.3
nvidia-nvtx-cu11==11.8.86
nvidia-pyindex==1.0.9
nvtx==0.2.5
oauthlib==3.2.2
olefile==0.47
onnx==1.14.0
openai==1.63.2
#opencv-contrib-python==*********
#opencv-python==********
#opencv-python-headless==*********
openpyxl==3.1.2
opentelemetry-api==1.21.0
opentelemetry-exporter-jaeger==1.21.0
opentelemetry-exporter-jaeger-proto-grpc==1.21.0
opentelemetry-exporter-jaeger-thrift==1.21.0
opentelemetry-instrumentation==0.42b0
opentelemetry-instrumentation-flask==0.42b0
opentelemetry-instrumentation-grpc==0.42b0
opentelemetry-instrumentation-requests==0.42b0
opentelemetry-instrumentation-wsgi==0.42b0
opentelemetry-sdk==1.21.0
opentelemetry-semantic-conventions==0.42b0
opentelemetry-util-http==0.42b0
opt-einsum==3.3.0
orderly-set==5.3.0
orjson==3.10.15
overrides==7.7.0
packaging==24.2

# 手动安装
# paddlepaddle-gpu==3.0.0rc1
# paddlex==3.0.0rc0
# paddleocr==2.9.1
pandas==1.5.3
pandoc==2.4
pandocfilters==1.5.0
Parsley==1.3
parso==0.8.3
partd==1.4.0
pathspec==0.12.1
pathy==0.10.2
patsy==1.0.1
pdf2docx==0.5.8
pdf2image==1.17.0
pdfkit==1.0.0
pdfminer.six==20221105
pdfplumber==0.10.3
peft==0.7.1
pexpect==4.8.0
phrasetree==0.0.9
pickleshare==0.7.5
pillow==11.1.0
pip==25.0.1
platformdirs==3.11.0
pluggy==1.3.0
plumbum==1.9.0
ply==3.11
poe-api-wrapper==1.3.7
polygraphy==0.49.0
pooch==1.7.0
premailer==3.10.0
preshed==3.0.9
prettytable==3.9.0
primp==0.14.0
prometheus-client==0.17.1
prompt-toolkit==3.0.39
propcache==0.3.0
protobuf==4.24.4
psutil==5.9.4
ptxcompiler==0.8.1+1.g2cb1b35
ptyprocess==0.7.0
pure-eval==0.2.2
py-cpuinfo==9.0.0
pyarrow==19.0.1
pyarrow-hotfix==0.6
pyasn1==0.5.0
pyasn1-modules==0.3.0
pybind11==2.11.1
pybind11-global==2.11.1
pyclipper==1.3.0.post6
pycocotools==2.0+nv0.7.3
pycodestyle==2.11.1
pycparser==2.21
pycryptodome==3.21.0
pydantic==2.11.1
pydantic_core==2.33.0
pyflakes==3.2.0
Pygments==2.16.1
PyGObject==3.42.1
pyinstrument==4.5.1
PyJWT==2.8.0
pylibcugraph==23.8.0
pylibcugraphops==23.8.0
pylibraft==23.8.0
pylint==2.17.5
pymilvus==2.5.5
PyMuPDF==1.25.5
PyMySQL==1.1.0
pynvml==11.4.1
pyparsing==3.1.1
pypdf==4.3.1
PyPDF2==3.0.1
pypdfium2==4.30.1
pyquaternion==0.9.9
PyStemmer==*******
pytesseract==0.3.10
pytest==8.0.1
pytest-flakefinder==1.1.0
pytest-rerunfailures==12.0
pytest-shard==0.1.2
pytest-xdist==3.3.1
python-dateutil==2.8.2
python-docx==1.1.2
python-dotenv==1.0.1
python-hostlist==1.23.0
python-iso639==2025.2.18
python-json-logger==2.0.7
python-magic==0.4.27
python-multipart==0.0.20
python-oxmsg==0.0.2
python-pptx==0.6.23
pytorch-quantization==2.1.2
pytz==2023.3
PyYAML==6.0.2
pyzmq==25.1.1
qianfan
raft-dask==23.8.0
RapidFuzz==3.12.2
readerwriterlock==1.0.9
redis==5.0.1
referencing==0.30.2
regex==2023.10.3
requests==2.32.3
requests-oauthlib==1.3.1
requests-toolbelt==1.0.0
resampy==0.4.2
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rich==13.3.4
rmm==23.8.0
rpds-py==0.10.4
rsa==4.9
ruamel.yaml==0.18.10
ruamel.yaml.clib==0.2.12
s3fs==2023.12.2
s3transfer==0.7.0
safetensors==0.5.3
scikit-image==0.25.2
scikit-learn==1.6.1
scipy==1.15.2
seaborn==0.13.2
Send2Trash==1.8.2
sentence-transformers==3.4.1
sentencepiece==0.2.0
setuptools==76.1.0
shapely==2.0.7
simsimd==6.2.1
six==1.16.0
smart-open==6.4.0
#smolagents==1.10.0
sniffio==1.3.1
sortedcontainers==2.4.0
soundfile==0.12.1
soupsieve==2.5
spacy==3.7.1
spacy-legacy==3.0.12
spacy-loggers==1.0.5
sphinx-glpi-theme==0.3
sql_metadata==2.10.0
SQLAlchemy==2.0.25
sqlparse==0.4.4
srsly==2.4.8
stack-data==0.6.3
starlette==0.38.6
statsmodels==0.14.4
stringzilla==3.12.3
striprtf==0.0.26
sympy==1.12
tabulate==0.9.0
tbb==2021.10.0
tblib==2.0.0
tenacity==8.5.0
tensorboard==2.9.0
tensorboard-data-server==0.6.1
tensorboard-plugin-wit==1.8.1
tensorrt==8.6.1
termcolor==2.5.0
terminado==0.17.1
thinc==8.2.1
thop==0.1.1.post2209072238
threadpoolctl==3.2.0
thrift==0.16.0
thriftpy2==0.4.16
tifffile==2025.2.18
tiktoken==0.9.0
tinycss2==1.2.1
tokenizers==0.21
toml==0.10.2
tomli==2.0.1
tomlkit==0.13.2
toolz==0.12.0
toposort==1.5
torch==2.1.0a0+32f93b1
torch-tensorrt==0.0.0
torchdata==0.7.0a0
torchtext==0.16.0a0
torchvision==0.16.0a0
tornado==6.4.2
tqdm==4.66.1
traitlets==5.14.3
transformer-engine==0.12.0+170797
transformers==4.50.3
treelite==3.2.0
treelite-runtime==3.2.0
triton==2.1.0+e621604
typer==0.9.0
types-dataclasses==0.6.6
types-decorator==5.1.8.20240106
types-PyMySQL==*******
types-python-dateutil==2.9.0.20241206
types-PyYAML==*********
types-requests==2.25.0
types-setuptools==69.0.0.20240125
types-waitress==2.1.4.20240106
typing_extensions==4.13.0
typing-inspect==0.9.0
typing-inspection==0.4.0
tzdata==2025.1
ucx-py==0.33.0
uff==0.6.9
ujson==5.10.0
ultralytics==8.2.51
ultralytics-thop==2.0.14
unstructured==0.17.2
unstructured-client==0.32.0
uri-template==1.3.0
urllib3==1.26.16
uvicorn==0.30.6
waitress==2.1.2
wasabi==1.1.2
wcwidth==0.2.8
weasel==0.3.2
webcolors==24.11.1
webencodings==0.5.1
websocket-client==1.8.0
Werkzeug==3.0.1
wheel==0.41.2
wrapt==1.17.2
xdoctest==1.0.2
xgboost==1.7.5
xinference-client==0.14.1
xlrd==2.0.1
XlsxWriter==3.2.2
xxhash==3.5.0
yarl==1.18.3
zhipuai==2.1.4
zhon==2.0.2
zict==3.0.0
zipp==3.16.2
simplejson