import json
import os
import re
from typing import Dict, List

import numpy as np
import pytesseract as pt
from PIL import Image
from docx import Document
from docx.oxml.table import CT_Tbl
from docx.oxml.text.paragraph import CT_P
from docx.parts.image import ImagePart
from docx.table import Table
from docx.text.paragraph import Paragraph

from common.fs.fs import get_s3_file_system


class DocxParser:
    reference_pattern = re.compile(r"\[(\d+(?:-\d+)?(?:,\d+(?:-\d+)?)*?)\]")
    rid_pattern = re.compile("rId\d+")

    def __init__(self):
        self.res = {}
        self.id = 0
        self.img_dir = os.path.join(
            os.path.dirname(os.path.dirname(__file__)), "parsed_docs"
        )
        if not os.path.exists(self.img_dir):
            os.makedirs(self.img_dir)

    def read_table(self, table):
        return [[cell.text for cell in row.cells] for row in table.rows]

    def get_tables(self, tables):
        res = []
        if not tables or len(tables) == 0:
            return res
        for table in tables:
            table_data = [[cell.text for cell in row.cells] for row in table.rows]
            res.append({"text": table_data, "id": self.get_id()})

    def is_image(self, graph: Paragraph, doc: Document):
        images = graph._element.xpath(".//pic:pic")  # 获取所有图片
        for image in images:
            for img_id in image.xpath(".//a:blip/@r:embed"):  # 获取图片id
                part = doc.part.related_parts[img_id]  # 根据图片id获取对应的图片
                if isinstance(part, ImagePart):
                    return True
        return False

    # 获取图片（该行只能有一个图片）
    def get_images(self, graph: Paragraph, doc: Document, path: str, resource_dir=""):
        res = []
        images = graph._element.xpath(".//pic:pic")  # 获取所有图片
        for image in images:
            for img_id in image.xpath(".//a:blip/@r:embed"):  # 获取图片id
                part = doc.part.related_parts[img_id]  # 根据图片id获取对应的图片
                if isinstance(part, ImagePart):
                    img_path = os.path.join(path, img_id + ".png")
                    img_url = os.path.join(
                        os.path.basename(path), os.path.basename(img_path)
                    )
                    with open(img_path, "wb") as f:
                        f.write(part.blob)
                        f.close()
                    if resource_dir:
                        remote_resource_dir = os.path.join(resource_dir, "images")
                        get_s3_file_system().upload(img_path, remote_resource_dir)
                    res.append(
                        {
                            "path": img_path,
                            "id": self.get_id(),
                            "text": DocxParser.get_text_by_ocr(Image.open(img_path)),
                        }
                    )

        return res

    def get_text_by_ocr(img):
        """
        img = Image.read(img_file)
        """
        context = pt.image_to_string(img, lang="chi_sim+eng")
        return context.replace("\n", "").strip()

    def parse(self, path: str, resource_dir: str = "") -> Dict:
        self.res["file_name"] = path.split("/")[-1]
        doc = Document(path)
        outline_map = {}
        pre_outline = ""
        outlines = []
        all_text = []
        paragraphs = []
        images = []
        tables = []
        img_dir = os.path.join(self.img_dir, os.path.splitext(path)[0])
        if not os.path.exists(img_dir):
            os.mkdir(img_dir)
        if not get_s3_file_system().exists(resource_dir):
            resource_dir = ""
        for block in doc.element.body:
            if isinstance(block, CT_P):
                p = Paragraph(block, doc)
                if self.is_image(p, doc):
                    images_info = self.get_images(
                        p, doc, img_dir, resource_dir=resource_dir
                    )
                    images.extend(images_info)
                else:
                    if p.text.strip() and p.text.strip() != "\n":
                        all_text.append(p.text.strip())
                        if (
                            p.style.name.startswith("Heading")
                            or p.alignment == 1
                            or re.search(r"[一二三四五六七八九十]{1,2}(、)", p.text.strip())
                        ):  # 1表示居中对齐
                            if pre_outline != "" or len(paragraphs) > 0:
                                outline_map[pre_outline] = paragraphs
                                outlines.append(
                                    {
                                        "title": pre_outline,
                                        "text": paragraphs,
                                        "id": self.get_id(),
                                        "images": images,
                                        "tables": tables,
                                    }
                                )
                                paragraphs = []
                                images = []
                                tables = []
                            pre_outline = (
                                p.text.replace("\t", "")
                                .replace("\n", "")
                                .replace(" ", "")
                            )
                        else:
                            if p.text.strip() and p.text.strip() != "\n":
                                paragraphs.append({"text": p.text, "id": self.get_id()})
                        # images.extend(img_data)
            elif isinstance(block, CT_Tbl):
                table = Table(block, doc)
                tables.append({"id": self.get_id(), "text": self.read_table(table)})
        if pre_outline or paragraphs:
            outlines.append(
                {
                    "title": pre_outline,
                    "text": paragraphs,
                    "id": self.get_id(),
                    "images": images,
                    "tables": tables,
                }
            )
        self.add_doc_ref(all_text, outlines)
        self.res["outline"] = outlines
        self.res["tables"] = tables
        return self.res

    def get_id(self):
        self.id += 1
        return self.id

    def add_doc_ref(self, texts, outline):
        reference = []
        for i, text in enumerate(texts):
            if text.find("参考文献") != -1:
                reference = texts[i:]
                break
        if not reference:
            return
        for item in outline:
            paragraphs = item["text"]
            for ps in paragraphs:
                match_list = re.findall(DocxParser.reference_pattern, ps["text"])
                if match_list and len(match_list) > 0:
                    ref_ids = np.array(match_list, dtype=np.int32).tolist()

                    for ref_id in ref_ids:
                        if not ps.get("doc_ref", ""):
                            ps["doc_ref"] = []
                        ps["doc_ref"].append(reference[ref_id])

    @staticmethod
    def save_result(name: str, res: Dict) -> None:
        folder_path = "data/result"
        if not os.path.exists(folder_path):
            os.mkdir(folder_path)
        path = folder_path + "/" + name + ".json"
        f = open(path, "w")
        json.dump(res, f, ensure_ascii=False)
        f.close()

    @staticmethod
    def handle_tables(document: Document):
        tables = []
        for t in document.tables:
            rows = []
            for r in t.rows:
                row = []
                for c in r.cells:
                    row.append(c.text)
                rows.append(row)
            tables.append(rows)

        return {"data": tables}

    @staticmethod
    def generate_outline_map(outlines: List) -> List:
        patterns_map = {}
        patterns = [r"[一二三四五六七八九十]{1,2}(、)", r"label"]
        for outline in outlines:
            if re.search(patterns[0], outline["title"]) is not None:
                if not patterns_map.__contains__(patterns[0]):
                    patterns_map[patterns[0]] = [outline]
                else:
                    patterns_map[patterns[0]].append(outline)
                if not patterns_map[patterns[1]][-1].__contains__("outline"):
                    patterns_map[patterns[1]][-1]["outline"] = [outline]
                else:
                    patterns_map[patterns[1]][-1]["outline"].append(outline)
            else:
                if not patterns_map.__contains__(patterns[1]):
                    patterns_map[patterns[1]] = [outline]
                else:
                    patterns_map[patterns[1]].append(outline)
        return patterns_map[patterns[1]]


if __name__ == "__main__":
    js = DocxParser().parse(
        "/Users/<USER>/IdeaProjects/diplus/ask_bot/ask_doc/data1/蕴荟资料清单-0725/04. 蕴荟场景话术.docx",
        "ask-doc/doc-resource/doc_1/",
    )
    # ExtentNodeParser().get_nodes_from_json(js)
    # 'ask-doc/doc-resource/doc_1/thumbnail.png'
    # k = get_s3_file_system().url("")
    # with open(
    #     "/Users/<USER>/IdeaProjects/diplus/ask_bot/askbot_service/askbot_document_builder/parsed_docs/蕴荟资料清单.pdf.json",
    #     "r",
    # ) as f:
    #     js = json.load(f)
    #     title_nodes, all_nodes = ExtentNodeParser().get_nodes_from_json(js)
    #     print("")
