import os

from common.logging.logger import get_logger
from config.doc_config import ASK_DOC_BUSINESS_NAME
from nl2document.builder.data_process.docx_parser import DocxParser
from nl2document.builder.data_process.html_parser import HtmlParser
from nl2document.builder.data_process.xlsx_parser import XlsxParser
from nl2document.builder.parser.extend_node_parser import ExtentNodeParser
from config import doc_config
from nl2document.builder.dianxing_data_process.file_parser import (
    FileParser as DianxingFileParser,
)

logger = get_logger(__name__)


class FileParser:
    def check_path(self, file_path):
        if not os.path.exists(file_path):
            raise RuntimeError("{} not exit".format(file_path))
        if not os.path.isfile(file_path):
            raise RuntimeError("{} is not a file".format(file_path))
        return True

    def parse(self, path: str, file_type: str, language: str, file_id: str = ""):
        logger.info(
            f"file parser parse file {path} file type {file_type} language {language} file_id: {file_id}"
        )
        self.check_path(path)
        if ASK_DOC_BUSINESS_NAME == "dianxin" or doc_config.deployMode == "cmcc":
            return DianxingFileParser().parse(path, file_type, language, file_id)
        else:
            suffix = path.split(".")[-1]
            if suffix in ["docx"]:
                return DocxParser().parse(path)
            # elif suffix in ["pdf"]:
            #     return PdfParser().parse(path)
            elif suffix in ["html"]:
                return HtmlParser().parse(path)
            elif suffix in ["xlsx"]:
                return XlsxParser().parse(path)
            else:
                raise RuntimeError("{} not support".format(suffix))


if __name__ == "__main__":
    rs = FileParser().parse("/data1/html/Q22：数字化转型的出发点和落脚点是什么？.html")
    title_nodes, all_nodes = ExtentNodeParser().get_nodes_from_json(rs)
