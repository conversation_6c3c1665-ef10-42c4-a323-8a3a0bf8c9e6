import json
import os
import re
from typing import Dict

import cv2
import fitz
import pdfplumber
from pdfminer.layout import <PERSON><PERSON>har

from nl2document.common.base.ask_doc_utils import get_md5
from nl2document.builder.data_process.pdf_parser import PdfParser


def convert_pdf2img(path):
    """
    file_relative_path : 文件相对路径
    """
    page_num = 1
    filename = path.split(".")[-2]
    if not os.path.exists(filename):
        os.makedirs(filename)

    pdf = fitz.open(path)
    for page in pdf:
        rotate = int(0)
        # 每个尺寸的缩放系数为2，这将为我们生成分辨率提高4的图像。
        # 此处若是不做设置，默认图片大小为：792X612, dpi=96
        zoom_x = 4  # (2-->1584x1224)
        zoom_y = 4
        mat = fitz.Matrix(zoom_x, zoom_y)
        pixmap = page.get_pixmap(matrix=mat, alpha=False)
        pixmap.pil_save(f"{filename}/{page_num}.png")
        print(f"第{page_num}保存图片完成")
        page_num = page_num + 1


def save_result(folder_path: str, name: str, res: Dict) -> None:
    if not os.path.exists(folder_path):
        os.mkdir(folder_path)
    path = folder_path + "/" + name + ".json"
    f = open(path, "w", encoding="utf-8")
    json.dump(res, f, ensure_ascii=False)
    f.close()


categories = [
    "title",
    "paragraph",
    "catalog",
    "picture",
    "footer",
    "second-title",
    "table",
    "header",
    "margin",
]


# categories = ["title", "paragraph", "catalog", "picture", "footer"]


# annotation{
#     "keypoints": [x1,y1,v1,...],
#     "num_keypoints": int,
#     "id": int,
#     "image_id": int,
#     "category_id": int,
#     "segmentation": RLE or [polygon],
#     "area": float,
#     "bbox": [x,y,width,height],
#     "iscrowd": 0 or 1,
# }
# 用于生成模型需要的数据集 图片+块
def gen_data(path, json_path, scale=2, start_page=0):
    image_dict = {}
    category_dict = {}
    res = {"annotations": [], "categories": [], "images": []}
    for i, item in enumerate(categories):
        res["categories"].append({"id": i, "name": item})
        category_dict[item] = i

    file_md5 = get_md5(path)
    filename = path.split(".")[-2]
    if not os.path.exists(filename):
        os.makedirs(filename)
    pdf = pdfplumber.open(path)

    for i, page in enumerate(pdf.pages[start_page:], 1):
        im = page.to_image(resolution=72 * scale)
        image_path = f"{filename}/{file_md5}_{i}.png"
        im.save(image_path)
        res["images"].append(
            {
                "id": i,
                "width": im.annotated.width,
                "height": im.annotated.height,
                "file_name": f"{file_md5}_{i}.png",
                "doc_name": path.split("/")[-1],
                "page_no": i,
                "precedence": 0,
            }
        )
        image_dict[f"{file_md5}_{i}.png"] = i

    with open(json_path) as f:
        jstr = json.load(f)
    paragraphs = jstr["paragraphs"]
    tables = jstr["tables"]
    images = jstr["images"]
    catalogs = [i.get("text") for i in jstr["catalog"] if i.get("text")]

    flag = True
    for paragraph in paragraphs:
        text = paragraph["text"]
        chars = paragraph["chars"]
        page = paragraph["page"]
        if page in [14, 18, 54, 43]:
            continue
        if flag:
            flag = False
            image = cv2.imread(f"{filename}/{file_md5}_{page}.png")
        h, w, _ = image.shape
        left = min(chars, key=lambda tup: tup["bbox"][0])["bbox"][0] * scale
        right = max(chars, key=lambda tup: tup["bbox"][2])["bbox"][2] * scale
        top = h - max(chars, key=lambda tup: tup["bbox"][3])["bbox"][3] * scale
        below = h - min(chars, key=lambda tup: tup["bbox"][1])["bbox"][1] * scale

        _catalog = (
            category_dict["title"] if text in catalogs else category_dict["paragraph"]
        )
        _catalog = category_dict["footer"] if text[0] == "第" else _catalog
        _catalog = (
            category_dict["catalog"] if re.search(r"[\．\.\ ]{5,}", text) else _catalog
        )

        res["annotations"].append(
            {
                "text": text,
                "id": len(res["annotations"]) + 1,
                "image_id": image_dict.get(f"{file_md5}_{page}.png"),
                "category_id": _catalog,
                "bbox": [left, top, right - left, below - top],
                "area": (right - left) * (below - top),
                "iscrowd": 0,
                "precedence": 0,
                "segmentation": [[left, top, left, below, right, below, right, top]],
            }
        )
        cv2.rectangle(
            image,
            (round(left), round(top)),
            (round(right), round(below)),
            (0, 0, 255),
            1,
        )
    for k in range(1, len(pdf.pages) + 1):
        page_table = tables.get(str(k), [])
        page_image = images.get(str(k), [])
        if k in [14, 18, 54, 43]:
            continue
        if page_table and len(page_table) > 0:
            for table in page_table:
                left = table["bbox"][0] * scale
                below = h - table["bbox"][1] * scale
                right = table["bbox"][2] * scale
                top = h - table["bbox"][3] * scale

                res["annotations"].append(
                    {
                        "text": "table",
                        "id": len(res["annotations"]) + 1,
                        "image_id": image_dict.get(f"{file_md5}_{k}.png"),
                        "category_id": category_dict["table"],
                        "bbox": [left, top, right - left, below - top],
                        "area": (right - left) * (below - top),
                        "iscrowd": 0,
                        "precedence": 0,
                        "segmentation": [
                            [left, top, left, below, right, below, right, top]
                        ],
                    }
                )
        if page_image and len(page_image) > 0:
            for _image in page_image:
                left = _image["bbox"][0] * scale
                below = h - _image["bbox"][1] * scale
                right = _image["bbox"][2] * scale
                top = h - _image["bbox"][3] * scale

                res["annotations"].append(
                    {
                        "text": "image",
                        "id": len(res["annotations"]) + 1,
                        "image_id": image_dict.get(f"{file_md5}_{k}.png"),
                        "category_id": category_dict["picture"],
                        "bbox": [left, top, right - left, below - top],
                        "area": (right - left) * (below - top),
                        "iscrowd": 0,
                        "precedence": 0,
                        "segmentation": [
                            [left, top, left, below, right, below, right, top]
                        ],
                    }
                )

    return res


# 根据块的bounding box画图
def draw_img(path):
    img_dir = path[: path.rfind("/")]
    with open(path) as f:
        jstr = json.load(f)
    annotations = jstr["annotations"]
    images = jstr["images"]
    file_name_dict = {}
    for i in images:
        file_name_dict[i["id"]] = i["file_name"]
    res = {}
    for annotation in annotations:
        if not res.__contains__(file_name_dict[annotation["image_id"]]):
            res[file_name_dict[annotation["image_id"]]] = []
        res[file_name_dict[annotation["image_id"]]].append(annotation["bbox"])
    for item in file_name_dict.values():
        bboxs = res.get(item, [])

        image = cv2.imread(img_dir + "/" + item)

        for bbox in bboxs:
            if bbox:
                left = bbox[0]
                top = bbox[1]
                right = bbox[2] + left
                below = bbox[3] + top
                cv2.rectangle(
                    image,
                    (round(left), round(top)),
                    (round(right), round(below)),
                    (0, 0, 255),
                    1,
                )
        cv2.imshow(item, image)
        cv2.waitKey()
        cv2.destroyAllWindows()


def gen_char_dataset(path, json_file, scale=2, start_page=0):
    with open(json_file) as f:
        jstr = json.load(f)
        annotations = jstr["annotations"]
        text_category = [
            {"text": k["text"], "type": categories[k["category_id"]]}
            for k in annotations
        ]
    file_md5 = get_md5(path)
    filename = path.split(".")[-2]
    res = {"lang": "zh", "split": "val", "documents": []}
    pdf = pdfplumber.open(path)
    for i, page in enumerate(pdf.pages[start_page:], 1):
        if i in [14, 18, 54, 43]:
            continue
        im = page.to_image(resolution=72 * scale)
        image_path = f"{filename}/{file_md5}_{i}.png"
        # im.save(image_path)
        page_chars = {
            "id": f"{i}",
            "img": {
                "fname": f"{file_md5}_{i}.png",
                "width": im.annotated.width,
                "height": im.annotated.height,
            },
            "document": [],
        }
        for obj in page.layout:
            if isinstance(obj, LTChar):
                # if obj.get_text() == " ":
                #     continue
                x0, y0, x1, y1 = [
                    obj.bbox[0] * scale,
                    obj.bbox[1] * scale,
                    obj.bbox[2] * scale,
                    obj.bbox[3] * scale,
                ]
                page_chars["document"].append(
                    {
                        "box": [
                            x0,
                            im.annotated.height - y0,
                            x1,
                            im.annotated.height - y1,
                        ],
                        "text": obj.get_text(),
                        "label": "I-paragraph",
                    }
                )
        start = 0
        if file_md5 == "082d9fa42e77a49c0c2d46284e576c23":
            for k, chars in enumerate(page_chars["document"]):
                page_chars["document"][k]["label"] = "I-footer"
                if page_chars["document"][k]["text"] == "第":
                    page_chars["document"][k]["label"] = "S-footer"
                    continue
                if page_chars["document"][k]["text"] == "页":
                    page_chars["document"][k]["label"] = "E-footer"
                    start = k + 1
                    break
        elif file_md5 == "a89abf95611a3ca74a900b8dbbf00837":
            page_chars["document"][0]["label"] = "footer"
            start = 1

        for item in text_category[:]:
            if (
                file_md5 == "082d9fa42e77a49c0c2d46284e576c23"
                and item["text"][0] == "第"
            ):
                text_category.remove(item)
                continue
            if (
                file_md5 == "a89abf95611a3ca74a900b8dbbf00837"
                and item["text"][0] in ["1", "2", "3"]
                and len(item["text"]) == 1
            ):
                text_category.remove(item)
                continue
            if start >= len(page_chars["document"]):
                break
            if (
                item["text"][0] == page_chars["document"][start]["text"]
                and item["text"][len(item["text"]) - 1]
                == page_chars["document"][start + len(item["text"]) - 1]["text"]
            ):
                for _index in range(start + 1, start + len(item["text"]) - 1):
                    page_chars["document"][_index]["label"] = "I-" + item["type"]
                page_chars["document"][start]["label"] = "S-" + item["type"]
                page_chars["document"][start + len(item["text"]) - 1]["label"] = (
                    "E-" + item["type"]
                )
                start = start + len(item["text"])

                text_category.remove(item)
            else:
                print(i)
        img = cv2.imread(image_path)
        for item in page_chars["document"]:
            cv2.rectangle(
                img,
                (round(item["box"][0]), round(item["box"][1])),
                (round(item["box"][2]), round(item["box"][3])),
                (0, 0, 255),
                1,
            )
        cv2.imshow(str(i), img)
        cv2.waitKey()
        cv2.destroyAllWindows()
        res["documents"].append(page_chars)

    save_result(
        "/data1/docs/2018919133723717458",
        "charset",
        res,
    )
    print(res)


# 查找文件夹下指定的后缀文件列表
def find_files_with_suffix(folder_path, suffix):
    # 使用os模块获取文件夹中所有文件的路径
    all_files = os.listdir(folder_path)
    # 筛选以指定后缀名结尾的文件
    filtered_files = [file for file in all_files if file.endswith(suffix)]

    return filtered_files


# labelme 标注生成的json文件处理脚本
def deal_label_me_json(
    lable_json_path: str,
    res_file: str = None,
    pdf_path: str = "/Users/<USER>/IdeaProjects/diplus/ask_bot/ask_doc/data/docs/ElectronicBill.pdf",
):
    jsonfiles = find_files_with_suffix(lable_json_path, ".json")
    if len(jsonfiles) == 0:
        return
    category_dict = {}
    if res_file:
        with open(res_file) as f:
            res = json.load(f)
        # for item in res["images"]:
        #     if item["name"] == img_name:
        #         return
        for i, item in enumerate(categories):
            category_dict[item] = i
    else:
        res = {"annotations": [], "categories": [], "images": []}
        for i, item in enumerate(categories):
            res["categories"].append({"id": i, "name": item})
            category_dict[item] = i
    for jsonfile in jsonfiles:
        if jsonfile.find("_") == -1:
            continue
        with open(lable_json_path + jsonfile) as f:
            jstr = json.load(f)
        img_name = jstr["imagePath"]
        shapes = jstr["shapes"]
        page_num = int(img_name.split(".")[0].split("_")[1])
        img_path = lable_json_path[: lable_json_path.rfind("/") + 1] + img_name
        pdf = pdfplumber.open(pdf_path)
        img = cv2.imread(img_path)
        h, w, _ = img.shape
        scale = h / 842
        img_id = len(res["images"])

        if img_name in [item["file_name"] for item in res["images"]]:
            continue
        res["images"].append(
            {
                "id": img_id,
                "width": w,
                "height": h,
                "file_name": img_name,
                "doc_name": pdf_path.split("/")[-1],
                "page_no": page_num,
                "precedence": 0,
            }
        )
        for shape in shapes:
            label = shape["label"]
            left, top = shape["points"][0]
            right, below = shape["points"][1]
            if category_dict["table"] == int(label) or category_dict["picture"] == int(
                label
            ):
                continue
            page = pdf.pages[page_num - 1].crop(
                (left / scale, top / scale, right / scale, below / scale)
            )
            min_x = w
            max_x = -1
            min_y = h
            max_y = -1
            for obj in page.chars:
                min_x = min(min_x, min(obj["x0"], obj["x1"]))
                max_x = max(max_x, max(obj["x0"], obj["x1"]))
                min_y = min(min_y, min(obj["y0"], obj["y1"]))
                max_y = max(max_y, max(obj["y0"], obj["y1"]))
                left = (min_x - 1) * scale
                below = h - min_y * scale
                right = max_x * scale
                top = h - (max_y + 1) * scale
            res["annotations"].append(
                {
                    "id": len(res["annotations"]),
                    "image_id": img_id,
                    "category_id": int(label),
                    "bbox": [left, top, right - left, below - top],
                    "area": (right - left) * (below - top),
                    "iscrowd": 0,
                    "precedence": 0,
                    "segmentation": [
                        [left, top, left, below, right, below, right, top]
                    ],
                }
            )
            cv2.rectangle(
                img,
                (round(left), round(below)),
                (round(right), round(top)),
                (0, 0, 255),
                1,
            )

        for table in PdfParser.handle_tables(pdf.pages[page_num - 1]):
            left = table["bbox"][0] * scale
            below = h - table["bbox"][1] * scale
            right = table["bbox"][2] * scale
            top = h - table["bbox"][3] * scale
            res["annotations"].append(
                {
                    "id": len(res["annotations"]),
                    "image_id": img_id,
                    "category_id": category_dict["table"],
                    "bbox": [left, top, right - left, below - top],
                    "area": (right - left) * (below - top),
                    "iscrowd": 0,
                    "precedence": 0,
                    "segmentation": [
                        [left, top, left, below, right, below, right, top]
                    ],
                }
            )
            cv2.rectangle(
                img,
                (round(left), round(below)),
                (round(right), round(top)),
                (0, 0, 255),
                1,
            )
        for _image in pdf.pages[page_num - 1].images:
            left = _image["x0"] * scale
            below = h - _image["y0"] * scale
            right = _image["x1"] * scale
            top = h - _image["y1"] * scale

            res["annotations"].append(
                {
                    "id": len(res["annotations"]),
                    "image_id": img_id,
                    "category_id": category_dict["picture"],
                    "bbox": [left, top, right - left, below - top],
                    "area": (right - left) * (below - top),
                    "iscrowd": 0,
                    "precedence": 0,
                    "segmentation": [
                        [left, top, left, below, right, below, right, top]
                    ],
                }
            )
            cv2.rectangle(
                img,
                (round(left), round(below)),
                (round(right), round(top)),
                (0, 0, 255),
                1,
            )
        cv2.imshow(img_name, img)
        cv2.waitKey()
        cv2.destroyAllWindows()
    save_result(lable_json_path, "gendataset", res)
    print(json.dumps(res, indent=4, separators=(", ", ": "), ensure_ascii=False))


if __name__ == "__main__":
    deal_label_me_json(
        "/data1/docs/ElectronicBill/",
        res_file="/data1/docs/ElectronicBill/gendataset.json",
    )
    gen_char_dataset(
        "/data1/docs/2018919133723717458.pdf",
        "/data1/docs/2018919133723717458/valitation.json",
    )
    path = "/data1/docs/2018919133723717458.pdf"
    json_path = "/data1/docs/ElectronicBill/valitation.json"
    ElectronicBill = (
        "/Users/<USER>/Downloads/Scripts/data/samples/ElectronicBill.pdf.json"
    )

    res = gen_data(path, json_path=json_path)
    save_path = path.split(".")[0]
    save_result(save_path, path.split("/")[-1], res)
    draw_img("/data1/docs/2018919133723717458/2018919133723717458.pdf.json")
    # print(json.dumps(res, indent=4, separators=(', ', ': '), ensure_ascii=False))
