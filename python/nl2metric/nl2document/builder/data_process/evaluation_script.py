import json

# from paddleocr import PaddleOCR, draw_ocr
import os
import re
from io import BytesIO
from itertools import groupby
from operator import itemgetter
from typing import Dict, List

import pdfplumber
from easyocr import easyocr
from pdfminer.layout import LTChar
from pdfminer.pdftypes import PDFStream
from pdfplumber import PDF
from pdfplumber.page import Page
from PIL import Image

list_label_template = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]


class PdfParser:
    def __init__(self):
        self.res = {}
        self.pageid_num = {}

    def getPdfContent(self, file_name):
        pdf = pdfplumber.open(file_name)
        start_page = -1
        end_page = 0
        is_ocr_pdf = True
        for i in range(0, len(pdf.pages)):
            pattern = r"\s*\.{10,}\s*\d{1,3}\s*"
            pageObj = pdf.pages[i]

            extractedText = pageObj.extract_text()
            if extractedText:
                is_ocr_pdf = False
            if re.search(pattern, extractedText) is not None:
                if end_page == 0:
                    start_page = i + 1
                end_page = i + 1
            else:
                if end_page > 0:
                    break
        if start_page >= len(pdf.pages):
            return 0
        print(file_name + ":" + str(is_ocr_pdf))
        for i, page in enumerate(pdf.pages):
            self.pageid_num = {page.page_obj.pageid: i}
        return [start_page, end_page], is_ocr_pdf

    def parser(self, path: str) -> None:
        pdf = pdfplumber.open(path)
        file_name = path.split("/")[-1]

        outlines = []
        images = {}
        tables = {}
        pages = []

        start_page, is_orc_file = self.getPdfContent(path)
        for page in pdf.pages[start_page[1] :]:
            pages.append(self.handle_texts(page))
            images[page.page_number] = PdfParser.handle_images(page, file_name)

            tables[page.page_number] = self.handle_tables(page)
        catalog = self.parse_catalog(pdf, start_page[0], start_page[1])

        outlines = self.process_page_content(pdf, start_page, images, tables)
        outlines = self.generate_outline_map(outlines)
        start_page, is_orc_file = self.getPdfContent(path)
        try:
            pdf.doc.get_outlines()
            self.res["has_outline"] = True
        except Exception as e:
            self.res["has_outline"] = False

        self.res["is_image"] = is_orc_file
        self.res["catalog"] = catalog
        self.res["paragraphs"] = outlines
        self.res["file_name"] = file_name
        self.res["images"] = images
        self.res["tables"] = tables
        # print(res)
        self.save_result(file_name, self.res)

    def parse_catalog(self, pdf, start_page, end_page):
        lines = []
        try:
            # i = 2/0
            for outline in pdf.doc.get_outlines():
                level = outline[0] - 1
                title = outline[1].replace(" ", "")
                objid = outline[2][0].objid
                lines.append(
                    {
                        "text": title,
                        "level": level,
                        "objid": objid,
                        "page": self.pageid_num[objid],
                    }
                )
            return lines
        except Exception as e:
            print("没有outline，解析失败！")
            if start_page == -1:
                return {}

            pre_line_y = 0.0
            pre_line_x = 0
            line = ""
            pattern = r"\s*\.{10,}\s*\d{1,3}\s*"
            for page in pdf.pages[start_page - 1 : end_page]:
                for obj in page.layout:
                    if isinstance(obj, LTChar):
                        if 2 < abs(obj.matrix[5] - pre_line_y):
                            pre_line_y = obj.matrix[5]

                            if re.search(pattern, line):
                                r = [i for i in line.split("...") if i != ""]
                                title = r[0].strip()
                                page_num = int(
                                    [i for i in r[1].split(".") if i != ""][0]
                                )
                                lines.append(
                                    {"text": title, "page": page_num, "x": pre_line_x}
                                )
                            line = ""
                            pre_line_x = round(obj.matrix[4])
                        line += obj.get_text()

            if line and re.search(pattern, line):
                r = [i for i in line.split("..") if i != ""]
                title = r[0]
                page_num = int([i for i in r[1].split(".") if i != ""][0])
                lines.append({"text": title, "page": page_num, "x": pre_line_x})

            sorted_index = [
                i[0] for i in sorted(enumerate(lines), key=lambda x: x[1]["x"])
            ]
            level = 0
            for i, index in enumerate(sorted_index):
                if i != 0 and lines[index]["x"] != lines[sorted_index[i - 1]]["x"]:
                    level = level + 1
                lines[index]["level"] = level
            return lines

    def get_tables_and_images_for_outline(
        self,
        obj: LTChar,
        image_map: Dict,
        table_map: Dict,
        pre_page_num: int,
        page_num: int,
    ):
        images = []
        tables = []
        for i in range(pre_page_num, page_num):
            while image_map.get(i) and len(image_map.get(i)) > 0:
                image = image_map.get(i).pop(0)
                images.append(image)
            while table_map.get(i) and len(table_map.get(i)) > 0:
                table = table_map.get(i).pop(0)
                tables.append(table)

        # bbox:
        # 0 x0：从页面左侧到框左边缘的距离。
        # 1 y0：从页面底部到框的下边缘的距离。
        # 2 x1：从页面左侧到方框右边缘的距离。
        # 3 y1：从页面底部到框的上边缘的距离
        while (
            image_map
            and image_map.get(page_num)
            and len(image_map.get(page_num)) > 0
            and obj.bbox[3] < image_map.get(page_num)[0]["bbox"][1]
        ):
            image = image_map.get(page_num).pop(0)
            images.append(image)

        while (
            table_map
            and image_map.get(page_num)
            and len(table_map.get(page_num)) > 0
            and obj.bbox[3] < table_map.get(page_num)[0]["bbox"][1]
        ):
            table = table_map.get(page_num).pop(0)
            tables.append(table)
        return images, tables

    def del_title(titles: []):
        res = []
        for title in titles:
            res.append(title.replace(" ", ""))
        return res

    def is_title(lines, line, titles):
        if lines and len(lines) > 0:
            tmp = lines[-1][1]
            if tmp + line.replace(" ", "") in titles:
                lines.pop()
                line = line + "c"
                return True

        if line.replace(" ", "") in titles:
            return True
        return False

    def process_original_outline(
        self, pdf: PDF, start_page: [], image_map: Dict, table_map: Dict, catalog: []
    ) -> List:
        line = ""
        pre_page_num = 0
        pre_outline = ""
        pre_line_y = 0.0
        outlines = []
        lines = []
        pre_line_x = 0
        pre_objid = -1
        pre_bbox = [0, 0, 0, 0]
        outline_dict = {}
        level_dict = {}
        all_titles = [i["text"] for i in catalog]
        for item in catalog:
            title = item["text"]
            level_dict[title] = item["level"]
            if item.__contains__("objid"):
                if not outline_dict.__contains__(item["objid"]):
                    outline_dict[item["objid"]] = []
                outline_dict[item["objid"]].append(title)

        for i, page in enumerate(pdf.pages, 1):
            if start_page[0] != -1 and i >= start_page[0] and i <= start_page[1]:
                continue
            if len(outline_dict) > 0:
                titles = outline_dict.get(page.page_obj.pageid)
            else:
                titles = all_titles

            page_num = page.page_number
            for obj in page.layout:
                if isinstance(obj, LTChar):
                    font = obj.fontname
                    size = obj.size
                    if 2 < abs(obj.matrix[5] - pre_line_y):
                        pre_line_y = obj.matrix[5]
                        tmp = (
                            lines[-1][1] + line.replace(" ", "")
                            if lines and len(lines) > 0
                            else line.replace(" ", "")
                        )
                        if line.replace(" ", "") in titles or tmp in titles:
                            if tmp in titles and lines and len(lines) > 0:
                                line = tmp
                                lines.pop()

                            font = obj.fontname
                            size = obj.size
                            if pre_objid != -1 and pre_outline:
                                images, tables = self.get_tables_and_images_for_outline(
                                    obj, image_map, table_map, pre_page_num, page_num
                                )
                                paragraph = ""
                                paragraphs = []
                                pre_x = 0
                                min_line_x = float("inf")
                                for line_x, line_text in lines:
                                    min_line_x = min(line_x, min_line_x)
                                for line_x, line_text in lines:
                                    if (
                                        paragraph != ""
                                        and line_x != pre_x
                                        and line_x != min_line_x
                                    ):
                                        paragraphs.append(
                                            {"text": str(paragraph), "page": page_num}
                                        )
                                        paragraph = ""
                                    paragraph += line_text
                                    pre_x = line_x
                                if paragraph != "":
                                    paragraphs.append(
                                        {"text": str(paragraph), "page": page_num}
                                    )
                                lines = []
                                outlines.append(
                                    {
                                        "title": str(pre_outline),
                                        "bbox": pre_bbox,
                                        "page": pre_page_num,
                                        "text": paragraphs,
                                        "font": font,
                                        "size": size,
                                        "level": level_dict[
                                            pre_outline.replace(" ", "")
                                        ],
                                        "images": images,
                                        "tables": tables,
                                    }
                                )
                            pre_objid = page.page_obj.pageid
                            pre_outline = line
                            pre_page_num = page_num
                            pre_bbox = [
                                obj.bbox[0],
                                obj.bbox[1],
                                obj.bbox[2],
                                obj.bbox[3],
                            ]

                        else:
                            lines.append((pre_line_x, line))
                        pre_line_x = round(obj.matrix[4])
                        line = ""
                    line += obj.get_text()

        paragraph = ""
        paragraphs = []
        pre_x = 0
        if lines and len(lines) > 0:
            min_line_x = min(lines, key=lambda tup: tup[0])[0]
            for line_x, line_text in lines:
                if paragraph != "" and line_x != pre_x and line_x != min_line_x:
                    paragraphs.append({"text": str(paragraph), "page": page_num})
                    paragraph = ""
                paragraph += line_text
                pre_x = line_x
            if paragraph != "":
                paragraphs.append({"text": str(paragraph), "page": page_num})
            outlines.append(
                {
                    "title": str(pre_outline),
                    "bbox": pre_bbox,
                    "page": pre_page_num,
                    "text": paragraphs,
                    "font": font,
                    "size": size,
                    "level": level_dict[pre_outline.replace(" ", "")],
                    "images": images,
                    "tables": tables,
                }
            )
        return outlines

    def process_page_content(
        self, pdf: PDF, start_page: [], image_map: Dict, table_map: Dict
    ) -> List:
        if not pdf.pages or len(pdf.pages) <= 0:
            return []
        line = ""
        pre_page_num = 0
        pre_outline = ""
        pre_line_y = 0.0
        outlines = []
        lines = []
        pre_first_char = {}
        pre_line_last_char = {}
        tmp = []
        for i, page in enumerate(pdf.pages, 1):
            # if start_page[0] != -1 and i >= start_page[0] and i <= start_page[1]:
            #     continue
            page_num = page.page_number
            for obj in page.layout:
                if isinstance(obj, LTChar):
                    if 2 < abs(obj.matrix[5] - pre_line_y):
                        pre_line_y = obj.matrix[5]
                        if re.match(
                            r"[\．\.\ ]*$", line
                        ) is None and line.strip() not in ["\uf076", "\uf043"]:
                            line = line.replace(" ", "").replace("\uf076", "")
                            lines.append(
                                {
                                    "text": str(line),
                                    "page": pre_first_char["page"],
                                    "first_char": pre_first_char,
                                    "end_char": pre_line_last_char,
                                    "level": 0,
                                }
                            )

                        pre_first_char = {
                            "bbox": obj.bbox,
                            "char": obj.get_text(),
                            "width": obj.width,
                            "height": obj.height,
                            "page": page_num,
                        }
                        line = ""
                    if obj.get_text() != " ":
                        pre_line_last_char = {
                            "bbox": obj.bbox,
                            "char": obj.get_text(),
                            "width": obj.width,
                            "height": obj.height,
                            "page": page_num,
                        }
                    line += obj.get_text()
            if re.match(r"[\．\.\ ]*$", line) is None and line.strip() not in [
                "\uf076",
                "\uf043",
            ]:
                line = line.replace(" ", "").replace("\uf076", "")
                # lines.append(
                #     {"text": str(line), "page": page_num, "line_x": [pre_line_x["x"],pre_line_last_char["x"],pre_line_last_char , pre_line_x], "level": 0})
                lines.append(
                    {
                        "text": str(line),
                        "page": pre_first_char["page"],
                        "first_char": pre_first_char,
                        "end_char": pre_line_last_char,
                        "level": 0,
                    }
                )
                # line["first_char"] = {"bbox": obj.bbox, "char": obj.get_text(), "x": obj.matrix[4], "y": obj.matrix[5],"width": obj.width, "height": obj.height}
                line = ""
            header_or_footer = []
            if len(lines) > 0:
                for item in lines[:]:
                    if (
                        item["first_char"]["bbox"][1] < 60
                        or item["end_char"]["bbox"][2] > page.width - 60
                        or item["first_char"]["bbox"][3] > page.height - 60
                    ):
                        lines.remove(item)
                        header_or_footer.append(item)
                    else:
                        break

                if len(tmp) > 0 and tmp[-1]["page"] == page_num - 1:
                    lines.insert(0, tmp.pop(-1))
                min_line_x = min(lines, key=lambda x: x["first_char"]["bbox"][0])[
                    "first_char"
                ]["bbox"][0]
                max_line_x = max(lines, key=lambda x: x["end_char"]["bbox"][2])[
                    "end_char"
                ]["bbox"][2]
                max_line_x = page.width - min_line_x
                paragraph = ""
                pre_item = {
                    "first_char": {"bbox": []},
                    "end_char": {"bbox": [], "width": 0},
                }
                count = 0
                merge_lines = []
                i = 0
                # 把有可能是同一行的给拼接起来
                while i < len(lines):
                    start = i
                    while (
                        i < len(lines) - 1
                        and abs(
                            lines[i]["first_char"]["bbox"][1]
                            - lines[i + 1]["first_char"]["bbox"][1]
                        )
                        < 2
                    ):
                        lines[start]["text"] += lines[i + 1]["text"]
                        lines[start]["end_char"] = lines[i + 1]["end_char"]
                        i += 1
                    merge_lines.append(lines[start])
                    if i == start:
                        i += 1
                lines = merge_lines
                chars = []
                for i, item in enumerate(lines):
                    if paragraph != "" and (
                        (
                            item["first_char"]["bbox"][0] - min_line_x > 2
                            or pre_item["end_char"]["bbox"][2]
                            + pre_item["end_char"]["width"] / 1.5
                            < max_line_x
                        )
                    ):
                        tmp.append(
                            {
                                "text": str(paragraph),
                                "page": lines[i - count]["first_char"]["page"],
                                "first_char": lines[i - count]["first_char"],
                                "end_char": lines[i - count]["end_char"],
                                "chars": chars,
                                "level": 0,
                            }
                        )
                        count = 0
                        paragraph = ""
                        chars = []
                    paragraph += item["text"]
                    pre_item = item
                    count += 1
                    chars.append(item["first_char"])
                    chars.append(item["end_char"])
                if paragraph:
                    tmp.append(
                        {
                            "text": str(paragraph),
                            "page": pre_item["first_char"]["page"],
                            "first_char": lines[len(lines) - count]["first_char"],
                            "end_char": lines[len(lines) - count]["end_char"],
                            "chars": chars,
                            "level": 0,
                        }
                    )
                for _item in header_or_footer:
                    tmp.append(
                        {
                            "text": _item["text"],
                            "page": _item["first_char"]["page"],
                            "first_char": _item["first_char"],
                            "end_char": _item["end_char"],
                            "chars": [_item["first_char"], _item["end_char"]],
                            "level": 0,
                        }
                    )
                lines = []
        outlines = tmp
        # i = 0
        # while i < len(tmp):
        #     if  tmp[i]['text'][-1] in [":","："] and not re.findall("(如图|下图)",tmp[i]['text']):
        #         s = tmp[i]['text']
        #         j = i;
        #
        #         while i+1 < len(tmp) and re.search("^[A-Za-z0-9一二三四五六七八九十]{1,2}", tmp[i+1]['text'][:2]) is not None:
        #             i += 1;
        #             s += " " + tmp[i]['text']
        #             if i == 18:
        #                 break
        #             if i+1 < len(tmp) and abs(tmp[i]["first_char"]["bbox"][0] - tmp[i + 1]["first_char"]["bbox"][0]) > 2:
        #                 break
        #         tmp[j]['text'] = s
        #
        #         outlines.append(tmp[j])
        #     else:
        #         outlines.append(tmp[i])
        #     i += 1

        # outlines["catalog"] = [{"text":i["text"],"page":i["page"]} for i in outlines]
        return outlines

    def generate_outline_map(self, outlines: List) -> List:
        level_map = {}
        for outline in outlines:
            level = outline["level"]
            if not level_map.__contains__(level):
                level_map[level] = [outline]
            else:
                level_map[level].append(outline)
            if level != 0:
                if not level_map[level - 1][-1].__contains__("outline"):
                    level_map[level - 1][-1]["outline"] = [outline]
                else:
                    level_map[level - 1][-1]["outline"].append(outline)
        return level_map[0] if level_map and len(level_map) != 0 else None

    def handle_texts(self, page: Page) -> Dict:
        temp_res = {}
        content_text = page.extract_text()
        temp_res["text"] = content_text
        temp_res["page_num"] = page.page_number
        lines = []
        line_num = 1
        for line in page.extract_text_lines():
            text = line.get("text")
            font = ""
            size = ""
            x0 = ""
            x1 = ""
            y0 = ""
            y1 = ""
            top = ""
            bottom = ""
            chars = line.get("chars")
            for chr in chars:
                font = chr.get("fontname")
                size = chr.get("size")
                x0 = chr.get("x0")
                x1 = chr.get("x1")
                y0 = chr.get("y0")
                y1 = chr.get("y1")
                top = chr.get("top")
                bottom = chr.get("bottom")

                break
            lines.append(
                {
                    "font": font,
                    "text": text,
                    "line_num": line_num,
                    "size": size,
                    "position": {
                        "x0": x0,
                        "x1": x1,
                        "y0": y0,
                        "y1": y1,
                        "top": top,
                        "bottom": bottom,
                    },
                }
            )
        line_num += 1
        temp_res["lines"] = lines
        return temp_res

    @staticmethod
    def valid_image(data: [bytes]) -> bool:
        if data[6:10] in (b"JFIF", b"Exif"):
            if not data.rstrip(b"\0\r\n").endswith(b"\xff\xd9"):
                return False
        try:
            image_bytes = BytesIO(data)
            Image.open(image_bytes).verify()
        except:
            return False
        return True

    @staticmethod
    def handle_images(page: Page, name: str) -> List:
        images = []
        image_number = 1
        for image in page.images:
            folder_path = "data/images/" + name
            if not os.path.exists(folder_path):
                os.mkdir(folder_path)
            stream: PDFStream = image["stream"]
            data = stream.get_data()

            if PdfParser.valid_image(data):
                text = "TEST"
                if text:
                    path = (
                        folder_path
                        + "/"
                        + str(page.page_number)
                        + "_"
                        + str(image_number)
                        + ".jpeg"
                    )
                    of = open(path, "wb")
                    of.write(data)
                    of.close()
            image_number += 1
            if text:
                images.append(
                    {
                        "path": path,
                        "page_num": page.page_number,
                        "bbox": [image["x0"], image["y0"], image["x1"], image["y1"]],
                        "text": text,
                    }
                )
        return images

    @staticmethod
    def extract_text_from_image(image_path):
        ocr_reader = easyocr.Reader(
            ["ch_sim", "en"]
        )  # this needs to run only once to load the model into memory
        ocr_result = ocr_reader.readtext(image_path)
        # text = pt.image_to_string(img, lang="chi_sim")
        page_text = ""
        total_char = 0
        erro_char = 0
        for ocr_line in ocr_result:
            page_text += ocr_line[1]
            for char in page_text:
                total_char += 1
                if "\u4e00" <= char <= "\u9fff":
                    try:
                        char.encode("gb2312")
                    except:
                        erro_char += 1

        if total_char != 0 and erro_char / total_char > 0.3:
            print("error" + str(total_char) + " " + str(erro_char) + page_text)
        else:
            print(str(total_char) + " " + str(erro_char) + page_text)
        return page_text

    def handle_tables(self, page: Page) -> List:
        tables = []
        if page.find_tables():
            for table in page.find_tables():
                data = table.extract()
                tables.append(
                    {
                        "bbox": [
                            table.bbox[0],
                            table.bbox[1],
                            table.bbox[2],
                            table.bbox[3],
                        ],
                        "data": data,
                    }
                )

        # if page.extract_tables():
        #     temp_res = {"page_num": page.page_number, "data": page.extract_tables()}
        #     return temp_res

        return tables

    def save_result(self, name, res: Dict) -> None:
        folder_path = "data/samples"
        if not os.path.exists(folder_path):
            os.mkdir(folder_path)
        path = folder_path + "/" + name + ".json"
        f = open(path, "w", encoding="utf-8")
        json.dump(res, f)
        f.close()


def parser_json(json_str: [], res={}):
    for item in json_str:
        page_num = item.get("page")
        title = item.get("title", "")
        text = item.get("text", [])
        outline = item.get("outline", "")
        if not res.__contains__(page_num):
            res[page_num] = []
        if title:
            res[page_num].append(title)
        if isinstance(text, list):
            text = list(filter(lambda x: x and len(x) > 0, text))
            if len(text) > 0:
                parser_json(text, res)
        else:
            if text.strip():
                res[page_num].append(text)
        parser_json(outline, res)


def compare(a=[], b=[], key="", res={}):
    if not a:
        a = []
    if not b:
        b = []
    if a == b:
        res.update({key: {"pass": str(len(a)) + "/" + str(len(a))}})
        return
    b_pages = {}
    diff_dict = {}
    if b:
        parser_json(b, b_pages)
    if a:
        diff_len = 0
        diff_dict["pass"] = 0
        for page, items in groupby(a, key=itemgetter("page")):
            a_texts = [item.get("text").replace(" ", "") for item in items]
            diff = set(a_texts) - set(b_pages.get(page, []))

            if len(diff) > 0:
                diff_dict[page] = list(diff)
                diff_len += len(diff)
                # print("page:"+str(page)+" diff =>"+ str(diff))
        diff_dict["pass"] = str(len(a) - diff_len) + "/" + str(len(a))
        res.update({key: diff_dict})


def sample_test(sample_path: str, test_path: str):
    if os.path.isdir(sample_path) and os.path.isdir(test_path):
        for file_name in os.listdir(sample_path):
            res = {}
            file_name = "ElectronicBill.pdf.json"

            print(file_name)
            with open(sample_path + "/" + file_name) as sf:
                sample = json.load(sf)

            try:
                with open(test_path + "/" + file_name) as f:
                    test = json.load(f)
            except FileNotFoundError as e:
                print(e)
                continue
            file_info = ["has_outline", "is_image"]
            for key in file_info:
                if sample.get(key) != test.get(key):
                    print(
                        key
                        + "=> sample:"
                        + str(sample.get(key))
                        + "!= test:"
                        + str(test.get(key))
                    )
            sample_catalog = sample.get("catalog")
            test_catalog = test.get("catalog")
            compare(sample_catalog, test_catalog, "catalog", res)
            compare(sample.get("paragraphs"), test.get("outline"), "paragraphs", res)
            print(
                json.dumps(res, indent=4, separators=(", ", ": "), ensure_ascii=False)
            )
            print(
                "---------------------------------------------------------------------------------"
            )


if __name__ == "__main__":
    # for file_name in os.listdir("docs"):
    #     if file_name.find(".pdf") > 0 >= file_name.find("image"):
    #         # from tika import parser
    #         # p = parser.from_file("docs/" + "国寿职业运动员团体失能收入损失保险  .pdf")
    #         l = PdfParser().parser("docs/" + file_name)
    #     print(file_name)
    sample_path = "data/sample_hand"
    test_path = "data/result1"
    sample_test(sample_path, test_path)
