import json
import os
import shutil
import tempfile
import time
import uuid
from datetime import datetime
from typing import List

from llama_index.core import (
    ServiceContext,
    StorageContext,
)
from llama_index.core.schema import TextNode
from llama_index.core.service_context_elements.llm_predictor import LLMPredictor
from llama_index.core.vector_stores import (
    MetadataFilters,
    MetadataFilter,
    FilterOperator,
)
from pydantic import BaseModel

from common.llm.embedding import get_embedding_model
from common.llm.llama_llm import get_llm
from common.logging.logger import get_elk_logger, get_logger
from config import doc_config
from config.doc_config import (
    llm_predictor_model,
    deployMode,
    ENABLE_FEISHU_ALERT,
    FEISHU_ALERT_WEBHOOK_URL,
    HOST_NAME,
    milvus_meta_collection_name,
)
from nl2document.builder.document_index_builder import DocumentIndexBuilder
from nl2document.builder.downloader.registry import DownloaderFactory
from nl2document.builder.meta_index_builder import Meta<PERSON>ndex
from nl2document.builder.parser.document_parser import DocumentPars<PERSON>, NodeResult
from nl2document.common.alert.alert import FeishuAlert
from nl2document.common.base.const import (
    MIME_TYPE_MAP,
    LLAMA_INDEX_FILE_PARENT_LIST,
)
from nl2document.common.models.base_model import (
    update_document,
    UploadStatus,
    get_document_by_id,
    get_data_source,
)
from nl2document.common.models.model import (
    CommonDocumentModel,
)
from nl2document.common.msg.doc import FileStatus
from nl2document.common.utils import extract_company_and_date
from nl2document.common.vector.vector_store import get_vector_store

log = get_logger(__name__)
elk_logger = get_elk_logger()


llm_predictor = LLMPredictor(
    llm=get_llm(llm_predictor_model),
)

index_builder_exception_key_msg_map = {
    "not a file or image http url": "上传文档与会议文档格式要求不符，请检查文件",
    "not support": "不支持该格式文档。(支持类型包括 .pdf, .pptx, .docx)",
    "Document parsed json is None": "上传文档与会议文档格式要求不符，请检查文件并重新上传",
    "Nodes is empty": "上传文档与会议文档格式要求不符，请检查文件并重新上传",
}


def get_index_builder_exception_out_msg(e: Exception):
    for key, value in index_builder_exception_key_msg_map.items():
        if key in str(e):
            return value
    return "系统错误，请稍后重试"


def contains_target_subdir(path: str, target: str) -> bool:
    # 将路径分割成目录列表
    parts = path.split(os.sep)

    for part in parts[0:-1]:  # 排除最后一个元素，因为它们可能是根目录或文件名
        if part.lower() == target:
            return True
    return False


class DocumentAddRequest(BaseModel):
    # 文档务必要先分类
    # 分类树：
    # project_folder:
    # -- language: chinese/english // 按语言分类，语言模型不一样
    #    -- type: text/files // 按文件类型分类，暂定纯文本和其他
    source_url: str

    @property
    def name(self):
        return os.path.basename(self.source_url)

    @property
    def file_type(self):
        if contains_target_subdir(self.source_url, "text"):
            return "text"
        elif contains_target_subdir(self.source_url, "ppt"):
            return "ppt"
        elif contains_target_subdir(self.source_url, "files"):
            return "model"
        else:
            return "invalid"

    @property
    def language(self):
        if contains_target_subdir(self.source_url, "english"):
            return "english"
        return "chinese"


embed_model, _, _ = get_embedding_model(doc_config.doc_embedding_model)


class IndexBuilderBase:
    def __init__(self):
        self._doc_parser = DocumentParser()
        self._service_context = ServiceContext.from_defaults(
            embed_model=embed_model,
            llm_predictor=llm_predictor,
        )
        self.folder_id = str(uuid.uuid4())
        if ENABLE_FEISHU_ALERT:
            self.alert = FeishuAlert(webhook_url=FEISHU_ALERT_WEBHOOK_URL)
        else:
            self.alert = None

    def _download_document(
        self, file_dir: str, document_url: str, document: CommonDocumentModel
    ) -> str:
        save_path = os.path.join(file_dir, os.path.basename(document_url))

        downloader = DownloaderFactory.get_downloader(document_url, deployMode)
        save_path = downloader.download(document, document_url, save_path)

        log.info(
            "Add remote document, document %s download complete",
            document_url,
        )
        return save_path

    #     解析文档的后置操作
    def _parse_doc_post_process(
        self, node_result: NodeResult, document_model: CommonDocumentModel
    ):
        if doc_config.enable_build_meta_indexes:
            MetaIndex().build(node_result.nodes)

    def _create_index(self, source_path: str, document_model: CommonDocumentModel):
        vector_store = get_vector_store()
        storage_context = StorageContext.from_defaults(vector_store=vector_store)
        log.info(
            f"start parse document nodes to create index, file_id: {document_model.id}"
        )
        node_result = self._doc_parser.parse(source_path, document_model)
        # self._update_document_overview(node_result, document_model)
        nodes = node_result.nodes
        parent_id_list, parent_name_list = get_data_source().get_parent_id_name_list(
            document_model.id
        )
        log.info(
            f"parent_id_list: {parent_id_list},parent_name_list:{parent_name_list}"
        )
        document_model.meta_info[LLAMA_INDEX_FILE_PARENT_LIST] = parent_id_list
        company_id, year, month = extract_company_and_date(
            parent_name_list, document_model.file_name
        )
        document_model.meta_info["company_id"] = company_id
        document_model.meta_info["date"] = f"{year}-{month}"
        document_model.meta_info["year"] = year
        document_model.meta_info["month"] = month

        for node in nodes:
            node.metadata.update(document_model.meta_info)
        self._parse_doc_post_process(node_result, document_model)
        document_index = DocumentIndexBuilder(
            storage_context, self._service_context
        ).build(all_nodes=nodes)
        return document_index

    def exec_documents_task(self, documents: List[CommonDocumentModel]):
        for document in documents:
            start_time = datetime.now()
            try:
                log.info(
                    f"start build index for document: {document} file_id: {document.id}"
                )
                # 暂不支持csv后缀
                if (
                    document.file_status == FileStatus.Ready
                    and not document.file_name.endswith("csv")
                ):
                    self.delete_document(document)
                    # need rebuild index
                    document_request = DocumentAddRequest(
                        source_url=document.source_url,
                    )
                    self._add_document(document, document_request)
            except Exception as e:
                log.exception(
                    f"Failed to build index for document {document.id}: {str(e)}"
                )
                self._handle_builder_exception(e, document, start_time)

    def _handle_builder_exception(
        self, e: Exception, document: CommonDocumentModel, start_time: datetime
    ):
        update_document(
            document.id,
            upload_status=UploadStatus.INDEX_BUILD_FAILED,
            file_status=FileStatus.Fail,
            parse_result=get_index_builder_exception_out_msg(e),
            parse_error_msg=e.__str__(),
        )
        now = datetime.now()
        extra = {
            "timestamp": now.strftime("%Y-%m-%d %H:%M:%S"),
            "duration": str(now - start_time),
            "module_type": "index_builder",
            "file_id": document.id,
            "input": {
                "source_path": document.source_url,
            },
            "output": {
                "exception": e.__str__(),
            },
        }
        elk_logger.info(
            msg="recv exception",
            extra=extra,
        )
        log.exception(msg=f"recv exception", extra=extra)
        if self.alert:
            self.alert.send_alert(
                f"索引构建失败，请手动处理\n"
                f"环境: {HOST_NAME}\n"
                f"详细内容:\n"
                f"{json.dumps(extra, ensure_ascii=False, indent=4)} "
            )

    def delete_document(self, document: CommonDocumentModel):
        shutil.rmtree(document.index_dir, ignore_errors=True)
        filters = MetadataFilters(
            filters=[
                MetadataFilter(
                    key="file_id", value=document.id, operator=FilterOperator.EQ
                )
            ]
        )
        vector_store = get_vector_store()
        vector_store.delete_nodes(filters=filters)
        log.info(f"Successfully deleted embedding for document {document.id}")

    def _add_document(
        self,
        document: CommonDocumentModel,
        document_request: DocumentAddRequest,
    ):
        with tempfile.TemporaryDirectory() as temp_dir:
            start_time = time.time()
            local_source_path = self._download_document(
                temp_dir,
                document_request.source_url,
                document,
            )
            log.info(
                f"temp directory {temp_dir}, local source path {local_source_path}"
            )
            document = get_document_by_id(document.id)
            if document.upload_status == UploadStatus.DELETED:
                log.info(f"document {document} has been deleted")
                return
            self._create_index(local_source_path, document)
            document = get_document_by_id(document.id)
            if document.upload_status == UploadStatus.DELETED:
                log.info(f"document {document} has been deleted")
                self.delete_document(document)
                return
            update_document(
                document.id,
                upload_status=UploadStatus.INDEX_BUILD_SUCCESS,
                file_status=FileStatus.Done,
            )
            now = time.time()
            log.info(
                f"Successfully created index for document: {document} file_id: {document.id}"
            )
            elk_logger.info(
                msg=None,
                extra={
                    "timestamp": datetime.now(),
                    "duration": now - start_time,
                    "module_type": "index_builder",
                    "file_id": document.id,
                    "input": {
                        "source_path": document_request.source_url,
                    },
                    "output": {
                        "code": 200,
                    },
                },
            )


def get_mime_type(ext) -> str:
    ret = MIME_TYPE_MAP.get(ext)
    if ret:
        return ret
    return f"application/{ext[1:]}"
