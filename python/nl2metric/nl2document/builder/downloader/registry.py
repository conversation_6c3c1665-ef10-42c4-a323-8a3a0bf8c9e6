from typing import Dict, Type, TypeVar, Optional
from threading import Lock
from nl2document.builder.downloader.base_downloader import BaseDownloader
from nl2document.builder.downloader.downloaders import S3Downloader, LocalFileDownloader
from nl2document.common.base.const import LOCAL_FILE_URL
from common.logging.logger import get_logger

logger = get_logger(__name__)

T = TypeVar("T", bound=BaseDownloader)


class DownloaderFactory:
    """Factory class that handles registration and creation of downloaders"""

    _downloaders: Dict[str, Type[T]] = {}
    _lock = Lock()

    @classmethod
    def register(cls, key: str):
        """Decorator to register a downloader class

        Args:
            key: The key to register this downloader under (deploy_mode or default type)

        Returns:
            The decorator function

        Thread-safe:
            Uses Lock to ensure thread-safety during registration
        """

        def decorator(downloader_cls: Type[T]):
            with cls._lock:
                if key in cls._downloaders:
                    logger.warning(
                        f"Downloader for key {key} is being overwritten by {downloader_cls.__name__}"
                    )
                logger.info(
                    f"Registering downloader {downloader_cls.__name__} for key {key}"
                )
                cls._downloaders[key] = downloader_cls
            return downloader_cls

        return decorator

    @classmethod
    def is_registered(cls, key: str) -> bool:
        with cls._lock:
            return key in cls._downloaders

    @classmethod
    def get_downloader(cls, document_url: str, deploy_mode: str) -> T:
        """Get appropriate downloader instance

        Args:
            document_url: The document URL
            deploy_mode: The deployment mode

        Returns:
            A downloader instance

        Raises:
            ValueError: If no appropriate downloader is found

        Thread-safe:
            Uses Lock to ensure thread-safety during lookup
        """
        with cls._lock:
            # Check for local file first
            if document_url.startswith(LOCAL_FILE_URL):
                downloader_cls = cls._downloaders.get("local")
                if downloader_cls:
                    return downloader_cls()

            # Try to get registered downloader for deploy mode
            downloader_cls = cls._downloaders.get(deploy_mode)
            if downloader_cls:
                return downloader_cls()

            # Fall back to S3 downloader
            downloader_cls = cls._downloaders.get("s3")
            if downloader_cls:
                return downloader_cls()

            raise ValueError(f"No downloader registered for deploy mode {deploy_mode}")


# Create decorator function
register_downloader = DownloaderFactory.register
register_downloader("s3")(S3Downloader)
register_downloader("local")(LocalFileDownloader)
