from abc import ABC, abstractmethod
from nl2document.common.models.model import CommonDocumentModel


class BaseDownloader(ABC):
    """Base class for all document downloaders

    This class defines the interface that all downloaders must implement.
    Subclasses should implement the download method to handle specific
    download scenarios.
    """

    @abstractmethod
    def download(
        self, document: CommonDocumentModel, document_url: str, save_path: str
    ) -> str:
        """Download document to local path

        Args:
            document: Document model containing metadata
            document_url: URL to download from
            save_path: Path to save the downloaded file

        Returns:
            str: The actual path where file is saved
        """
        pass
