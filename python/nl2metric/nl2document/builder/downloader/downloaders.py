import shutil
from common.fs.fs import get_s3_file_system
from nl2document.builder.downloader.base_downloader import BaseDownloader
from nl2document.common.base.const import LOCAL_FILE_URL


class LocalFileDownloader(BaseDownloader):
    def download(self, document, document_url: str, save_path: str) -> str:
        local_file_path = document_url[len(LOCAL_FILE_URL) :]
        shutil.copy(local_file_path, save_path)
        return save_path


class S3Downloader(BaseDownloader):
    def download(self, document, document_url: str, save_path: str) -> str:
        get_s3_file_system().download(document_url, save_path)
        return save_path
