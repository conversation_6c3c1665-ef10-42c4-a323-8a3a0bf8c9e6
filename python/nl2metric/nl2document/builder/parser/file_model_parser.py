import json
from typing import List, Dict, Any, Optional, Literal, Union, Tuple, Set
import collections
import re
import os
from nl2document.builder.parser.ppt_pdf_parser.debug import (
    label_ocr,
    label_layout,
    obj_convert_dict,
    dump,
    load,
)
from common.logging.logger import get_logger
from nl2document.builder.parser.ppt_pdf_parser.utils import (
    Content,
    Layout,
    OCR,
    Align,
    EachPageContent,
    get_img_height_width,
    str2nums,
    remove_punctuation,
)
from nl2document.builder.parser.ppt_pdf_parser.content_aligin import (
    AliginContentOCR,
    AlignContentYOLO,
    SplitErrorMerge,
    ContentMerge,
    ContentFilter,
    Check,
    LevelMerge,
    cal_area_rate,
    convert_format,
    nums2str,
    cal_mid_point,
    cal_box,
)

from nl2document.builder.parser.table_parser.ocr_for_table import TableOCR
from nl2document.builder.parser.table_parser.table_parser import <PERSON><PERSON>ars<PERSON>, TableOut
from nl2document.builder.parser.ppt_pdf_parser import convert_api
from nl2document.builder.parser.ppt_pdf_parser.content_format import content_format
from nl2document.builder.parser.ppt_pdf_parser.ppt_preprocess import MyPPT, load_ppt
from nl2document.builder.parser.ppt_pdf_parser.pdf_preprocess import MyPDF, load_pdf
from nl2document.builder.parser.parse_model import ParseModel
from nl2document.builder.parser.ROUGE import RougeMetric
from nl2document.builder.parser.parsed_document import ParsedDocument
from nl2document.builder.parser.parsed_document import ContentType
from nl2document.builder.parser.label_tree_association import (
    tree_associate,
    tree_associate_pdf,
)
from common.trace.perfomance_monitor import performance_context

logger = get_logger(__name__)
os.environ["FLAGS_allocator_strategy"] = "auto_growth"


def filter_unused_content(aligns: List[Align]) -> List[Align]:
    res = []
    for a in aligns:
        if a.label in ("text", "title") and (
            re.search(r"[第共]\d+页[,，]*[第共]\d+页", "".join(a.content.split()))
            # or re.search(r"共\d+页[,，]第\d+页", "".join(a.content.split()))
            or re.match(r"第\d+页", "".join(a.content.split()))
            or re.match(r"[-一]+\d+[-一]+", "".join(a.content.split()))
        ):
            continue
        else:
            res.append(a)

    return res


def _doc_process(
    file_path: str, parse_model: ParseModel
) -> Tuple[List[str], List[str], List[List[Content]]]:
    """
    数据预处理

    Args:
        file_path: 文件路径
        parse_model: YOLO-OCR模型
        save_content: 是否保存内容

    Return:
        处理之后的图片, 没有处理的图片, 文档内容
    """
    save_path = file_path.rsplit(".", 1)[0]
    if file_path.lower().endswith(".pptx"):
        ppt: MyPPT = load_ppt(file_path, parse_model)
        contents: List[List[Content]] = ppt.contents()
        # contents: List[List[Content]] = content_format('pptx', ppt.contents())
        # del_pngs: List[str] = convert_api.convert_with_libreoffice(
        #     ppt.save(), "png", save_path
        # )
        none_pngs: List[str] = convert_api.convert_with_libreoffice(
            file_path, "png", save_path
        )
    elif file_path.lower().endswith(".pdf"):
        pdf: MyPDF = load_pdf(file_path, parse_model)
        contents: List[List[Content]] = content_format(
            parse_model, "pdf", pdf.contents()
        )
        # del_pngs: List[str] = convert_api.convert_with_libreoffice(
        #     pdf.save(), "png", save_path
        # )
        none_pngs: List[str] = convert_api.convert_with_libreoffice(
            file_path, "png", save_path
        )
    else:
        raise RuntimeError(f"该文件 `{file_path}` 暂不支持解析")

    del_pngs = []

    return del_pngs, none_pngs, contents


def _confirm_file_suffix(file_path: str, layouts: List[List[Layout]]) -> str:
    """
    通过模型来判断文件是ppt还是pdf
    Args:
        layouts: YOLO模型识别结果
        contents: 利用工具从文档提取得到的内容
    """
    _, suffix = os.path.splitext(os.path.basename(file_path.lower()))
    if suffix == ".pptx":
        return "pptx"
    elif suffix == ".docx":
        return "docx"
    elif suffix == ".pdf":
        page = len(layouts)
        catalog_num, header_title_num, flow_chart_num = 0, 0, 0
        for page_layouts in layouts:
            for l in page_layouts:
                if l.label == "catalog":
                    catalog_num += 1
                elif l.label == "header_title":
                    header_title_num += 1
                elif l.label == "flow_chart":
                    flow_chart_num += 1

        if catalog_num > 0 or header_title_num == 0:  # 如果出现catalog, 则默认为pdf
            return "pdf"
        # header_title或flow_chart占文档页面的1/3, 则默认为ppt
        elif header_title_num >= page // 3 or flow_chart_num >= page // 3:
            return "pptx"
        else:  # 对于其他情况, 则默认为ppt
            return "pptx"
    else:
        raise RuntimeError(f"暂不支持后缀名为`{suffix}`的解析")


class LabelClassifier:
    def __init__(self, layouts: List[Layout], ocrs: List[OCR], contents: List[Content]):
        self._layouts = layouts
        self._ocrs = ocrs
        self._contents = contents

    def table_labels(self) -> Dict[str, List]:
        layouts, ocrs = [], []
        contents = [c for c in self._contents if c.content_type != "figure"]
        for layout in self._layouts:
            if layout.label != "table":
                continue

            layouts.append(layout)
            x1, y1, x2, y2 = str2nums(layout.bbox)
            for ocr in self._ocrs:
                mid_x, mid_y = cal_mid_point(ocr.bbox)
                if x1 < mid_x < x2 and y1 < mid_y < y2:
                    ocrs.append(ocr)
                    # self.matcheds.add(ocr.bbox)

        return {"layouts": layouts, "ocrs": ocrs, "contents": contents}

    def figure_labels(self) -> Dict[str, List]:
        layouts, ocrs = [], []
        contents = [c for c in self._contents if c.content_type == "figure"]
        for layout in self._layouts:
            if layout.label != "figure":
                continue

            layouts.append(layout)
            x1, y1, x2, y2 = str2nums(layout.bbox)
            for ocr in self._ocrs:
                mid_x, mid_y = cal_mid_point(ocr.bbox)
                if x1 < mid_x < x2 and y1 < mid_y < y2:
                    ocrs.append(ocr)
                    # self.matcheds.add(ocr.bbox)

        return {"layouts": layouts, "ocrs": ocrs, "contents": contents}

    def flow_chart_labels(self) -> Dict[str, List]:
        layouts, ocrs = [], []
        for layout in self._layouts:
            if layout.label != "flow_chart":
                continue

            layouts.append(layout)
            x1, y1, x2, y2 = str2nums(layout.bbox)
            for ocr in self._ocrs:
                mid_x, mid_y = cal_mid_point(ocr.bbox)
                if x1 < mid_x < x2 and y1 < mid_y < y2:
                    ocrs.append(ocr)

        return {"layouts": layouts, "ocrs": ocrs, "contents": self._contents}

    def text_labels(
        self, tables: List[TableOut]
    ) -> Tuple[List[Layout], List[OCR], List[Content]]:
        layouts, matcheds = [], set()
        filter_layouts = [_t for _t in self._layouts if _t.label != "table"]
        for l in filter_layouts + [t.layout for t in tables]:
            # 找出layout
            if l.label in ("title", "text"):
                layouts.append(l)

            # 找出OCR
            if l.label not in ("table", "figure", "flow_chart"):
                continue
            x1, y1, x2, y2 = str2nums(l.bbox)
            for ocr in self._ocrs:
                mid_x, mid_y = cal_mid_point(ocr.bbox)
                if x1 < mid_x < x2 and y1 < mid_y < y2:
                    matcheds.add(ocr.bbox)

        ocrs = [o for o in self._ocrs if o.bbox not in matcheds]
        contents = [c for c in self._contents if c.content_type != "figure"]

        return layouts, ocrs, contents


# def _statistics_bbox_round(
#     img_width_height: List[Tuple[int, int]],
#     aligns: List[List[Align]],
#     top: int = 200,
#     bottom: int = 200,
#     left: int = 150,
#     right: int = 150,
#     over_rate: float = 0.7,
# ) -> Set[str]:
#     """统计高频的bbox"""
#     final_filter, candi_bboxs, res = set(), [], set()
#     for (width, heigh), _aligns in zip(img_width_height, aligns):
#         for align in _aligns:
#             # 如果是数字, 直接过滤
#             if align.content.isdigit():
#                 final_filter.add(align.content)
#                 res.add(align.bbox)
#                 continue
#             # 如果是特定内容, 则不过滤
#             # if align.content in []:
#             #     continue

#             x1, y1, x2, y2 = str2nums(align.bbox)
#             if y1 > top and y2 < heigh - bottom and x1 > left and x2 < width - right:
#                 continue

#             candi_bboxs.append((align.bbox, align.content))

#     # 统计bbox重合的次数
#     bbox_times = collections.defaultdict(set)
#     for c_b in candi_bboxs:
#         for _c_b in candi_bboxs:
#             if cal_area_rate(c_b[0], _c_b[0]) < over_rate:
#                 continue
#             bbox_times[c_b[0]].update([c_b[0], _c_b[0]])

#     pages = len(aligns)
#     for b in bbox_times.values():
#         # 文档页数比较少, 需要全部重复才能过滤
#         if pages < 10 and len(b) >= pages:
#             res.update(b)
#         # 文档页数比较多, 只要重复的文本超过1/2则过滤
#         elif 10 < pages <= 20 and len(b) >= pages // 2:
#             res.update(b)
#         # 文档页数比较多, 只要重复的文本超过1/3则过滤
#         elif 20 < pages <= 40 and len(b) >= pages // 3:
#             res.update(b)
#         elif pages > 40 and len(b) >= pages // 4:
#             res.update(b)

#     final_filter.update([c_b[-1] for c_b in candi_bboxs if c_b[0] in res])
#     if final_filter:
#         logger.info(f"以下内容被判定为无用信息, 将被过滤: {final_filter}")

#     return res


# class ProcessOcrs:
#     def __init__(self, file_type: str, ocrs: List[List[OCR]]):
#         self._file_type = file_type
#         self._ocrs = self._remove_ill_char(ocrs)

#     def _remove_ill_char(self, ocrs: List[List[OCR]]) -> List[List[OCR]]:
#         if self._file_type != "pptx":
#             return ocrs

#         res = []
#         for _ocrs in ocrs:
#             res.append([])
#             for ocr in _ocrs:
#                 if len(ocr.origin_text) == 1 and ocr.origin_text in ("口",):
#                     continue
#                 if len(ocr.origin_text) > 1 and ocr.origin_text[0] == "口":
#                     ocr.origin_text = ocr.origin_text[1:]
#                     ocr.text = ocr.text[1:]

#                 res[-1].append(ocr)

#         return res

#     def _process_each_page(self, ocrs: List[OCR]) -> List[OCR]:
#         res, matched, i = [], set(), 0
#         while i < len(ocrs):
#             curr = ocrs[i]
#             curr_bbox = str2nums(curr.bbox)
#             _, curr_midy = cal_mid_point(curr_bbox)
#             candis: List[OCR] = []
#             for ocr in ocrs:
#                 if curr.bbox == ocr.bbox:
#                     continue
#                 ocr_bbox = str2nums(ocr.bbox)
#                 _, ocr_midy = cal_mid_point(ocr_bbox)
#                 if (
#                     ocr_bbox[1] < curr_midy < ocr_bbox[3]
#                     or curr_bbox[1] < ocr_midy < curr_bbox[3]
#                 ):
#                     candis.append(ocr)

#             if not candis:  # 没有水平平行
#                 res.append(curr)
#                 matched.add(curr.bbox)
#             else:
#                 candis.append(curr)
#                 candis.sort(key=lambda x: str2nums(x.bbox)[0])
#                 ocr1, ocr2 = candis[:2]
#                 if re.match(r"图表\s*\d+\s*[:：]", ocr1.origin_text):
#                     res.append(
#                         OCR(
#                             bbox=cal_box([ocr1.bbox, ocr2.bbox]),
#                             text=ocr1.text + ocr2.text,
#                             font=(ocr1.font + ocr2.font) // 2,
#                             origin_text=ocr1.origin_text + ocr2.origin_text,
#                         )
#                     )
#                     matched.update([ocr1.bbox, ocr2.bbox])

#             ocrs = [o for o in ocrs if o.bbox not in matched]

#         return res

#     def concat_by_line(self) -> List[List[OCR]]:
#         res = []
#         for ocrs in self._ocrs:
#             res.append(self._process_each_page(ocrs))

#         return res


# 加载检测、OCR、表格检测模型
parse_model = ParseModel("ch")
table_parser_model = TableOCR()


def single_ppt_pdf_parse(
    file_path: str,
    language: Literal["ch", "english"] = "ch",
    output_path: Optional[str] = None,
    is_del_tmp_files: bool = False,
    is_debug=False,
) -> Dict:
    """
    Args:
        ppt_file_path: ppt文件路径
        language: 使用哪种语言进行解析
    """
    # 对文件进行重命名
    basedir = os.path.dirname(file_path)
    basename, suffix = os.path.splitext(os.path.basename(file_path))
    new_name = remove_punctuation(basename, True)
    new_file_path = f"{basedir}/{new_name}{suffix}"
    os.rename(file_path, new_file_path)

    if suffix not in (".pptx", ".docx", ".pdf"):
        raise RuntimeError(f"暂不支持解析后缀名为`{suffix}`的文件")

    # 数据预处理
    with performance_context("doc process"):
        del_pngs, none_pngs, contents = _doc_process(new_file_path, parse_model)
    # 调用YOLO和OCR模型
    with performance_context(f"yolo recognition, {len(none_pngs)} pages"):
        layouts: List[List[Layout]] = parse_model.yolo_rec(none_pngs)
    with performance_context(f"ocr recognition, {len(none_pngs)} pages"):
        ocrs: List[List[OCR]] = parse_model.ocr_rec(none_pngs)
    real_suffix = _confirm_file_suffix(new_file_path.lower(), layouts)
    # ocrs: List[List[OCR]] = ProcessOcrs(real_suffix, ocr_model_res)._ocrs
    # 存储每一页的结果
    storages: List[Dict[str, Any]] = []
    # TODO 保存YOLO和OCR结果
    page = 0
    with performance_context(f"traverse page, {len(none_pngs)} pages"):
        for _none_png, _contents, _ocrs, _layouts in zip(
            none_pngs, contents, ocrs, layouts
        ):
            page += 1
            if is_debug:
                logger.info(f"执行到第 {page} 页......")
            _contents: List[Content] = SplitErrorMerge(
                _contents, _ocrs
            ).disassemble_err_merge()
            if is_debug:
                label_layout(_none_png, _layouts, sign="origin")
                dump(
                    f"{basedir}/{new_name}/layout_{page}.json",
                    [layout.to_json() for layout in _layouts],
                )
                label_ocr(_none_png, _ocrs, sign="origin")
                dump(
                    f"{basedir}/{new_name}/ocr_{page}_origin.json",
                    [ocr.to_json() for ocr in _ocrs],
                )
                dump(
                    f"{basedir}/{new_name}/{new_name}_{page}.json",
                    [ocr.to_json() for ocr in _contents],
                )

            labelClassifier = LabelClassifier(_layouts, _ocrs, _contents)
            # 表格解析
            table_dic = labelClassifier.table_labels()
            table_dic["ocrs"] = table_parser_model(_none_png)
            tableParser = TableParser(file_type=real_suffix, **table_dic)
            table_outs: List[TableOut] = tableParser(_none_png)
            tables: List[Align] = [t.align for t in table_outs]
            # 图片解析
            figure_dics = labelClassifier.figure_labels()
            # 文本解析
            text_layouts, text_ocrs, text_contents = labelClassifier.text_labels(
                table_outs
            )

            # 对齐Content-OCR, 不会有嵌套的内容
            aliginContentOCR = AliginContentOCR(real_suffix, text_contents, text_ocrs)
            content_ocrs: List[Content] = aliginContentOCR.align_content_ocr(
                is_no_match=True
            )
            if is_debug:
                label_ocr(_none_png, content_ocrs + tables, "content_ocr")
                dump(
                    path=f"{basedir}/{new_name}/result/{new_name}_{page}_content_ocr.json",
                    datas=[ocr.to_json() for ocr in content_ocrs + tables],
                )

            # 对齐文档-OCR-YOLO
            alignContentYOLO = AlignContentYOLO(
                file_type=real_suffix,
                content_ocrs=content_ocrs,
                figures=[],
                no_match_contents=[],
                no_match_ocrs=[],
                layouts=text_layouts,
                img_path=_none_png,
            )
            align_content_ocr_for_texts: List[
                Align
            ] = alignContentYOLO.align_content_yolo()
            if is_debug:
                dump(
                    path=f"{basedir}/{new_name}/result/{new_name}_{page}_layout_ocr.json",
                    datas=[
                        ocr.to_json() for ocr in align_content_ocr_for_texts + tables
                    ],
                )
                label_layout(
                    _none_png, align_content_ocr_for_texts + tables, "layout_ocr"
                )

            # 合并数据
            mergeContent = ContentMerge(align_content_ocr_for_texts, parse_model)
            texts_merge: List[Align] = mergeContent.merge()
            if is_debug:
                dump(
                    path=f"{basedir}/{new_name}/result/{new_name}_{page}_text_merge.json",
                    datas=[ocr.to_json() for ocr in texts_merge + tables],
                )
                label_layout(_none_png, texts_merge + tables, "merge")

            # 规则校验
            Check(real_suffix, texts_merge, [], []).check()
            if is_debug:
                dump(
                    path=f"{basedir}/{new_name}/result/{new_name}_{page}_content_check.json",
                    datas=[ocr.to_json() for ocr in texts_merge + tables],
                )
                label_layout(_none_png, texts_merge + tables, "content_check")

            storages.append(
                {
                    "file_type": real_suffix,
                    "img_path": _none_png,
                    "img_width_height": get_img_height_width(_none_png),
                    "aligns": texts_merge + tables,
                    "no_match_contents": [],
                    "no_match_ocrs": [],
                }
            )

        # 目录解析

    # # 统计OCR重叠的频率
    # if storages[0]['file_type'] == 'pptx':
    #     count_filtered_bboxs: Set[str] = _statistics_bbox_round(
    #         img_width_height=[s["img_width_height"] for s in storages],
    #         aligns=[s["no_match_ocrs"] for s in storages],
    #     )
    # else:
    #     count_filtered_bboxs = set()
    document = ParsedDocument(os.path.basename(file_path))
    for _page, info in enumerate(storages):
        # 格式转化
        each_page_contents: List[EachPageContent] = convert_format(
            file_type=info["file_type"],
            contents=[[a] for a in filter_unused_content(info["aligns"])],
            page=_page + 1,
        )
        #     if is_debug:
        #         dump(
        #             f"{base_dir}/{base_mane}/result/{base_mane}_{_page+1}_final.json",
        #             obj_convert_dict(each_page_contents),
        #         )

        #     # 规则过滤
        #     contentFilter = ContentFilter(
        #         file_type=info['file_type'],
        #         img_path=info["img_path"],
        #         contents=info['aligns']+info['no_match_ocrs'],
        #         img_width=info["img_width_height"][0],
        #         img_height=info["img_width_height"][1],
        #         filtered_bboxs=count_filtered_bboxs
        #     )

        #     if is_debug:
        #         label_layout(contentFilter._img_path, [Layout(a.bbox, a.label, a.conf) for a in contentFilter.filter()], "")
        #         # label_ocr(contentFilter._img_path, [OCR(o.bbox, "", o.font) for o in contentFilter.filter()], "final")
        #         dump(f"{base_dir}/{base_mane}/result/{base_mane}_{_page+1}.json", [a.to_json() for a in contentFilter.filter()])

        #     # 处理成层级
        #     if info["file_type"] == 'pptx':
        #         merge_level: List[List[Align]] = LevelMerge(contentFilter.filter()).merge_by_level()
        #         each_page_contents: List[EachPageContent] = convert_format(contentFilter._file_type, merge_level, _page+1)
        #     else:
        #         each_page_contents: List[EachPageContent] = convert_format(contentFilter._file_type, [[a] for a in contentFilter.filter()], _page+1)
        #     if is_debug:
        #         label_layout(contentFilter._img_path, [Layout(nums2str(a.bbox), a.label, a.conf)
        #                                                for a in each_page_contents], "final")
        #         # label_ocr(contentFilter._img_path, [OCR(o.bbox, "", o.font) for o in content_merge], "final")
        #         dump(f"{base_dir}/{base_mane}/result/{base_mane}_{_page + 1}_final.json",
        #              obj_convert_dict(each_page_contents))
        ppt_page_postprocess(
            each_page_contents,
            document,
            _page + 1,
            int(info["img_width_height"][1]),
            real_suffix,
        )

    #     if is_debug:
    #         label_layout(contentFilter._img_path, [Layout(nums2str(a.bbox), a.label, a.conf)
    #                      for a in each_page_contents], "final")
    #         # label_ocr(contentFilter._img_path, [OCR(o.bbox, "", o.font) for o in content_merge], "final")
    #         dump(f"{base_dir}/{base_mane}/result/{base_mane}_{_page+1}_final.json", obj_convert_dict(each_page_contents))

    final_text_res = document.to_json()
    if output_path != None:
        os.makedirs(output_path, exist_ok=True)
        parse_output_file = os.path.join(
            output_path, f"{final_text_res['file_name']}.json"
        )
        with open(parse_output_file, "w") as f:
            json.dump(final_text_res, f, ensure_ascii=False)
        print("parse_output_dir path is >>> ", parse_output_file)
    return final_text_res


# 按页后处理内容
def ppt_page_postprocess(
    each_page_contents: List[EachPageContent],
    document: ParsedDocument,
    page_num: int,
    page_height: int,
    real_suffix: str = "pdf",
):
    print(f"deal page content of page {page_num}")
    virtual_last_title_font_size = 160
    page_contents = sort_ppt_page_contents(each_page_contents, page_height, real_suffix)
    for page_content in page_contents:
        content_label = page_content["label"]
        content_text = page_content["content"]
        if content_label in ["title", "header_title", "caption"]:
            # 标题处理
            content_bbox = page_content["bbox"]
            x1, y1, x2, y2 = (
                content_bbox[0],
                content_bbox[1],
                content_bbox[2],
                content_bbox[3],
            )
            font_size = (
                160
                if content_label in ["header_title"] or content_text == "目录"
                else 140
            )
            virtual_last_title_font_size = font_size

            if (
                document.add_title(
                    content_text,
                    "",
                    True,
                    float(page_content.font if page_content.font else font_size),
                )
                == False
                and content_text
            ):
                document.add_content(content_text, page_num)
        elif content_label == "table":
            # 表格处理
            table_dict = {"html": json.dumps(content_text, ensure_ascii=False)}
            document.add_content(table_dict, page_num, ContentType.TABLE)
        elif content_label == "text":
            sub_contents = page_content["sub_content"]
            # 文本处理
            if content_text and not sub_contents:  # 存文本 没有嵌套
                document.add_content(content_text, page_num)
            else:  # 有嵌套的话 只取嵌套里面的内容 不需要外面那一层text 不然就重复了
                get_content_sub_texts(
                    document, sub_contents, page_num, virtual_last_title_font_size
                )
        elif content_label == "figure" and content_text:
            # 图片文本内容处理
            document.add_content(content_text, page_num)


# 整理页内文本的顺序
def sort_ppt_page_contents(
    each_page_contents: List[EachPageContent],
    page_height: int,
    real_suffix: str,
) -> List[EachPageContent]:
    # 整理文本内容顺序
    sorted_page_contents = []
    if real_suffix == "pptx":
        sorted_page_contents = tree_associate(each_page_contents, page_height)
    elif real_suffix == "pdf":
        sorted_page_contents = tree_associate_pdf(each_page_contents)
    return sorted_page_contents


# 处理有嵌套的text块
def get_content_sub_texts(
    document: ParsedDocument,
    sub_contents: List[EachPageContent],
    page_num: int,
    virtual_font_size: int,
):
    for sub_content in sub_contents:
        content_label = sub_content["label"]
        content_text = sub_content["content"]
        if content_label in ["title"]:
            font_size = virtual_font_size - 20
            if (
                document.add_title(
                    content_text,
                    "",
                    True,
                    float(sub_content.font if sub_content.font else font_size),
                )
                == False
                and content_text
            ):
                document.add_content(content_text, page_num)
            sub_sub_contents = sub_content["sub_content"]
            if sub_sub_contents:
                get_content_sub_texts(document, sub_sub_contents, page_num, font_size)
        elif content_label in ["text"]:
            if content_text:
                document.add_content(content_text, page_num)
        elif content_label == "figure" and content_text:
            # 图片文本内容处理
            document.add_content(content_text, page_num)


if __name__ == "__main__":
    # file_dir = "/home/<USER>/ppt_documents"
    # for file in os.listdir(file_dir):
    #     if file.endswith("pptx"):
    #         if os.path.exists(f"{file_dir}/{file}" + ".json"):
    #             continue
    #         else:
    #             res = single_ppt_parse(f"{file_dir}/{file}", "ch", file_dir)
    import time

    start_time = time.time()
    res = single_ppt_pdf_parse(
        "/home/<USER>/pdf_documents_0912/pdf原文档/2564_教育行业-江苏省城乡结对多师课堂简介.pdf",
        "ch",
        "/home/<USER>/pdf_documents_0912/pdf原文档",
        is_debug=True,
    )
    over_time = time.time()
    print(f"程序耗时: {over_time - start_time} 秒")
