from typing import List, Dict, Any, Literal
import fitz
from fitz import Document
import pdfplumber
import os
import re
import uuid
import copy
import sys

# sys.path.append("/home/<USER>/Code/ask-bi/python/nl2metric")
from nl2document.builder.parser.ppt_pdf_parser.utils import (
    OCR,
    remove_punctuation,
    is_contain_chinese,
)
from nl2document.builder.parser.ppt_pdf_parser.debug import dump
from nl2document.builder.parser.parse_model import ParseModel
from common.logging.logger import get_logger

logger = get_logger(__name__)


# 文档提取时保留该标点符号
PUNCTUATION = r"""\\}>|：）℃≧《≤\u001b=@≦\"～·》Ⅰ-”~⑤；①！〕● ／{/】]•【÷③[□…:％#¥？%)。.!（．－≥&’\u0003+—(',$×④⋯‘?_②、–⑥〔;■―°“＝*＋，<"""
WHITESPACE = " \t\n\r\v\f"
ASCII_LETTERS = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
DIGITS = "0123456789"
PRINTABLE = PUNCTUATION + WHITESPACE + ASCII_LETTERS + DIGITS
# 文档提取时忽略该内容
IGNORES = ("一、 二、 三、 四、 五、 六、 七、 八、 九、 十、 一 二 三 四 五 六 七 八 九 十").split(" ")

COLOR_MAPPING = {  # 最终颜色用黑色"1"和红色"2"表示
    16711680: 1,  # 黑色
    8421504: 1,  # 黑色
    0: 1,  # 默认取黑色
    16777215: 2,  # 白色
    12582912: 2,  # 红色
}


def _remove_ill_char(text: str, printable: str = PRINTABLE) -> str:
    res_text = ""
    for char in text:
        if is_contain_chinese(char) or char in printable:
            res_text += char

    return res_text


def multi_match_by_re(text: str, recur: str, pos: str, sign: str = "/////") -> str:
    for t in re.findall(recur, text):
        if pos == "前":
            text = text.replace(t, f"{sign}{t[1:]}")
        else:
            text = text.replace(t, f"{t[:-1]}{sign}")

    return text


def _remove_ill_space_for_text(
    content: Dict[str, Any],
    pre_line_symbol: List[str] = [
        r"\n图表\s*\d+",
        r"\n[（\(]?[一二三四五六七八九十]{1,2}[）\)]?[\.、]",
        r"\n[（\(]?[0-9a-zA-Z][）\)]?[\.．、]",
        # r"\n第[一二三四五六七八九十]+节\s*\S*[]"
    ],
    post_line_symbol: List[str] = [
        r"[。%]\n",
        r"[·.]{5}\d+\n",
        r"附表\n",
        r"目录\n",
        r"[.]{5,}\s*\d+\n",
    ],
    special_post_symbol: List[str] = [
        r"第\s*.{1,3}\s*[章节]\n",
        r"^[一二三四五六七八九十]{1,3}\.\n",
        r"[\(（][一二三四五六七八九十\d+][（\)][.·]?\n",
        r"\d+(?:[·．.]\d*)+\n",  # 对于数字, 必须有1.或1.1
        r"[a-zA-Z1-9]{1,3}[\)）][·．.\.、]?\n",
        # r"[a-zA-A]\n[a-zA-A]",  # 字母之间出现换行符, 替换成空格
    ],
    pre_symbol: str = r"""【 ① ② ③ ... \\ ··· ( （ …… 〕 、 #""",
    post_symbol: str = r"""!\"#$%&')*,、.;=>?@]^_`|}~………，。？～！）+-「」】\“”‘；/》〉...···:：〕""",
) -> List[Dict[str, Any]]:
    if content["type"] in ("figure", "table"):
        return [content]

    # if "第二节" in content["origin_content"]:
    #     print()

    res = []
    # 如果有连续三个以上的空格, 将其拆成换行符
    text = re.sub(r"\s{3,}", "\n", _remove_ill_char(content["origin_content"]))
    # 处理标点符号
    for pre_sign in pre_symbol.split(" "):
        text = text.replace(f"\n{pre_sign}", pre_sign)
    for post_sign in post_symbol:
        text = text.replace(f"{post_sign}\n", post_sign)
    # 处理换行
    for pre_line_sign in pre_line_symbol:
        text = multi_match_by_re(text, pre_line_sign, "前")
    for post_line_sign in post_line_symbol:
        text = multi_match_by_re(text, post_line_sign, "后")
    # 替换成空格
    for s_p_s in special_post_symbol:
        text = multi_match_by_re(text, s_p_s, "后", " ")

    for t in text.split("/////"):
        t = t.strip()
        # if not t or re.match(r"第\n*[0-9iv]\n*页(共\n*[0-9]\n*页)*", t):
        #     continue
        if not t:
            continue

        # 如果目录前面有数字，且有换行符, 则替换成空格
        if re.match(r"\d+\n\.*(..........|··········)", text):
            t = t.replace("\n", " ", 1)
        # ....前面有换行符
        if ".........." in t or "··········" in t or ".........." in t:
            t = t.replace("\n", "")

        cont = copy.deepcopy(content)
        cont["origin_content"] = "\n".join(re.split(r"\n+", t))
        res.append(cont)

    return res


def _remove_ill_space_for_table(
    text: str,
    pre_symbol: str = "、，【",
    post_symbol: str = r"、”。",
    special_pre_symbol: List[str] = [r"¥\n[0-9,.\s]+$"],
) -> str:
    if not text or not remove_punctuation(text, True):
        return ""

    text = re.sub(r"\n+", "\n", text.strip())
    # 处理标点符号
    for pre_sign in pre_symbol:
        text = text.replace(f"{pre_sign}\n", pre_sign)
    for post_sign in post_symbol:
        text = text.replace(f"\n{post_sign}", post_sign)

    # 必须包含小数点、数字才能删除里面所有的空白符
    if "." in text and re.match(r"[0-9,.\s]+$", text):
        text = "".join(text.split())

    # 处理金额
    for s_p_s in special_pre_symbol:
        m = re.search(s_p_s, text)
        if m:
            sign = m.group()
            text = text.replace(sign, sign.replace("\n", "", 1))

    return text


class MyPDF:
    def __init__(
        self,
        fig_text_doc: Document,
        out_fold_path: str,
        filename: str,
        stem: str,
        parse_model: ParseModel,
        handled_mode: Literal["del_img", "none"],
        ill_signs: str = "—，~．、→•；⎾⏌～“”｜/+-",
        ignores: List[str] = IGNORES,
        **kwargs,
    ):
        """
        Args:
            fig_text_doc: 用于处理图片和文本
            out_fold_path: 文件输出目录
            file_name: 文件名
            stem: 文件名, 没有后缀名
            parse_model: OCR-YOLO模型
            handled_mode: 对文件的处理模式
                del_img: 删除pdf图片
                none: 不做任何处理
        """
        self._fig_text_doc: Document = fig_text_doc
        self._out_fold_path: str = out_fold_path
        self._filename: str = filename
        self._stem: str = stem
        self._parse_model: ParseModel = parse_model
        self._handled_mode: str = handled_mode
        self._ill_signs: str = ill_signs
        self._ignores: List[str] = ignores
        self._fig_char_num: int = kwargs.get("char_num", 10)
        os.makedirs(f"{out_fold_path}/{stem}", exist_ok=True)

    def _extract_table_meta(self) -> List[List[Dict[str, Any]]]:
        res: List[List[str, Any]] = []
        with pdfplumber.open(f"{self._out_fold_path}/{self._filename}") as pdf:
            for page in pdf.pages:
                res.append([])
                for table_num, table in enumerate(page.extract_tables()):
                    x1, y1, x2, y2 = page.find_tables()[table_num].bbox
                    texts = [_remove_ill_space_for_table(_t) for t in table for _t in t]
                    res[-1].append(
                        {
                            "type": "table",
                            "bbox": f"[{int(x1)}, {int(y1)}, {int(x2)}, {int(y2)}]",
                            "img_path": "",
                            "origin_content": "\n\n\n\n\n".join(
                                [t for t in texts if t]
                            ),
                            "content": "",
                            "sub_texts": [],
                            "sub_sizes": [],
                            "sub_bolds": [],
                            "sub_colors": [],
                            "sub_italics": [],
                            "sub_hyperlink": [],
                        }
                    )

        return res

    def _filter_text_in_table(
        self, tables: List[Dict[str, Any]], contents: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        texts, matcheds = [], set()
        t_texts = [remove_punctuation(t["origin_content"], True) for t in tables]
        for cont in contents:
            if cont["bbox"] in matcheds:
                continue
            if cont["type"] == "figure":
                texts.append(cont)
            text = remove_punctuation(cont["origin_content"], True)
            if not any(text in t_text for t_text in t_texts):
                texts.append(cont)

        return texts

    def _ill_format(self, text: str) -> str:
        for sign in self._ill_signs:
            texts = [t.strip() for t in text.split(sign)]
            text = f"{sign}".join(texts)

        return text

    def _text(self, block) -> Dict[str, Any]:
        x1, y1, x2, y2 = map(int, block["bbox"])
        texts, sub_text, sub_size, sub_bold, sub_color = [], [], [], [], []
        for line in block["lines"]:
            for span in line["spans"]:
                text: str = span["text"].strip()
                if not text:  # 内容为空
                    continue
                t = text + "\n"
                text_len = len(t)
                texts.append(t)
                bold = True if "Bold" in span["font"] else False
                color = COLOR_MAPPING.get(span["color"], 1)
                sub_text.extend(list(t))
                sub_size.extend([int(span["size"]) for _ in range(text_len)])
                sub_bold.extend([bold for _ in range(text_len)])
                sub_color.extend([color for _ in range(text_len)])

        origin_text = remove_punctuation(self._ill_format("".join(texts)), False)
        simple_text = remove_punctuation(origin_text, True)
        # 内容为空、在过滤条件里、为数字, 则都过滤掉
        if (
            not (origin_text or simple_text)
            or origin_text in self._ignores
            or origin_text.isdigit()
        ):
            return

        dic = {
            "type": "text",
            "bbox": f"[{x1}, {y1}, {x2}, {y2}]",
            "img_path": "",
            "origin_content": origin_text,
            "content": simple_text,
            "sub_texts": sub_text,
            "sub_sizes": sub_size,
            "sub_bolds": sub_bold,
            "sub_colors": sub_color,
            "sub_italics": [False for _ in range(len(sub_text))],
            "sub_hyperlink": ["" for _ in range(len(sub_text))],
        }

        return dic

    def _figure(self, img) -> Dict[str, Any]:
        xref: int = img[0]  # 获取图片的引用 ID
        img_info = self._fig_text_doc.extract_image(xref)
        x1, y1 = img_info["xres"], img_info["yres"]
        bbox = f'[{x1}, {y1}, {x1 + img_info["width"]}, {y1 + img_info["height"]}]'
        path = f"{self._out_fold_path}/{self._stem}/{xref}.{img_info['ext']}"
        if not os.path.exists(path):
            with open(path, "wb") as f:
                f.write(img_info["image"])

        ocrs: List[OCR] = self._parse_model.ocr_rec(path)
        origin_content, content = [], []
        for ocr in ocrs:
            origin_content.append(ocr.origin_text)
            content.append(ocr.text)

        fig_char_num = len("".join(content))
        if not (origin_content or content) or fig_char_num < self._fig_char_num:
            os.remove(path)
            return

        dic = {
            "type": "figure",
            "bbox": bbox,
            "sha1": xref,
            "img_path": path,
            "origin_content": "\n".join(origin_content),
            "content": remove_punctuation("".join(content)),
            "sub_texts": [],
            "sub_sizes": [],
            "sub_bolds": [],
            "sub_colors": [],
            "sub_italics": [],
            "sub_hyperlink": [],
        }

        return dic

    def _content_extract(self) -> List[List[Dict[str, Any]]]:
        res: List[List[Dict[str, Any]]] = []
        for each_page_doc in self._fig_text_doc:
            res.append([])
            for img in each_page_doc.get_images(full=True):
                img_dic = self._figure(img)
                if img_dic:
                    res[-1].append(img_dic)

            for block in each_page_doc.get_text("dict")["blocks"]:
                if block["type"] != 0:
                    continue
                text_dic = self._text(block)
                if text_dic:
                    res[-1].append(text_dic)

        return res

    def contents(self) -> List[List[Dict[str, Any]]]:
        res: List[List[Dict[str, Any]]] = []
        contents: List[List[Dict[str, Any]]] = self._content_extract()
        # 从文档删除图片
        xrefs = [_x["sha1"] for x in contents for _x in x if _x["type"] == "figure"]
        for xref in set(xrefs):
            self._fig_text_doc._deleteObject(xref)

        page_tables = self._extract_table_meta()
        for page_table, conts in zip(page_tables, contents):
            tmp_texts = []
            for cont in conts:
                # if cont["type"] == "text":
                #     dump(
                #         f'/home/<USER>/Code/文档解析/origin_contents/{uuid.uuid1().hex}_{self._filename.replace(".pdf",".json")}',
                #         {"text": cont["origin_content"]},
                #     )
                tmp_texts.extend(_remove_ill_space_for_text(cont))

            # 过滤table里面的文本
            texts_figs = self._filter_text_in_table(page_table, tmp_texts)
            res.append(texts_figs + page_table)

        return res

    def save(self) -> str:
        save_path: str = f"{self._out_fold_path}/{self._stem}"
        path: str = f"{save_path}/{self._handled_mode}_{self._filename}"
        try:
            self._fig_text_doc.save(path)
            self._fig_text_doc.close()
        except:
            logger.info(f"文件 `{self._filename}` 保存失败")
            path = f"{self._out_fold_path}/{self._filename}"

        return path


def load_pdf(
    pdf_file_path: str,
    parse_model: ParseModel,
    handled_mode: str = "del_img",
    **kwargs,
) -> MyPDF:
    basename, suffix = os.path.splitext(os.path.basename(pdf_file_path))
    try:
        return MyPDF(
            fig_text_doc=fitz.open(pdf_file_path),
            out_fold_path=os.path.dirname(pdf_file_path),
            filename=f"{basename}{suffix}",
            stem=basename,
            parse_model=parse_model,
            handled_mode=handled_mode,
            **kwargs,
        )
    except:
        raise RuntimeError(f"文件 `{basename}{suffix}` 读取失败")


# if __name__ == "__main__":
#     pdf = load_pdf(
#         pdf_file_path="/home/<USER>/Code/文档解析/测试集/debug/6681-2024第一季度完税证明企业所得税.pdf",
#         parse_model=ParseModel(),
#     )
#     pdf.contents()
