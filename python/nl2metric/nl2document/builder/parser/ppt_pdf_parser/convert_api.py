from typing import List, Tuple, Union, Literal
import pptx
from pptx import presentation
from pdf2image import convert_from_path
import pdfplumber
import subprocess
import os
import hmac
import io
import json
import zipfile
import requests
import hashlib
import time
from glob import glob
from tqdm import tqdm


# secret_key = "SKoauszshyvqrbqv"
# access_key = "VZYRMHKHWHTOXCMY"
# host = "http://**************/open"

# secret_key = "SKoauszshyvqrbqv"
# access_key = "VZYRMHKHWHTOXCMY"
# host = "http://**************/open"


def get_pdf_page(pdf_path: str) -> int:
    with pdfplumber.open(pdf_path) as pdf:
        return len(pdf.pages)


def file_convert(file_path: str, target: str, save_path: str) -> str:
    # 23机器安装的版本是 LibreOffice ******* 30(Build:2), 51机器安装的版本是 ******* 40(Build:2)
    # LibreOffice ******* 40(Build:2), 以这个版本为主, 版本过高可能导致ppt转pdf出问题
    basename, suffix = os.path.splitext(os.path.basename(file_path))
    subprocess.run(
        [
            "/usr/bin/libreoffice",
            "--headless",
            "--convert-to",
            target,
            "--outdir",
            save_path,
            file_path,
        ]
    )
    new_file_path = f"{save_path}/{basename}.{target}"
    if not os.path.isfile(new_file_path):
        raise RuntimeError(f"`{basename}{suffix}` 转 `{basename}.{target}` 失败")

    if suffix.lower == ".pptx" and target == "pdf":
        ppt: presentation.Presentation = pptx.Presentation(file_path)
        if len(ppt.slides) != get_pdf_page(new_file_path):
            raise RuntimeError(f"`{basename}{suffix}` 转 `{basename}.{target}` 出现页面丢失")

    return new_file_path


def pdf_convert_png(pdf_path: str, save_path: str) -> List[str]:
    pngs: List[str] = []
    basename, _ = os.path.splitext(os.path.basename(pdf_path))
    for i, image in enumerate(convert_from_path(pdf_path)):
        page = f"0{i+1}" if i < 9 else str(i + 1)
        png = f"{save_path}/{basename}_{page}.png"
        image.save(png, "PNG")
        pngs.append(png)

    return pngs


def convert_with_libreoffice(
    file_path: str, target: str = "png", save_path: str = None
) -> Union[str, List[str]]:
    """
    将不同类型的文档转换成指定类型的文档

    Args:
        file_path: 文件路径
        target: 目标文件类型
        dirname: 文件夹路径
    """
    save_path = save_path if save_path else os.path.dirname(file_path)
    basename, suffix = os.path.splitext(os.path.basename(file_path))
    os.makedirs(save_path, exist_ok=True)

    # doc -> docx
    if suffix.lower() == ".doc" and target == "docx":
        return file_convert(file_path, target, save_path)

    # (pptx, docx, doc) -> pdf
    elif suffix.lower() in (".pptx", ".docx", ".doc") and target == "pdf":
        return file_convert(file_path, "pdf", save_path)

    # (pptx, docx, pdf ,doc) -> png
    elif suffix.lower() in (".pptx", ".docx", ".pdf", ".doc") and target == "png":
        pdf_path = (
            file_path
            if suffix.lower() == ".pdf"
            else file_convert(file_path, "pdf", save_path)
        )
        return pdf_convert_png(pdf_path, save_path)

    else:
        raise RuntimeError(f"`{basename}{suffix}` 转 `{basename}.{target}` 暂未实现")


# # 应用信息
# def _wps4_sig(method, url, date, body):
#     if body is None:
#         bodySha = ""
#     else:
#         bodySha = hashlib.sha256(body.encode("utf-8")).hexdigest()

#     content = "WPS-4" + method + url + "application/json" + date + bodySha
#     signature = hmac.new(secret_key.encode("utf-8"), content.encode("utf-8"), hashlib.sha256).hexdigest()

#     return "WPS-4 %s:%s" % (access_key, signature)


# def _wps4_request(method, host, uri, body=None, cookie=None, headers=None):
#     requests.packages.urllib3.disable_warnings()

#     if body is not None:
#         body = json.dumps(body, ensure_ascii=False)

#     date = time.strftime("%a, %d %b %Y %H:%M:%S GMT", time.gmtime())
#     # date = "Wed, 02 Jun 2021 12:15:40 GMT"

#     # print date
#     header = {"Content-type": "application/json"}
#     header["Wps-Docs-Date"] = date
#     header["Wps-Docs-Authorization"] = _wps4_sig(method, uri, date, body)

#     if headers != None:
#         # header = {}
#         for key, value in headers.items():
#             header[key] = value

#     url = "%s%s" % (host, uri)
#     response = requests.request(method, url, data=body, headers=header, cookies=cookie, verify=False)

#     return response


# def get_down_info(doc_name, doc_url, target_file_format) -> Tuple[str,str]:
#     method = "POST"
#     uri = "/api/cps/sync/v1/convert"
#     body = {
#         "task_id": "dipeak_convert_task",
#         "doc_filename": doc_name,
#         "doc_url": doc_url,
#         "target_file_format": target_file_format,
#     }

#     # 发送请求并获取响应
#     response = _wps4_request(method=method, host=host, uri=uri, body=body)
#     # 打印响应结果
#     if response.status_code == 200:
#         print(f"status_code: {response.status_code}, text: {response.text}")
#         text_json = json.loads(response.text)
#         return text_json.get("data", {}).get("download_id"), text_json.get("data", {}).get("route_key")
#     else:
#         print(f"Request failed with error: {response.text}")


# def download_file(download_id, route_key, file_path: str, target_file_format:str) -> List[str]:
#     print(f"download_id: {download_id}, route_key: {route_key}")
#     method = "GET"
#     uri = "/api/cps/v1/download/" + download_id
#     headers = {"Route-Key": route_key}

#     file_path_list: List[str] = []
#     dir_path,(file_base, _) = os.path.dirname(file_path),os.path.splitext(os.path.basename(file_path))
#     try:
#         # 发送请求获取转换后文件
#         response = _wps4_request(method=method, host=host, uri=uri, headers=headers)
#         response.raise_for_status()  # 检查请求是否成功

#         if target_file_format == "png":
#             # 使用内存中的字节流创建ZipFile对象
#             with zipfile.ZipFile(io.BytesIO(response.content)) as zip_file:
#                 # 遍历ZIP文件中的每个成员
#                 for zip_info in zip_file.infolist():
#                     # 格式化文件路径
#                     n_th_png = zip_info.filename.split("_")[-1]  # 获取第n张图片
#                     save_path = os.path.join(dir_path, f"{file_base}_{n_th_png}")
#                     file_path_list.append(save_path)
#                     with zip_file.open(zip_info) as source_file:
#                         with open(save_path, "wb") as target_file:
#                             with io.BufferedReader(source_file) as reader:
#                                 while chunk := reader.read(8192):
#                                     target_file.write(chunk)
#             print(f"转换后所有图片已保存到: {dir_path}")

#         elif target_file_format == "pdf":
#             save_path = os.path.join(dir_path, f"{file_base}.pdf")
#             file_path_list.append(save_path)
#             with open(save_path, "wb") as file:
#                 for chunk in response.iter_content(chunk_size=8192):
#                     if chunk:  # 检查块是否为空
#                         file.write(chunk)
#             print(f"转换后pdf已保存到: {dir_path}")

#         elif target_file_format == "docx":
#             save_path = os.path.join(dir_path, f"{file_base}.docx")
#             with open(save_path, "wb") as file:
#                 for chunk in response.iter_content(chunk_size=8192):
#                     if chunk:  # 检查块是否为空
#                         file.write(chunk)
#             print(f"转换后doc已保存到: {dir_path}")

#         elif target_file_format == "xlsx":
#             save_path = os.path.join(dir_path, f"{file_base}.xlsx")
#             with open(save_path, "wb") as file:
#                 for chunk in response.iter_content(chunk_size=8192):
#                     if chunk:  # 检查块是否为空
#                         file.write(chunk)
#             print(f"转换后xlsx已保存到: {dir_path}")

#         elif target_file_format == "pptx":
#             save_path = os.path.join(dir_path, f"{file_base}.pptx")
#             with open(save_path, "wb") as file:
#                 for chunk in response.iter_content(chunk_size=8192):
#                     if chunk:  # 检查块是否为空
#                         file.write(chunk)
#             print(f"转换后pptx已保存到: {dir_path}")
#         else:
#             print(f"wrong file type: {target_file_format}")

#     except Exception as e:
#         print(f"下载转换后文件失败: {e}")

#     return file_path_list


# def upload_file(file_path: str):
#     url = "http://**************:30025/upload"
#     files = {"file": open(file_path, "rb")}
#     response = requests.post(url, files=files)

#     return response


# def convert_with_wps(file_path: str, target_file_format: str):
#     # 文件上传
#     upload_file(file_path)
#     # 转换
#     basename = os.path.basename(file_path)
#     doc_url = f"http://**************:30025/download/{basename}"
#     download_id, route_key = get_down_info(basename, doc_url, target_file_format)
#     target_file_paths = download_file(download_id, route_key, file_path, target_file_format)


#     return target_file_paths


# # def convert_with_wps(
# #     file_path: str, target: Literal["pdf", "png"]
# # ) -> Tuple[str, List[str]]:
# #     """
# #     将不同类型的文档转换成指定类型的文档

# #     Args:
# #         file_path: 文件路径
# #         target: 目标文件类型
# #     """
# #     if target == "pdf":
# #         return wps_convert_api.wps_converter(file_path, "pdf")
# #     elif target == "png":
# #         return wps_convert_api.wps_converter(file_path, "png")
# #     else:
# #         raise RuntimeError(f"`{target}`不在转换范围内")


# if __name__ == '__main__':
#     convert_with_wps(
#         file_path='/home/<USER>/Code/文档解析/测试集/debug/【保险】【金融云】【行业报告】金融云市场分析-方正证券.pdf',
#         target_file_format='ofd'
#     )

# if __name__ == "__main__":
#     root_path = "/home/<USER>/Code/文档解析"
#     for file_path in tqdm(glob(f"{root_path}/**.doc")):
#         convert_with_libreoffice(
#             file_path=file_path,
#             target="docx",
#         )
