"""Simple node parser."""
import os.path
import re
from typing import List, Optional

from llama_index.core.callbacks import CallbackManager
from llama_index.core.constants import DEFAULT_CHUNK_SIZE, DEFAULT_CHUNK_OVERLAP

from llama_index.core.schema import (
    BaseNode,
    Document,
    NodeRelationship,
    TextNode,
    MetadataMode,
)
from llama_index.core.node_parser import (
    SimpleNodeParser,
    TextSplitter,
    SentenceSplitter,
)

from nl2document.common.base.const import (
    POSITION_META_KEY,
    QA_NODE_KEY,
    LLAMA_INDEX_PAGE_LABLE,
    LLAMA_INDEX_FILE_NAME_FROM_JSON,
    QA_ANSWER_KEY,
    CHUNK_TYPE_KEY,
    IMAGE_PATHS_KEY,
)


def set_nodes_metadata(nodes: List[BaseNode], key: str, value: str):
    for node in nodes:
        node.metadata[key] = value


def get_default_text_splitter(
    chunk_size: Optional[int] = None,
    chunk_overlap: Optional[int] = None,
    callback_manager: Optional[CallbackManager] = None,
) -> TextSplitter:
    """Get default text splitter."""
    chunk_size = chunk_size or DEFAULT_CHUNK_SIZE
    chunk_overlap = (
        chunk_overlap if chunk_overlap is not None else DEFAULT_CHUNK_OVERLAP
    )

    return SentenceSplitter(
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
        callback_manager=callback_manager,
    )


class ExtentNodeParser(SimpleNodeParser):
    """Extent node parser.

    Splits a json / document into Nodes using a TextSplitter.

    Args:
        text_splitter (Optional[TextSplitter]): text splitter
        include_metadata (bool): whether to include metadata in nodes
        include_prev_next_rel (bool): whether to include prev/next relationships

    """

    def __init__(
        self,
        text_splitter: Optional[TextSplitter] = None,
        include_metadata: bool = True,
        include_prev_next_rel: bool = True,
        callback_manager: Optional[CallbackManager] = None,
        # metadata_extractor: Optional[MetadataExtractor] = None,
    ) -> None:
        """Init params."""
        text_splitter = text_splitter or get_default_text_splitter(
            callback_manager=callback_manager,
        )
        params = {
            "text_splitter": text_splitter,
            "include_metadata": include_metadata,
            "include_prev_next_rel": include_prev_next_rel,
            # "metadata_extractor": metadata_extractor,
        }
        if callback_manager:
            params["callback_manager"] = callback_manager
        super().__init__(**params)

    def get_nodes_from_json(
        self,
        json_str: str,
        extra_info=None,
        show_progress: bool = False,
    ):
        """Parse json into nodes.

        Args:
            json_str (str): json str to parse
        """
        file_name = json_str.get("file_name")
        author = json_str.get("author")
        create_time = json_str.get("create_time")
        pages = json_str.get("pages")
        tables = json_str.get("tables")
        images = json_str.get("images")
        outline = json_str.get("outline")
        type = json_str.get("type")
        title_nodes = []
        content_nodes = []
        table_nodes = []

        if json_str.get("file_type") == "xlsx":
            title_nodes, all_nodes = self.deal_excel_document(
                outline,
                json_str.get("file_name"),
            )
        elif outline:
            title_nodes, all_nodes, table_nodes = self.deal_outlines(
                outline=outline,
                file_name=file_name,
            )
        else:
            all_nodes = self.deal_pages(pages, file_name)

        for n in all_nodes:
            if not n.child_nodes:
                content_nodes.append(n)

        if self.metadata_extractor:
            self.metadata_extractor.process_nodes(content_nodes)
            self.metadata_extractor.process_nodes(table_nodes)

        if extra_info is not None:
            for node in all_nodes:
                node.metadata.update(extra_info)

        if type == "qa":
            for node in all_nodes:
                node.metadata.update({QA_NODE_KEY: ""})

        return title_nodes, all_nodes

    def deal_pages(self, pages: str, file_name: str):
        if not pages or len(pages) == 0:
            return []
        docs = []
        for page in pages:
            text = page.get("text", "")
            page_num = page.get("page_num")
            metadata = {
                LLAMA_INDEX_PAGE_LABLE: page_num,
                LLAMA_INDEX_FILE_NAME_FROM_JSON: file_name,
            }
            if len(text.strip()) > 0:
                docs.append(Document(text=text, metadata=metadata))
        return self.get_nodes_from_documents(documents=docs)

    def deal_images(self, images, metadata, parent_node=None):
        image_nodes = []
        if not images or len(images) == 0:
            return image_nodes
        metadata.update({CHUNK_TYPE_KEY: "image"})
        for i, image in enumerate(images):
            image_path = image.get("path", "")
            metadata.update({IMAGE_PATHS_KEY: [image_path]})
            split_image_nodes = self.get_nodes_from_document(
                document=Document(metadata=metadata, text=image["text"]),
                text_splitter=self.text_splitter,
                include_metadata=self.include_metadata,
                include_prev_next_rel=self.include_prev_next_rel,
                id_prefix="p_" + str(image.get("id", "")),
            )
            set_nodes_metadata(
                split_image_nodes, POSITION_META_KEY, "p_" + str(image.get("id", ""))
            )
            image_nodes.extend(split_image_nodes)
        if parent_node:
            for node in image_nodes:
                node.relationships[
                    NodeRelationship.PARENT
                ] = parent_node.as_related_node_info()
            self.add_child_relationship(parent_node, image_nodes)
        return image_nodes

    def deal_excel_chunk(self, table_data: str, row_data: str) -> str:
        rows_data = table_data[1:-1]
        if rows_data.strip() == "":
            return "[" + row_data + "]"
        else:
            return "[" + rows_data + "," + row_data + "]"

    def deal_excel_chunk_add_title_rows(self, sheet_texts, title_rows_num):
        title_rows = "[]"
        i = 1
        for text in sheet_texts:
            if i <= title_rows_num:
                cols = [('"' + str(i) + '"') if i else '""' for i in text.get("text")]
                row_data = "[" + ",".join(cols) + "]"
                title_rows = self.deal_excel_chunk(
                    table_data=title_rows, row_data=row_data
                )
                i += 1
        return title_rows

    def get_excel_meta_data_str(self, metadata):
        meta_data_str = ""
        i = 0
        for key, value in metadata.items():
            if i > 0:
                meta_data_str += "\n"
            key_value_str = str(key) + ":" + str(value)
            meta_data_str += key_value_str
            i += 1
        return meta_data_str

    def deal_excel_document(self, outline, file_name):
        title_nodes = []
        all_nodes = []
        metadata = {
            CHUNK_TYPE_KEY: "table",
            LLAMA_INDEX_FILE_NAME_FROM_JSON: file_name,
        }
        metadata_str = self.get_excel_meta_data_str(metadata)

        for sheet_index, excel_sheet in enumerate(outline):
            sheet_content_nodes = []
            split_title_nodes = []
            sheet_texts = excel_sheet.get("text")
            sheet_name = excel_sheet.get("sheet_name")
            if sheet_name != "":
                split_title_nodes = self.get_nodes_from_document(
                    document=Document(metadata=metadata, text=sheet_name),
                    text_splitter=self.text_splitter,
                    include_metadata=self.include_metadata,
                    id_prefix="sheet_title_" + str(sheet_index + 1),
                    include_prev_next_rel=self.include_prev_next_rel,
                )
                set_nodes_metadata(
                    split_title_nodes,
                    POSITION_META_KEY,
                    "sheet_title_" + str(sheet_index + 1),
                )
            title_rows_num = 2  # 用几行作为标题栏
            title_rows = self.deal_excel_chunk_add_title_rows(
                sheet_texts=sheet_texts, title_rows_num=title_rows_num
            )
            table_data = title_rows
            for row_index, text in enumerate(sheet_texts):
                if row_index < title_rows_num:
                    continue
                cols = [
                    i if i and str(i).startswith("images/") else i
                    for i in text.get("text")
                ]
                cols = [('"' + str(i) + '"') if i else '""' for i in cols]
                row_data = "[" + ",".join(cols) + "]"
                # 判断token长度
                length = len(
                    self.text_splitter.tokenizer(
                        self.deal_excel_chunk(
                            table_data=(metadata_str + table_data), row_data=row_data
                        )
                    )
                )
                if length >= self.text_splitter.chunk_size:
                    split_excel_nodes = self.get_nodes_from_document(
                        document=Document(metadata=metadata, text=table_data),
                        text_splitter=self.text_splitter,
                        include_metadata=self.include_metadata,
                        id_prefix="sheet_" + str(sheet_index + 1),
                        include_prev_next_rel=self.include_prev_next_rel,
                    )
                    set_nodes_metadata(
                        split_excel_nodes,
                        POSITION_META_KEY,
                        "sheet_" + str(sheet_index + 1),
                    )
                    sheet_content_nodes.extend(split_excel_nodes)
                    table_data = self.deal_excel_chunk(
                        table_data=title_rows, row_data=row_data
                    )
                else:
                    if row_data.strip() != "[]":
                        table_data = self.deal_excel_chunk(
                            table_data=table_data, row_data=row_data
                        )

            split_excel_nodes = self.get_nodes_from_document(
                document=Document(metadata=metadata, text=table_data),
                text_splitter=self.text_splitter,
                include_metadata=self.include_metadata,
                id_prefix="sheet_" + str(sheet_index + 1),
                include_prev_next_rel=self.include_prev_next_rel,
            )
            set_nodes_metadata(
                split_excel_nodes, POSITION_META_KEY, "sheet_" + str(sheet_index + 1)
            )

            sheet_content_nodes.extend(split_excel_nodes)
            # 处理前后关系
            for node_num, sheet_content_node in enumerate(sheet_content_nodes):
                if node_num < (len(sheet_content_nodes) - 1):
                    sheet_content_nodes[node_num].relationships[
                        NodeRelationship.NEXT
                    ] = sheet_content_nodes[node_num + 1].as_related_node_info()
                if 0 < node_num < len(sheet_content_nodes):
                    sheet_content_nodes[node_num].relationships[
                        NodeRelationship.PREVIOUS
                    ] = sheet_content_nodes[node_num - 1].as_related_node_info()
            # 处理从属关系
            split_title_node_infos = []
            for split_title_node in split_title_nodes:
                sheet_content_node_infos = []
                split_title_node_infos.append(split_title_node.as_related_node_info())
                for sheet_content_node in sheet_content_nodes:
                    sheet_content_node_infos.append(
                        sheet_content_node.as_related_node_info()
                    )
                if sheet_content_node_infos:
                    split_title_node.relationships[NodeRelationship.CHILD] = (
                        sheet_content_node_infos
                        if (len(sheet_content_node_infos) > 1)
                        else sheet_content_node_infos[0]
                    )
            for sheet_content_node in sheet_content_nodes:
                if split_title_node_infos:
                    sheet_content_node.relationships[NodeRelationship.PARENT] = (
                        split_title_node_infos
                        if (len(split_title_node_infos) > 1)
                        else split_title_node_infos[0]
                    )

            title_nodes.extend(split_title_nodes)
            all_nodes.extend(split_title_nodes)
            all_nodes.extend(sheet_content_nodes)

        return title_nodes, all_nodes

    def deal_tables(self, tables, metadata, parent_node=None):
        table_nodes = []
        if not tables or len(tables) == 0:
            return table_nodes
        for i, table in enumerate(tables):
            table_data = ""
            for cols in table.get("text"):
                cols = [i if i else "" for i in cols]
                table_data += "\t".join(cols)
                table_data += "\n"
            metadata.update({CHUNK_TYPE_KEY: "table"})
            split_table_nodes = self.get_nodes_from_document(
                document=Document(metadata=metadata, text=table_data),
                text_splitter=self.text_splitter,
                include_metadata=self.include_metadata,
                id_prefix="t_" + str(table.get("id")),
                include_prev_next_rel=self.include_prev_next_rel,
            )
            set_nodes_metadata(
                split_table_nodes, POSITION_META_KEY, "t_" + str(table.get("id"))
            )
            table_nodes.extend(split_table_nodes)

        if parent_node:
            for node in table_nodes:
                node.relationships[
                    NodeRelationship.PARENT
                ] = parent_node.as_related_node_info()
            self.add_child_relationship(parent_node, table_nodes)
        return table_nodes

    def is_question(self, text):
        if re.match(r"^(q|Q|问题|问).+[?？]$", text):
            return True
        return False

    def is_answer(self, text):
        if re.match(r"^(A|a|答|【A1】【A2】).+", text):
            return True
        return False

    def deal_outlines(
        self,
        outline: str,
        parent_node=None,
        file_name: str = None,
        level="0",
    ):
        title_nodes = []
        all_nodes = []
        table_nodes = []
        if not outline and len(outline) == 0:
            return title_nodes, all_nodes, table_nodes

        q_flag = self.is_question(parent_node.text) if parent_node else False
        for i, item in enumerate(outline):
            id_prefix = "{}-{}".format(level, str(i + 1))
            # title : 1.xxx\n1.1xxx\n1.1.1xxx
            title = (
                item.get("title")
                if parent_node is None
                else parent_node.metadata.get("title") + "\n" + item.get("title")
            )
            title_metadata = {
                LLAMA_INDEX_PAGE_LABLE: item.get("page"),
                "title": title,
                LLAMA_INDEX_FILE_NAME_FROM_JSON: file_name,
            }
            split_title_nodes = (
                self.get_nodes_from_document(
                    document=Document(metadata=title_metadata, text=item.get("title")),
                    text_splitter=self.text_splitter,
                    include_metadata=self.include_metadata,
                    id_prefix=item.get("id"),
                    include_prev_next_rel=self.include_prev_next_rel,
                )
                if item.get("title")
                else []
            )
            set_nodes_metadata(split_title_nodes, POSITION_META_KEY, item.get("id", ""))

            head_title_node = split_title_nodes[0] if split_title_nodes else None
            tmp_image_nodes = self.deal_images(
                item.get("images"),
                metadata=dict(title_metadata),
                parent_node=head_title_node,
            )
            tmp_table_nodes = self.deal_tables(
                item.get("tables"),
                metadata=dict(title_metadata),
                parent_node=head_title_node,
            )
            if self.is_question(item.get("title", "")):
                head_title_node.metadata[QA_ANSWER_KEY] = [[]]
            paragraph_nodes: List[TextNode] = []
            if item.get("text"):
                texts = item.get("text")
                if q_flag:
                    texts = self.deal_answer(
                        texts, title, all_nodes, split_title_nodes, parent_node
                    )

                split_paragraph_nodes = self.get_nodes_from_merge_paragraphs(
                    texts, title
                )
                paragraph_nodes.extend(split_paragraph_nodes)
                self.add_parent_next_prev_relationship(
                    head_title_node, split_paragraph_nodes
                )
                for title_node in split_title_nodes:
                    self.add_child_relationship(title_node, paragraph_nodes)
                    if parent_node:
                        title_node.relationships[
                            NodeRelationship.PARENT
                        ] = parent_node.as_related_node_info()

                if self.is_question(item.get("title", "")) or item.get("title_type"):
                    if not head_title_node.metadata.__contains__(QA_ANSWER_KEY):
                        head_title_node.metadata[QA_ANSWER_KEY] = [[]]
                    for k, n in enumerate(split_paragraph_nodes):
                        head_title_node.metadata[QA_ANSWER_KEY][-1].append(n.id_)
                        if k == 0:
                            continue
                        if self.is_answer(n.text):
                            head_title_node.metadata[QA_ANSWER_KEY].append([n.id_])
            sub_outline = item.get("outline")
            if sub_outline:
                (
                    sub_title_nodes,
                    sub_all_nodes,
                    sub_table_nodes,
                ) = self.deal_outlines(
                    sub_outline,
                    parent_node=head_title_node,
                    file_name=file_name,
                    level=id_prefix,
                )
                all_nodes.extend(sub_all_nodes)
                table_nodes.extend(sub_table_nodes)
            if not parent_node:
                title_nodes.extend(split_title_nodes)
            if parent_node:
                self.add_child_relationship(parent_node, split_title_nodes)
            table_nodes.extend(tmp_table_nodes)
            all_nodes.extend(split_title_nodes)
            all_nodes.extend(paragraph_nodes)
            all_nodes.extend(tmp_table_nodes)
            all_nodes.extend(tmp_image_nodes)
        return title_nodes, all_nodes, table_nodes

    def get_node_by_id(self, id, nodes):
        if not nodes or not id:
            return None
        for node in nodes:
            if node.id_ == id:
                return node

    # 合并小段落，组成chunk
    def get_nodes_from_merge_paragraphs(self, paragraphs, title):
        all_split_nodes = []
        metadata_size = len(
            self.text_splitter.tokenizer(
                str(
                    {
                        LLAMA_INDEX_PAGE_LABLE: "",
                        "title": title,
                        "doc_ref": [],
                    }
                )
            )
        )
        merge_paragraphs = self.merge_paragraphs(paragraphs, metadata_size)
        for paragraph in merge_paragraphs:
            metadata = {
                LLAMA_INDEX_PAGE_LABLE: paragraph.get("page"),
                "title": title,
                "doc_ref": paragraph.get("doc_ref"),
            }
            split_paragraph_nodes = self.get_nodes_from_document(
                document=Document(
                    metadata=metadata,
                    text=paragraph.get("text"),
                ),
                text_splitter=self.text_splitter,
                include_metadata=self.include_metadata,
                id_prefix=paragraph.get("id"),
                include_prev_next_rel=self.include_prev_next_rel,
            )
            set_nodes_metadata(
                split_paragraph_nodes, POSITION_META_KEY, paragraph.get("id")
            )
            all_split_nodes.extend(split_paragraph_nodes)
        return all_split_nodes

    def get_nodes_from_document(
        self,
        document: BaseNode,
        text_splitter: TextSplitter,
        id_prefix="",
        include_metadata: bool = True,
        include_prev_next_rel: bool = True,
    ):
        # nodes = get_nodes_from_document(
        #     document=document,
        #     text_splitter=text_splitter,
        #     include_metadata=include_metadata,
        #     include_prev_next_rel=False,
        # )
        text_splitter.include_prev_next_rel = include_prev_next_rel
        text_splitter.include_metadata = include_metadata
        nodes = text_splitter.get_nodes_from_documents(document)
        # for node in nodes:
        #     node.id_ = str(id_prefix) + "#" + node.id_
        if include_prev_next_rel:
            for i, node in enumerate(nodes):
                if i > 0:
                    node.relationships[NodeRelationship.PREVIOUS] = nodes[
                        i - 1
                    ].as_related_node_info()
                if i < len(nodes) - 1:
                    node.relationships[NodeRelationship.NEXT] = nodes[
                        i + 1
                    ].as_related_node_info()
        return nodes

    def add_parent_next_prev_relationship(self, head_title_node, split_paragraph_nodes):
        for i, paragraph_node in enumerate(split_paragraph_nodes):
            if head_title_node:
                paragraph_node.relationships[
                    NodeRelationship.PARENT
                ] = head_title_node.as_related_node_info()
            if i > 0:
                paragraph_node.relationships[
                    NodeRelationship.PREVIOUS
                ] = split_paragraph_nodes[i - 1].as_related_node_info()
            if i < len(split_paragraph_nodes) - 1:
                paragraph_node.relationships[
                    NodeRelationship.NEXT
                ] = split_paragraph_nodes[i + 1].as_related_node_info()

    def add_child_relationship(
        self, source_node: BaseNode, child_nodes: List[TextNode]
    ):
        if source_node is None:
            return
        childs = source_node.relationships.get(NodeRelationship.CHILD)
        child_node_infos = [
            child_node.as_related_node_info() for child_node in child_nodes
        ]

        if childs is None:
            source_node.relationships[NodeRelationship.CHILD] = child_node_infos
        else:
            source_node.relationships.get(NodeRelationship.CHILD).extend(
                child_node_infos
            )

    def deal_answer(
        self, paragraphs: List, title, all_nodes, titile_nodes, parent_node=None
    ):
        new_paragraphs = []
        anser_paragraphs = []
        for i, paragraph in enumerate(paragraphs):
            if re.match(r"^(A|a|答|【A1】【A2】).+", paragraph.get("text")):
                anser_paragraphs.extend(paragraphs[i:])
                break
            else:
                new_paragraphs.append(paragraph)
        metadata_size = len(
            self.text_splitter.tokenizer(
                str(
                    {
                        LLAMA_INDEX_PAGE_LABLE: paragraph.get("page"),
                        "title": title,
                        "doc_ref": [],
                    }
                )
            )
        )
        anser_paragraphs = self.merge_paragraphs(anser_paragraphs, metadata_size)
        answer_node = []
        for paragraph in anser_paragraphs:
            metadata = {
                LLAMA_INDEX_PAGE_LABLE: paragraph.get("page"),
                "title": title,
                "doc_ref": paragraph.get("doc_ref"),
            }
            split_answer_nodes = self.get_nodes_from_document(
                document=Document(
                    metadata=metadata,
                    text=paragraph.get("text"),
                ),
                text_splitter=self.text_splitter,
                include_metadata=self.include_metadata,
                id_prefix=paragraph.get("id"),
            )
            set_nodes_metadata(
                split_answer_nodes, POSITION_META_KEY, paragraph.get("id")
            )
            answer_node.extend(split_answer_nodes)
        parent_node.metadata[QA_ANSWER_KEY][-1].extend(
            [i.id_ for i in titile_nodes if i.text.find("说明") == -1]
        )

        if answer_node:
            self.add_child_relationship(parent_node, answer_node)
            parent_node.metadata[QA_ANSWER_KEY].append([i.id_ for i in answer_node])

        all_nodes.extend(answer_node)
        return new_paragraphs

    def merge_paragraphs(self, paragraphs: List, metadata_size=0):
        res = []
        cur_size = metadata_size + 5
        cur_paragraph = {"text": "", "page": -1, "id": [], "doc_ref": []}

        def merge_consecutive_numbers(numbers):
            numbers = [x for x in numbers if isinstance(x, int)]
            if not numbers:
                return ""
            merged_numbers = []
            start = numbers[0]
            end = numbers[0]

            for i in range(1, len(numbers)):
                if numbers[i] == end + 1:
                    end = numbers[i]
                else:
                    merged_numbers.append(
                        str(start) if start == end else f"{start}_{end}"
                    )
                    start = numbers[i]
                    end = numbers[i]
            if start == end:
                merged_numbers.append(str(start))
            else:
                merged_numbers.append(f"{start}_{end}")

            return "#".join(merged_numbers)

        for i, paragraph in enumerate(paragraphs):
            if len(paragraph.get("text")) == 0:
                continue
            length = len(self.text_splitter.tokenizer(paragraph.get("text"))) + len(
                self.text_splitter.tokenizer(str(paragraph.get("doc_ref", "")))
            )
            cur_page = cur_paragraph.get("page")
            if cur_size + length >= self.text_splitter.chunk_size or (
                cur_page != -1 and cur_page != paragraph.get("page")
            ):
                cur_paragraph["id"] = merge_consecutive_numbers(cur_paragraph.get("id"))
                res.append(cur_paragraph)
                res.append(paragraph)
                cur_size = 0
                cur_paragraph = {"text": "", "page": -1, "id": [], "doc_ref": []}
            else:
                cur_paragraph["text"] = cur_paragraph["text"] + paragraph.get("text")
                cur_paragraph["page"] = paragraph.get("page")
                cur_paragraph["id"].append(paragraph.get("id"))
                cur_paragraph["doc_ref"].extend(paragraph.get("doc_ref", []))
                cur_size += length
        if cur_size != 0:
            cur_paragraph["id"] = merge_consecutive_numbers(cur_paragraph.get("id"))
            res.append(cur_paragraph)
        return res
