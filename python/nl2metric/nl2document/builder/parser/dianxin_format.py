import json
import os.path
import re
from typing import Dict


def get_dianxin_format_json(parsed_document_nodes_json: Dict) -> Dict:
    parsed_document_dianxin_json = {}
    first_node = parsed_document_nodes_json[0]
    first_node_metadata = get_node_metadata(first_node["metadata"])
    file_name = first_node_metadata["file_name"]
    parsed_document_dianxin_json["file_name"] = file_name
    file_type = file_name.split(".")[-1].lower()
    parsed_document_dianxin_json["file_type"] = file_type
    parsed_document_dianxin_json["file_title"] = get_file_title(
        first_node_metadata["part_name"]
    )

    document_parts = []
    for parsed_document_node in parsed_document_nodes_json:
        node_content = parsed_document_node["content"]
        node_metadata = get_node_metadata(parsed_document_node["metadata"])
        node_part_name = node_metadata["part_name"]
        part_titles = get_part_titles(node_part_name)
        node_content_type = node_metadata["content_type"]
        node_page = node_metadata["page_label"]
        deal_document_parts(document_parts, node_content, part_titles, node_page, 0)

    parsed_document_dianxin_json["parts"] = document_parts
    return parsed_document_dianxin_json


def deal_document_parts(
    document_parts: [],
    node_content: str,
    part_titles: [],
    node_page: int,
    part_level: int,
):
    now_title = ""
    if len(part_titles) > 0:
        now_title = part_titles[0]
    is_title_already = False
    for document_part in document_parts:
        if document_part["part_name"] == now_title:
            is_title_already = True
            if len(part_titles) > 1:
                next_part_titles = list(part_titles)
                next_part_titles.pop(0)
                deal_document_parts(
                    document_part["parts"],
                    node_content,
                    next_part_titles,
                    node_page,
                    part_level + 1,
                )
                return
            else:
                document_part["contents"].append(
                    {
                        "text": node_content,
                        "id": len(document_part["contents"]) + 1,
                        "page": node_page,
                    }
                )
                return
    if not is_title_already:
        document_parts.append(
            {
                "part_name": now_title,
                "contents": [],
                "part_level": part_level,
                "part_sort": len(document_parts) + 1,
                "parts": [],
            }
        )
        if len(part_titles) > 1:
            next_part_titles = list(part_titles)
            next_part_titles.pop(0)
            deal_document_parts(
                document_parts[-1]["parts"],
                node_content,
                next_part_titles,
                node_page,
                part_level + 1,
            )
            return
        else:
            document_parts[-1]["contents"].append(
                {
                    "text": node_content,
                    "id": len(document_parts[-1]["contents"]) + 1,
                    "page": node_page,
                }
            )
            return


def get_part_titles(part_name: str) -> []:
    if not part_name:
        return []
    titles = part_name.split("_")
    return titles


def get_file_title(first_node_partname: str) -> str:
    file_title = (
        first_node_partname.split("_")[0]
        if len(first_node_partname.split("_")) > 0
        else ""
    )
    if file_title:
        if (
            re.match(r"^(\d+|[一二三四五六七八九十]{1,3}[、.])", file_title)
            or file_title == "目录"
            or re.findall(r"[。，,:：]", file_title)
        ):
            return ""
        else:
            return file_title
    return ""


def get_node_metadata(metadata_str: str) -> Dict:
    metadata = {}
    lines = metadata_str.split("\n")
    for line in lines:
        key, value = line.split(": ")
        # 将解析出的键和值添加到字典中
        metadata[key] = value
    return metadata


def save_result(file_path: str, parsed_document_dianxin_json: Dict):
    file_name = os.path.basename(file_path)
    file_dir = os.path.dirname(file_path)
    json_file_path = os.path.join(file_dir, "dianxin_format" + file_name)
    with open(json_file_path, "w", encoding="utf-8") as file:
        json.dump(parsed_document_dianxin_json, file, indent=4, ensure_ascii=False)


if __name__ == "__main__":
    file_path = "/Users/<USER>/Downloads/83483_疏附县政务一体化平台新建项目案例介绍.docx.nodes.json"
    with open(file_path, "r") as f:
        nodes_json_datas = json.load(f)
    parsed_document_dianxin_json = get_dianxin_format_json(nodes_json_datas)
    save_result(file_path, parsed_document_dianxin_json)
