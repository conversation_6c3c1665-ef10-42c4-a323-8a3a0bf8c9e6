import math
from collections import Counter
import os
import sys

import jieba


class RougeMetric:
    """
    A class to compute ROUGE-N score for text evaluation.

    Args:
        ngram_weights (list): A list of weights for each n-gram recall. Default is [1.0].
        eps (float): A small value added to the numerator and denominator to avoid zero division errors.
    """

    def __init__(self, ngram_weights: list = None, verbose: bool = False):
        if not ngram_weights:
            self.ngram_weights = [0.25, 0.25, 0.25, 0.25]
        else:
            self.ngram_weights = ngram_weights
        self.N = len(self.ngram_weights)
        self.verbose = verbose
        self.eps = 1e-3

    def _split_word_ch(self, text):
        add_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        sys.path.append(add_path)
        try:
            stopwords_file = "stopwords.txt"
            with open(stopwords_file, "r") as words:
                stopwords = [i.strip() for i in words]
        except:
            stopwords = ["，", ",", "。", "."]
        cut_list = " ".join(jieba.cut(text)).split(" ")
        return [i for i in cut_list if not i in stopwords]

    def _ngram_counts(self, sentence, n):
        """
        Compute the n-gram counts for a given sentence.

        Args:
            sentence (list): A list of tokens representing the sentence.
            n (int): The order of n-gram.

        Returns:
            Counter: A Counter object containing the n-gram counts.
        """
        return Counter(zip(*[sentence[i:] for i in range(n)]))

    def _recall(self, candidate_counts, reference_counts):
        if sum(reference_counts.values()) == 0:
            return 0
        clip_counts = dict()
        for ngram, count in reference_counts.items():
            cancidate = candidate_counts.get(ngram, 0)
            clip_counts[ngram] = min(count, cancidate)
        return sum(clip_counts.values()) / sum(reference_counts.values())

    def compute_rouge(self, candidate_counts, reference_counts):
        weights = [w / sum(self.ngram_weights) for w in self.ngram_weights]
        r = [0] * len(weights)
        for i in range(len(weights)):
            r[i] = max(self._recall(candidate_counts[i], reference_counts[i]), self.eps)
        log_r = sum(w * math.log(r[i]) for i, w in enumerate(weights))
        if self.verbose:
            print("Raw n-gram recall:", r)
        return math.exp(log_r)

    def compute_score(self, candidate, reference):
        """
        Compute the ROUGE score for a given candidate and reference sentences.

        Args:
            candidate (string): A string of the candidate sentence.
            references (list): A list of reference sentences.

        Returns:
            float: The ROUGE score.
        """
        # convert string to tokens
        candidate_tokens = self._split_word_ch(candidate)
        reference_tokens = self._split_word_ch(reference)

        candidate_counts = [
            self._ngram_counts(candidate_tokens, n) for n in range(1, self.N + 1)
        ]
        reference_counts_list = [
            self._ngram_counts(reference_tokens, n) for n in range(1, self.N + 1)
        ]

        return self.compute_rouge(candidate_counts, reference_counts_list)
