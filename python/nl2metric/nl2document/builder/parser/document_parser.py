#! coding: utf-8
import json
import os
from pathlib import Path
from typing import Dict, List, Optional, Tuple

from llama_index.core.node_parser import NodeParser

# from llama_index.readers.file import PDFReader

from nl2document.common.base.ask_doc_utils import get_md5
from common.logging.logger import get_logger, get_elk_logger
from nl2document.common.models.model import CommonDocumentModel
from nl2document.builder.parser.excel_parser import ExcelParser
from nl2document.builder.parser.qa_parse import parse_qa_document
from llama_index.core.schema import Document
from llama_index.core.schema import BaseNode
from pydantic import BaseModel
from nl2document.builder.data_process.file_parser import FileParser
from nl2document.builder.yidong_data_process.doc_parser import DocParser
from nl2document.builder.yidong_data_process.txt_parser import TxtParser
from nl2document.builder.yidong_data_process.picture_parser import PictureParser
from nl2document.builder.yidong_data_process.meeting_pdf_parser import MeetingPdfParser
from nl2document.builder.parser.node_parser import GeneralNodeParser
from nl2document.builder.yidong_data_process.yidong_node_parser import YiDongNodeParser
from config.app_config import general_parser_chunk_size, general_parser_chunk_overlap
from common.trace.perfomance_monitor import performance_context

logger = get_logger(__name__)
elk_logger = get_elk_logger()

parsed_docs_dir = os.path.join(
    os.path.dirname(os.path.dirname(__file__)), "parsed_docs"
)


class DocumentURL(BaseModel):
    name: Optional[str] = None
    url: str


class NodeResult:
    def __init__(
        self,
        nodes: Optional[List[BaseNode]] = None,
        root_nodes: Optional[List[BaseNode]] = None,
        image_dir: Optional[str] = None,
    ):
        self.nodes: Optional[List[BaseNode]] = nodes
        self.root_nodes: Optional[List[BaseNode]] = root_nodes
        self.image_dir: Optional[str] = image_dir


class DocumentParser:
    def __init__(
        self,
        general_parser_chunk_size_: int = general_parser_chunk_size,
        general_parser_chunk_overlap_: int = general_parser_chunk_overlap,
        general_parser_table_chunk_size_: int = 4096 * 6,
        general_parser_table_chunk_overlap_: int = 100,
    ):
        self._yidong_nodes_parser = YiDongNodeParser()
        self._general_nodes_parser = GeneralNodeParser(
            chunk_sort_id=0,
            chunk_size=general_parser_chunk_size_,
            chunk_overlap=general_parser_chunk_overlap_,
            table_chunk_size=general_parser_table_chunk_size_,
            table_chunk_overlap=general_parser_table_chunk_overlap_,
        )

    def _parse_document(
        self,
        source_path: str,
        file_type: str,
        platform: str,
        upload_type: str,
        file_id: str,
        language: str,
    ) -> Tuple[List[Document], Dict]:
        documents: List[Document] = None
        documents_json: Dict = None

        extra_info = {
            "input": {
                "source_path": source_path,
                "file_type": file_type,
                "platform": platform,
                "upload_type": upload_type,
                "file_id": file_id,
            }
        }

        if os.path.isfile(Path(source_path)):
            path = Path(source_path)
            file_md5 = get_md5(path)
            logger.info(
                f"document info: {file_md5}, {path}, {upload_type}, {file_type}"
            )
            file_name = path.name
            log_msg = "Add local document, document {}, md5 {}.".format(
                file_name, file_md5
            )
            file_suffix = file_name.split(".")[-1].lower()

            if (
                file_suffix in ["xlsx", "xls", "xlsm", "xltx"]
                and upload_type != "meeting"
            ):
                logger.info(
                    msg="{} Load data type: ExcelParser.".format(log_msg),
                    extra=extra_info,
                )
                excel_parser = ExcelParser()
                documents_json = excel_parser.parse(path)
            elif (
                file_suffix
                in [
                    "html",
                    "docx",
                    "doc",
                    "pdf",
                    "pptx",
                    "ppt",
                ]
                and upload_type != "meeting"
            ):
                logger.info(
                    msg="{} Load data type: FileParser.".format(log_msg),
                    extra=extra_info,
                )
                documents_json = FileParser().parse(
                    str(path), file_type, language, file_id
                )
                logger.info(msg="Document parse to json result over.", extra=extra_info)
            elif file_suffix in ["docx"] and upload_type == "meeting":  # 移动会议docx
                logger.info(
                    msg="{} Load data type: Meeting DocParser.".format(log_msg),
                    extra=extra_info,
                )
                documents_json = DocParser().parse(str(path), platform)
                logger.info(
                    msg="Meeting docx document parse to json result over.",
                    extra=extra_info,
                )
            elif file_suffix in ["txt"] and upload_type == "meeting":  # 移动会议txt
                logger.info(
                    msg="{} Load data type: Meeting TxtParser.".format(log_msg),
                    extra=extra_info,
                )
                documents_json = TxtParser().parse(str(path), platform)
                logger.info(
                    msg="Meeting txt document parse to json result over.",
                    extra=extra_info,
                )
            elif file_suffix in ["pdf"] and upload_type == "meeting":  # 白板会议图片pdf
                logger.info(
                    msg="{} Load data type: Meeting Pdf Parser.".format(log_msg),
                    extra=extra_info,
                )
                documents_json = MeetingPdfParser().parse(str(path), None, file_id)
                logger.info(
                    msg="Meeting Pdf document parse to json result over.",
                    extra=extra_info,
                )
            elif file_suffix in ["json"]:
                with open(path) as f:
                    documents_json = json.load(f)
            else:
                logger.info(
                    msg="{} Load data type: PDFReader.".format(log_msg),
                    extra=extra_info,
                )
                # reader = PDFReader()
                # documents = reader.load_data(path)
        elif (
            file_type.lower() in ["png", "jpeg", "jpg"]
            and upload_type == "meeting"
            and source_path
        ):  # 移动会议图片
            # eg : path = "https://ysx-dev-cloud-record.eos-guangzhou-1.cmecloud.cn/14249216955457372168/1234567890
            # /d29416e706a948499c8175385d881d94?AWSAccessKeyId=6AT6QCAV794I87E2MW48&Expires=1723085468&response
            # -content-disposition=attachment%3B%20filename%3D1280X1280.png&Signature=58aWkxRk2AlPswf4Po4rjSTOYOk%3D"
            logger.info(msg="Load data type: Meeting PictureParser.", extra=extra_info)
            documents_json = PictureParser().parse(source_path)
            logger.info(
                msg="Meeting Picture parse to json result over.", extra=extra_info
            )
        else:
            logger.exception(
                msg="{} is not a file or image http url".format(source_path),
                extra=extra_info,
            )
            elk_logger.info(
                msg="{} is not a file or image http url".format(source_path),
                extra=extra_info,
            )
            raise RuntimeError("{} is not a file or image http url".format(source_path))
        return documents, documents_json

    def parse(self, path: str, document_model: CommonDocumentModel) -> NodeResult:
        if document_model.file_type == "QA":
            document_json = parse_qa_document(Path(path))
            root_nodes, nodes = self._general_nodes_parser.get_nodes_from_json(
                document_json, document_model.id, ""
            )
            return NodeResult(nodes=nodes, root_nodes=root_nodes)
        else:
            with performance_context("parse document"):
                documents, document_json = self._parse_document(
                    path,
                    document_model.file_type,
                    document_model.platform,
                    document_model.upload_type,
                    document_model.id,
                    "chinese",
                )

            extra_info = {
                "input": {
                    "source_path": path,
                    "file_type": document_model.file_type,
                    "platform": document_model.platform,
                    "upload_type": document_model.upload_type,
                    "file_id": document_model.id,
                }
            }

            if documents:
                for index_tmp, document_tmp in enumerate(documents):
                    document_tmp.id_ = "{}_{}".format(Path(path).name, index_tmp)
                nodes = NodeParser().get_nodes_from_documents(documents)
                root_nodes = None
                image_dir = None
            else:
                if not document_json:
                    logger.exception(
                        msg="Document parsed json is None", extra=extra_info
                    )
                    elk_logger.info(
                        msg="Document parsed json is None", extra=extra_info
                    )
                assert document_json is not None
                logger.info(
                    msg="Start split json to nodes for document.", extra=extra_info
                )
                # image_dir = document_json.get("image_path")
                # if image_dir:
                #     image_dir = os.path.join(
                #         os.path.join(os.path.dirname(os.path.dirname(__file__)), image_dir),
                #         "*",
                #     )
                #     get_s3_file_system().upload(
                #         image_dir, os.path.join(resource_dir, "images")
                #     )
                if document_model.upload_type == "meeting":
                    root_nodes, nodes = self._yidong_nodes_parser.get_nodes_from_json(
                        document_json, document_model.id, ""
                    )
                else:
                    with performance_context("get nodes from json"):
                        (
                            root_nodes,
                            nodes,
                        ) = self._general_nodes_parser.get_nodes_from_json(
                            document_json, document_model.id, ""
                        )
            if len(nodes) == 0:
                logger.error(
                    msg=f"Add local document failed, nodes is empty, document {path}.",
                    extra=extra_info,
                )
                elk_logger.info(msg="Nodes is empty.", extra=extra_info)
                raise RuntimeError("must input nodes")
            # for node in nodes:
            #   node.excluded_llm_metadata_keys = EXCLUDED_LLM_METADATA_KEYS
            #  node.excluded_embed_metadata_keys = EXCLUDED_EMBED_METADATA_KEYS
            result = NodeResult(nodes=nodes, root_nodes=root_nodes)
            logger.info(msg=f"Parse document json to nodes over.", extra=extra_info)
            return result
