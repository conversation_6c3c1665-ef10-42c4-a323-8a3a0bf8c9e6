from typing import Dict, <PERSON><PERSON>, List


from llama_index.core.schema import (
    BaseNode,
    NodeRelationship,
    TextNode,
)

from common.logging.logger import get_logger

from common.base.const import (
    LLAMA_INDEX_PAGE_LABLE,
    LLAMA_INDEX_FILE_NAME,
    LLAMA_INDEX_FILE_ID,
    LLAMA_INDEX_FOLDER_ID,
    LLAMA_INDEX_FILE_NAME_FROM_JSON,
    LLAMA_INDEX_META_PART_NAME,
)

logger = get_logger(__name__)


class DianXingNodeParser:
    def get_nodes_from_json(
        self, json: Dict, file_id: int, folder_id: int
    ) -> Tuple[List[BaseNode], List[BaseNode]]:
        root_node = TextNode()
        root_node.text = json.get("file_title", "")
        root_node.relationships = {}
        root_node.metadata = {}
        root_node.metadata["file_name"] = json.get("file_name", "")
        root_node.metadata["file_title"] = json.get("file_title", "")
        root_node.metadata["file_type"] = json.get("file_type", "")
        root_node.metadata[LLAMA_INDEX_FILE_ID] = file_id
        root_node.metadata[LLAMA_INDEX_FOLDER_ID] = folder_id

        node_list = []
        self.traverse_parts(root_node, json.get("parts", []), node_list, [])
        return [root_node], node_list

    def traverse_parts(
        self, root_node: TextNode, parts: List, node_list: List, part_name_list: List
    ):
        for part in parts:
            part_name = part.get("part_name", None)
            assert part_name is not None
            tmp_part_name_list = list(part_name_list)
            tmp_part_name_list.append(part_name)
            part_name_str = ""
            for index, part_name in enumerate(tmp_part_name_list):
                if index != 0:
                    part_name_str += "_"
                part_name_str += part_name
            self.traverse_contents(
                root_node, part.get("contents", []), node_list, part_name_str
            )
            self.traverse_parts(
                root_node, part.get("parts", []), node_list, tmp_part_name_list
            )

    def traverse_contents(
        self, root_node: TextNode, contents: List, node_list: List, part_name: str
    ):
        content_nodes = []
        for content in contents:
            content_node = TextNode()
            content_node.text = content.get("text", "")
            content_node.relationships = {}

            # add meta
            content_node.metadata = {}
            content_node.metadata[LLAMA_INDEX_META_PART_NAME] = part_name
            content_node.metadata[LLAMA_INDEX_FILE_NAME] = root_node.metadata[
                "file_name"
            ]
            content_node.metadata[LLAMA_INDEX_FILE_ID] = str(
                root_node.metadata[LLAMA_INDEX_FILE_ID]
            )
            content_node.metadata[LLAMA_INDEX_FOLDER_ID] = str(
                root_node.metadata[LLAMA_INDEX_FOLDER_ID]
            )
            content_node.metadata[LLAMA_INDEX_PAGE_LABLE] = str(content.get("page", ""))
            content_nodes.append(content_node)

        # add relationship
        for index, content_node in enumerate(content_nodes):
            if index != 0:
                content_node.relationships[NodeRelationship.PREVIOUS] = content_nodes[
                    index - 1
                ].as_related_node_info()
                # print(f"prev node {content_nodes[index-1].as_related_node_info()}")
            if index != len(content_nodes) - 1:
                content_node.relationships[NodeRelationship.NEXT] = content_nodes[
                    index + 1
                ].as_related_node_info()
                # print(f"next node {content_nodes[index+1].as_related_node_info()}")
            node_list.append(content_node)
            # print(f"content node: {content_node}, part name {content_node.metadata[LLAMA_INDEX_META_PART_NAME]}")
