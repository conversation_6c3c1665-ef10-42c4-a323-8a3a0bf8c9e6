import json
import hashlib
import os
import time
import http.client
import requests
from common.fs.storage import create_s3_client

content_type = b"application/json; charset=utf-8"
APPID = "SX20250314TQEEGO"
APPKEY = b"ujfZCsGBOsgMalTsvYWNaIovtwomWkTQ"

s3_client = create_s3_client()
wps_bucket_name = "ask-doc"


def upload_file(file_path: str):
    remote_path = f"wps/documents/{os.path.basename(file_path)}"
    s3_client.upload_file(
        file_path,
        wps_bucket_name,
        remote_path,
    )
    s3_file_url = s3_client.generate_presigned_url(
        ClientMethod="get_object",
        Params={"Bucket": wps_bucket_name, "Key": remote_path},
        ExpiresIn=3600,  # URL有效期为1小时（以秒为单位）
    )
    print(s3_file_url)
    return s3_file_url


def start_convert_task(conn, file_url: str):
    url = "https://solution.wps.cn/api/developer/v1/office/pdf/convert/to/docx"
    payload = {"url": file_url}
    payload = json.dumps(payload, separators=(",", ":"))
    task_Content_Md5 = (
        hashlib.md5(payload.encode("utf-8")).hexdigest().lower().encode("utf-8")
    )
    date = time.strftime("%a, %d %b %Y %H:%M:%S GMT", time.gmtime()).encode("utf-8")
    # print(date.decode())
    signature = hashlib.sha1(
        APPKEY + task_Content_Md5 + content_type + date
    ).hexdigest()
    headers = {
        "Date": date.decode("utf-8"),
        "Content-Md5": task_Content_Md5.decode("utf-8"),
        "Content-Type": content_type.decode("utf-8"),
        "Authorization": f"WPS-2:{APPID}:{signature}",
    }
    conn.request("POST", url, payload, headers)
    res = conn.getresponse()
    task_data = res.read().decode("utf-8")
    # print(task_data)
    task_id = json.loads(task_data)["data"]["task_id"]
    return task_id


# 根据id查询结果
def get_convert_result(conn, task_id: str):
    uri = f"/api/developer/v1/tasks/convert/to/docx/{task_id}"
    date = time.strftime("%a, %d %b %Y %H:%M:%S GMT", time.gmtime()).encode("utf-8")
    # print(date.decode())
    query_Content_Md5 = (
        hashlib.md5(uri.encode("utf-8")).hexdigest().lower().encode("utf-8")
    )
    signature = hashlib.sha1(
        APPKEY + query_Content_Md5 + content_type + date
    ).hexdigest()
    headers = {
        "Date": date.decode("utf-8"),
        "Content-Md5": query_Content_Md5.decode("utf-8"),
        "Content-Type": content_type.decode("utf-8"),
        "Authorization": f"WPS-2:{APPID}:{signature}",
    }
    conn.request("GET", uri, headers=headers)
    res = conn.getresponse()
    data = res.read().decode("utf-8")
    # print(data)
    return json.loads(data)


# 下载转换后文档
def download_file(url: str, file_path: str):
    dirname, basename = os.path.dirname(file_path), os.path.basename(file_path)
    response = requests.get(url, stream=True)
    if response.status_code == 200:
        basename_without_suffix = ".".join(basename.split(".")[:-1])
        save_path = f"{dirname}/{basename_without_suffix}.docx"
        with open(save_path, "wb") as file:
            for chunk in response.iter_content(chunk_size=1024):
                file.write(chunk)

        print(f"文件已成功下载到: {dirname}")
        return save_path
    else:
        print(f"下载失败，HTTP 状态码: {response.status_code}")


def pdf_convert_docx_with_wps(file_path: str) -> str:
    conn = http.client.HTTPSConnection("solution.wps.cn")
    s3_file_url = upload_file(file_path)
    task_id = start_convert_task(conn, s3_file_url)
    while True:
        time.sleep(1)
        data = get_convert_result(conn, task_id)
        if data["data"]["status"] == 1 and data["data"]["progress"] == 100:
            break
    download_url = data["data"]["download_url"]
    doc_file_path = download_file(download_url, file_path)
    # s3_client.delete_object(Bucket=wps_bucket_name, Key=f"wps/documents/{os.path.basename(file_path)}")

    return doc_file_path


if __name__ == "__main__":
    pdf_convert_docx_with_wps(
        file_path="/Users/<USER>/Desktop/工作内容/宝武/财务数据/pdf文档/2022-宝信软件-半年度报告.pdf"
    )
