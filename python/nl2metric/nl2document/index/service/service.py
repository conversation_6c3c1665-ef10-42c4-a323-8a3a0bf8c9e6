import asyncio
import json
import traceback

from colorama import Fore
from fastapi import APIRouter, Request
from fastapi.responses import StreamingResponse
from langchain_core.runnables import <PERSON>nableLambda
from llama_index.core import ServiceContext
from llama_index.core.text_splitter import Token<PERSON>extSplitter
from llama_index.core.vector_stores import (
    MetadataFilters,
    MetadataFilter,
    FilterOperator,
    FilterCondition,
)

from backend_stage_reporter.cb_mgr import process_cb_task
from backend_stage_reporter.reporter import get_reporter_cb
from common.llm.embedding import get_embedding_model
from common.llm.llama_llm import get_llm
from common.logging.logger import get_logger
from common.trace import tracer
from common.types.base import (
    CHAIN_META,
    ChainMeta,
    ChainRuntime,
)
from common.types.callback_handler import LogCallbackHandler
from common.utils.string_utils import class_to_dict

# from condense.condense_query import call_condense_query
from config import app_config, doc_config
from config.doc_config import (
    doc_query_document_context_window,
)
from nl2document.builder.meta_index_builder import MetaIndex
from nl2document.common.base.const import (
    LLAMA_INDEX_FILE_ID,
    LLAMA_INDEX_FILE_PARENT_LIST,
    LLAMA_INDEX_COMPANY_ID,
    BLACK_VALUE,
    LLAMA_INDEX_YEAR,
    LLAMA_INDEX_MONTH,
)
from nl2document.common.models.base_model import (
    get_data_source,
    update_document,
    UploadStatus,
)
from nl2document.common.msg.doc import FileStatus
from nl2document.common.msg.docservice import (
    QueryDocumentData,
    QueryDocumentRequest,
    QueryDocumentResponse,
    SourceNode,
    DeleteDocumentRequest,
    RetrieverRequest,
)
from nl2document.common.vector.base import _to_milvus_filter
from nl2document.common.vector.vector_store import get_vector_store
from nl2document.index.chains.query_document import (
    call_synthesize_answer,
    retrieve,
    retrieve_nodes_postprocess,
)
from nl2document.index.chains.re_query_by_rule import (
    ReQueryByRule,
    ReplaceRule,
    TrimRule,
)
from scripts.doc.bj_telecom.batch_remove_dirty_files import get_file_ids

logger = get_logger(__name__)


def get_company_filer(req: QueryDocumentRequest):
    if not req.company_ids:
        return None
    ids = [BLACK_VALUE]
    if req.company_ids:
        ids += req.company_ids
    company_filer = MetadataFilter(
        key=LLAMA_INDEX_COMPANY_ID,
        value=ids,
        operator=FilterOperator.IN,
    )
    return MetadataFilters(filters=[company_filer], condition=FilterCondition.AND)


def get_time_filter(req: QueryDocumentRequest) -> MetadataFilters:
    """Get time-based filters from a QueryDocumentRequest.

    Constructs MetadataFilters based on start_year, start_month, end_year, and end_month.
    Assumes metadata fields 'year' and 'month' exist in the vector store.

    Args:
        req: QueryDocumentRequest object containing time filter parameters.

    Returns:
        MetadataFilters object with time-based filters.
    """
    filters = []

    # Handle start year and month
    if req.start_year:
        if req.start_month:
            # Combine year and month for a precise start filter
            start_date = f"{req.start_year if req.start_year else '-'}-{req.start_month.zfill(2) if req.start_month else '-'}"
            filters.append(
                MetadataFilter(
                    key="date",  # Assuming a 'date' field in YYYY-MM format
                    value=start_date,
                    operator=FilterOperator.GTE,
                )
            )
        else:
            # Only year filter
            filters.append(
                MetadataFilter(
                    key="year",
                    value=f"{req.start_year if req.start_year else '-'}",
                    operator=FilterOperator.EQ,
                )
            )

    # Handle end year and month
    if req.end_year:
        if req.end_month:
            # Combine year and month for a precise end filter
            end_date = f"{req.end_year if req.end_year else '-'}-{req.end_month.zfill(2) if req.end_month else '-'}"
            filters.append(
                MetadataFilter(
                    key="date",  # Assuming a 'date' field in YYYY-MM format
                    value=end_date,
                    operator=FilterOperator.LTE,
                )
            )
        else:
            # Only year filter
            filters.append(
                MetadataFilter(
                    key="year",
                    value=f"{req.end_year if req.end_year else '-'}",
                    operator=FilterOperator.EQ,
                )
            )

    # Return MetadataFilters with AND condition if filters exist
    if filters:
        return MetadataFilters(filters=filters, condition=FilterCondition.AND)
    else:
        # Return an empty MetadataFilters if no time filters are specified
        return None


def get_dir_file_filer(req: QueryDocumentRequest):
    if not req.ids and not req.dir_ids:
        return None
    # ARRAY_CONTAINS_ANY(dir, [dir_ids]) or file_id in [file_ids]
    if req.ids and not req.dir_ids:
        return MetadataFilters(
            filters=[
                MetadataFilter(
                    key=LLAMA_INDEX_FILE_ID,
                    value=req.ids,
                    operator=FilterOperator.IN,
                )
            ],
        )
    if req.ids and not req.dir_ids:
        return MetadataFilters(
            filters=[
                MetadataFilter(
                    key=LLAMA_INDEX_FILE_PARENT_LIST,
                    value=req.dir_ids,
                    operator=FilterOperator.ANY,
                )
            ],
        )
    return MetadataFilters(
        filters=[
            MetadataFilter(
                key=LLAMA_INDEX_FILE_ID,
                value=req.ids,
                operator=FilterOperator.IN,
            ),
            MetadataFilter(
                key=LLAMA_INDEX_FILE_PARENT_LIST,
                value=req.dir_ids,
                operator=FilterOperator.ANY,
            ),
        ],
        condition=FilterCondition.OR,
    )


class DocumentIndexService:
    def __init__(self):
        self._embed_model, _, _ = get_embedding_model(doc_config.doc_embedding_model)
        self.router = APIRouter()

        self.router.add_api_route(
            "/api/doc_index/delete_index",
            self.delete_documents_index,
            methods=["POST"],
        )
        self.router.add_api_route(
            "/api/doc_index/query_document",
            self.query_document,
            methods=["POST"],
        )

    def get_re_query_by_rule(self):
        return ReQueryByRule(
            [
                TrimRule(),  # 去除首尾空白
                # todo  Science City Project
                ReplaceRule("旅客登机桥下的", ""),  # 简单替换
                ReplaceRule("登机桥安全", "登机桥功能台账表保障安全"),
                ReplaceRule("登机桥服务", "登机桥保障服务"),
                ReplaceRule("登机桥效能", "登机桥保障效能"),
                ReplaceRule("登机桥运行", "登机桥保障运行"),
                ReplaceRule("保养内容", "保养工作"),
                ReplaceRule("保养项目", "保养工作"),
                ReplaceRule("安全的关键点", "安全的关键功能部位"),
                ReplaceRule("管轮", "滚轮"),
                ReplaceRule("允差范围", "性能偏差值或状态控制标准"),
                ReplaceRule("性能精度评查范围", "性能偏差值或状态控制标准"),
                ReplaceRule("不合格的标准", "不合格报警值或状态预警"),
                ReplaceRule("预警标准", "不合格报警值或状态预警"),
            ]
        )

    async def query_document(self, req: QueryDocumentRequest, request: Request):
        async def generate_response(nodes_with_score, chain_config, source_nodes):
            try:
                yield f"data: {json.dumps({'event': 'nodes', 'nodes': class_to_dict(source_nodes)}, ensure_ascii=False)}\n\n"
                chain = call_synthesize_answer()
                chain = chain.with_config(stream=True)

                async for chunk in chain.astream(nodes_with_score, config=chain_config):
                    if isinstance(chunk, (dict, str)):
                        content = chunk
                    else:
                        content = class_to_dict(chunk)
                    yield f"data: {json.dumps({'event': 'llm_content', 'content': content}, ensure_ascii=False)}\n\n"
                yield "data: [DONE]\n\n"

                logger.info("Query document stream response completed")
            except Exception as e:
                logger.error(
                    f"Stream processing failed: {str(e)}, trace: {traceback.format_exc()}"
                )
                yield f"data: {json.dumps({'code': 500, 'msg': f'Stream processing failed: {str(e)}'}, ensure_ascii=False)}\n\n"

        headers = request.headers
        trace_id = headers.get("Traceid", tracer.get_trace_id())
        backend_cb = None
        try:
            logger.info(f"receive retrieve nodes req folder ids {req.ids}")

            service_context = ServiceContext.from_defaults(
                llm=get_llm(req.model_type),
                embed_model=self._embed_model,
                text_splitter=TokenTextSplitter(),
                context_window=doc_query_document_context_window,
            )
            chain_metadata = {
                "job_type": "query_document",
                ChainMeta.LLAMAINDEX_SERVICE_CONTEXT: service_context,
                "model_type": req.model_type,
                ChainMeta.RUN_TIME: {
                    ChainRuntime.QUESTION: req.query,
                },
                ChainMeta.DOCUMENT_IDS: req.ids,
                "query_params": {
                    "rank_topk": doc_config.rank_topk,
                    "ask_doc_similarity_top_k": doc_config.ask_doc_similarity_top_k,
                    "score_threshold": doc_config.ask_doc_similarity_threshold,
                },
            }
            trace_name = f"{req.model_type}: QUERY_DOCUMENT {req.query}"
            cbs = []
            if app_config.ENABLE_LANGFUSE:
                backend_cb = get_reporter_cb(trace_id, None, trace_name, None)
                cbs = [
                    LogCallbackHandler(id=trace_id, host=app_config.CLUSTER_ID),
                ]
                if backend_cb is not None:
                    cbs.append(backend_cb)
                event = asyncio.Event()
                asyncio.create_task(process_cb_task(event, backend_cb))
                current_task = asyncio.current_task()
                current_task.add_done_callback(lambda _: event.set())

            if not app_config.USE_ASYNC_LANGCHAIN_LOG:
                cbs.append(LogCallbackHandler(id=trace_id, host=app_config.CLUSTER_ID))

            # 先执行检索
            chain_config = {
                CHAIN_META: chain_metadata,
                "callbacks": cbs,
                "max_concurrency": app_config.MAX_CONCURRENCY,
            }

            dir_ids_file_filters = get_dir_file_filer(req)
            time_filters = get_time_filter(req)
            company_filer = get_company_filer(req)
            meta_filters = MetadataFilters(
                filters=[],
                condition=FilterCondition.AND,
            )
            if dir_ids_file_filters is not None:
                meta_filters.filters.append(dir_ids_file_filters)
            if time_filters is not None:
                meta_filters.filters.append(time_filters)
            if company_filer is not None:
                meta_filters.filters.append(company_filer)
            # meta_filters.filters
            if doc_config.enable_meta_retriever:
                chain_config[CHAIN_META]["query_meta_filters"] = _to_milvus_filter(
                    meta_filters
                )
                meta_file_ids = await MetaIndex().retriever_as_filters(
                    query=req.query, filters=meta_filters
                )

                logger.info(f"meta_file_ids: {meta_file_ids}")
                if meta_file_ids:
                    doc_filters = MetadataFilters(
                        filters=[
                            MetadataFilter(
                                key=LLAMA_INDEX_FILE_ID,
                                value=meta_file_ids,
                                operator=FilterOperator.IN,
                            ),
                        ],
                    )
                else:
                    # 如果没有meta_file_ids，则使用meta_filters
                    doc_filters = meta_filters
            else:
                doc_filters = meta_filters
            chain_config[CHAIN_META]["query_doc_filters"] = _to_milvus_filter(
                doc_filters
            )
            rewriter = self.get_re_query_by_rule()
            chain = (
                RunnableLambda(rewriter)
                | RunnableLambda(retrieve, name="retrieve_nodes").bind(
                    filters=doc_filters
                )
                | RunnableLambda(
                    retrieve_nodes_postprocess, name="retrieve_nodes_postprocess"
                )
            )
            nodes_with_score = await chain.ainvoke(
                req.query,
                config=chain_config,
            )
            source_nodes = SourceNode(
                textNodes=[node.textNode for node in nodes_with_score],
            )
            if req.only_nodes:
                return class_to_dict(
                    QueryDocumentResponse(
                        code=0,
                        msg="",
                        data=QueryDocumentData(
                            sourceNodes=source_nodes,
                            content="",
                        ),
                    )
                )

            if req.stream:
                return StreamingResponse(
                    generate_response(nodes_with_score, chain_config, source_nodes),
                    media_type="text/event-stream",
                )
            else:
                try:
                    chain = call_synthesize_answer()
                    response = await chain.ainvoke(
                        nodes_with_score,
                        config=chain_config,
                    )
                    logger.info(Fore.CYAN + "QueryDocument: %s" + Fore.RESET, response)
                    return class_to_dict(
                        QueryDocumentResponse(
                            code=0,
                            msg="",
                            data=QueryDocumentData(
                                sourceNodes=source_nodes,
                                content=response,
                            ),
                        )
                    )
                except Exception as e:
                    logger.error(
                        f"Query processing failed: {str(e)}, trace: {traceback.format_exc()}"
                    )
                    return {"code": 500, "msg": f"Query processing failed: {str(e)}"}
        except Exception as e:
            logger.error(
                f"Query document failed: {str(e)}, trace: {traceback.format_exc()}"
            )
            return {"code": 500, "msg": f"Query document failed: {str(e)}"}

    async def delete_documents_index(self, req: DeleteDocumentRequest):
        try:
            for id_ in req.ids:
                vector_store = get_vector_store()
                filters = MetadataFilters(
                    filters=[
                        MetadataFilter(
                            key="file_id", value=req.ids, operator=FilterOperator.IN
                        )
                    ]
                )
                MetaIndex().vector_store().delete_nodes(filters=filters)
                vector_store.delete_nodes(filters=filters)
                update_document(
                    id_,
                    upload_status=UploadStatus.DELETED,
                    file_status=FileStatus.Deleted,
                )
            return {"code": 200, "msg": "SUCCESS"}
        except Exception as e:
            logger.exception(e)
            return {"code": 500, "msg": str(e)}


document_index_service = DocumentIndexService()
