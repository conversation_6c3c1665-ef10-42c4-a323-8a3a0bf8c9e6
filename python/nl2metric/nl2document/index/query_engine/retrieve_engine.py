import os
import time
from typing import Optional, List, Callable, Awaitable, Union

from langchain_core.runnables import <PERSON><PERSON><PERSON>Lambda, RunnableSequence
from langchain_core.runnables.base import RunnableLike
from llama_index.core import ServiceContext, get_response_synthesizer, QueryBundle
from llama_index.core.callbacks import CallbackManager
from llama_index.core.query_engine import RetrieverQueryEngine
from llama_index.core.schema import NodeWithScore
from llama_index.core.vector_stores import MetadataFilters

from nl2document.common.index.cascade_index.retriever import (
    CascadeIndexEmbeddingRetriever,
)
from common.logging.logger import get_logger
from config import doc_config
from nl2document.common.base.trace import RetrieverTracer, PostprocessorTracer
from nl2document.index.postprocessors.di_rerank import create_rank_model
from nl2document.index.postprocessors.node import QANextLevelNodePostprocessor
from nl2document.index.retrievers.retrievers import CustomRetriever
from llama_index.core import VectorStoreIndex
from langchain_core.runnables import RunnableConfig
from llama_index.core.base.base_retriever import BaseRetriever
from llama_index.core.response_synthesizers import BaseSynthesizer
from llama_index.core.postprocessor.types import BaseNodePostprocessor

from common.types.base import CHAIN_META, ChainMeta, ChainRuntime

logger = get_logger(__name__)

ask_doc_similarity_top_k = os.environ.get("ask_doc_similarity_top_k")


class AsyncRetrieverQueryEngine(RetrieverQueryEngine):
    def __init__(
        self,
        retriever: BaseRetriever,
        response_synthesizer: Optional[BaseSynthesizer] = None,
        node_postprocessors: Optional[List[BaseNodePostprocessor]] = None,
        callback_manager: Optional[CallbackManager] = None,
        runnable_config: Optional[RunnableConfig] = None,
    ):
        self._runnable_config = runnable_config
        super().__init__(
            retriever=retriever,
            response_synthesizer=response_synthesizer,
            node_postprocessors=node_postprocessors,
            callback_manager=callback_manager,
        )

    async def aretrieve(
        self, query_bundle: QueryBundle, **kwargs
    ) -> List[NodeWithScore]:
        chain = RunnableLambda(
            self._retriever.aretrieve, name="embedding_and_vector_index_retriever"
        ) | RunnableLambda(
            self.async_apply_node_postprocessors, name="rerank postprocessors"
        ).bind(
            query_bundle=query_bundle
        )
        return chain

    async def async_apply_node_postprocessors(
        self, nodes: List[NodeWithScore], query_bundle: QueryBundle
    ) -> List[NodeWithScore]:
        if self._runnable_config:
            self._runnable_config[CHAIN_META][ChainMeta.RUN_TIME][
                ChainRuntime.RETRIEVE_NODES_WITH_SCORE
            ] = nodes
        start = time.time()
        for node_postprocessor in self._node_postprocessors:
            if hasattr(node_postprocessor, "apostprocess_nodes"):
                nodes = await node_postprocessor.apostprocess_nodes(
                    nodes, query_bundle=query_bundle
                )
            else:
                nodes = node_postprocessor.postprocess_nodes(
                    nodes, query_bundle=query_bundle
                )
        logger.info(f"rerank postprocessors {time.time() - start} seconds")
        return nodes


def generate_retriever_engine(
    index: VectorStoreIndex,
    callback_manager: Optional[CallbackManager],
    service_context: ServiceContext,
    filters: Optional[MetadataFilters] = None,
    query_params: Optional[dict] = None,
    runnable_config: Optional[RunnableConfig] = None,
) -> RetrieverQueryEngine:
    if query_params is None:
        query_params = {
            "rank_topk": doc_config.rank_topk,
            "ask_doc_similarity_top_k": doc_config.ask_doc_similarity_top_k,
            "score_threshold": doc_config.ask_doc_similarity_threshold,
        }
    vector_index_retriever = CascadeIndexEmbeddingRetriever(
        name="vector_index_retriever",
        index=index,
        similarity_ref_top_k=query_params.get("ask_doc_similarity_top_k"),
        service_context=service_context,
        filters=filters,
    )

    custom_retriever = RetrieverTracer(
        CustomRetriever(
            retrievers=[RetrieverTracer(vector_index_retriever)],
        )
    )
    node_postprocessors = [
        # PrevNextNodePostprocessor(
        #     docstore=index.vector_store,
        #     num_nodes=1,
        #     mode="both",
        # ),
        # QANextLevelNodePostprocessor(
        #     docstore=index.vector_store,
        # ),
        create_rank_model(service_context, query_params.get("rank_topk")),
    ]
    node_postprocessors = [PostprocessorTracer(p) for p in node_postprocessors]
    query_engine = AsyncRetrieverQueryEngine(
        retriever=custom_retriever,
        callback_manager=callback_manager,
        node_postprocessors=node_postprocessors,
        response_synthesizer=get_response_synthesizer(service_context=service_context),
        runnable_config=runnable_config,
    )
    return query_engine
