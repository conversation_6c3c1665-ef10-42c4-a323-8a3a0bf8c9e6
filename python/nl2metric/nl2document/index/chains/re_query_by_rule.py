from typing import Callable, List, Pattern
import re
from abc import ABC, abstractmethod
from common.logging.logger import get_logger
from langchain_core.runnables.config import RunnableConfig

from common.types.base import CHAIN_META, ChainMeta, ChainRuntime

logger = get_logger(__name__)


class BaseRule(ABC):
    """规则基类"""

    @abstractmethod
    def __call__(self, query: str) -> str:
        """执行规则转换"""
        pass

    @classmethod
    def _validate_input(cls, query: str) -> bool:
        """验证输入"""
        return isinstance(query, str) and len(query.strip()) > 0


class Rule(BaseRule):
    """通用规则类"""

    def __init__(self, func: Callable[[str], str], *args, **kwargs):
        self.func = func
        self.args = args
        self.kwargs = kwargs

    def __call__(self, query: str) -> str:
        if not self._validate_input(query):
            logger.warning(f"Invalid input query: {query}")
            return query

        try:
            return self.func(query, *self.args, **self.kwargs)
        except Exception as e:
            logger.error(f"Error applying rule {self.func.__name__}: {str(e)}")
            return query


class ReplaceRule(BaseRule):
    """替换规则类"""

    def __init__(self, pattern: str, replacement: str):
        self.pattern = pattern
        self.replacement = replacement

    def __call__(self, query: str) -> str:
        if not self._validate_input(query):
            return query
        try:
            return query.replace(self.pattern, self.replacement)
        except Exception as e:
            logger.error(f"Error in ReplaceRule: {str(e)}")
            return query


class RegexRule(BaseRule):
    """正则表达式规则类"""

    def __init__(self, pattern: str | Pattern, replacement: str):
        self.pattern = re.compile(pattern) if isinstance(pattern, str) else pattern
        self.replacement = replacement

    def __call__(self, query: str) -> str:
        if not self._validate_input(query):
            return query
        try:
            return self.pattern.sub(self.replacement, query)
        except Exception as e:
            logger.error(f"Error in RegexRule: {str(e)}")
            return query


class TrimRule(BaseRule):
    """修剪规则类"""

    def __init__(self, chars: str | None = None):
        self.chars = chars

    def __call__(self, query: str) -> str:
        if not self._validate_input(query):
            return query
        try:
            return query.strip(self.chars)
        except Exception as e:
            logger.error(f"Error in TrimRule: {str(e)}")
            return query


class RemoveSpecialCharsRule(BaseRule):
    """移除特殊字符规则"""

    def __init__(self, keep_chars: str = "", remove_chars: str = ""):
        self.pattern = (
            re.compile(f"[^{re.escape(keep_chars)}]")
            if keep_chars
            else re.compile(f"[{re.escape(remove_chars)}]")
            if remove_chars
            else re.compile(r"[^a-zA-Z0-9\u4e00-\u9fff\s]")
        )

    def __call__(self, query: str) -> str:
        if not self._validate_input(query):
            return query
        try:
            return self.pattern.sub("", query)
        except Exception as e:
            logger.error(f"Error in RemoveSpecialCharsRule: {str(e)}")
            return query


class NormalizeSpacesRule(BaseRule):
    """标准化空白字符规则"""

    def __init__(self):
        self.pattern = re.compile(r"[\s\u3000]+")

    def __call__(self, query: str) -> str:
        if not self._validate_input(query):
            return query
        try:
            return self.pattern.sub(" ", query).strip()
        except Exception as e:
            logger.error(f"Error in NormalizeSpacesRule: {str(e)}")
            return query


class RemoveDuplicatesRule(BaseRule):
    """移除重复词规则"""

    def __init__(self, separator: str = " "):
        self.separator = separator

    def __call__(self, query: str) -> str:
        if not self._validate_input(query):
            return query
        try:
            words = query.split(self.separator)
            unique_words = []
            for word in words:
                if word and word not in unique_words:
                    unique_words.append(word)
            return self.separator.join(unique_words)
        except Exception as e:
            logger.error(f"Error in RemoveDuplicatesRule: {str(e)}")
            return query


class ReQueryByRule:
    """查询重写规则执行器
    # 创建规则链
    rewriter = ReQueryByRule([
        TrimRule(),  # 去除首尾空白
        ReplaceRule("旧文本", "新文本"),  # 简单替换
        RegexRule(r"\s+", " "),  # 合并多个空格
    ])


    # 添加自定义规则
    def uppercase_rule(q: str) -> str:
        return q.upper()


    rewriter.add_rule(Rule(uppercase_rule))

    # 测试
    result = rewriter("  旧文本  with   spaces  ")
    print(f"处理结果: {result}")  # 输出: 新文本 WITH SPACES
    """

    def __init__(self, rules: List[BaseRule] | None = None):
        self.rules = rules or []

    def add_rule(self, rule: BaseRule) -> None:
        """添加新规则"""
        if not isinstance(rule, BaseRule):
            raise TypeError(f"Rule must be instance of BaseRule, got {type(rule)}")
        self.rules.append(rule)

    def __call__(self, query: str, config: RunnableConfig) -> str:
        """执行所有规则"""
        if not isinstance(query, str):
            logger.warning(f"Invalid query type: {type(query)}")
            return str(query)

        try:
            for rule in self.rules:
                query = rule(query)
            config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.QUESTION] = query
            return query
        except Exception as e:
            logger.error(f"Error applying rules: {str(e)}")
            return query
