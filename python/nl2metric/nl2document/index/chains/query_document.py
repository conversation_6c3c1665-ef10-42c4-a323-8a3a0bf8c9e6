import os
import time
from typing import List, Dict
import asyncio

from llama_index.core.types import TokenAsyncGen
from llama_index.core.vector_stores import (
    MetadataFilters,
    MetadataFilter,
    FilterOperator,
    FilterCondition,
)
from common.fs.fs import get_s3_file_system
from config.doc_config import DeployMode, deployMode, llama_verbose
from nl2document.common.models.base_model import get_data_source
from nl2document.common.vector.vector_store import get_vector_store
from common.logging.logger import get_logger
from common.types.base import ParamsExtractStage, CHAIN_META, ChainMeta, ChainRuntime
from common.utils.json_utils import extract_json_from_string
from common.llm.llama_llm import get_llm
from config import doc_config
from llama_index.core import ServiceContext
from llama_index.core.text_splitter import TokenTextSplitter
from nl2document.common.base.const import (
    LLAMA_INDEX_COLUMN_INDEX,
    LLAMA_INDEX_MEETING_ID,
    <PERSON><PERSON><PERSON>_INDEX_FILE_ID,
    LLAMA_INDEX_FOLDER_ID,
    LLAMA_INDEX_META_USER_ID,
    LLAMA_INDEX_PAGE_LABLE,
    LLAMA_INDEX_FILE_NAME,
    LLAMA_INDEX_META_PART_NAME,
    LLAMA_INDEX_CONFERENCE_CONTENT_TYPE,
    LLAMA_INDEX_CONFERENCE_THEME,
    LLAMA_INDEX_CONFERENCE_SUMMARY_TITLE,
    LLAMA_INDEX_CONFERENCE_PROMOTER,
    LLAMA_INDEX_CONFERENCE_PERSONS,
    LLAMA_INDEX_CONFERENCE_CHAPTER_TITLE,
    LLAMA_INDEX_CONFERENCE_SPEAKER,
    LLAMA_INDEX_CONFERENCE_SPEAKER_START_TIME,
    LLAMA_INDEX_CONFERENCE_SPEAKER_END_TIME,
    LLAMA_INDEX_CONFERENCE_SPEAKERS,
    LLAMA_UPLOAD_TYPE,
)
from nl2document.common.models.model import (
    ONE_DAY_SECONDS,
    UploadType,
)
from nl2document.common.msg.doc import (
    ImageNodePb,
    NodeWithScorePb,
    TextNodePb,
)
from nl2document.index.query_engine.retrieve_engine import generate_retriever_engine
from nl2document.common.base.prompt_template import (
    ASKBOT_COMMON_CH_TREE_SUMMARIZE_TMPL,
    CH_TEXT_QA_PROMPT_TMPL,
    CH_TREE_SUMMARIZE_TMPL,
    WITHOUT_RETRIVAL_CONTENT_TMPL,
    BJ_TELECOM_SUMMARIZE_TMPL,
    SIMILARITY_TMPL,
)

from llama_index.core import QueryBundle, get_response_synthesizer, PromptTemplate
from llama_index.core.prompts import PromptType
from llama_index.core.schema import MetadataMode, NodeWithScore
from llama_index.core.response_synthesizers import ResponseMode
from langchain_core.runnables import (
    Runnable,
    RunnableLambda,
    RunnableConfig,
    RunnableParallel,
)
from llama_index.core.query_engine import RetrieverQueryEngine
from langchain_core.runnables.utils import Input, Output
from llama_index.core import StorageContext, VectorStoreIndex

from config.app_config import VLLM_MODEL_NAME
from common.llm.embedding import get_embedding_model

logger = get_logger(__name__)


async def retrieve(query, filters, config: RunnableConfig):
    if isinstance(query, dict):
        query = query["question"]
    service_context = config[CHAIN_META].get(ChainMeta.LLAMAINDEX_SERVICE_CONTEXT)
    collection_name = (
        config[CHAIN_META].get(ChainMeta.RUN_TIME).get(ChainRuntime.COLLECTION_NAME, "")
    )
    vector_store = get_vector_store(collection_name=collection_name)
    storage_context = StorageContext.from_defaults(vector_store=vector_store)
    index: VectorStoreIndex = VectorStoreIndex.from_vector_store(
        vector_store=vector_store,
        service_context=service_context,
        storage_context=storage_context,
        show_progress=True,
    )
    logger.info("get index over.")
    retriever_engine: RetrieverQueryEngine = generate_retriever_engine(
        index,
        None,
        service_context=service_context,
        filters=filters,
        query_params=config[CHAIN_META].get("query_params"),
        runnable_config=config,
    )
    logger.info(f"prepare retrieve nodes with score")
    chain = RunnableLambda(retriever_engine.aretrieve, name="retrieve")
    nodes_with_scores: List[NodeWithScore] = await chain.ainvoke(
        QueryBundle(query), config=config
    )
    logger.info(f"retrieve nodes with score size {len(nodes_with_scores)}")
    return nodes_with_scores


def retrieve_default(query, filters):
    _embed_model, _, _ = get_embedding_model(doc_config.doc_embedding_model)
    service_context = ServiceContext.from_defaults(
        llm=get_llm(VLLM_MODEL_NAME),
        embed_model=_embed_model,
        text_splitter=TokenTextSplitter(),
        context_window=doc_config.doc_query_document_context_window,
    )

    chain_metadata = {
        ChainMeta.LLAMAINDEX_SERVICE_CONTEXT: service_context,
        "query_params": {
            "rank_topk": doc_config.rank_topk,
            "ask_doc_similarity_top_k": doc_config.ask_doc_similarity_top_k,
            "score_threshold": doc_config.ask_doc_similarity_threshold,
        },
    }
    chain_config = {
        CHAIN_META: chain_metadata,
    }
    nodes_with_scores = asyncio.run(retrieve(query, filters, config=chain_config))
    return nodes_with_scores


async def retrieve_nodes_postprocess(
    nodes_with_scores: List[NodeWithScore], config: RunnableConfig
):
    ret_nodes = []

    for node_with_score in nodes_with_scores:
        if node_with_score.score < config[CHAIN_META].get("query_params").get(
            "score_threshold"
        ):
            continue
        node = node_with_score.node
        if node.metadata.get("chunk_type") == "image":
            image_node = ImageNodePb(
                content=node.get_content(metadata_mode=MetadataMode.NONE),
                page=int(node.metadata.get(LLAMA_INDEX_PAGE_LABLE, "-1")),
                fileId=node.metadata.get(LLAMA_INDEX_FILE_ID, ""),
                folderId=node.metadata.get(LLAMA_INDEX_FOLDER_ID, ""),
                url=get_s3_file_system().url(
                    os.path.join(
                        doc_config.ASK_DOC_S3_BUCKET_NAME,
                        "images",
                        node.metadata.get("image_path", ""),
                    ),
                    expires=ONE_DAY_SECONDS * 2,
                ),
                fileName=node.metadata.get(LLAMA_INDEX_FILE_NAME, ""),
                partName=node.metadata.get(LLAMA_INDEX_META_PART_NAME, ""),
                nodeId=node.node_id,
            )
            node_with_score_pb = NodeWithScorePb(
                imageNode=image_node,
                score=node_with_score.score,
            )
        else:
            folderId = node.metadata.get(LLAMA_INDEX_FOLDER_ID, "")
            fileName = node.metadata.get(LLAMA_INDEX_FILE_NAME, "")
            partName = node.metadata.get(LLAMA_INDEX_META_PART_NAME, "")
            chapter_title = node.metadata.get(LLAMA_INDEX_CONFERENCE_CHAPTER_TITLE, "")

            # 添加列索引标识，以便前端处理
            column_index = node.metadata.get(LLAMA_INDEX_COLUMN_INDEX, "")
            text_node = TextNodePb(
                content=node.get_content(metadata_mode=MetadataMode.NONE),
                page=int(node.metadata.get(LLAMA_INDEX_PAGE_LABLE, "-1") or "-1"),
                fileId=str(node.metadata.get(LLAMA_INDEX_FILE_ID, "")),
                folderId=folderId,
                fileName=fileName,
                partName=partName,
                nodeId=node.node_id,
                chapter_title=chapter_title,
                columnIndex=True if column_index == "1" else False,
            )
            node_with_score_pb = NodeWithScorePb(
                textNode=text_node,
                score=node_with_score.score,
            )
        ret_nodes.append(node_with_score_pb)
    config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.NODES_WITH_SCORE] = ret_nodes
    logger.info(f"ask doc retrieve nodes {len(ret_nodes)}")
    return ret_nodes


async def synthesize_answer(nodes_with_score, config: RunnableConfig):
    query = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.QUESTION]
    service_context = config[CHAIN_META][ChainMeta.LLAMAINDEX_SERVICE_CONTEXT]
    text_chunks = []
    prompt_tmpl = config[CHAIN_META][ChainMeta.RUN_TIME].get(
        "summary_template", ASKBOT_COMMON_CH_TREE_SUMMARIZE_TMPL
    )
    for node in nodes_with_score:
        if node.textNode is not None:
            text_chunks.append(node.textNode.get_content())
        elif node.imageNode is not None:
            text_chunks.append(node.imageNode.content)

    response_synthesizer = get_response_synthesizer(
        use_async=True,
        response_mode=ResponseMode.TREE_SUMMARIZE,
        summary_template=PromptTemplate(prompt_tmpl, prompt_type=PromptType.SUMMARY),
        text_qa_template=PromptTemplate(
            CH_TEXT_QA_PROMPT_TMPL, prompt_type=PromptType.QUESTION_ANSWER
        ),
        service_context=service_context,
        streaming=True,
        verbose=llama_verbose,
    )
    resp: TokenAsyncGen = await response_synthesizer.aget_response(
        query_str=query, text_chunks=text_chunks, config=config
    )
    async for token in resp:
        yield token


def call_synthesize_answer() -> Runnable[Input, Output]:
    chain = RunnableLambda(
        synthesize_answer,
        name=ParamsExtractStage.NL2DOCUMENT_SYNTHESIZE_ANSWER,
    )
    return chain
