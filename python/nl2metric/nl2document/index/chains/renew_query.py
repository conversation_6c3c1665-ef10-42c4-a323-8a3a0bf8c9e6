from langchain_core.messages import AIMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnableLambda, Runnable, RunnableConfig
from langchain_core.runnables.utils import Input, Output
from common.types.base import (
    CHAIN_META,
    ChainMeta,
    ChainRuntime,
)

from common.llm.general import create_chat_model
from common.logging.logger import get_logger
from config import doc_config

template = """
    你是一名专业的查询重写助手，需要对用户的查询根据以下四种策略进行重写：

    1. 一般查询重写（GQR）：提炼原始查询，去除冗余，但保留所有相关信息。
    2. 关键词重写（KWR）：提取关键词，特别是名词和主题词。
    3. 伪答案重写（PAR）：基于常识或背景知识，为查询生成可能的答案并重写。
    4. 核心内容提取（CCE）：删除次要细节，仅保留关键信息。

    * 输出语言与输入语言相同 *
    输入："{query}"
    请直接输出以下格式的重写结果：

    一般查询重写（GQR）：<这里填写重写结果>
    关键词重写（KWR）：<这里填写重写结果>
    伪答案重写（PAR）：<这里填写重写结果>
    核心内容提取（CCE）：<这里填写重写结果>
"""

logger = get_logger(__name__)


def query_rewrite(query: str, model_type: str) -> Runnable[Input, Output]:
    """
    使用 LangChain 对输入的查询执行四种重写策略：GQR、KWR、PAR、CCE。

    参数:
        query (str): 原始用户查询。
        model_type (str): 指定的模型类型。
    返回:
        Runnable: 一个 Runnable 对象，用于执行查询重写。
    """
    prompt = ChatPromptTemplate.from_template(template)

    # 初始化 LLM
    chain = (
        RunnableLambda(lambda _: prompt.invoke({"query": query}).to_messages())
        | create_chat_model(model_type)
        | RunnableLambda(_parse).bind(query=query)
    )
    return chain


# 解析响应
def _parse(text: AIMessage, query: str) -> str:
    """
    解析模型返回的响应，并构造人类可读的输出。

    参数:
        text (AIMessage): 模型的响应。
        query (str): 原始用户查询。
    返回:
        str: 处理后的结果文本。
    """
    try:
        # 直接根据换行符提取结果
        content = text.content.strip()
        lines = content.splitlines()
        result = {
            line.split("：")[0]: line.split("：")[1] for line in lines if "：" in line
        }
    except Exception as e:
        logger.error(f"解析响应时发生错误: {e}")
        return f"查询：{query}"

    ret = [f"查询：{query}"]
    if "核心内容提取（CCE）" in result:
        ret.append(f"核心内容提取：{result['核心内容提取（CCE）']}")
    if "关键词重写（KWR）" in result:
        ret.append(f"关键词重写：{result['关键词重写（KWR）']}")
    if "伪答案重写（PAR）" in result:
        ret.append(f"伪答案重写：{result['伪答案重写（PAR）']}")
    if "一般查询重写（GQR）" in result:
        ret.append(f"一般查询重写：{result['一般查询重写（GQR）']}")
    return "\n".join(ret).strip()
