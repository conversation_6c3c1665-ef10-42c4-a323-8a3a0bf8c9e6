from llama_index.core.vector_stores import (
    VectorStoreQuery,
    MetadataFilters,
    MetadataFilter,
    FilterOperator,
    FilterCondition,
)

from nl2document.common.vector.vector_store import get_vector_store

if __name__ == "__main__":
    vector_store = get_vector_store()
    # filters=[MetadataFilter(key='node_type_name', value='sentence', operator=<FilterOperator.EQ: '=='>),
    # MetadataFilter(key='file_id', value='0759e391-1527-49c2-9c11-bc6bd57d2ecd', operator=<FilterOperator.EQ: '=='>),
    # MetadataFilter(key='user_id', value='123456', operator=<FilterOperator.EQ: '=='>)]
    # condition=<FilterCondition.AND: 'and'>
    filters = MetadataFilters(
        filters=[
            MetadataFilter(
                key="file_id",
                value="0759e391-1527-49c2-9c11-bc6bd57d2ecd",
                operator=FilterOperator.EQ,
            ),
            MetadataFilter(key="user_id", value="123456", operator=FilterOperator.EQ),
            MetadataFilter(
                key="node_type_name", value="sentence", operator=FilterOperator.EQ
            ),
        ],
        condition=FilterCondition.AND,
    )
    query = VectorStoreQuery(
        query_embedding=query_embeddings,
        similarity_top_k=4,
        node_ids=None,
        filters=filters,
    )
    query_result = vector_store.query(query)
    print(query_result)
