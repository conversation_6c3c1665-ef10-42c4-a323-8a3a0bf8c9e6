import json
import os
from askbot_common.generated.common.doc_pb2 import DocumentIndexType
from google.protobuf.json_format import MessageT<PERSON><PERSON><PERSON>

from askbot_common.logger import get_logger
from askbot_common.generated.service.docservice_pb2 import (
    ListDocumentsRequest,
    RetrieveNodesRequest,
    SuggestionQuestionsRequest,
)
from llama_index import ServiceContext, PromptTemplate, get_response_synthesizer
from llama_index.callbacks.base import CallbackManager
from llama_index.callbacks.llama_debug import <PERSON>lamaDebugHandler
from llama_index.node_parser import SimpleNodeParser
from llama_index.text_splitter import TokenTextSplitter
from llama_index.response_synthesizers import ResponseMode
from llama_index.prompts import PromptType

from askbot_common.llm.prompt_template import (
    CH_TEXT_QA_PROMPT_TMPL,
    CH_TREE_SUMMARIZE_TMPL,
)
from askbot_document_builder.builder.index_builder import (
    Index<PERSON><PERSON><PERSON>,
    DocumentAddRequest,
)

from askbot_document_common.common.config import (
    LLAMA_INDEX_FILE_ID,
    LLAMA_INDEX_FOLDER_ID,
    LLAMA_INDEX_PAGE_LABLE,
    LLAMA_INDEX_FILE_NAME,
    LLAMA_INDEX_META_PART_NAME,
)
from askbot_document_common.common import env
from askbot_document_common.common import custom_models
from askbot_common.utils import ObjectEncoder
from askbot_document_common.models.model import (
    FolderModel,
    list_documents_by_ids,
)
from askbot_document_common.llms.predictor import get_llm
from askbot_document_common.index.document_index import DocumentIndex
from askbot_document_index.service.service import AskBotDocumentIndexService
from askbot_document_index.tests.samples_parser import (
    get_sample_questions,
    set_sample_recall_results,
    set_sample_rank_results,
    set_sample_synthesizer_result,
)

logger = get_logger(__name__)

rebuild_index = False


def main():
    # validate retrieve result...
    embed_model, tokenizer = custom_models.get_embedding_model()
    # cb_manager = CallbackManager([LlamaDebugHandler()])
    cb_manager = CallbackManager([])
    service_context = ServiceContext.from_defaults(
        llm=get_llm(),
        embed_model=embed_model,
        callback_manager=cb_manager,
        node_parser=SimpleNodeParser(text_splitter=TokenTextSplitter()),
    )
    scene_id = "5EJriyLTJA2L03H"
    file_ids = [596, 597]
    folder = FolderModel(id=scene_id)
    if rebuild_index:
        # build index
        files = list_documents_by_ids(file_ids)
        index_builder = IndexBuilder()
        for document in files:
            document_request = DocumentAddRequest(
                source_url=document.source_path,
            )
            index_builder._add_document(document, document_request)
        index_builder.exec_scene_task(scene_id, files)
    index = DocumentIndex.from_persist_path(
        folder.index_dir,
        service_context=service_context,
    )
    print("Init index Over.")

    retriever = index.sentence_index.as_retriever(
        name="sentence_cascade_index_retriever",
        similarity_ref_top_k=env.ask_doc_similarity_top_k,
        service_context=service_context,
    )

    file_path = "/dipeak/askbot-service/code/askbot_service/askbot_document_index/tests/yicai_sample.xlsx"
    files, questions, pages, parts = get_sample_questions(file_path)
    recall_result = []
    for index, question in enumerate(questions):
        nodes_with_score = retriever.retrieve(question)
        print(f"question: {question}")
        matched = False
        recall_info = []
        for node_with_score in nodes_with_score:
            node = node_with_score.node
            score = node_with_score.score
            fileName = node.metadata.get(LLAMA_INDEX_FILE_NAME, "")
            page = int(node.metadata.get(LLAMA_INDEX_PAGE_LABLE, "-1"))
            partName = node.metadata.get(LLAMA_INDEX_META_PART_NAME, "")
            if fileName == files[index] and page in pages[index]:
                matched = True
            else:
                logger.info(
                    f"fileName {fileName} original fileName {files[index]} and page {page} original page {pages[index]}"
                )
            node_info = {
                "node_id": node.node_id,
                "recall_score": score,
                "file_name": fileName,
                "page": page,
                "part_name": partName,
            }
            recall_info.append(node_info)
            print(
                f"recall score {score} file name {fileName}, page {page}, partName {partName}"
            )
        print("--------------------")
        final_recall_info = {
            "matched": matched,
            "recall_results": recall_info,
        }
        recall_string = json.dumps(final_recall_info, ensure_ascii=False, indent=4)
        recall_result.append(recall_string)
    set_sample_recall_results(file_path, recall_result)

    # validate rank result...
    rerank_result = []
    synthesizer_result = []
    for index, question in enumerate(questions):
        request = RetrieveNodesRequest(
            id=str(folder.id),
            query=question,
            index_type=DocumentIndexType.Folder,
        )

        service = AskBotDocumentIndexService()
        response = service.RetrieveNodes(request, None)
        rerank_nodes = response.node_with_score_list
        rerank_info = []
        rerank_text_chunks = []
        matched = False
        for node_with_score in rerank_nodes:
            node = node_with_score.textNode
            score = node_with_score.score
            fileName = node.fileName
            partName = node.partName
            page = node.page
            if fileName == files[index] and page in pages[index]:
                matched = True
            node_info = {
                "node_id": node.nodeId,
                "recall_score": score,
                "file_name": fileName,
                "page": page,
                "part_name": partName,
            }
            rerank_info.append(node_info)
            rerank_text_chunks.append(node.content)
            print(
                f"rerank score {score} file name {fileName}, page {page}, partName {partName}"
            )
        print("--------------------")
        final_rerank_info = {
            "matched": matched,
            "rerank_results": rerank_info,
        }
        rerank_string = json.dumps(final_rerank_info, ensure_ascii=False, indent=4)
        rerank_result.append(rerank_string)
        # validate synthesize
        # synthesizer
        # synthesizer = get_response_synthesizer(response_mode=ResponseMode.TREE_SUMMARIZE,
        #     summary_template=PromptTemplate(
        #         CH_TREE_SUMMARIZE_TMPL, prompt_type=PromptType.SUMMARY
        #     ),
        #     text_qa_template=PromptTemplate(
        #         CH_TEXT_QA_PROMPT_TMPL, prompt_type=PromptType.QUESTION_ANSWER
        #     ),
        #     service_context=service_context,
        # )
        # response = synthesizer.get_response(question, rerank_text_chunks)
        # synthesizer_result.append(response)
    set_sample_rank_results(file_path, rerank_result)
    set_sample_synthesizer_result(file_path, synthesizer_result)


if __name__ == "__main__":
    main()
