import json
from typing import List

from pymilvus import (
    FieldSchema,
    CollectionSchema,
    DataType,
    Collection,
    connections,
    Milvus,
)
from sqlalchemy import select, create_engine
from sqlalchemy.orm import Session

from config import doc_config
from config.doc_config import milvus_collection_name
from nl2document.common.models.base_model import UploadStatus, CMCCDocDataSource
from nl2document.common.models.model import CMCCFileModel, CommonDocumentModel
from nl2document.common.msg.doc import FileStatus

milvus = Milvus(host="**************", port="31870")
collection_name = milvus_collection_name
connection_string = f"mysql+pymysql://{doc_config.DB_USER}:{doc_config.DB_PASSWORD}@{doc_config.DB_HOST}:{doc_config.DB_PORT}/{doc_config.DB_NAME}"
engine = create_engine(connection_string, echo=False)


def get_node_by_exp(exp: str):
    results = milvus.query(
        collection_name=collection_name,
        expr=exp,
        output_fields=["file_id", "node_type_name", "file_name", "_node_content"],
    )
    results_list = [result for result in results]
    for index, result in enumerate(results_list):
        results_list[index]["_node_content"] = json.loads(result["_node_content"])
        results_list[index]["_node_content"] = {
            "text": results_list[index]["_node_content"]["text"]
        }
        print(results_list[index])


def get_node_data_with_file_id(file_id: str, file_name: str):
    # 执行基于表达式的查询
    query_expression = f'file_id == "{file_id}"'
    results = milvus.query(
        collection_name=collection_name,
        expr=query_expression,
        output_fields=["file_id", "node_type_name", "file_name", "_node_content"],
    )
    results_list = [result for result in results]
    for index, result in enumerate(results_list):
        results_list[index]["_node_content"] = json.loads(result["_node_content"])
        # results_list[index]['_node_content'] = {
        #     "text": results_list[index]['_node_content']['text']
        # }
        print(result)
    with open(
        f"/Users/<USER>/github_project/ask-bi/python/nl2metric/nl2document/index/tests/node_json/{file_name}.json",
        "w",
        encoding="utf-8",
    ) as f:
        json.dump(results_list, f, ensure_ascii=False, indent=4)


"""
{"part_name":"","parent_node":"309bfa8e-b50e-4bfb-93b9-0ddf87f7f8f9","node_type_name":"sentence","file_id":"733646c9-27dd-480c-9b46-37306dd505ee","folder_id":"","file_name":"3report (1).pdf","meeting_id":"123456","user_id":"123456","_node_content":"{\"id_\": \"00027f07-34f6-4a9e-91bc-7ad266547925\", \"embedding\": null, \"metadata\": {\"part_name\": \"\", \"parent_node\": \"309bfa8e-b50e-4bfb-93b9-0ddf87f7f8f9\", \"node_type_name\": \"sentence\", \"file_id\": \"733646c9-27dd-480c-9b46-37306dd505ee\", \"folder_id\": \"\", \"file_name\": \"3report (1).pdf\", \"meeting_id\": \"123456\", \"user_id\": \"123456\"}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {}, \"text\": \"\\u4ece\\u4e0b\\u94bb\\u7ed3\\u679c\\u4e2d\\u53ef\\u4ee5\\u770b\\u51fa\\uff0c\\u201d\\u4e2d\\u56fd\\u7535\\u4fe1\\u80a1\\u4efd\\u6709\\u9650\\u516c\\u53f8\\u5317\\u4eac\\u5206\\u516c\\u53f8\\u201d\\u7684\\u5408\\u540c\\u6536\\u5165\\u603b\\u91d1\\u989d\\u4e3a56282.4\\u5143\\uff0c\\u800c\\\"\\u4e2d\\u56fd\\u7535\\u4fe1\\u96c6\\u56e2\\u6709\\u9650\\u516c\\u53f8\\u5317\\u4eac\\u5206\\u516c\\u53f8\\\"\\u7684\\u5408\\u540c\\u6536\\u5165\\u603b\\u91d1\\u989d\\u4e3a1218.08\\u5143\\u3002\", \"mimetype\": \"text/plain\", \"start_char_idx\": null, \"end_char_idx\": null, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"None","doc_id":"None","ref_doc_id":"None"}
"""


def get_and_update_first_document_status(status) -> List[CommonDocumentModel]:
    with Session(engine) as session:
        stmt = (
            select(CMCCFileModel)
            .where(CMCCFileModel.upload_status.in_(status))
            .limit(10)
            .with_for_update()
        )

        cmcc_files = list(session.scalars(stmt))
        if len(cmcc_files) == 0:
            return []
        return [
            CommonDocumentModel(
                id=cmcc_file.file_id,
                file_type=cmcc_file.file_type,
                file_name=cmcc_file.file_name,
                platform=cmcc_file.platform,
                upload_type=cmcc_file.upload_type,
                source_url=cmcc_file.file_id,
                file_status=FileStatus.Ready,
                meta_info={
                    "meeting_id": cmcc_file.meeting_id,
                    "user_id": cmcc_file.user_id,
                    "file_id": cmcc_file.file_id,
                },
            )
            for cmcc_file in cmcc_files
        ]


def get_cmcc_file_list():
    status = [UploadStatus.INDEX_BUILD_SUCCESS]
    documents = get_and_update_first_document_status(status)
    for doc in documents:
        get_node_data_with_file_id(doc.id, doc.file_name)


if __name__ == "__main__":
    # expr = 'file_id == "0759e391-1527-49c2-9c11-bc6bd57d2ecd" and node_type_name=="sentence" and user_id=="123456" '
    # get_node_by_exp(expr)
    get_node_data_with_file_id("265e0284-6bd7-46e1-8dd8-6bcae02d3813", "")
    # get_cmcc_file_list()
