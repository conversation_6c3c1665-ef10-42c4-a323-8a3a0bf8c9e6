import json
import os
from askbot_common.generated.common.doc_pb2 import DocumentIndexType
from askbot_document_common.common import env
from askbot_document_common.llms.general import GeneralLLM
from askbot_document_common.models.model import get_folder_tree, get_library
from askbot_document_index.manager.document_manager import DEFAULT_LIBRARY_NAME

from google.protobuf.json_format import MessageToJson

from askbot_common.generated.service.docservice_pb2 import (
    ListDocumentsRequest,
    RetrieveNodesRequest,
    SuggestionQuestionsRequest,
)
from askbot_common.utils import ObjectEncoder
from askbot_document_index.service.service import AskBotDocumentIndexService


def test_retrieve_nodes():
    service = AskBotDocumentIndexService()
    default_library = get_library(DEFAULT_LIBRARY_NAME)
    folder = get_folder_tree(default_library.id, "formal_data")
    request = RetrieveNodesRequest(
        id=str(folder.id),
        query="在施工安全保证体系中，提到的施工中安全注意事项包括哪些具体措施",
        index_type=DocumentIndexType.Folder,
    )
    result = service.RetrieveNodes(request, None)
    print(f"retrieve result is {result}")


def test_suggestion_questions():
    service = AskBotDocumentIndexService()
    default_library = get_library(DEFAULT_LIBRARY_NAME)
    for name in ["蕴荟基本信息", "蕴荟场景话术", "蕴荟Product Story解析", "蕴荟FAQ", "蕴荟5合1"]:
        folder = get_folder_tree(default_library.id, name)
        request = SuggestionQuestionsRequest(
            id=str(folder.id), index_type=DocumentIndexType.Folder
        )
        print(
            f"q: {name}, resp: {service.SuggestionQuestions(request, None).questions}"
        )


def test_list_documents():
    service = AskBotDocumentIndexService()
    request = ListDocumentsRequest()
    print(service.ListDocuments(request, None))


if __name__ == "__main__":
    llm = GeneralLLM()
    response = llm.complete("who are u")
    print(response.text)
