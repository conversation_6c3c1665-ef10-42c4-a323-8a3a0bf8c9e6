import aiohttp
from typing import List, Optional, Dict, Any

from xinference_client.client.restful.restful_client import RESTfulRerankModelHandle

from common.logging.logger import get_logger
from common.utils.rate_limit import rate_limit
from common.utils.retry import retry_async
from config.doc_config import rerank_rate_limit

logger = get_logger(__name__)


class RerankModel(RESTfulRerankModelHandle):
    @retry_async(retries=10, delay=1, exceptions=(Exception,))
    @rate_limit(qps=rerank_rate_limit)
    async def arerank(
        self,
        documents: List[str],
        query: str,
        top_n: Optional[int] = None,
        max_chunks_per_doc: Optional[int] = None,
        return_documents: Optional[bool] = None,
        return_len: Optional[bool] = None,
        **kwargs,
    ) -> Dict[str, Any]:
        """
        Asynchronously returns an ordered list of documents ordered by their relevance to the provided query.

        Parameters
        ----------
        query: str
            The search query
        documents: List[str]
            The documents to rerank
        top_n: int
            The number of results to return, defaults to returning all results
        max_chunks_per_doc: int
            The maximum number of chunks derived from a document
        return_documents: bool
            if return documents
        return_len: bool
            if return tokens len

        Returns
        -------
        Dict[str, Any]
            The scores of documents ordered by their relevance to the provided query

        Raises
        ------
        RuntimeError
            Report the failure of rerank and provide the error message.
        """
        url = f"{self._base_url}/v1/rerank"
        request_body = {
            "model": self._model_uid,
            "documents": documents,
            "query": query,
            "top_n": top_n,
            "max_chunks_per_doc": max_chunks_per_doc,
            "return_documents": return_documents,
            "return_len": return_len,
        }
        request_body.update(kwargs)
        logger.info(f"Rerank query: {query}, model: {self._model_uid}, url: {url}")
        async with aiohttp.ClientSession() as session:
            async with session.post(
                url, json=request_body, headers=self.auth_headers
            ) as response:
                if response.status != 200:
                    response_data = await response.json()
                    raise RuntimeError(
                        f"Failed to rerank documents, detail: {response_data.get('detail', 'Unknown error')}"
                    )
                response_data = await response.json()
                return response_data


if __name__ == "__main__":
    from config.doc_config import rerank_model_name, rerank_api_base, rerank_rate_limit

    model = RerankModel(rerank_model_name, rerank_api_base, {})
    import asyncio
    import uuid

    async def main():
        tasks = [model.arerank(["你好"], str(uuid.uuid4())) for i in range(2000)]
        return await asyncio.gather(*tasks)

    print(asyncio.run(main()))
