from abc import ABC, abstractmethod
from typing import Union

from llama_index.core.llms import LLM

from common.llm.llama_llm import get_llm
from config.doc_config import report_generate_llm_model
from nl2document.common.models.report_generate_model import (
    ReportTextOp,
    ReportSectionConfig,
    TemplateTextOp,
    TemplateSectionConfig,
)

TEXT_OP_TYPE_POLISH = "润色"
TEXT_OP_TYPE_SUMMARY = "总结"

text_op_type_dict = {"数据描述润色": TEXT_OP_TYPE_POLISH, "总结与建议": TEXT_OP_TYPE_SUMMARY}


class TextOpExecutorBase(ABC):
    llm: LLM

    def __init__(self, llm):
        self.llm = llm

    @abstractmethod
    def execute(self, text: str, depends_on, inputs) -> str:
        pass


class PolishTextOpExecutor(TextOpExecutorBase, ABC):
    def __init__(self, llm):
        super().__init__(llm)

    def execute(self, text: str, depends_on, inputs) -> str:
        text_list = ";\n\n\n".join(inputs)
        prompt = f"""
        你是一个数据描述的润色及改写工具，根据提供给你的待润色内容仿照预期格式进行数据描述产出。注意，不可对原内容的数据进行篡改，只允许对文本描述进行润色优化，并遵循以下要求：
        {text}
        
        待润色的内容如下：
        ```
        {text_list}
        ```
        """
        return self.llm.complete(prompt).text


class SummaryTextOpExecutor(TextOpExecutorBase, ABC):
    def __init__(self, llm):
        super().__init__(llm)

    def execute(self, text: str, depends_on, inputs) -> str:
        depends_on = "\n".join(depends_on)
        prompt = f"""
        你是一个经营分析报告的撰写者，我将会给你提供一些必要的数据以及对应的输出要求，帮助你一步一步的生成最终的报告文档。
        经过前几轮的对话，你已经帮我生成了经营分析报告的前几个章节，现在需要你对上文生成最后的总结与建议，前几个章节内容如下：
        {depends_on}
        
        要求：
        {text}
        下面请开始撰写：
        """
        return self.llm.complete(prompt).text


class SectionTextExecutor:
    def __init__(self, llm):
        self.llm = llm

    def executeSection(
        self, section_config: TemplateSectionConfig, text_results, data_time_params
    ) -> str:
        text_result = "\n\n".join(text_results)
        prompt = f"""
        你是一个专业、负责、有深度的企业经营分析人员，正在进行报告撰写，请根据以下需求对下述的文本素材进行整理改写,
        限制字数在{section_config.min_word_len}-{section_config.max_word_len}之间
        注意使用所有数据，不得删减数据内容
        需求:
        ```
        {section_config.section_intention}
        ```
        文本内容:
        ```
        帐期开始时间：{data_time_params.time_range_start}
        帐期结束时间：{data_time_params.time_range_end}
        {text_result}
        ```
"""
        return self.llm.complete(prompt).text


def get_text_op_executor(llm) -> TextOpExecutorBase:
    return PolishTextOpExecutor(llm)


def execute_text_op(
    text_op: Union[ReportTextOp, TemplateTextOp],
    depends_on,
    inputs,
    time_str=None,
    language_style=None,
):
    llm = get_llm(report_generate_llm_model)
    if text_op.type == TEXT_OP_TYPE_POLISH:
        executor = PolishTextOpExecutor(llm)
        filtered_input = [
            text for dataOpId, text in inputs if dataOpId in text_op.input_data_op_ids
        ]
        if time_str is not None:
            filtered_input.insert(0, time_str)
        if language_style is not None:
            filtered_input.insert(0, f"请使用语言风格{language_style}")
    elif text_op.type == TEXT_OP_TYPE_SUMMARY:
        executor = SummaryTextOpExecutor(llm)
        filtered_input = []
    else:
        raise RuntimeError(f"Unknown text op type: {text_op.type}")
    return executor.execute(text_op.prompt, depends_on, filtered_input)


def generate_section_text(
    section_config: TemplateSectionConfig, text_results, data_time_params
):
    llm = get_llm(report_generate_llm_model)
    executor = SectionTextExecutor(llm)
    return executor.executeSection(section_config, text_results, data_time_params)


def section_summary_op(section_config: ReportSectionConfig, input_text: str):
    llm = get_llm(report_generate_llm_model)
    prompt = """你是一个专业、负责、有深度的企业经营分析人员，正在进行报告撰写，请根据以下需求对下述的文本素材进行整理改写,
        限制字数在{min_word}-{max_word}之间
        注意使用所有数据，不得删减数据内容
        需求:
        ```
        {intention}
        ```
        文本内容:
        ```
        {input}
        ```
        """
    response = llm.complete(
        prompt.format(
            intention=section_config.section_intention,
            input=input_text,
            min_word=section_config.min_word_len,
            max_word=section_config.max_word_len,
        )
    )
    return response.text
