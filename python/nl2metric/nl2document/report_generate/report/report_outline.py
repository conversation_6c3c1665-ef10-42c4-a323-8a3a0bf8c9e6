import copy
import datetime
import json
import os
import random
import tempfile
import time
import traceback
from abc import abstractmethod, ABC
from concurrent.futures import ThreadPoolExecutor
from typing import List
from datetime import datetime

import pandas as pd
from cachetools import LRUCache
from sqlalchemy import inspect

from common.fs.fs import get_s3_file_system
from common.fs.storage import create_s3_client
from common.logging.logger import get_logger
from common.utils.concurrent_utils import run_concurrently
from common.utils.string_utils import class_to_dict
from nl2document.common.models.report_generate_model import (
    get_report_by_id,
    get_report_filter_params,
    get_report_data_time_params,
    check_data_filter_params_equal,
    check_data_time_params_equal,
    Report,
    save_or_create_report,
    save_or_create_column_filter_values,
    save_or_create_data_time_params_msg,
    copy_section_config,
    copy_data_section_op,
    copy_text_op,
    get_report_section_config,
    get_report_section_data_op,
    get_report_section_text_op,
    save_or_create_report_section_config,
    get_report_data_op,
    get_report_text_op,
    get_report_section_config_list,
    create_report_text_op,
    create_report_data_op,
    create_report_section_config,
    create_report_data_filter_params,
    create_report_data_time_params,
    ReportDataTimeParams,
    ReportDataFilterParams,
    ReportSectionDataOp,
    ReportTextOp,
    ReportSectionConfig,
    get_template_by_id,
    get_template_filter_params,
    get_template_data_time_params,
    get_data_op_by_id,
    save_or_create_data_operator,
    save_or_create_text_operator,
    create_or_update_template,
    get_template_section_config,
    get_template_section_config_with_section_id,
    save_or_create_template_section_config,
    get_template_section_data_op,
    get_template_section_text_op,
    get_template_section_data_op_with_section_id,
    TemplateTextOp,
    TemplateSectionConfig,
    get_report_template_by_id,
    save_and_create_template,
    ReportTemplate,
    Template,
    TemplateDataTimeParams,
    save_or_create_data_time_params_msg_for_report,
    save_or_create_column_filter_values_for_report,
    delete_report_template_by_id,
    save_or_create_template_data_time_params_for_report,
)
from nl2document.common.msg.report_generate_msg import (
    CreateOrSaveOutlineRequest,
    OutlineNode,
    ReportDetailInfo,
    ReportSectionConfigData,
    ExportReportData,
    TemplateDetailInfo,
    CreateOrUpdateDataOpRequest,
    CreateOrUpdateTextOpRequest,
    UpdateTemplateOutlineRequest,
    UpdateTemplateSectionRequest,
    TemplateSectionConfigData,
    TemplateSectionOperatorsData,
    SaveTemplateRequest,
    CreateNewTemplateRequest,
    DeleteTemplateRequest,
    CreateAuthTemplateReportRequest,
)
from nl2document.common.utils import save_to_pdf, save_to_docx
from nl2document.report_generate.report.data_op_executor import (
    generate_report_section,
    generate_report_section_for_update,
    process_data_op,
    process_text_op,
    exec_data_op,
    generate_template_section,
    generate_template_section_for_update,
    ALL_INDUSTRIES_NATIONWIDE_TEMPLATE_ID,
    SINGLE_INDUSTRY_NATIONWIDE_TEMPLATE_ID,
    ALL_INDUSTRIES_PROVINCE_TEMPLATE_ID,
    SINGLE_INDUSTRY_PROVINCE_TEMPLATE_ID,
)
from nl2document.report_generate.report.graph_executor import (
    build_dependency_graph,
    topological_sort,
)
from nl2document.report_generate.report.text_op_executor import (
    TEXT_OP_TYPE_SUMMARY,
    TEXT_OP_TYPE_POLISH,
    execute_text_op,
)

logger = get_logger(__name__)

# LRUCache
cache = LRUCache(maxsize=128)


def serialize_req(req: CreateOrSaveOutlineRequest):
    """
    Converts the req object into a JSON string for caching.
    Ensures that lists and nested objects are serialized properly.
    """
    req.reportTitle = ""
    req.reportId = 0
    req.reportIntention = ""
    return json.dumps(req, sort_keys=True, default=str)


def get_cached_outline_nodes(req: CreateOrSaveOutlineRequest):
    req_key = serialize_req(req)  # Convert req to a hashable string
    if req_key in cache:
        logger.info(f"Cache hit for req: {req_key}")
        return cache[req_key]
    return None


def set_cached_outline_nodes(req: CreateOrSaveOutlineRequest, outline_nodes):
    req_key = serialize_req(req)  # Convert req to a hashable string
    cache[req_key] = outline_nodes


class OutlineGeneratorBase(ABC):
    def __init__(self):
        pass

    @abstractmethod
    def generate_outline(self):
        pass

    @abstractmethod
    def parse_template_outline(self):
        pass


def get_outline_generator() -> OutlineGeneratorBase:
    return JingfenReportOutlineGenerator()


class JingfenReportOutlineGenerator(OutlineGeneratorBase):
    def __init__(self):
        super().__init__()

    def generate_outline(self):
        return [
            OutlineNode(
                id=1,
                title="一、整体签约情况",
                content="",
                allowChildren=False,
                maxChildrenCount=0,
                children=None,
            ),
            OutlineNode(
                id=2,
                title="二、分行业签约",
                content="",
                allowChildren=False,
                maxChildrenCount=0,
                children=None,
            ),
            OutlineNode(
                id=3,
                title="三、分省签约",
                content="",
                allowChildren=False,
                maxChildrenCount=0,
                children=None,
            ),
            OutlineNode(
                id=4,
                title="四、总结及建议",
                content="",
                allowChildren=False,
                maxChildrenCount=0,
                children=None,
            ),
        ]

    def parse_template_outline(self):
        pass


def get_node_section_id(section_list: list, nodes: list[OutlineNode]) -> list:
    for node in nodes:
        section_list.append(node)
        if node.children is not None:
            section_list = get_node_section_id(section_list, node.children)
    return section_list


def write_section_node_content(
    outline_nodes: list[OutlineNode], results: dict, depth: int = 2
):
    for node in outline_nodes:
        content = results[node.id].result()
        content = content.replace("\n", "<br/>")
        node.content = f"{'#' * depth} {node.title} <br/><br/> {content}"
        if node.children is not None:
            write_section_node_content(node.children, results, depth + 1)


def write_one_section_node_content(
    outline_nodes: list[OutlineNode], node_id: int, content: str
):
    content = content.replace("\n", "<br/>")
    for node in outline_nodes:
        if node.id == node_id:
            node.content = content
            return True  # 找到并更新后可以终止递归
        # 如果有子节点，递归查找子节点
        if node.children:
            found = update_node_title(node.children, node_id, content)
            if found:  # 如果在子节点中找到并更新了，就可以结束
                return True


def update_node_title(nodes: list[OutlineNode], section_id, new_title):
    # 递归遍历节点
    for node in nodes:
        # 检查当前节点是否匹配
        if node.id == section_id:
            node.title = new_title
            return True  # 找到并更新后可以终止递归
        # 如果有子节点，递归查找子节点
        if node.children:
            found = update_node_title(node.children, section_id, new_title)
            if found:  # 如果在子节点中找到并更新了，就可以结束
                return True
    return False  # 如果没有找到匹配的节点


def add_depends_to_node(text_ops: list[TemplateTextOp], node: OutlineNode):
    for text_op in text_ops:
        if text_op.type == TEXT_OP_TYPE_SUMMARY:
            if node.dependsOn:
                node.dependsOn.extend(text_op.input_section_ids)
            else:
                node.dependsOn = text_op.input_section_ids


def process_nodes_depends(template_id, nodes: list[OutlineNode]):
    for node in nodes:
        section_id = node.id
        template_section_config = get_template_section_config_with_section_id(
            template_id, section_id
        )
        template_section_text_op = get_template_section_text_op(template_id, section_id)
        if template_section_config.text_op_ids:
            text_ops = [
                text_op
                for text_op in template_section_text_op
                if text_op.text_op_id in template_section_config.text_op_ids
            ]
            add_depends_to_node(text_ops, node)


def save_or_create_outline(req: CreateOrSaveOutlineRequest):
    # check if report exists
    template_id = req.templateId
    template = get_template_by_id(template_id)

    outline_nodes = [OutlineNode(**node) for node in template.outline]
    if req.reportId is not None:
        report = get_report_by_id(req.reportId)
        if report is None:
            raise Exception("report not found")
        report.name = req.reportTitle
        report.intention = req.reportIntention
    else:
        report = Report(
            name=req.reportTitle,
            intention=req.reportIntention,
            model_name=template.model_name,
            template_id=template.template_id,
            outline=class_to_dict(outline_nodes),
            creator=req.creator,
            scene_id=req.sceneId,
        )
        report.id = save_or_create_report(report)

    save_or_create_data_time_params_msg_for_report(report.id, req.dataTimeParams)
    save_or_create_column_filter_values_for_report(report.id, req.dataFilterParams)

    dataTimeParams = TemplateDataTimeParams(
        time_range_start=datetime.strptime(
            req.dataTimeParams.timeRangeStart, "%Y-%m-%d"
        ),
        time_range_end=datetime.strptime(req.dataTimeParams.timeRangeEnd, "%Y-%m-%d"),
        time_column=req.dataTimeParams.timeColumn,
    )

    # tasks = [
    #     lambda: copy_section_config(report.id, req.templateId),
    #     lambda: copy_data_section_op(report.id, req.templateId),
    #     lambda: copy_text_op(report.id, req.templateId),
    # ]
    # run_concurrently(tasks, max_workers=3)
    # save data params
    # save_or_create_column_filter_values(report.id, filterParams)
    #
    # save_or_create_data_time_params_msg(report.id, dataTimeParams)

    process_nodes_depends(template_id, outline_nodes)
    section_list = get_node_section_id([], outline_nodes)
    graph, in_degree = build_dependency_graph(section_list)
    execution_order = topological_sort(section_list, graph, in_degree)
    node_map = {node.id: node for node in section_list}
    results = {}  # 用于存储每个节点的 Future 对象
    logger.info(f"execution_order: {execution_order}")
    with ThreadPoolExecutor(max_workers=5) as executor:
        for node_id in execution_order:
            node = node_map[node_id]
            # 提交任务，并将 Future 存储在 results 字典中
            future = executor.submit(
                generate_report_section,
                template,
                dataTimeParams,
                node,
                results,
                language_style=req.languageStyle,
            )
            results[node_id] = future
        for node_id, future in results.items():
            try:
                result = future.result()  # 获取任务结果
                logger.info(
                    f"Node {node_id} executed successfully with result: {result}"
                )
            except Exception as e:
                logger.info(f"Node {node_id} execution failed with exception: {e}")
                logger.error("Stack trace: %s", traceback.format_exc())
                raise e
        write_section_node_content(outline_nodes, results)
    report.outline = class_to_dict(outline_nodes)
    set_cached_outline_nodes(req, outline_nodes)
    save_or_create_report(report)
    return report.id, outline_nodes

    # outline_generator = get_outline_generator()
    # report_id = req.reportId
    # report = None
    # filterParams = None
    # dataTimeParams = None
    # outline_nodes: list[OutlineNode] = []
    # if report_id is not None:
    #     report = get_report_by_id(report_id)
    #     if report is None:
    #         raise Exception("report not found")
    #     filterParams, dataTimeParams = run_concurrently(
    #         [
    #             lambda: get_report_filter_params(report_id),
    #             lambda: get_report_data_time_params(report_id)
    #         ]
    #     )
    #     outline_nodes = [
    #         OutlineNode(**node)
    #         for node in report.outline
    #     ]
    # # check intention is same or not
    # # if same, update filter params
    # if report is None or (report.intention != req.reportIntention):
    #     temp_outline_nodes = outline_generator.generate_outline()
    #     if report:
    #         for node, temp_node in zip(outline_nodes, temp_outline_nodes):
    #             node.title = temp_node.title
    #     else:
    #         outline_nodes = temp_outline_nodes
    # if report is None:
    #     report = Report(
    #         name=req.reportTitle,
    #         intention=req.reportIntention,
    #         model_name=req.modelName,
    #         template_id=req.templateId,
    #         outline=class_to_dict(outline_nodes),
    #         creator=req.creator,
    #         scene_id=req.sceneId,
    #     )
    #     # 保存报告业务筛选口径
    #     #  report_section_config copy from template
    #     #  report_section_data_op copy from template
    #     # 保存报告时间参数
    #     report.id = save_or_create_report(report)
    #     tasks = [
    #         lambda: copy_section_config(report.id, req.templateId),
    #         lambda: copy_data_section_op(report.id, req.templateId),
    #         lambda: copy_text_op(report.id, req.templateId),
    #     ]
    #     run_concurrently(tasks, max_workers=3)
    # # save data params
    # if req.dataFilterParams is not None:
    #     save_or_create_column_filter_values(report.id, req.dataFilterParams)
    # if req.dataTimeParams is not None:
    #     save_or_create_data_time_params_msg(report.id, req.dataTimeParams)
    #
    # # update filter params
    # cache_outline_nodes = get_cached_outline_nodes(req)
    # if cache_outline_nodes:
    #     outline_nodes = cache_outline_nodes
    #     report.outline = class_to_dict(outline_nodes)
    #     # sleep random in 50 - 70s
    #     time.sleep(random.randint(50, 70))
    #     save_or_create_report(report)
    # else:
    #     if (not check_data_filter_params_equal(req.dataFilterParams, filterParams)
    #             or not check_data_time_params_equal(req.dataTimeParams, dataTimeParams)):
    #         process_nodes_depends(report.id, outline_nodes)
    #         section_list = get_node_section_id([], outline_nodes)
    #         graph, in_degree = build_dependency_graph(section_list)
    #         execution_order = topological_sort(section_list, graph, in_degree)
    #         node_map = {node.id: node for node in section_list}
    #         results = {}  # 用于存储每个节点的 Future 对象
    #         logger.info(f"execution_order: {execution_order}")
    #         with ThreadPoolExecutor(max_workers=5) as executor:
    #             for node_id in execution_order:
    #                 node = node_map[node_id]
    #                 # 提交任务，并将 Future 存储在 results 字典中
    #                 future = executor.submit(generate_report_section, report, node, results)
    #                 results[node_id] = future
    #             for node_id, future in results.items():
    #                 try:
    #                     result = future.result()  # 获取任务结果
    #                     logger.info(f"Node {node_id} executed successfully with result: {result}")
    #                 except Exception as e:
    #                     logger.info(f"Node {node_id} execution failed with exception: {e}")
    #                     logger.error("Stack trace: %s", traceback.format_exc())
    #             write_section_node_content(outline_nodes, results)
    #         report.outline = class_to_dict(outline_nodes)
    #         set_cached_outline_nodes(req, outline_nodes)
    #         save_or_create_report(report)
    # return report.id, outline_nodes


def create_or_update_data_op(req: CreateOrUpdateDataOpRequest):
    # update data op
    request_data_op = req.dataOp
    template_data_op = process_data_op(request_data_op)
    if req.result:
        template_data_op.result = req.result
    return save_or_create_data_operator(template_data_op)


def get_data_operator_preview(req: CreateOrUpdateDataOpRequest) -> pd.DataFrame:
    data_op_class = req.dataOp
    template = get_template_by_id(data_op_class.templateId)
    data_time_params = get_template_data_time_params(data_op_class.templateId)
    data_filter_params = get_template_filter_params(data_op_class.templateId)
    data_op = process_data_op(data_op_class)
    return exec_data_op(
        template.model_name, data_time_params, data_filter_params, data_op, True
    )


def get_text_operator_preview(req: CreateOrUpdateTextOpRequest):
    # update data op
    text_op_class = req.textOp
    template = get_template_by_id(text_op_class.templateId)
    data_filter_params, data_time_params = run_concurrently(
        [
            lambda: get_template_filter_params(text_op_class.templateId),
            lambda: get_template_data_time_params(text_op_class.templateId),
        ]
    )

    if text_op_class.type == TEXT_OP_TYPE_POLISH:
        template_section_data_op = get_template_section_data_op_with_section_id(
            text_op_class.templateId, text_op_class.sectionId
        )
        data_op_dict = {
            data_op.data_op_id: data_op for data_op in template_section_data_op
        }
        tasks = []
        text_list = []

        for data_op_id in text_op_class.inputDataOpIds:
            tasks.append(
                lambda _data_op_id=data_op_id: exec_data_op(
                    model_name=template.model_name,
                    data_time_params=data_time_params,
                    data_filter_params=data_filter_params,
                    report_section_data_op=data_op_dict[_data_op_id],
                )
            )
        text_list = run_concurrently(tasks, max_workers=5, timeout_seconds=300)

        text_op = process_text_op(text_op_class)
        result_text = execute_text_op(text_op, [], text_list)
        return result_text
    elif text_op_class.type == TEXT_OP_TYPE_SUMMARY:
        outline_nodes = [OutlineNode(**node) for node in template.outline]
        # process_nodes_depends(text_op_class.templateId, outline_nodes)
        # section_list = get_node_section_id([], outline_nodes)
        # graph, in_degree = build_dependency_graph(section_list)
        # execution_order = topological_sort(section_list, graph, in_degree)
        # node_map = {node.id: node for node in section_list}
        # results = {}  # 用于存储每个节点的 Future 对象
        # logger.info(f"execution_order: {execution_order}")
        # with ThreadPoolExecutor(max_workers=5) as executor:
        #     for node_id in execution_order:
        #         if node_id != text_op_class.sectionId:
        #             node = node_map[node_id]
        #             # 提交任务，并将 Future 存储在 results 字典中
        #             future = executor.submit(generate_template_section, template, node, results)
        #             results[node_id] = future
        depends_on_results = [
            node.content
            for node in outline_nodes
            if node.id in text_op_class.inputSectionIds
        ]
        text_op = process_text_op(text_op_class)
        result_text = execute_text_op(text_op, depends_on_results, [])
        return result_text


def create_or_update_text_op(req: CreateOrUpdateTextOpRequest):
    # update data op
    request_text_op = req.textOp
    template_data_op = process_text_op(request_text_op)
    if req.resultText:
        template_data_op.result = req.resultText
    return save_or_create_text_operator(template_data_op)


def update_template_section(req: UpdateTemplateSectionRequest) -> str:
    template = get_template_by_id(req.templateId)
    if template is None:
        raise Exception(f"template {req.templateId} not found")
    section_config = get_template_section_config_with_section_id(
        req.templateId, req.sectionId
    )
    if section_config is None:
        section_config = TemplateSectionConfig(
            template_id=req.templateId,
            section_intention=req.sectionIntention,
            section_id=req.sectionId,
            max_word_len=req.maxWordLen,
            min_word_len=req.minWordLen,
            data_op_ids=req.dataOpList,
            text_op_ids=req.textOpList,
        )
    else:
        section_config.section_intention = req.sectionIntention
        section_config.max_word_len = req.maxWordLen
        section_config.min_word_len = req.minWordLen
        section_config.data_op_ids = req.dataOpList
        section_config.text_op_ids = req.textOpList
    # update section config
    save_or_create_template_section_config([section_config])
    if req.isPreview:
        nodes = [OutlineNode(**node) for node in template.outline]

        result_text = generate_template_section_for_update(
            template, nodes, req.sectionId
        )

        write_one_section_node_content(nodes, req.sectionId, result_text)
        template.outline = class_to_dict(nodes)
        create_or_update_template(template)
        report_template = get_report_template_by_id(req.templateId)
        report_template.outline = class_to_dict(nodes)
        save_and_create_template(report_template)
        return result_text.replace("\n", "<br/>")
    return ""


def update_template_outline(req: UpdateTemplateOutlineRequest):
    template = get_template_by_id(req.templateId)
    if template is None:
        raise Exception(f"template {req.templateId} not found")
    template.outline = class_to_dict(req.outline)
    return create_or_update_template(template)


def save_template_to_db(req: SaveTemplateRequest):
    template = get_template_by_id(req.templateId)
    report_template = get_report_template_by_id(req.templateId)

    if template is None:
        template = Template(
            template_id=report_template.id,
        )

    if req.dataFilterParams is not None:
        save_or_create_column_filter_values(req.templateId, req.dataFilterParams)

    if req.dataTimeParams is not None:
        save_or_create_data_time_params_msg(req.templateId, req.dataTimeParams)

    template.name = req.templateTitle
    template.intention = req.templateIntention
    template.model_name = req.modelName
    template.creator = req.creator
    template.scene_id = req.sceneId

    create_or_update_template(template)

    if template.outline:
        outline_nodes = [OutlineNode(**node) for node in template.outline]
        report_template.outline = class_to_dict(outline_nodes)

    report_template.name = req.templateTitle

    save_and_create_template(report_template)


def create_new_template(req: CreateNewTemplateRequest):
    report_template = ReportTemplate(name=req.name, create_user=req.createUser)
    template_id = save_and_create_template(report_template)
    template = Template(
        template_id=template_id,
        name=req.name,
        intention="",
        model_name="",
        scene_id="",
        creator=req.createUser,
    )
    create_or_update_template(template)
    return template_id


def delete_template(req: DeleteTemplateRequest):
    template_id = req.templateId
    delete_report_template_by_id(template_id)


def get_report_detail_info(report_id: int) -> ReportDetailInfo:
    report_meta_info = get_report_by_id(report_id)
    filter_params = get_report_filter_params(report_id)
    data_time_params = get_report_data_time_params(report_id)
    outlineNodes = [OutlineNode(**node) for node in report_meta_info.outline]
    return ReportDetailInfo(
        reportInfo=report_meta_info.to_report_meta_info_class(),
        dataFilterParams=[
            _filter.to_column_filter_values_class() for _filter in filter_params
        ],
        dataTimeParams=data_time_params.to_data_time_params_class()
        if data_time_params
        else None,
        outlineNodes=outlineNodes,
    )


def get_template_detail_info(template_id: int) -> TemplateDetailInfo:
    template_meta_info = get_template_by_id(template_id)
    if template_meta_info is None:
        return TemplateDetailInfo()
    filter_params = get_template_filter_params(template_id)
    data_time_params = get_template_data_time_params(template_id)
    if template_meta_info.outline:
        outlineNodes = [OutlineNode(**node) for node in template_meta_info.outline]
    else:
        outlineNodes = []
    return TemplateDetailInfo(
        templateInfo=template_meta_info.to_template_meta_info_class(),
        dataFilterParams=[
            _filter.to_column_filter_values_class() for _filter in filter_params
        ],
        dataTimeParams=data_time_params.to_data_time_params_class()
        if data_time_params
        else None,
        outlineNodes=outlineNodes,
    )


def get_report_section_config_info(
    report_id: int, section_id: int
) -> ReportSectionConfigData:
    section_config = get_report_section_config(report_id, section_id)
    data_op_list = get_report_section_data_op(report_id, section_id)
    text_op_list = get_report_section_text_op(report_id, section_id)
    return ReportSectionConfigData(
        reportId=report_id,
        sectionId=section_id,
        sectionIntention=section_config.section_intention,
        maxWordLen=section_config.max_word_len,
        minWordLen=section_config.min_word_len,
        dataOp=[data_op.to_report_section_data_op_class() for data_op in data_op_list],
        textOp=[text_op.to_text_op_class() for text_op in text_op_list],
    )


def get_template_section_config_info(
    template_id: int, section_id: int
) -> TemplateSectionConfigData | None:
    section_config = get_template_section_config_with_section_id(
        template_id, section_id
    )
    if section_config is None:
        return None
    data_op_list = get_template_section_data_op_with_section_id(template_id, section_id)
    data_op_dict = {data_op.data_op_id: data_op for data_op in data_op_list}
    dataOp = []
    textOp = []

    if section_config.data_op_ids:
        dataOp = [
            data_op_dict[data_op].to_template_data_op_class()
            for data_op in section_config.data_op_ids
        ]

    text_op_list = get_template_section_text_op(template_id, section_id)
    text_op_dict = {text_op.text_op_id: text_op for text_op in text_op_list}
    if section_config.text_op_ids:
        textOp = [
            text_op_dict[text_op].to_text_op_class()
            for text_op in section_config.text_op_ids
        ]

    return TemplateSectionConfigData(
        templateId=template_id,
        sectionId=section_id,
        sectionIntention=section_config.section_intention,
        maxWordLen=section_config.max_word_len,
        minWordLen=section_config.min_word_len,
        dataOp=dataOp,
        textOp=textOp,
    )


def get_template_section_operators_info(
    template_id: int, section_id: int
) -> TemplateSectionOperatorsData:
    data_op_list = get_template_section_data_op_with_section_id(template_id, section_id)
    text_op_list = get_template_section_text_op(template_id, section_id)
    return TemplateSectionOperatorsData(
        dataOp=[data_op.to_template_data_op_class() for data_op in data_op_list],
        textOp=[text_op.to_text_op_class() for text_op in text_op_list],
    )


# 深度优先遍历函数
def dfs_collect_content(nodes: List[OutlineNode]) -> List[str]:
    contents = []

    def dfs(node: OutlineNode):
        # 将当前节点的 content 添加到列表中
        contents.append(node.content)
        # 如果有子节点，递归遍历每个子节点
        if node.children:
            for child in node.children:
                dfs(child)

    # 遍历每个根节点，执行 DFS
    for node in nodes:
        dfs(node)

    return contents


def export_report_data(report_id: int) -> ExportReportData:
    report = get_report_by_id(report_id)
    if report is None:
        raise Exception(f"report {report_id} not found")
    outline_nodes = [OutlineNode(**node) for node in report.outline]
    contents = dfs_collect_content(outline_nodes)
    final_report = "\n".join(contents)
    with tempfile.TemporaryDirectory() as temp_dir:
        pdf_path = os.path.join(temp_dir, "report.pdf")
        word_path = os.path.join(temp_dir, "report.docx")
        remote_pdf_url = f"report_generate/doc_{report_id}/report.pdf"
        remote_docx_url = f"analysis_report/doc_{report_id}/report.docx"
        save_to_pdf(pdf_path, final_report)
        create_s3_client().upload_file(
            pdf_path,
            "ask-doc",
            remote_pdf_url,
        )
        save_to_docx(word_path, final_report)
        create_s3_client().upload_file(
            word_path,
            "ask-doc",
            remote_docx_url,
        )
        return ExportReportData(
            pdfUrl=get_s3_file_system().url("ask-doc/" + remote_pdf_url),
            wordUrl=get_s3_file_system().url("ask-doc/" + remote_docx_url),
        )


def copy_report(report_id: int):
    report = get_report_by_id(report_id)
    new_report = Report(
        name=f"{report.name}-{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}-副本",
        intention=report.intention,
        template_id=report.template_id,
        model_name=report.model_name,
        outline=report.outline,
        status=report.status,
        scene_id=report.scene_id,
        creator=report.creator,
    )
    new_report_id = save_or_create_report(new_report)

    data_time_params = get_report_data_time_params(report_id)
    data_time_params_new = ReportDataTimeParams(
        report_id=new_report_id,
        time_column=data_time_params.time_column,
        time_range_start=data_time_params.time_range_start,
        time_range_end=data_time_params.time_range_end,
    )
    create_report_data_time_params(data_time_params_new)

    data_filter_params = get_report_filter_params(report_id)
    data_filter_params_list_new = []
    for _filter in data_filter_params:
        data_filter_params_list_new.append(
            ReportDataFilterParams(
                report_id=new_report_id,
                column_name=_filter.column_name,
                column_code=_filter.column_code,
                operator=_filter.operator,
                filter_values=_filter.filter_values,
            )
        )
    create_report_data_filter_params(data_filter_params_list_new)

    data_op_list = get_report_data_op(report_id)
    data_op_list_new = []
    for data_op in data_op_list:
        data_op_new = ReportSectionDataOp(
            report_id=new_report_id,
            section_id=data_op.section_id,
            data_op_id=data_op.data_op_id,
            name=data_op.name,
            compute_type=data_op.compute_type,
            operator=data_op.operator,
            operator_desc=data_op.operator_desc,
            metric=data_op.metric,
            group_by=data_op.group_by,
            data_filter=data_op.data_filter,
            data_desc_template=data_op.data_desc_template,
            output_data_section_params=data_op.output_data_section_params,
            output_order_by=data_op.output_order_by,
            output_limit=data_op.output_limit,
            time_granularity=data_op.time_granularity,
            time_range_start=data_op.time_range_start,
            time_range_end=data_op.time_range_end,
        )
        data_op_list_new.append(data_op_new)
    create_report_data_op(data_op_list_new)

    text_op_list = get_report_text_op(report_id)
    text_op_list_new = []
    for text_op in text_op_list:
        text_op_new = ReportTextOp(
            report_id=new_report_id,
            section_id=text_op.section_id,
            text_op_id=text_op.text_op_id,
            name=text_op.name,
            prompt=text_op.prompt,
        )
        text_op_list_new.append(text_op_new)

    create_report_text_op(text_op_list_new)

    report_section_config = get_report_section_config_list(report_id)
    report_section_config_new_list = []
    for section_config in report_section_config:
        section_config_new = ReportSectionConfig(
            report_id=new_report_id,
            section_id=section_config.section_id,
            section_intention=section_config.section_intention,
            max_word_len=section_config.max_word_len,
            min_word_len=section_config.min_word_len,
            data_op_ids=section_config.data_op_ids,
            text_op_ids=section_config.text_op_ids,
        )
        report_section_config_new_list.append(section_config_new)
    create_report_section_config(report_section_config_new_list)
