import calendar
from concurrent.futures import Future
from datetime import datetime
from functools import partial
from typing import Dict, Union, Any

import numpy as np
import pandas as pd
from dateutil.relativedelta import relativedelta

from biz.cmcc_jingfen_report_biz.report_config import (
    report_province_name,
    report_department_name,
)
from common.utils.concurrent_utils import run_concurrently
from config.doc_config import ppt_hidden_dimension_values

from nl2document.common.models.report_generate_model import (
    ReportSectionDataOp,
    ReportDataTimeParams,
    ReportDataFilterParams,
    Report,
    get_report_data_time_params,
    get_report_filter_params,
    get_report_section_data_op,
    get_report_section_text_op,
    get_report_section_config,
    TemplateDataOp,
    TemplateTextOp,
    Template,
    TemplateDataTimeParams,
    TemplateDataFilterParams,
    get_template_filter_params,
    get_template_data_time_params,
    get_template_section_data_op_with_section_id,
    get_template_section_text_op,
    get_template_section_config_with_section_id,
)
from nl2document.common.msg.report_generate_msg import (
    OutlineNode,
    TemplateDataOpClass,
    TemplateTextOpClass,
)
from nl2document.report_generate.report.text_op_executor import (
    execute_text_op,
    section_summary_op,
    generate_section_text,
)
from nl2document.report_generate.xengine_data.model_data import get_data_by_model

OPTYPE_COUNT = "总计"
OPTYPE_COUNT_MOM = "总计环比"
OPTYPE_COUNT_YOY = "总计同比"
OPTYPE_COUNT_LAST_YOY = "总计去年同期同比"
OPTYPE_COUNT_YOY_WAVE = "总计同比波动"
OPTYPE_PERCENTAGE = "占比"
OPTYPE_PERCENTAGE_CHANGE = "占比变化"
OPTYPE_PERCENTAGE_CHANGE_WAVE = "占比变化波动"
OPTYPE_PPT_GRAPH_OP_1 = "ppt图模版1"
OPTYPE_PPT_GRAPH_OP_2 = "ppt图模版2"
OPTYPE_YOY_RANK = "同比排名"
OPTYPE_MOM_RANK = "环比排名"
OPTYPE_BIG_DEAL = "大单签约"
OP_SUM = "sum"
OP_COUNT = "count"
OP_COUNT_DISTINCT = "count_distinct"

ALL_INDUSTRIES_NATIONWIDE_TEMPLATE_ID = -1
SINGLE_INDUSTRY_NATIONWIDE_TEMPLATE_ID = -2
ALL_INDUSTRIES_PROVINCE_TEMPLATE_ID = -3
SINGLE_INDUSTRY_PROVINCE_TEMPLATE_ID = -4

compute_type_dict = {
    "聚合": OPTYPE_COUNT,
    "聚合环比": OPTYPE_COUNT_MOM,
    "聚合同比": OPTYPE_COUNT_YOY,
    "同比波动": OPTYPE_COUNT_YOY_WAVE,
    "同比排名": OPTYPE_YOY_RANK,
    "环比排名": OPTYPE_MOM_RANK,
    "大单签约": OPTYPE_BIG_DEAL,
}


op_type_dict = {
    "求和": OP_SUM,
    "计数": OP_COUNT,
    "去重计数": OP_COUNT_DISTINCT,
}

time_granularity_dict = {
    "年": "year",
    "半年": "half year",
    "季": "quarter",
    "月": "month",
}

hidden_dimension_values_list = ppt_hidden_dimension_values.split(";")


def convert_units(value, ppt_mode=False):
    """
    将数值转换为适当的单位。
    - 如果数值大于等于 1 亿，转换为以亿为单位并保留两位小数。
    - 如果数值大于等于 1 万且小于 1 亿，转换为以万为单位并保留两位小数。
    - 其他情况不进行转换。
    """
    try:
        value = float(value)
    except:
        return value
    if ppt_mode:
        if value >= 1e8:
            return f"{value / 1e8:.1f} 亿"
        elif value >= 1e4:
            return f"{value / 1e4:.1f} 万"
        elif value <= (-1) * 1e8:
            return f"{value / 1e8:.1f} 亿"
        elif value <= (-1) * 1e4:
            return f"{value / 1e4:.1f} 万"
        else:
            return f"{value:.1f}"
    else:
        if value >= 1e8:
            return f"{value / 1e8:.2f} 亿"
        elif value >= 1e4:
            return f"{value / 1e4:.2f} 万"
        elif value <= (-1) * 1e8:
            return f"{value / 1e8:.2f} 亿"
        elif value <= (-1) * 1e4:
            return f"{value / 1e4:.2f} 万"
        else:
            return f"{value:.2f}"


def get_node_depth(nodes: list[OutlineNode], section_id: int, depth: int = 2):
    for node in nodes:
        if node.id == section_id:
            return node, depth  # 找到节点，返回当前深度

        # 如果有子节点，递归查找深度
        if node.children:
            temp_node, child_depth = get_node_depth(
                node.children, section_id, depth + 1
            )
            if child_depth != -1:  # 如果找到，则返回深度
                return node, child_depth

    return None, -1  # 没找到返回-1


"""
- 计算类型：总计
- 算子说明：目标时间范围、特定分组和条件下的汇总值
- 目标度量：签约金额
- 算子：sum
- 分组：/
- 过滤：/
- 目标时间范围：2024.1.1-2024.3.31 
- 时间粒度：年
- 取值范围：total



"""


def calculate_changes(
    group_df,
    report_section_data_op: ReportSectionDataOp,
    current_time_start,
    current_time_end,
):
    prev_time_start, prev_time_end = get_previous_period(
        pd.Timestamp(current_time_start),
        pd.Timestamp(current_time_end),
        report_section_data_op.time_granularity,
        report_section_data_op.compute_type,
    )
    metric = report_section_data_op.metric
    time_column = report_section_data_op.time_column

    # 当前时间周期的数据
    df_current = group_df[
        (group_df[time_column] >= current_time_start)
        & (group_df[time_column] <= current_time_end)
    ]
    current_sum = df_current[metric].sum()

    # 上一个时间周期的数据
    df_prev = group_df[
        (group_df[time_column] >= prev_time_start)
        & (group_df[time_column] <= prev_time_end)
    ]
    prev_sum = df_prev[metric].sum()

    # 计算环比变化率
    if prev_sum != 0:
        change = (current_sum - prev_sum) / prev_sum
    else:
        change = None  # 防止除以0的情况

    return pd.Series(
        {
            # '当前时间周期总和': current_sum,
            # '上一个时间周期总和': prev_sum,
            "增长率": change
        }
    )


def get_previous_period(
    start_date: pd.Timestamp, end_date: pd.Timestamp, freq: str, compute_type: str
):
    def is_last_day(date):
        return date.day == calendar.monthrange(date.year, date.month)[1]

    # 定义时间偏移量字典
    offset_map = {
        "M": relativedelta(months=1),
        "month": relativedelta(months=1),
        "Q": relativedelta(months=3),
        "quarter": relativedelta(months=3),
        "Y": relativedelta(years=1),
        "year": relativedelta(years=1),
        "half year": relativedelta(months=6),
    }

    # 根据计算类型选择时间偏移量
    if compute_type in ("同比", "总计同比波动", "总计同比"):
        offset = relativedelta(years=1)
    elif compute_type in ("环比", "总计环比"):
        offset = offset_map.get(freq, relativedelta(years=1))
    else:
        # 默认都是同比，也就是去年同期
        offset = relativedelta(months=12)

    if offset is None:
        raise ValueError("Unsupported frequency")

    # 计算上一个时间周期的开始和结束时间
    prev_start = start_date - offset
    prev_end = end_date - offset

    # 处理月末特殊情况
    if is_last_day(end_date):
        prev_end = prev_end.replace(
            day=calendar.monthrange(prev_end.year, prev_end.month)[1]
        )

    return prev_start.to_pydatetime(), prev_end.to_pydatetime()


def calculate_percentage_groupby(
    group_df, report_section_data_op, current_time_start, current_time_end
):
    metric = report_section_data_op.metric
    time_column = report_section_data_op.time_column
    df_current = group_df[
        (group_df[time_column] >= current_time_start)
        & (group_df[time_column] <= current_time_end)
    ]
    current_sum = df_current[metric].sum()
    if report_section_data_op.percentage_filter and isinstance(
        report_section_data_op.percentage_filter, list
    ):
        for percentage_filter in report_section_data_op.percentage_filter:
            # column_name = 业务类型
            # filter_values = [ICT业务]
            if (
                isinstance(percentage_filter, dict)
                and "columnName" in percentage_filter
                and "values" in percentage_filter
            ):
                df_current = df_current[
                    df_current[percentage_filter["columnName"]].isin(
                        percentage_filter["values"]
                    )
                ]
    target_sum = df_current[metric].sum()
    if current_sum != 0:
        percentage = target_sum / current_sum
    else:
        percentage = 0

    return pd.Series(
        {
            # '当前时间周期总和': current_sum,
            # '上一个时间周期总和': prev_sum,
            "份额": percentage
        }
    )


def calculate_percentage_change(df, report_section_data_op, time_column):
    prev_time_start, prev_time_end = get_previous_period(
        pd.Timestamp(report_section_data_op.time_range_start),
        pd.Timestamp(report_section_data_op.time_range_end),
        report_section_data_op.time_granularity,
        report_section_data_op.compute_type,
    )
    percentage_now, df_now = calculate_percentage(
        df, report_section_data_op, time_column
    )
    report_section_data_op.time_range_start = prev_time_start
    report_section_data_op.time_range_end = prev_time_end
    percentage_prev, df_prev = calculate_percentage(
        df, report_section_data_op, time_column
    )
    if df_now.empty or df_prev.empty:
        return report_section_data_op.data_op_id, ""

    if report_section_data_op.group_by:
        merged_df = pd.merge(
            df_now, df_prev, on=report_section_data_op.group_by, how="inner"
        )
        merged_df["份额变化"] = merged_df["份额_x"] - merged_df["份额_y"]
        result_df = merged_df[["份额变化"]]
        return report_section_data_op.data_op_id, generate_data_op_text_desc(
            result_df,
            report_section_data_op,
            total_change=percentage_now - percentage_prev,
            ppt_mode=True,
        )
    else:
        return report_section_data_op.data_op_id, generate_data_op_text_desc(
            df_now.iloc[0] - df_prev.iloc[0],
            report_section_data_op,
            ppt_mode=True,
        )


def calculate_percentage(df, report_section_data_op, time_column):
    df = df[
        (df[time_column] >= report_section_data_op.time_range_start)
        & (df[time_column] <= report_section_data_op.time_range_end)
    ]
    if df.empty:
        return None, df
    total_number = df[report_section_data_op.metric].sum()
    percentage = None
    df_filtered = df.copy()
    if report_section_data_op.percentage_filter and isinstance(
        report_section_data_op.percentage_filter, list
    ):
        for percentage_filter in report_section_data_op.percentage_filter:
            # column_name = 业务类型
            # filter_values = [ICT业务]
            if (
                isinstance(percentage_filter, dict)
                and "columnName" in percentage_filter
                and "values" in percentage_filter
            ):
                df_filtered = df_filtered[
                    df_filtered[percentage_filter["columnName"]].isin(
                        percentage_filter["values"]
                    )
                ]
        target_number = df_filtered[report_section_data_op.metric].sum()
        percentage = target_number / total_number
    if report_section_data_op.group_by:
        df = df.groupby(report_section_data_op.group_by)
        calculate_percentage_partial = partial(
            calculate_percentage_groupby,
            report_section_data_op=report_section_data_op,
            current_time_start=report_section_data_op.time_range_start,
            current_time_end=report_section_data_op.time_range_end,
        )
        df = df.apply(calculate_percentage_partial)

        return percentage, df
    else:
        return None, pd.Series({"份额": percentage})


def exec_data_op(
    model_name: str,
    data_time_params: Union[ReportDataTimeParams, TemplateDataTimeParams],
    data_filter_params: Union[
        list[ReportDataFilterParams], list[TemplateDataFilterParams]
    ]
    | None,
    report_section_data_op: Any,
    data_preview=False,
    report_mode=False,
    ppt_mode=False,
    df=None,
    province=None,
    department=None,
):
    # 根据段落ID、报告ID从获取ReportSectionConfig，
    # 按照ReportSectionConfig中的data ops 和 text ops 的顺序，执行data op
    # 执行数据操作
    if df is None:
        df = get_data_by_model(model_name, data_time_params, data_filter_params)
    else:
        df = df
    if df.empty:
        raise Exception(f"No data found with your filter")
    if report_section_data_op.data_filter and isinstance(
        report_section_data_op.data_filter, list
    ):
        for data_filter in report_section_data_op.data_filter:
            # column_name = 业务类型
            # filter_values = [ICT业务]
            if (
                isinstance(data_filter, dict)
                and "columnName" in data_filter
                and "values" in data_filter
            ):
                df = df[df[data_filter["columnName"]].isin(data_filter["values"])]
    # 有分组的才会分段展示

    # report生成时候，用最外面的帐期，不用数据算子的时间范围
    if report_mode:
        report_section_data_op.time_range_start = data_time_params.time_range_start
        report_section_data_op.time_range_end = data_time_params.time_range_end
        report_section_data_op.time_column = data_time_params.time_column
    time_column = report_section_data_op.time_column

    if report_section_data_op.compute_type == OPTYPE_BIG_DEAL:
        if province is not None:
            df = df[df[report_province_name].isin([province])]
        if department is not None:
            df = df[df[report_department_name].isin([department])]
        df = df[
            (df[time_column] >= report_section_data_op.time_range_start)
            & (df[time_column] <= report_section_data_op.time_range_end)
        ]
        # 过滤百万元以上大单
        df = df[df[report_section_data_op.metric] >= 1000000]
        if report_section_data_op.group_by:
            df = df.groupby(report_section_data_op.group_by)
            df = df.agg(
                {report_section_data_op.metric: report_section_data_op.operator}
            )
            ascending = report_section_data_op.enum_order == "asc"
            df = df.sort_values(by=report_section_data_op.metric, ascending=ascending)

            if data_preview:
                return df
            else:
                return report_section_data_op.data_op_id, generate_data_op_text_desc(
                    df, report_section_data_op, ppt_mode=ppt_mode
                )
        else:
            number_count = df.agg({report_section_data_op.metric: "count"})
            df = df.agg(
                {report_section_data_op.metric: report_section_data_op.operator}
            )
            if data_preview:
                return df
            else:
                return report_section_data_op.data_op_id, generate_data_op_text_desc(
                    df.iloc[0],
                    report_section_data_op,
                    total_change=number_count.iloc[0],
                    ppt_mode=ppt_mode,
                )

    if report_section_data_op.compute_type == OPTYPE_COUNT:
        if province is not None:
            df = df[df[report_province_name].isin([province])]
        if department is not None:
            df = df[df[report_department_name].isin([department])]
        # filter data
        df = df[
            (df[time_column] >= report_section_data_op.time_range_start)
            & (df[time_column] <= report_section_data_op.time_range_end)
        ]
        if report_section_data_op.group_by:
            df = df.groupby(report_section_data_op.group_by)
            df = df.agg(
                {report_section_data_op.metric: report_section_data_op.operator}
            )
            ascending = report_section_data_op.enum_order == "asc"
            df = df.sort_values(by=report_section_data_op.metric, ascending=ascending)
            if data_preview:
                return df
            else:
                return report_section_data_op.data_op_id, generate_data_op_text_desc(
                    df, report_section_data_op, ppt_mode=ppt_mode
                )
        else:
            df = df.agg(
                {report_section_data_op.metric: report_section_data_op.operator}
            )
            if data_preview:
                return df
            else:
                return report_section_data_op.data_op_id, generate_data_op_text_desc(
                    df.iloc[0], report_section_data_op, ppt_mode=ppt_mode
                )
    if (
        report_section_data_op.compute_type == OPTYPE_COUNT_YOY
        or report_section_data_op.compute_type == OPTYPE_COUNT_MOM
    ):
        if province is not None:
            df = df[df[report_province_name].isin([province])]
        if department is not None:
            df = df[df[report_department_name].isin([department])]
        total_change = calculate_changes(
            df,
            report_section_data_op,
            report_section_data_op.time_range_start,
            report_section_data_op.time_range_end,
        )

        if report_section_data_op.group_by:
            calculate_changes_partial = partial(
                calculate_changes,
                report_section_data_op=report_section_data_op,
                current_time_start=report_section_data_op.time_range_start,
                current_time_end=report_section_data_op.time_range_end,
            )
            if df.empty:
                if data_preview:
                    return df
                else:
                    return report_section_data_op.data_op_id, ""

            df = df.groupby(report_section_data_op.group_by).apply(
                calculate_changes_partial
            )
            ascending = report_section_data_op.enum_order == "asc"
            df = df.sort_values(by="增长率", ascending=ascending)
            if data_preview:
                return df
            else:
                return report_section_data_op.data_op_id, generate_data_op_text_desc(
                    df, report_section_data_op, total_change.iloc[0], ppt_mode=ppt_mode
                )
        else:
            if data_preview:
                return total_change
            else:
                return report_section_data_op.data_op_id, generate_data_op_text_desc(
                    total_change.iloc[0], report_section_data_op, ppt_mode=ppt_mode
                )
    if (
        report_section_data_op.compute_type == OPTYPE_MOM_RANK
        or report_section_data_op.compute_type == OPTYPE_YOY_RANK
    ):
        calculate_changes_partial = partial(
            calculate_changes,
            report_section_data_op=report_section_data_op,
            current_time_start=report_section_data_op.time_range_start,
            current_time_end=report_section_data_op.time_range_end,
        )

        if report_section_data_op.group_by == report_province_name:
            target = province
        elif report_section_data_op.group_by == report_department_name:
            target = department
        else:
            raise (
                RuntimeError(
                    f"groupby: {report_section_data_op.group_by},"
                    f"only support rank for groupby {report_province_name} or {report_department_name}"
                )
            )
        if df.empty:
            if data_preview:
                return pd.DataFrame([0])
            else:
                return report_section_data_op.data_op_id, ""
        df = df.groupby(report_section_data_op.group_by).apply(
            calculate_changes_partial
        )
        df["Rank"] = df["增长率"].rank(method="min", ascending=False)
        if target is not None and target in df.index:
            rank = df.loc[target, "Rank"]
        else:
            rank = pd.DataFrame([0])
        if data_preview:
            return rank
        else:
            if target is None:
                rank = 0
            return report_section_data_op.data_op_id, generate_data_op_text_desc(
                rank, report_section_data_op, ppt_mode=ppt_mode
            )
    if report_section_data_op.compute_type == OPTYPE_COUNT_LAST_YOY:
        if province is not None:
            df = df[df[report_province_name].isin([province])]
        if department is not None:
            df = df[df[report_department_name].isin([department])]
        prev_time_start, prev_time_end = get_previous_period(
            pd.Timestamp(report_section_data_op.time_range_start),
            pd.Timestamp(report_section_data_op.time_range_end),
            report_section_data_op.time_granularity,
            report_section_data_op.compute_type,
        )
        prev_df = calculate_changes(
            df, report_section_data_op, prev_time_start, prev_time_end
        )
        if prev_df.iloc[0] is None:
            if data_preview:
                return None
            else:
                return report_section_data_op.data_op_id, ""
        if data_preview:
            return prev_df
        else:
            return report_section_data_op.data_op_id, generate_data_op_text_desc(
                prev_df.iloc[0],
                report_section_data_op,
                ppt_mode=ppt_mode,
            )

    if report_section_data_op.compute_type == OPTYPE_COUNT_YOY_WAVE:
        if province is not None:
            df = df[df[report_province_name].isin([province])]
        if department is not None:
            df = df[df[report_department_name].isin([department])]
        prev_time_start, prev_time_end = get_previous_period(
            pd.Timestamp(report_section_data_op.time_range_start),
            pd.Timestamp(report_section_data_op.time_range_end),
            report_section_data_op.time_granularity,
            report_section_data_op.compute_type,
        )
        if df.empty:
            if data_preview:
                return None
            else:
                return report_section_data_op.data_op_id, ""
        if report_section_data_op.group_by:
            calculate_changes_partial = partial(
                calculate_changes,
                report_section_data_op=report_section_data_op,
                current_time_start=report_section_data_op.time_range_start,
                current_time_end=report_section_data_op.time_range_end,
            )
            df_now = (
                df.groupby(report_section_data_op.group_by)
                .apply(calculate_changes_partial)
                .fillna(0)
            )
            calculate_changes_partial_prev = partial(
                calculate_changes,
                report_section_data_op=report_section_data_op,
                current_time_start=prev_time_start,
                current_time_end=prev_time_end,
            )
            df_prev = (
                df.groupby(report_section_data_op.group_by)
                .apply(calculate_changes_partial_prev)
                .fillna(0)
            )
            if df_now.empty or df_prev.empty:
                if data_preview:
                    return None
                else:
                    return report_section_data_op.data_op_id, ""

            merged_df = pd.merge(
                df_now, df_prev, on=report_section_data_op.group_by, how="inner"
            )
            merged_df["增长率波动"] = merged_df["增长率_x"] - merged_df["增长率_y"]
            result_df = merged_df[["增长率波动"]]
            # result_df = result_df.fillna(0)
            ascending = report_section_data_op.enum_order == "asc"
            result_df = result_df.sort_values(by="增长率波动", ascending=ascending)
            if data_preview:
                return result_df
            else:
                return report_section_data_op.data_op_id, generate_data_op_text_desc(
                    result_df, report_section_data_op, ppt_mode=ppt_mode
                )
        else:
            now_df = calculate_changes(
                df,
                report_section_data_op,
                report_section_data_op.time_range_start,
                report_section_data_op.time_range_end,
            )
            prev_df = calculate_changes(
                df, report_section_data_op, prev_time_start, prev_time_end
            )
            if prev_df.iloc[0] is None or now_df.iloc[0] is None:
                if data_preview:
                    return None
                else:
                    return report_section_data_op.data_op_id, ""
            if data_preview:
                return now_df - prev_df
            else:
                return report_section_data_op.data_op_id, generate_data_op_text_desc(
                    now_df.iloc[0] - prev_df.iloc[0],
                    report_section_data_op,
                    ppt_mode=ppt_mode,
                )

    if report_section_data_op.compute_type == OPTYPE_PERCENTAGE:
        total_percentage, df = calculate_percentage(
            df, report_section_data_op, report_section_data_op.time_column
        )
        if df.empty:
            return report_section_data_op.data_op_id, ""
        if report_section_data_op.group_by:
            return report_section_data_op.data_op_id, generate_data_op_text_desc(
                df, report_section_data_op, total_percentage, ppt_mode=ppt_mode
            )
        else:
            return (
                report_section_data_op.data_op_id,
                f"{report_section_data_op.name}为{df.iloc[0] * 100:.1f}%",
            )
        # df = df[
        #     (df[time_column] >= report_section_data_op.time_range_start) &
        #     (df[time_column] <= report_section_data_op.time_range_end)
        #     ]
        # total_number = df.agg({report_section_data_op.metric: report_section_data_op.operator}).iloc[0]
        # if report_section_data_op.group_by:
        #     df = df.groupby(report_section_data_op.group_by)
        #     df = df.agg({report_section_data_op.metric: report_section_data_op.operator}).apply(lambda x: x/total_number)
        #     df = df.rename(columns={report_section_data_op.metric: '份额'})
        #     return report_section_data_op.data_op_id, generate_data_op_text_desc(df, report_section_data_op)
        # else:
        #     if report_section_data_op.percentage_filter and isinstance(report_section_data_op.percentage_filter, list):
        #         for percentage_filter in report_section_data_op.percentage_filter:
        #             # column_name = 业务类型
        #             # filter_values = [ICT业务]
        #             if (isinstance(percentage_filter, dict)
        #                     and 'columnName' in percentage_filter and 'values' in percentage_filter):
        #                 df = df[df[percentage_filter['columnName']].isin(percentage_filter['values'])]
        #     target_number = df.agg({report_section_data_op.metric: report_section_data_op.operator})
        #     return report_section_data_op.data_op_id, f"{report_section_data_op.name}为{target_number/total_number * 100:.2f}%"

    if report_section_data_op.compute_type == OPTYPE_PERCENTAGE_CHANGE:
        return calculate_percentage_change(df, report_section_data_op, time_column)

    if report_section_data_op.compute_type == OPTYPE_PPT_GRAPH_OP_1:
        df1 = df.copy()
        df2 = df.copy()
        if df.empty:
            return df
        calculate_changes_partial = partial(
            calculate_changes,
            report_section_data_op=report_section_data_op,
            current_time_start=report_section_data_op.time_range_start,
            current_time_end=report_section_data_op.time_range_end,
        )
        df1 = (
            df1.groupby(report_section_data_op.group_by)
            .apply(calculate_changes_partial)
            .fillna(0)
            .apply(lambda x: round(x * 100, 1))
        )
        df2 = df2[
            (df2[time_column] >= report_section_data_op.time_range_start)
            & (df2[time_column] <= report_section_data_op.time_range_end)
        ]
        df2 = df2.groupby(report_section_data_op.group_by)
        df2 = df2.agg(
            {report_section_data_op.metric: report_section_data_op.operator}
        ).apply(lambda x: round(x / 100000000, 1))

        df_combined = df1.join(df2)
        df_combined_renamed = df_combined.rename(
            columns={"增长率": "中标金额同比(%)", "中标金额": "电信中标金额(亿元)"}
        )
        df_combined_renamed = df_combined_renamed.drop(
            index=hidden_dimension_values_list, errors="ignore"
        ).fillna(0)

        return df_combined_renamed

    if report_section_data_op.compute_type == OPTYPE_PPT_GRAPH_OP_2:
        prev_time_start, prev_time_end = get_previous_period(
            pd.Timestamp(report_section_data_op.time_range_start),
            pd.Timestamp(report_section_data_op.time_range_end),
            report_section_data_op.time_granularity,
            report_section_data_op.compute_type,
        )
        df1 = df.copy()
        df2 = df.copy()
        if df.empty:
            return df
        calculate_percentage_partial = partial(
            calculate_percentage_groupby,
            report_section_data_op=report_section_data_op,
            current_time_start=report_section_data_op.time_range_start,
            current_time_end=report_section_data_op.time_range_end,
        )

        df_now = (
            df2.groupby(report_section_data_op.group_by)
            .apply(calculate_percentage_partial)
            .fillna(0)
        )
        calculate_percentage_partial_prev = partial(
            calculate_percentage_groupby,
            report_section_data_op=report_section_data_op,
            current_time_start=prev_time_start,
            current_time_end=prev_time_end,
        )
        df_prev = (
            df1.groupby(report_section_data_op.group_by)
            .apply(calculate_percentage_partial_prev)
            .fillna(0)
        )
        if df_now.empty or df_prev.empty:
            return pd.DataFrame()
        merged_df = pd.merge(
            df_now, df_prev, on=report_section_data_op.group_by, how="inner"
        )
        merged_df["较去年同期份额变化(pp)"] = merged_df["份额_x"] - merged_df["份额_y"]
        merged_df = merged_df[["较去年同期份额变化(pp)"]].apply(lambda x: round(x * 100, 1))
        df_now = df_now.apply(lambda x: round(x * 100, 1))
        result_df = merged_df.join(df_now).rename(columns={"份额": "电信中标份额(%)"})
        result_df = result_df.drop(
            index=hidden_dimension_values_list, errors="ignore"
        ).fillna(0)
        return result_df


def generate_data_op_text_desc(
    data_value, data_op: ReportSectionDataOp, total_change=None, ppt_mode=False
):
    text_str: str
    name = data_op.name
    mean_str: str = None
    if not data_op.group_by:
        if pd.api.types.is_numeric_dtype(data_value):
            if data_op.compute_type == OPTYPE_BIG_DEAL:
                text_str = "{name}签约{number_count}个，签约金额{data_value}"
                return text_str.format(
                    name=name,
                    number_count=total_change,
                    data_value=convert_units(data_value, ppt_mode=ppt_mode),
                )
            if data_value > 0:
                if data_op.compute_type == OPTYPE_PERCENTAGE_CHANGE:
                    increase_text = "提升"
                else:
                    increase_text = "增长"
            else:
                increase_text = "下降"
                data_value = -data_value
            if data_op.compute_type in [
                OPTYPE_COUNT_MOM,
                OPTYPE_COUNT_YOY,
                OPTYPE_COUNT_LAST_YOY,
                OPTYPE_COUNT_YOY_WAVE,
                OPTYPE_PERCENTAGE,
                OPTYPE_PERCENTAGE_CHANGE,
            ]:
                if (
                    data_op.compute_type == OPTYPE_COUNT_YOY_WAVE
                    or data_op.compute_type == OPTYPE_PERCENTAGE_CHANGE
                ):
                    text_str = "{name}{increase_text}{data_value}pp"
                else:
                    text_str = "{name}{increase_text}{data_value}%"
                data_value_str = (
                    f"{data_value * 100:.1f}" if ppt_mode else f"{data_value * 100:.2f}"
                )
                return text_str.format(
                    name=name,
                    data_value=data_value_str,
                    increase_text=increase_text,
                )
            text_str = "{name}为{data_value}"
            return text_str.format(
                name=name,
                data_value=convert_units(data_value, ppt_mode=ppt_mode),
                increase_text=increase_text,
            )
        else:
            print(f"data value not valid. {type(data_value)}, data_op: {data_op}")
            return ""
    # 分组描述
    result_text_list = []
    # 输出应该取metric，如果是比率类的，写死为增长率
    if data_op.compute_type in [OPTYPE_COUNT_MOM, OPTYPE_COUNT_YOY]:
        output_orderby = "增长率"
    elif data_op.compute_type == OPTYPE_COUNT_YOY_WAVE:
        output_orderby = "增长率波动"
    elif data_op.compute_type in [OPTYPE_PERCENTAGE]:
        output_orderby = "份额"
    elif data_op.compute_type == OPTYPE_PERCENTAGE_CHANGE:
        output_orderby = "份额变化"
    else:
        output_orderby = data_op.metric
    if (
        data_op.compute_type == OPTYPE_YOY_RANK
        or data_op.compute_type == OPTYPE_MOM_RANK
    ):
        return f"{name}排名第{data_value:.0f}"

    # if data_op.compute_type == OPTYPE_PERCENTAGE_CHANGE:
    #     text_str = "{dimension_code}共{total_number}个{class_name}的{output_order_by}{compare_text}"
    #     dimension_value = data_value[data_value[output_orderby] < 0]
    #     code_list = dimension_value.index.unique().to_list()
    #     compare_text = "去年同期下降"
    #     if len(code_list) > 0:
    #         result_text_list.append(
    #             text_str.format(
    #                 compare_text=compare_text,
    #                 dimension_code=",".join(code_list),
    #                 metric=data_op.metric,
    #                 output_order_by=data_op.name,
    #                 total_number=len(code_list),
    #                 class_name=data_op.group_by,
    #             )
    #         )
    #     dimension_value = data_value[data_value[output_orderby] > 0]
    #     code_list = dimension_value.index.unique().to_list()
    #     compare_text = "去年同期增加"
    #     if len(code_list) > 0:
    #         result_text_list.append(
    #             text_str.format(
    #                 compare_text=compare_text,
    #                 dimension_code=",".join(code_list),
    #                 metric=data_op.metric,
    #                 output_order_by=data_op.name,
    #                 total_number=len(code_list),
    #                 class_name=data_op.group_by,
    #             )
    #         )
    #     ascending = data_op.enum_order == "asc"
    #     if not ascending:
    #         result_text_list.reverse()
    #     if mean_str is not None:
    #         result_text_list.insert(0, mean_str)
    #     return f"{name}:{';'.join(result_text_list)}"

    if data_op.compute_type == OPTYPE_BIG_DEAL:
        # 超亿元的
        text_str = "{dimension_code}的{name}超亿元"
        dimension_value = data_value[data_value[output_orderby] >= 100000000]
        code_list = dimension_value.index.unique().to_list()
        code_list = [x for x in code_list if x not in hidden_dimension_values_list]
        if len(code_list) > 0:
            result_text_list.append(
                text_str.format(
                    dimension_code=",".join(code_list),
                    name=data_op.name,
                )
            )
        # 超千万元的
        text_str = "{dimension_code}的{name}超千万元"
        dimension_value = data_value[
            (data_value[output_orderby] >= 10000000)
            & (data_value[output_orderby] < 100000000)
        ]
        code_list = dimension_value.index.unique().to_list()
        code_list = [x for x in code_list if x not in hidden_dimension_values_list]
        if len(code_list) > 0:
            result_text_list.append(
                text_str.format(
                    dimension_code=",".join(code_list),
                    name=data_op.name,
                )
            )
        return f"{';'.join(result_text_list)}"

    if data_op.compute_type == OPTYPE_PERCENTAGE:
        if data_op.segmentation_options == "mean" and total_change is not None:
            text_str = "{dimension_code}共{total_number}个{class_name}的{output_order_by}{compare_text}"
            dimension_value = data_value[data_value[output_orderby] < total_change]
            code_list = dimension_value.index.unique().to_list()
            code_list = [x for x in code_list if x not in hidden_dimension_values_list]
            compare_text = f"低于{total_change*100:.1f}%"
            if len(code_list) > 0:
                result_text_list.append(
                    text_str.format(
                        compare_text=compare_text,
                        dimension_code=",".join(code_list),
                        metric=data_op.metric,
                        output_order_by=data_op.name,
                        total_number=len(code_list),
                        class_name=data_op.group_by,
                    )
                )
            dimension_value = data_value[data_value[output_orderby] > total_change]
            code_list = dimension_value.index.unique().to_list()
            code_list = [x for x in code_list if x not in hidden_dimension_values_list]
            compare_text = f"高于{total_change*100:.1f}%"
            if len(code_list) > 0:
                result_text_list.append(
                    text_str.format(
                        compare_text=compare_text,
                        dimension_code=",".join(code_list),
                        metric=data_op.metric,
                        output_order_by=data_op.name,
                        total_number=len(code_list),
                        class_name=data_op.group_by,
                    )
                )
        else:
            data_value.dropna(inplace=True)
            text_str = "{dimension_code}的{output_order_by}为{data_value}"
            for code, value in data_value.iterrows():
                if code in hidden_dimension_values_list:
                    continue
                value_str = f"{value[0] * 100:.1f}%"
                result_text_list.append(
                    text_str.format(
                        data_value=value_str,
                        dimension_code=code,
                        metric=data_op.metric,
                        output_order_by=output_orderby,
                    )
                )
        ascending = data_op.enum_order == "asc"
        if not ascending:
            result_text_list.reverse()
        if mean_str is not None:
            result_text_list.insert(0, mean_str)
        return f"{name}:{';'.join(result_text_list)}"

    if data_op.segmentation_options:
        text_str = "{dimension_code}共{total_number}个{class_name}的{output_order_by}{compare_text}"
        bins: [float] = [-np.inf]
        if data_op.segmentation_options == "mean" and total_change is None:
            mean_value = data_value[output_orderby].mean()
            bins.append(mean_value)
            mean_str = (
                f"{output_orderby}的均值为{convert_units(mean_value, ppt_mode=ppt_mode)}"
            )

        elif data_op.segmentation_options == "mean" and total_change is not None:
            data_value_str = (
                f"{total_change * 100:.1f}" if ppt_mode else f"{total_change * 100:.2f}"
            )
            if data_op.compute_type == OPTYPE_PERCENTAGE_CHANGE:
                mean_str = f"各{data_op.group_by}的整体{data_op.name}为{data_value_str}pp"
            else:
                mean_str = f"各{data_op.group_by}的整体{data_op.name}为{data_value_str}%"
            if total_change > 0:
                bins.extend([0, total_change])
            else:
                bins.append(0)
        elif data_op.segmentation_options == "custom":
            bins.extend(data_op.output_data_section_params)
        bins.append(np.inf)

        str_bins = []
        data_value.dropna(inplace=True)
        for i in range(len(bins)):
            if bins[i] == np.inf:
                str_bins.append("无穷大")
                continue
            if bins[i] == -np.inf:
                str_bins.append("无限小")
                continue
            if output_orderby == "增长率":
                data_value_str = (
                    f"{bins[i] * 100:.1f}" if ppt_mode else f"{bins[i] * 100:.2f}"
                )
                str_bins.append(f"{data_value_str}%")
                continue
            if output_orderby == "增长率波动" or output_orderby == "份额变化":
                data_value_str = (
                    f"{bins[i] * 100:.1f}" if ppt_mode else f"{bins[i] * 100:.2f}"
                )
                str_bins.append(f"{data_value_str}pp")
                continue
            else:
                str_bins.append(convert_units(bins[i], ppt_mode=ppt_mode))
        for i in range(len(bins) - 1):
            if i == 0:
                if bins[i + 1] == 0 and (
                    output_orderby == "增长率"
                    or output_orderby == "增长率波动"
                    or output_orderby == "份额变化"
                ):
                    compare_text = f"负增长"
                else:
                    compare_text = f"小于{str_bins[i + 1]}"
                dimension_value = data_value[data_value[output_orderby] < bins[i + 1]]
            elif i == len(bins) - 2:
                if bins[i] == 0 and (
                    output_orderby == "增长率"
                    or output_orderby == "增长率波动"
                    or output_orderby == "份额变化"
                ):
                    compare_text = f"正增长"
                else:
                    compare_text = f"大于等于{str_bins[i]}"
                dimension_value = data_value[data_value[output_orderby] >= bins[i]]
            else:
                compare_text = f"在{str_bins[i]}到{str_bins[i + 1]}之间"
                dimension_value = data_value[
                    (bins[i] <= data_value[output_orderby])
                    & (data_value[output_orderby] < bins[i + 1])
                ]
            code_list = dimension_value.index.unique().to_list()
            code_list = [x for x in code_list if x not in hidden_dimension_values_list]

            if len(code_list) > 0:
                result_text_list.append(
                    text_str.format(
                        compare_text=compare_text,
                        dimension_code=",".join(code_list),
                        metric=data_op.metric,
                        output_order_by=data_op.name,
                        total_number=len(code_list),
                        class_name=data_op.group_by,
                    )
                )
    else:
        data_value.dropna(inplace=True)
        text_str = "{dimension_code}的{output_order_by}为{data_value}"
        for code, value in data_value.iterrows():
            if code in hidden_dimension_values_list:
                continue
            if data_op.compute_type in [
                OPTYPE_COUNT_MOM,
                OPTYPE_COUNT_YOY,
                OPTYPE_PERCENTAGE,
            ]:
                value_str = (
                    f"{value[0] * 100:.1f}%" if ppt_mode else f"{value[0] * 100:.2f}"
                )
            else:
                value_str = convert_units(value[0], ppt_mode=ppt_mode)
            result_text_list.append(
                text_str.format(
                    data_value=value_str,
                    dimension_code=code,
                    metric=data_op.metric,
                    output_order_by=output_orderby,
                )
            )
    ascending = data_op.enum_order == "asc"
    if not ascending:
        result_text_list.reverse()
    if mean_str is not None:
        result_text_list.insert(0, mean_str)
    return f"{name}:{';'.join(result_text_list)}"


def generate_report_section(
    template: Template,
    data_time_params,
    node: OutlineNode,
    results: Dict[int, Future],
    province=None,
    department=None,
    language_style=None,
) -> str:
    section_id = node.id
    data_filter_params = get_template_filter_params(template.template_id)
    template_section_data_op = get_template_section_data_op_with_section_id(
        template.template_id, section_id
    )
    template_section_text_op = get_template_section_text_op(
        template.template_id, section_id
    )
    text_op_dict = {text_op.text_op_id: text_op for text_op in template_section_text_op}
    data_op_dict = {data_op.data_op_id: data_op for data_op in template_section_data_op}
    template_section_config = get_template_section_config_with_section_id(
        template.template_id, section_id
    )
    if template_section_config is None:
        raise Exception(f"大纲段落：{node.title} 没有段落配置")
    tasks = []
    text_list = []
    if template_section_config.data_op_ids:
        for data_op_id in template_section_config.data_op_ids:
            tasks.append(
                lambda _data_op_id=data_op_id: exec_data_op(
                    model_name=template.model_name,
                    data_time_params=data_time_params,
                    data_filter_params=data_filter_params,
                    report_section_data_op=data_op_dict[_data_op_id],
                    report_mode=True,
                    province=province,
                    department=department,
                )
            )
        text_list = run_concurrently(tasks, max_workers=5, timeout_seconds=300)
    depends_on_results = []
    if node.dependsOn:
        depends_on_results = [results[i].result() for i in node.dependsOn]
    result_texts = []
    if template_section_config.text_op_ids:
        for text_op_id in template_section_config.text_op_ids:
            text_op_result = execute_text_op(
                text_op_dict[text_op_id],
                depends_on_results,
                text_list,
                language_style=language_style,
            )
            result_texts.append(text_op_result)
    # result_text = section_summary_op(report_section_config, result_text)    result_text = "\n\n".join(result_texts)
    result_text = generate_section_text(
        template_section_config, result_texts, data_time_params
    )
    return result_text


def generate_template_section(
    template: Template, node: OutlineNode, results: Dict[int, Future]
) -> str:
    section_id = node.id
    data_time_params = get_template_data_time_params(template.template_id)
    data_filter_params = get_template_filter_params(template.template_id)
    template_section_data_op = get_template_section_data_op_with_section_id(
        template.template_id, section_id
    )
    template_section_text_op = get_template_section_text_op(
        template.template_id, section_id
    )
    text_op_dict = {text_op.text_op_id: text_op for text_op in template_section_text_op}
    data_op_dict = {data_op.data_op_id: data_op for data_op in template_section_data_op}
    template_section_config = get_template_section_config_with_section_id(
        template.template_id, section_id
    )
    if template_section_config is None:
        raise Exception(f"大纲段落：{node.title} 没有段落配置")
    tasks = []
    text_list = []
    if template_section_config.data_op_ids:
        for data_op_id in template_section_config.data_op_ids:
            tasks.append(
                lambda _data_op_id=data_op_id: exec_data_op(
                    model_name=template.model_name,
                    data_time_params=data_time_params,
                    data_filter_params=data_filter_params,
                    report_section_data_op=data_op_dict[_data_op_id],
                )
            )
        text_list = run_concurrently(tasks, max_workers=5, timeout_seconds=300)
    depends_on_results = []
    if node.dependsOn:
        depends_on_results = [results[i].result() for i in node.dependsOn]
    result_texts = []
    if template_section_config.text_op_ids:
        for text_op_id in template_section_config.text_op_ids:
            text_op_result = execute_text_op(
                text_op_dict[text_op_id], depends_on_results, text_list
            )
            result_texts.append(text_op_result)
    # result_text = section_summary_op(report_section_config, result_text)
    result_text = "\n\n".join(result_texts)
    return result_text


def generate_template_section_for_update(
    template: Template, nodes: list[OutlineNode], section_id: int
):
    data_time_params = get_template_data_time_params(template.template_id)
    node_dict = {node.id: node for node in nodes}
    template_section_text_op = get_template_section_text_op(
        template.template_id, section_id
    )
    text_op_dict = {text_op.text_op_id: text_op for text_op in template_section_text_op}
    template_section_config = get_template_section_config_with_section_id(
        template.template_id, section_id
    )
    if template_section_config is None:
        raise Exception(f"大纲段落：{node_dict.get(section_id).title} 没有段落配置")
    # text_list = []
    # tasks = []
    # if template_section_config.data_op_ids:
    #     for data_op_id in template_section_config.data_op_ids:
    #         tasks.append(lambda _data_op_id=data_op_id: exec_data_op(report=template,
    #                                                            data_time_params=data_time_params,
    #                                                            data_filter_params=data_filter_params,
    #                                                            report_section_data_op=data_op_dict[_data_op_id]))
    #     text_list = run_concurrently(tasks, max_workers=5, timeout_seconds=300)
    # depends_on_results = []
    # if node_dict.get(section_id).dependsOn:
    #     depends_on_results = [node_dict[i].content for i in node_dict[section_id].dependsOn]
    # result_texts = []
    text_results = []
    if template_section_config.text_op_ids:
        for text_op_id in template_section_config.text_op_ids:
            text_results.append(text_op_dict[text_op_id].result)

    result_text = generate_section_text(
        template_section_config, text_results, data_time_params
    )
    # 获取节点深度，拼接标题
    temp_node, depth = get_node_depth(nodes, section_id)
    result_text = f"{'#' * depth} {temp_node.title} \n\n {result_text}"
    return result_text


def generate_report_section_for_update(
    report: Report, nodes: list[OutlineNode], section_id: int
):
    data_time_params = get_report_data_time_params(report.id)
    node_dict = {node.id: node for node in nodes}
    data_filter_params = get_report_filter_params(report.id)
    report_section_data_op = get_report_section_data_op(report.id, section_id)
    report_section_config = get_report_section_config(report.id, section_id)
    report_text_op = get_report_section_text_op(report.id, section_id)
    text_list = []
    tasks = []
    if report_section_data_op:
        for data_op in report_section_data_op:
            tasks.append(
                lambda _data_op=data_op: exec_data_op(
                    model_name=report.model_name,
                    data_time_params=data_time_params,
                    data_filter_params=data_filter_params,
                    report_section_data_op=_data_op,
                )
            )
        text_list = run_concurrently(tasks, max_workers=5, timeout_seconds=300)
    depends_on_results = []
    if node_dict.get(section_id).dependsOn:
        depends_on_results = [
            node_dict[i].content for i in node_dict[section_id].dependsOn
        ]
    result_text = ""
    for text_op in report_text_op:
        result_text = execute_text_op(text_op, depends_on_results, text_list)
        depends_on_results.append(result_text)
    result_text = section_summary_op(report_section_config, result_text)
    # 获取节点深度，拼接标题
    temp_node, depth = get_node_depth(nodes, section_id)
    result_text = f"{'#' * depth} {temp_node.title} \n\n {result_text}"
    return result_text


def process_data_op(input_data_op: TemplateDataOpClass) -> TemplateDataOp:
    template_data_op = TemplateDataOp(
        template_id=input_data_op.templateId,
        section_id=input_data_op.sectionId,
        data_op_id=input_data_op.dataOpId if input_data_op.dataOpId else None,
        name=input_data_op.name,
        compute_type=input_data_op.computeType,
        operator=input_data_op.operator,
        operator_desc=input_data_op.operatorDesc
        if input_data_op.operatorDesc
        else None,
        metric=input_data_op.metric,
        group_by=input_data_op.groupBy if input_data_op.groupBy else None,
        data_filter=input_data_op.dataFilter if input_data_op.dataFilter else None,
        output_data_section_params=input_data_op.outputDataSectionParams
        if input_data_op.outputDataSectionParams
        else None,
        time_granularity=input_data_op.timeGranularity
        if input_data_op.timeGranularity
        else None,
        time_range_start=datetime.strptime(input_data_op.timeRangeStart, "%Y-%m-%d"),
        time_range_end=datetime.strptime(input_data_op.timeRangeEnd, "%Y-%m-%d"),
        time_column=input_data_op.timeColumn,
        segmentation_options=input_data_op.segmentationOptions
        if input_data_op.segmentationOptions
        else None,
        enum_order=input_data_op.enumOrder if input_data_op.enumOrder else None,
    )
    return template_data_op


def process_text_op(input_text_op: TemplateTextOpClass) -> TemplateTextOp:
    template_text_op = TemplateTextOp(
        template_id=input_text_op.templateId,
        section_id=input_text_op.sectionId,
        text_op_id=input_text_op.textOpId if input_text_op.textOpId else None,
        name=input_text_op.name,
        type=input_text_op.type,
        input_section_ids=input_text_op.inputSectionIds
        if input_text_op.inputSectionIds
        else None,
        input_data_op_ids=input_text_op.inputDataOpIds
        if input_text_op.inputDataOpIds
        else None,
        prompt=input_text_op.prompt,
    )
    return template_text_op
