from dotenv import load_dotenv
from matplotlib import patches

env_file = "/Users/<USER>/ask-bi/python/nl2metric/.env"
print(f"load dotenv {env_file}")
load_dotenv(dotenv_path=env_file, override=True)

import os.path
import tempfile
import uuid

import pdfplumber


from common.fs.fs import get_s3_file_system
from common.fs.storage import create_s3_client
from pptx import Presentation
import subprocess
import pandas as pd
import matplotlib

matplotlib.use("agg")
import matplotlib.pyplot as plt
from textwrap import wrap, fill

import threading

lock = threading.Lock()

import platform

from common.utils.string_utils import class_to_dict
from nl2document.common.models.ppt_generate_model import (
    PPTTemplateTest,
    test_save_ppt_template_to_db,
    test_get_ppt_template_by_id,
)
from nl2document.common.msg.ppt_generate_msg import (
    PPTContentUnit,
    PPTSlide,
    PPTTemplateData,
)

system_name = platform.system()

if system_name == "Windows":
    plt.rcParams["font.family"] = ["SimHei"]
elif system_name == "Linux":
    plt.rcParams["font.family"] = ["WenQuanYi Zen Hei"]
elif system_name == "Darwin":
    plt.rcParams["font.family"] = ["Arial Unicode MS"]
else:
    print("未知操作系统，无法准确配置字体，可能出现中文显示问题")
    plt.rcParams["font.family"] = ["sans-serif"]
plt.rcParams["axes.unicode_minus"] = False

plt.rcParams.update(
    {
        "font.size": 9,  # 基础字号[1,4](@ref)
        "axes.titlesize": 10,  # 标题字号[2](@ref)
        "xtick.labelsize": 8,  # x轴刻度字号[5](@ref)
        "ytick.labelsize": 8,  # y轴刻度字号[4](@ref)
    }
)

import re


def split_string_with_punctuation(s, n):
    # 按n个字符分割字符串
    segments = [s[i : i + n] for i in range(0, len(s), n)]

    # 处理最后一行仅有一个标点的情况
    if len(segments) > 1:
        last_segment = segments[-1]
        # 判断是否为单个非字母/数字/空格字符（即标点）
        if len(last_segment) == 1 and re.fullmatch(
            r"[^\w\s]", last_segment, re.UNICODE
        ):
            segments[-2] += last_segment  # 合并到前一行
            segments.pop()  # 删除最后一行

    return "\n".join(segments)


def get_thumbnail_path(ppt_path: str) -> str:
    # pdf = pdfplumber.open(pdf_path)
    # img = pdf.pages[0].to_image().original.resize((200, 200))
    # img_path = f'{pdf_path}.png'
    base_name = os.path.basename(ppt_path)
    # img.save(img_path)
    remote_path = f"ppt-generate/template/{base_name}"
    create_s3_client().upload_file(
        ppt_path,
        "ask-doc",
        remote_path,
    )
    return remote_path


def convert_ppt_to_pdf(ppt_file, output_folder):
    # 确保输出目录存在
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    # 构建输出PDF文件的完整路径
    pdf_file = os.path.join(
        output_folder, os.path.basename(ppt_file).replace(".pptx", ".pdf")
    )
    # 调用LibreOffice命令行工具进行转换
    subprocess.run(
        [
            "soffice",
            "--headless",
            "--convert-to",
            "pdf",
            "--outdir",
            output_folder,
            ppt_file,
        ]
    )


def generate_graph(dfs, titles, path: str):
    global lock
    try:
        lock.acquire()
        if len(dfs) == 0:
            return ""
        width_list = [len(df.index.tolist()) for df in dfs]
        # 设置整体图形的大小
        fig, axes = plt.subplots(
            2,
            len(dfs),
            figsize=(12.7, 1.54),
            gridspec_kw={"width_ratios": width_list},
            constrained_layout=True,
            squeeze=False,
        )

        column_names = dfs[0].columns.tolist()

        def calculate_limits(column_name, dfs):
            """计算指定列的调整后最小值和最大值"""
            values = [df[column_name] for df in dfs]
            min_val = min(v.min() for v in values)
            max_val = max(v.max() for v in values)

            # 根据数值符号调整边界
            adj_min = min_val * 0.9 if min_val > 0 else min_val * 1.1
            adj_max = max_val * 1.1 if max_val > 0 else max_val * 0.9
            return adj_min, adj_max

        y_line_min, y_line_max = calculate_limits(column_names[0], dfs)
        y_bar_min, y_bar_max = calculate_limits(column_names[1], dfs)

        for index, df in enumerate(dfs):
            bars_fan = df[column_names[1]].plot(
                kind="bar", color="#B9CDE5", ax=axes[1, index], width=0.2
            )

            bar_centers = [p.get_x() + p.get_width() / 2 for p in bars_fan.patches]

            lines_fan = df[column_names[0]].plot(
                kind="line",
                marker="o",
                markersize=3,
                color="#E6B9B8",
                ax=axes[0, index],
                x=bar_centers,
            )
            xlim_range = axes[1, index].get_xlim()
            axes[0, index].set_xlim(xlim_range)
            # 在折线图的圆点上标注数值
            axes[0, index].set_ylim(y_line_min, y_line_max)
            for line in lines_fan.get_lines():
                xdata = line.get_xdata()
                ydata = line.get_ydata()
                for x, y in zip(xdata, ydata):
                    label = "{:.1f}".format(y)
                    color = "green" if y < 0 else "black"
                    axes[0, index].annotate(
                        label,
                        (x, y),
                        textcoords="offset points",
                        xytext=(0, 10),
                        ha="center",
                        fontsize=8,
                        color=color,
                    )

            axes[0, index].axis("off")
            if len(titles) != 0:
                axes[0, index].set_title(titles[index], pad=10, fontsize=10)

            axes[1, index].set_ylim(y_bar_min, y_bar_max)

            # 在柱状图上标注数值
            for p in bars_fan.patches:
                height = p.get_height()
                axes[1, index].text(
                    p.get_x() + p.get_width() / 2.0,
                    height + 0.1,
                    "{:1.1f}".format(height),
                    ha="center",
                    fontsize=8,
                )

            axes[1, index].spines["right"].set_visible(False)
            axes[1, index].spines["top"].set_visible(False)
            axes[1, index].spines["left"].set_visible(False)
            # 清除y轴上的刻度标记
            axes[1, index].set_yticks([])
            # 隐藏y轴标签
            axes[1, index].set_ylabel("")
            axes[1, index].set_xlabel("")

        # axes[0, 0].text(
        #     -0.05,
        #     0.9,
        #     split_string_with_punctuation(column_names[0], 3),
        #     transform=axes[0, 0].transAxes,
        #     verticalalignment="center",
        #     horizontalalignment="left",
        #     fontsize=7,
        # )
        # axes[1, 0].text(
        #     -0.05,
        #     0.2,
        #     split_string_with_punctuation(column_names[1], 3),
        #     transform=axes[1, 0].transAxes,
        #     verticalalignment="center",
        #     horizontalalignment="left",
        #     fontsize=7,
        # )
        axes[0, 0].annotate(
            split_string_with_punctuation(column_names[0], 3),
            xy=(-0.01, 0.9),  # 坐标点在子图坐标系左侧外部
            xycoords="axes fraction",  # 使用子图坐标系
            xytext=(-0.01, 0.9),  # 文本位置与坐标点一致（无箭头时）
            textcoords="axes fraction",  # 文本坐标系与子图一致
            verticalalignment="center",
            horizontalalignment="right",  # 右对齐保证文本在左侧
            fontsize=9,
            arrowprops=dict(arrowstyle="-", lw=0),  # 隐藏箭头（或设为 None）
            annotation_clip=False,  # 允许在子图外绘制
        )

        axes[1, 0].annotate(
            split_string_with_punctuation(column_names[1], 3),
            xy=(-0.01, 0.2),  # 坐标点在子图坐标系左侧外部
            xycoords="axes fraction",  # 使用子图坐标系
            xytext=(-0.01, 0.2),  # 文本位置与坐标点一致（无箭头时）
            textcoords="axes fraction",  # 文本坐标系与子图一致
            verticalalignment="center",
            horizontalalignment="right",  # 右对齐保证文本在左侧
            fontsize=9,
            arrowprops=dict(arrowstyle="-", lw=0),  # 隐藏箭头（或设为 None）
            annotation_clip=False,  # 允许在子图外绘制
        )

        # # 为每个子图添加左侧标题
        # for i, ax in enumerate([axes[0, 0], axes[1, 0]]):
        #     ax.annotate(
        #         split_string_with_punctuation(column_names[i], 3),
        #         xy=(-0.02, 0.5),  # 坐标点在子图坐标系左侧外部
        #         xycoords='axes fraction',  # 使用子图坐标系
        #         xytext=(-0.02, 0.5),  # 文本位置与坐标点一致（无箭头时）
        #         textcoords='axes fraction',  # 文本坐标系与子图一致
        #         verticalalignment='center',
        #         horizontalalignment='right',  # 右对齐保证文本在左侧
        #         fontsize=8,
        #         arrowprops=dict(arrowstyle='-', lw=0),  # 隐藏箭头（或设为 None）
        #         annotation_clip=False  # 允许在子图外绘制
        #     )
        for ax in axes.flat:
            ax.xaxis.set_tick_params(rotation=0)

        border_rect = patches.Rectangle(
            (0, 0),
            1,
            1,  # 初始大小设为(1,1)，会根据转换自动调整
            transform=fig.transFigure,
            linewidth=1,
            edgecolor="#BFBFBF",
            facecolor="none",
        )

        fig.patches.append(border_rect)

        # fig.subplots_adjust(wspace=0.07, hspace=0.15)
        plt.tight_layout()
        temp_filename = str(uuid.uuid4()) + ".png"
        file_path = os.path.join(path, temp_filename)
        # plt.show()
        plt.savefig(
            file_path,
            dpi=300,
            bbox_inches="tight",  # 自动裁剪空白[8](@ref)
            pad_inches=0.05,  # 保留最小边距[7](@ref)
        )
        plt.clf()
        plt.close()
    finally:
        lock.release()
    return file_path


if __name__ == "__main__":
    # path = get_thumbnail_path('/Users/<USER>/Downloads/template_group.pptx')
    # ppt_url = get_s3_file_system().url("ask-doc/" + path)
    # get_s3_file_system().download("ask-doc/" + path, 'example.pptx')
    # prs = Presentation('/Users/<USER>/Downloads/template_group.pptx')
    # convert_ppt_to_pdf('/Users/<USER>/Downloads/template_group.pptx', '/Users/<USER>/Downloads/')
    # summary_slide = prs.slides[0]
    # detail_slide = prs.slides[1]
    # detail_layout = detail_slide.slide_layout
    # slide = prs.slides.add_slide(detail_layout)
    # slide.
    # prs.save('test.pptx')

    # import numpy as np
    #
    # plt.rcParams['font.family'] = 'Arial Unicode MS'
    #
    #
    # 泛政务数据
    data_fan = {
        "中标金额同比（pp）": [
            4894.2,
            3732.4,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            -100,
            -100,
            -100,
            -100,
            -100,
            -100,
            -100,
            -100,
        ],
        "电信中标金额（亿元）": [
            9.6,
            10.5,
            19,
            6.7,
            16.9,
            22.7,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
            0,
        ],
    }
    df_fan = pd.DataFrame(
        data_fan,
        index=[
            "新疆",
            "重庆",
            "湖南",
            "江西",
            "江苏",
            "四川",
            "浙江",
            "福建",
            "宁夏",
            "西藏",
            "广东",
            "湖北",
            "陕西",
            "甘肃",
            "广西",
            "云南",
            "贵州",
            "安徽",
            "上海",
            "海南",
            "青海",
        ],
    )

    # 社会民生数据
    data_social = {
        "中标金额同比（pp）": [393.5, 172.1, 22.06, 0, 0, -48.1, -52.9, -97.1, -100, -100],
        "电信中标金额（亿元）": [0, 0.1, 0, 0, 0, 0, 0, 0, 0, 0],
    }
    df_social = pd.DataFrame(
        data_social,
        index=["河北", "黑龙江", "内蒙古", "天津", "山西", "辽宁", "河南", "山东", "北京", "吉林"],
    )

    # 新型工业化数据
    data_industry = {
        "中标金额同比（pp）": [4.4, 4.3, 4.2, 3.7, 3.3],
        "电信中标金额（亿元）": [12.4, 62.3, 13.7, 4.3, 27.7],
    }
    df_industry = pd.DataFrame(data_industry, index=["卫健", "能源化工", "工业", "车联网", "交通物流"])

    dfs = [df_fan, df_social]
    titles = ["南方21省", "北方10省"]

    generate_graph(
        dfs,
        titles,
        "/Users/<USER>/ask-bi/python/nl2metric/nl2document/report_generate/ppt",
    )

    # Slide1 = PPTSlide(
    #     id = 1,
    #     contents = [
    #         PPTContentUnit(
    #             id = 1,
    #             type = 'title',
    #             title = 'test1'
    #         ),
    #         PPTContentUnit(
    #             id = 2,
    #             type = 'text'
    #         )
    #     ]
    # )
    #
    # Slide2 = PPTSlide(
    #     id = 2,
    #     contents = [
    #         PPTContentUnit(
    #             id = 1,
    #             type = 'title',
    #             title = 'test1'
    #         ),
    #         PPTContentUnit(
    #             id = 2,
    #             type = 'text'
    #         )
    #     ]
    # )
    # template1 = PPTTemplateData(slides = [Slide1, Slide2])
    #
    # test = class_to_dict(template1)
    #
    # test_template_db = PPTTemplateTest(id = 1,
    #                                    slides = class_to_dict(template1))
    #
    # test_save_ppt_template_to_db(test_template_db)
    #
    #
    # read_from_db = test_get_ppt_template_by_id(1)
    #
    #
    # ppt = PPTTemplateData(**read_from_db.slides)
    #
    # for slide in ppt.slides:
    #     print(slide)
    #
    #
    #
    #
    # ppt2 = ppt
