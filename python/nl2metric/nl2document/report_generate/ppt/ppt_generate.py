import calendar
import json
import os
import subprocess
import tempfile
import sys
import time
import traceback
from distutils.util import strtobool
from datetime import datetime
from datetime import date
import random
from dateutil.relativedelta import relativedelta
from typing import List, Tuple

import pptx
from pptx import Presentation
from pptx.enum.text import MSO_AUTO_SIZE
from pptx.slide import Slide
from pptx.util import Inches, Pt

from common.fs.fs import get_s3_file_system
from common.fs.storage import create_s3_client
from common.logging.logger import get_logger
from common.utils.concurrent_utils import run_concurrently
from nl2document.common.models.ppt_generate_model import (
    create_ppt_generated,
    get_ppt_template_by_id,
    get_ppt_data_op_by_id,
    get_ppt_text_op_by_id,
    update_ppt_paths,
)
from nl2document.common.models.report_generate_model import TemplateDataTimeParams
from nl2document.common.msg.ppt_generate_msg import (
    GeneratePPTRequest,
    PPTTemplateData,
    PP<PERSON>lide,
    PPTContentUnit,
)
from nl2document.report_generate.ppt.graph_generate import generate_graph
from nl2document.report_generate.report.data_op_executor import exec_data_op
from nl2document.report_generate.report.report_outline import cache
from nl2document.report_generate.report.text_op_executor import execute_text_op
from nl2document.report_generate.tests.test_ppt import MockPPTData_Group
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor

from nl2document.report_generate.xengine_data.model_data import get_data_by_model

logger = get_logger(__name__)


class PPTContent:
    type: str
    content: str

    def __init__(self, type, title=None, content=None):
        self.type = type
        self.title = title
        self.content = content

    def __str__(self):
        return f"type:({self.type}, title: {self.title}, content:{self.content})"


def serialize_ppt_generate_req(req: GeneratePPTRequest) -> str:
    """
    Converts the req object into a JSON string for caching.

    Args:
        req: The PPT generation request to serialize

    Returns:
        str: A JSON string representation of the request

    Raises:
        TypeError: If the request cannot be serialized to JSON
    """
    try:
        return json.dumps(req, sort_keys=True, default=str)
    except TypeError as e:
        logger.error(f"Failed to serialize PPT request: {e}")
        raise


def get_cached_ppt(req: GeneratePPTRequest) -> tuple[str, str] | None:
    """
    Retrieves cached PPT and PDF URLs for a given request.

    Args:
        req: The PPT generation request

    Returns:
        tuple[str, str] | None: A tuple of (pdf_url, ppt_url) if cached, None otherwise
    """
    cached_key = serialize_ppt_generate_req(req)
    if cached_key in cache:
        logger.info(f"Cache hit for req: {cached_key}")
        return cache[cached_key]
    return None


def set_cached_ppt(req: GeneratePPTRequest, pdf_url: str, ppt_url: str) -> None:
    """Cache the generated PPT and PDF URLs for a given request.

    Args:
        req: The PPT generation request
        pdf_url: The URL of the generated PDF
        ppt_url: The URL of the generated PPT

    Raises:
        ValueError: If any of the URLs is empty or invalid
    """
    if not pdf_url or not ppt_url:
        raise ValueError("PDF URL and PPT URL must not be empty")

    cached_key = serialize_ppt_generate_req(req)
    cache[cached_key] = (pdf_url, ppt_url)


def load_template(remote_path: str, file_name: str):
    local_file_name = os.path.join(
        os.path.dirname(__file__),
        f"template_download/{file_name}",
    )
    os.makedirs(os.path.dirname(local_file_name), exist_ok=True)
    if not os.path.exists(local_file_name):
        get_s3_file_system().download(remote_path, local_file_name, recursive=True)

    prs = Presentation(local_file_name)
    return prs


def convert_ppt_to_pdf(ppt_file, pdf_path):
    if sys.platform.startswith("linux"):
        subprocess.run(
            [
                "/usr/bin/libreoffice",
                "--headless",
                "--convert-to",
                "pdf",
                "--outdir",
                pdf_path,
                ppt_file,
            ]
        )
    elif sys.platform.startswith("darwin"):
        subprocess.run(
            [
                "soffice",
                "--headless",
                "--convert-to",
                "pdf",
                "--outdir",
                pdf_path,
                ppt_file,
            ]
        )


def generate_ppt_content(prs, slide: Slide, ppt_content: List[PPTContent]):
    sorted_placeholders = sorted(slide.placeholders, key=lambda shape: shape.top)
    for index, shape in enumerate(sorted_placeholders):
        if index >= len(ppt_content):
            continue
        if ppt_content[index].type == "title":
            shape.text_frame.text = ppt_content[index].title
        if ppt_content[index].type == "text":
            content_str = ppt_content[index].content
            # if len(content_str) > 350:
            #     font_size = Pt(9)
            # elif len(content_str) > 300:
            #     font_size = Pt(10)
            # elif len(content_str) > 250:
            #     font_size = Pt(10.5)
            # elif len(content_str) > 200:
            #     font_size = Pt(11)
            # else:
            font_size = Pt(11)
            content_str_list = content_str.split("\n")
            if content_str_list[0].strip().startswith("输出"):
                content_str_list = content_str_list[1:]
            text_frame = shape.text_frame
            if ppt_content[index].title is not None:
                p = text_frame.paragraphs[0]
                run = p.add_run()
                run.text = ppt_content[index].title
                font = run.font
                font.size = Pt(12)
                font.bold = True
                if (
                    ppt_content[index].title == "全市场中标金额"
                    or ppt_content[index].title == "全市场中标份额"
                ):
                    run = p.add_run()
                    run.text = content_str_list[0]
                    run.font.size = Pt(11)
                    new_ppt_content = PPTContent(
                        type="text",
                        content="\n".join([s for s in content_str_list[1:] if s != ""]),
                    )
                    ppt_content.insert(index + 1, new_ppt_content)
                    # for para_str in content_str_list[1:]:
                    #     p = text_frame.add_paragraph()
                    #     run = p.add_run()
                    #     run.text = para_str
                    #     run.font.size = font_size

                else:
                    new_ppt_content = PPTContent(
                        type="text",
                        content="\n".join([s for s in content_str_list if s != ""]),
                    )
                    ppt_content.insert(index + 1, new_ppt_content)
                    # for para_str in content_str_list:
                    #     if para_str == "":
                    #         continue
                    #     p = text_frame.add_paragraph()
                    #     run = p.add_run()
                    #     run.text = para_str
                    #     run.font.size = Pt(11)
            else:
                p = text_frame.paragraphs[0]
                run = p.add_run()
                run.text = content_str
                run.font.size = font_size

        if ppt_content[index].type == "graph":
            if ppt_content[index].content != "":
                pic = shape.insert_picture(ppt_content[index].content)
                pic.crop_top = 0
                pic.crop_left = 0
                pic.crop_bottom = 0
                pic.crop_right = 0


default_ppt_model_name = os.environ.get(
    "default_ppt_model_name", "dxjyfx_winbidder_day_model_1209"
)

ppt_demo_mode = strtobool(os.environ.get("ppt_demo_mode", "False"))

demo_pdf_url = "ppt_generate/ppt_demo/result.pdf"
demo_ppt_url = "ppt_generate/ppt_demo/result.pptx"


def generate_ppt_text(content: PPTContentUnit, data_time_params, df) -> PPTContent:
    data_op_list = []
    for data_op_id in content.inputDataOpIds:
        data_op = get_ppt_data_op_by_id(data_op_id)
        data_op_list.append(data_op)
    tasks = []
    for data_op in data_op_list:
        tasks.append(
            lambda _data_op=data_op: exec_data_op(
                model_name=default_ppt_model_name,
                data_time_params=data_time_params,
                data_filter_params=None,
                report_section_data_op=_data_op,
                report_mode=True,
                ppt_mode=True,
                df=df,
            )
        )
    text_list = run_concurrently(tasks, max_workers=5, timeout_seconds=300)
    result_texts = []
    time_str = "时间：" + data_time_params.time_range_end.strftime("%Y-%m")
    text_op_list = []
    for text_op_id in content.inputTextOpIds:
        text_op = get_ppt_text_op_by_id(text_op_id)
        text_op_list.append(text_op)
    for text_op in text_op_list:
        text_op_result = execute_text_op(text_op, [], text_list, time_str)
        result_texts.append(text_op_result)

    return PPTContent(type="text", content="\n".join(result_texts))


def generate_ppt_graph(
    content: PPTContentUnit, temp_dir: str, data_time_params, df
) -> PPTContent:
    data_op_list = []
    for data_op_id in content.inputDataOpIds:
        data_op = get_ppt_data_op_by_id(data_op_id)
        data_op_list.append(data_op)
    tasks = []
    for data_op in data_op_list:
        tasks.append(
            lambda _data_op=data_op: exec_data_op(
                model_name=default_ppt_model_name,
                data_time_params=data_time_params,
                data_filter_params=None,
                report_section_data_op=_data_op,
                data_preview=True,
                report_mode=True,
                ppt_mode=True,
                df=df,
            )
        )
    df_list = run_concurrently(tasks, max_workers=5, timeout_seconds=300)
    df_sorted_list = [
        df.sort_values(by=df.columns[0], ascending=False)
        for df in df_list
        if not df.empty
    ]
    graph_path = generate_graph(df_sorted_list, content.subTitles, temp_dir)
    return PPTContent(type="graph", content=graph_path)


def generate_one_slide(
    ppt_slide: PPTSlide, temp_dir: str, data_time_params, df
) -> List[PPTContent]:
    result = []

    for content in ppt_slide.contents:
        if content.type == "title":
            result.append(PPTContent(type=content.type, title=content.title))
        elif content.type == "text":
            ppt_content = generate_ppt_text(content, data_time_params, df)
            ppt_content.title = content.title
            result.append(ppt_content)
        elif content.type == "graph":
            result.append(generate_ppt_graph(content, temp_dir, data_time_params, df))

    return result


def generate_ppt_template_data(
    template: PPTTemplateData, temp_dir: str, time_str: str, province: str = None
) -> List[Tuple[int, List[PPTContent]]]:
    date_obj = datetime.strptime(time_str, "%Y-%m")

    # 如果当前月份和请求时间字符串的月份相同，则减去一个月
    # 因为账期计算是一月一日到某一个月的月底
    # 如果是当月，这一个月份还没有过完，所以需要计算的账期是到上一个月底
    current_date = date.today()
    if date_obj.month == current_date.month and date_obj.year == current_date.year:
        date_obj = date_obj - relativedelta(months=1)

    _, last_day = calendar.monthrange(date_obj.year, date_obj.month)
    time_range_end = date_obj.replace(day=last_day)
    time_range_start = date_obj.replace(month=1, day=1)
    dataTimeParams = TemplateDataTimeParams(
        time_range_start=time_range_start,
        time_range_end=time_range_end,
        time_column="中标账期",
    )
    results_map = {}
    df = get_data_by_model(default_ppt_model_name, dataTimeParams, None)
    # 省市模版
    if province is not None:
        df = df[df["省份"].isin([province])]

    with ThreadPoolExecutor(max_workers=5) as executor:
        result_list = []
        for slide in template.slides:
            future = executor.submit(
                generate_one_slide, slide, temp_dir, dataTimeParams, df
            )
            results_map[slide.id] = (slide.slide_master_id, future)
        for slide_id, (slide_master_id, future) in results_map.items():
            try:
                result = future.result()
                logger.info(
                    f"Slide {slide_id} executed successfully with result: {result}"
                )
                result_list.append((slide_master_id, result))
            except Exception as e:
                logger.info(f"Slide {slide_id} execution failed with exception: {e}")
                logger.error("Stack trace: %s", traceback.format_exc())
                raise e
    return list(result_list)


def generate_ppt(req: GeneratePPTRequest):
    cached_result = get_cached_ppt(req)
    if cached_result is not None:
        pdf_remote_url, ppt_remote_url = cached_result
        # sleep random in 50 - 70s
        # time.sleep(random.randint(50, 70))
        return get_s3_file_system().url(pdf_remote_url), get_s3_file_system().url(
            ppt_remote_url
        )

    template = get_ppt_template_by_id(req.templateId)

    ppt_id = create_ppt_generated(req.templateId)

    ppt = PPTTemplateData(**template.slides)

    template_file_name = os.path.basename(template.path)

    # 如果预设的ppt存在，则直接返回
    if get_s3_file_system().exists("ask-doc/" + demo_ppt_url):
        time.sleep(10)
        return get_s3_file_system().url(
            "ask-doc/" + demo_pdf_url
        ), get_s3_file_system().url("ask-doc/" + demo_ppt_url)

    prs = load_template(template.path, template_file_name)
    # layout = prs.slide_layouts[1]

    with tempfile.TemporaryDirectory() as temp_dir:
        data = generate_ppt_template_data(ppt, temp_dir, req.timeStr, req.province)
        results = data
        for slide_master_id, result in results:
            if slide_master_id == 2:
                layout = prs.slide_layouts[1]
            elif slide_master_id == 3:
                layout = prs.slide_layouts[2]
            elif slide_master_id == 4:  # 底部有小注释的模板
                layout = prs.slide_layouts[3]
            slide = prs.slides.add_slide(layout)
            generate_ppt_content(prs, slide, result)
        ppt_file_name = (
            f"商情中标分析-{req.timeStr}.pptx"
            if req.province is None
            else f"商情中标分析-{req.timeStr}-{req.province}.pptx"
        )
        pdf_file_name = (
            f"商情中标分析-{req.timeStr}.pdf"
            if req.province is None
            else f"商情中标分析-{req.timeStr}-{req.province}.pdf"
        )
        ppt_path = os.path.join(temp_dir, ppt_file_name)
        prs.save(ppt_path)
        convert_ppt_to_pdf(ppt_path, temp_dir)
        pdf_path = os.path.join(temp_dir, pdf_file_name)
        remote_pdf_url = f"ppt_generate/ppt_{ppt_id}/{pdf_file_name}"
        remote_ppt_url = f"ppt_generate/ppt_{ppt_id}/{ppt_file_name}"
        create_s3_client().upload_file(
            pdf_path,
            "ask-doc",
            remote_pdf_url,
        )
        create_s3_client().upload_file(
            ppt_path,
            "ask-doc",
            remote_ppt_url,
        )
        remote_pdf_url = f"ask-doc/" + remote_pdf_url
        remote_ppt_url = f"ask-doc/" + remote_ppt_url
        set_cached_ppt(req, remote_pdf_url, remote_ppt_url)

        # 更新数据库记录
        update_ppt_paths(
            ppt_id=ppt_id, pdf_path=remote_pdf_url, ppt_path=remote_ppt_url
        )

        return get_s3_file_system().url(remote_pdf_url), get_s3_file_system().url(
            remote_ppt_url
        )
