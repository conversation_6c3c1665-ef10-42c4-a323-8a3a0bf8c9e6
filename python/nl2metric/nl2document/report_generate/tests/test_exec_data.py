import pandas as pd

from nl2document.common.models.report_generate_model import (
    ReportDataTimeParams,
    ReportSectionDataOp,
)
from nl2document.report_generate.report.data_op_executor import (
    get_previous_period,
    calculate_changes,
)


def test_df_op():
    df = pd.DataFrame(
        [
            {"name": "<PERSON>", "age": 25},
            {"name": "<PERSON>", "age": 30},
            {"name": "<PERSON>", "age": 35},
        ]
    )
    df = df[df["age"].isin([25, 30])]
    df = df.agg({"age": "sum"})
    print(df.iloc[0])


def test_df_grouper():
    df = pd.DataFrame(
        [
            {"合同签约金额": 2000, "合同签约日期": "2023-01-01", "合同类型": "B"},
            {"合同签约金额": 3000, "合同签约日期": "2022-01-01", "合同类型": "C"},
            {"合同签约金额": 4000, "合同签约日期": "2023-03-01", "合同类型": "D"},
            {"合同签约金额": 5000, "合同签约日期": "2024-04-01", "合同类型": "E"},
            {"合同签约金额": 1000, "合同签约日期": "2024-01-01", "合同类型": "A"},
        ]
    )
    df["合同签约日期"] = pd.to_datetime(df["合同签约日期"])
    result = df.groupby([pd.Grouper(key="合同签约日期", freq="M")])["合同签约金额"].sum()
    result = result.pct_change()  # 计算同比增长率
    print(result)


def test_df_period():
    df = pd.DataFrame(
        [
            {"合同签约金额": 1000, "合同签约日期": "2024-03-05"},
            {"合同签约金额": 1500, "合同签约日期": "2024-03-10"},
            {"合同签约金额": 2000, "合同签约日期": "2023-03-05"},
            {"合同签约金额": 2500, "合同签约日期": "2023-03-10"},
            {"合同签约金额": 3000, "合同签约日期": "2024-01-15"},
        ]
    )

    # 转换日期列为日期类型
    df["合同签约日期"] = pd.to_datetime(df["合同签约日期"])

    # 设置当前时间范围
    current_start = pd.Timestamp("2024-03-01")
    current_end = pd.Timestamp("2024-03-31")

    # 根据粒度计算上一个时间范围
    frequency = "Q"  # 可以是 'M'（月）、'Q'（季度）、'Y'（年）

    prev_start, prev_end = get_previous_period(
        current_start, current_end, frequency, "同比"
    )
    print(f"当前时间范围: {current_start} - {current_end}")
    print(f"上一个时间范围: {prev_start} - {prev_end}")
    # 过滤当前和上一个时间范围内的数据
    df_current = df[(df["合同签约日期"] >= current_start) & (df["合同签约日期"] <= current_end)]
    df_prev = df[(df["合同签约日期"] >= prev_start) & (df["合同签约日期"] <= prev_end)]

    # 计算总和
    current_sum = df_current["合同签约金额"].sum()
    prev_sum = df_prev["合同签约金额"].sum()

    # 计算环比变化率
    if prev_sum != 0:
        change = (current_sum - prev_sum) / prev_sum
    else:
        change = None  # 防止除以0的情况

    print(f"当前时间范围总和: {current_sum}")
    print(f"上一个时间范围总和: {prev_sum}")
    print(f"环比变化率: {change:.2%}" if change is not None else "无法计算环比变化率")


def test_df_groupby():
    # 示例 DataFrame
    df = pd.DataFrame(
        [
            {"合同签约金额": 1000, "合同签约日期": "2024-01-05", "合同类型": "A"},
            {"合同签约金额": 1500, "合同签约日期": "2024-01-15", "合同类型": "B"},
            {"合同签约金额": 2000, "合同签约日期": "2023-12-10", "合同类型": "A"},
            {"合同签约金额": 2500, "合同签约日期": "2023-12-20", "合同类型": "B"},
            {"合同签约金额": 3000, "合同签约日期": "2023-11-15", "合同类型": "A"},
            {"合同签约金额": 4000, "合同签约日期": "2023-01-15", "合同类型": "A"},
            {"合同签约金额": 5000, "合同签约日期": "2022-01-15", "合同类型": "B"},
        ]
    )

    # 转换日期列为日期类型
    df["合同签约日期"] = pd.to_datetime(df["合同签约日期"])

    # 设置当前时间周期
    current_month_start = pd.Timestamp("2024-01-01")
    current_month_end = pd.Timestamp("2024-01-31")

    data_time_params = ReportDataTimeParams(
        time_range_start=current_month_start,
        time_range_end=current_month_end,
        time_column="合同签约日期",
    )
    report_section_data_op = ReportSectionDataOp(
        report_id=1,
        section_id=1,
        data_op_id=3,
        name="2024年签约额总计同比波动",
        compute_type="总计同比波动",
        operator="sum",
        operator_desc="目标时间范围的同比增长率，比前一周期同比增长率的波动数值，以“N pp”形式输出",
        metric="合同签约金额",
        group_by=None,
        data_filter=None,
        output_data_section_params=None,
        time_range_start="2024-01-01",
        time_range_end="2024-01-31",
        time_granularity="year",
        output_limit=-1,
        output_order_by=None,
        data_desc_template="{name}{increase_text}{data_value}pp",
    )
    # calculate_changes_partial = partial(
    #     calculate_changes,
    #     report_section_data_op=report_section_data_op,
    #     data_time_params=data_time_params,
    #     current_time_start=current_month_start,
    #     current_time_end=current_month_end,
    # )
    result = calculate_changes(
        df, report_section_data_op, current_month_start, current_month_end
    )

    print(result.iloc[0])
