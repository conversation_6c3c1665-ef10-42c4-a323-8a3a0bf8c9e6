import random

import pandas as pd

from nl2document.report_generate.ppt.graph_generate import generate_graph


def generate_random_floats(min_value, max_value, n):
    """
    生成一个包含n个在[min_value, max_value]范围内的，带一位小数的随机浮点数列表。

    参数:
    min_value (float): 范围的最小值。
    max_value (float): 范围的最大值。
    n (int): 要生成的随机数的数量。

    返回:
    list: 包含n个随机浮点数的列表。
    """
    random_floats = []
    for _ in range(n):
        # 生成一个随机浮点数，然后四舍五入到一位小数
        random_float = round(random.uniform(min_value, max_value), 1)
        random_floats.append(random_float)
    return random_floats


def generate_random_province():
    data_province_south = {
        "中标金额同比(pp)": generate_random_floats(-2, 10, 21),
        "电信中标金额(亿元)": generate_random_floats(10, 50, 21),
    }
    df_province_south = pd.DataFrame(
        data_province_south,
        index=[
            "贵州",
            "云南",
            "新疆",
            "西藏",
            "陕西",
            "江西",
            "湖北",
            "甘肃",
            "湖南",
            "上海",
            "安徽",
            "广西",
            "浙江",
            "福建",
            "宁夏",
            "四川",
            "江苏",
            "青海",
            "重庆",
            "广东",
            "海南",
        ],
    )

    data_province_north = {
        "中标金额同比(pp)": generate_random_floats(-2, 10, 10),
        "电信中标金额(亿元)": generate_random_floats(10, 50, 10),
    }
    df_province_north = pd.DataFrame(
        data_province_north,
        index=["吉林", "内蒙古", "黑龙江", "辽宁", "天津", "北京", "山东", "山西", "河南", "河北"],
    )

    return [df_province_south, df_province_north]


class MockPPTData_Group:
    def __init__(self, path: str):
        # 泛政务数据
        data_fan = {
            "中标金额同比(pp)": [7.7, 7.6, 7.5, 7.0, 6.8, 6.7],
            "电信中标金额(亿元)": [9.6, 10.5, 19, 6.7, 16.9, 22.7],
        }
        df_fan = pd.DataFrame(data_fan, index=["农业农村", "住建", "应急", "政务", "政法公安", "要客"])

        # 社会民生数据
        data_social = {
            "中标金额同比(pp)": [6.6, 4.9, 4.7, 4.5, 4.4],
            "电信中标金额(亿元)": [27.9, 14.3, 21.7, 53, 24.9],
        }
        df_social = pd.DataFrame(data_social, index=["商业", "文宣", "互联网", "金融", "教育"])

        # 新型工业化数据
        data_industry = {
            "中标金额同比(pp)": [4.4, 4.3, 4.2, 3.7, 3.3],
            "电信中标金额(亿元)": [12.4, 62.3, 13.7, 4.3, 27.7],
        }
        df_industry = pd.DataFrame(
            data_industry, index=["卫健", "能源化工", "工业", "车联网", "交通物流"]
        )

        data_province_south = {
            "中标金额同比(pp)": [
                7.7,
                7.6,
                7.5,
                7.0,
                6.8,
                6.7,
                6.6,
                4.9,
                4.7,
                4.5,
                4.4,
                4.4,
                4.3,
                4.2,
                3.7,
                3.3,
                2.5,
                1.3,
                1.3,
                0.1,
                -0.9,
            ],
            "电信中标金额(亿元)": [
                9.6,
                10.5,
                19,
                6.7,
                16.9,
                22.7,
                27.9,
                14.3,
                21.7,
                53,
                24.9,
                12.4,
                62.3,
                13.7,
                4.3,
                27.7,
                50.9,
                3.9,
                8.8,
                45.9,
                5.8,
            ],
        }
        df_province_south = pd.DataFrame(
            data_province_south,
            index=[
                "贵州",
                "云南",
                "新疆",
                "西藏",
                "陕西",
                "江西",
                "湖北",
                "甘肃",
                "湖南",
                "上海",
                "安徽",
                "广西",
                "浙江",
                "福建",
                "宁夏",
                "四川",
                "江苏",
                "青海",
                "重庆",
                "广东",
                "海南",
            ],
        )

        data_province_north = {
            "中标金额同比(pp)": [4.0, 2.5, 1.8, 1.1, 0.9, 0.7, -0.4, -0.7, -1.2, -1.6],
            "电信中标金额(亿元)": [3.7, 5.2, 2.1, 1.8, 3.5, 20.4, 7.4, 2.4, 12.4, 10.5],
        }
        df_province_north = pd.DataFrame(
            data_province_north,
            index=["吉林", "内蒙古", "黑龙江", "辽宁", "天津", "北京", "山东", "山西", "河南", "河北"],
        )

        results = []
        from nl2document.report_generate.ppt.ppt_generate import PPTContent

        # slide 1
        result = []
        dfs = [df_fan, df_social, df_industry]
        titles = ["泛政务", "社会民生", "新型工业化"]
        result.append(PPTContent(type="text", content="全市场中标-1：整体"))
        result.append(
            PPTContent(
                type="text",
                content="""全市场中标金额：1-8月全市场中标10亿，同比2%；电信中标2亿，同比增长2%，较去年同期+2pp 
泛政务客户中标金额3亿，同比4%（-5pp）；社会民生客户中标金额6亿，同比7%（ +8pp）；新型工业化客户中标金额9亿，同比10%（- 11pp）
分行业看：1、2、3、4、5等5个行业增幅高于2%*； 5、4、3、2等4各行业增幅低于2%*""",
            )
        )
        result.append(
            PPTContent(type="graph", content=generate_graph(dfs, titles, path))
        )
        result.append(
            PPTContent(
                type="text",
                content="""全市场中标份额：1-8月电信全市场中标份额23%，较去年同期+ 21pp（移动份额21%、 - 2pp；联通份额12%、 - 2pp）
泛政务全市场份额21%（+43pp）；社会民生全市场份额32%（+-32pp）；新型工业化全市场份额43%（-21pp）
分行业看：卫健, 能源化工, 工业等行业份额较去年同期增加； 商业,文宣,互联网等行业份额较去年同期下降
""",
            )
        )
        result.append(
            PPTContent(type="graph", content=generate_graph(dfs, titles, path))
        )
        results.append(result)

        # slide 2
        result = []
        dfs = [df_province_south, df_province_north]
        titles = ["南方21省", "北方10省"]
        result.append(PPTContent(type="text", content="全市场中标-2：分区域"))
        result.append(
            PPTContent(
                type="text",
                content="""分省中标金额：
            南方21省中标金额32亿，同比增长32%，其中：四川、湖北、上海增幅高于12%；广西、安徽、江苏增幅低于12%
            北方10省中标金额12亿，同比增长12%，其中：吉林、内蒙古、黑龙江增幅高于12%；辽宁、山西、山东增幅低于12%
""",
            )
        )
        result.append(
            PPTContent(type="graph", content=generate_graph(dfs, titles, path))
        )
        result.append(
            PPTContent(
                type="text",
                content="""分省中标份额：
南方21省中标份额43%，较去年同期+21pp。其中：江西、湖北、甘肃份额高于21%， 广西、四川、江苏份额低于21% ；湖北、西藏、云南省份额提升， 贵州、新疆、陕西省份额下降
北方10省中标份额21%，较去年同期-18pp。其中：吉林、北京、河北份额高于12%，天津、河南、内蒙古份额低于12% ；辽宁、山东、黑龙江省份额提升， 陕西、北京省份额下降
        """,
            )
        )
        result.append(
            PPTContent(type="graph", content=generate_graph(dfs, titles, path))
        )
        results.append(result)
        self.results = results

        # slide 3
        result = []
        dfs = generate_random_province()
        titles = ["南方21省", "北方10省"]
        result.append(PPTContent(type="text", content="全市场中标-3：泛政务类"))
        result.append(
            PPTContent(
                type="text",
                content="""分省中标金额：
                    南方21省中标金额32亿，同比增长32%，其中：四川、湖北、上海增幅高于12%；广西、安徽、江苏增幅低于12%
                    北方10省中标金额12亿，同比增长12%，其中：吉林、内蒙古、黑龙江增幅高于12%；辽宁、山西、山东增幅低于12%
        """,
            )
        )
        result.append(
            PPTContent(type="graph", content=generate_graph(dfs, titles, path))
        )
        result.append(
            PPTContent(
                type="text",
                content="""分省中标份额：
        南方21省中标份额43%，较去年同期+21pp。其中：江西、湖北、甘肃份额高于21%， 广西、四川、江苏份额低于21% ；湖北、西藏、云南省份额提升， 贵州、新疆、陕西省份额下降
        北方10省中标份额21%，较去年同期-18pp。其中：吉林、北京、河北份额高于12%，天津、河南、内蒙古份额低于12% ；辽宁、山东、黑龙江省份额提升， 陕西、北京省份额下降
                """,
            )
        )
        result.append(
            PPTContent(type="graph", content=generate_graph(dfs, titles, path))
        )
        results.append(result)
        self.results = results

        # slide 4
        result = []
        dfs = generate_random_province()
        titles = ["南方21省", "北方10省"]
        result.append(PPTContent(type="text", content="全市场中标-4：社会民生类"))
        result.append(
            PPTContent(
                type="text",
                content="""分省中标金额：
                    南方21省中标金额32亿，同比增长32%，其中：四川、湖北、上海增幅高于12%；广西、安徽、江苏增幅低于12%
                    北方10省中标金额12亿，同比增长12%，其中：吉林、内蒙古、黑龙江增幅高于12%；辽宁、山西、山东增幅低于12%
        """,
            )
        )
        result.append(
            PPTContent(type="graph", content=generate_graph(dfs, titles, path))
        )
        result.append(
            PPTContent(
                type="text",
                content="""分省中标份额：
        南方21省中标份额43%，较去年同期+21pp。其中：江西、湖北、甘肃份额高于21%， 广西、四川、江苏份额低于21% ；湖北、西藏、云南省份额提升， 贵州、新疆、陕西省份额下降
        北方10省中标份额21%，较去年同期-18pp。其中：吉林、北京、河北份额高于12%，天津、河南、内蒙古份额低于12% ；辽宁、山东、黑龙江省份额提升， 陕西、北京省份额下降
                """,
            )
        )
        result.append(
            PPTContent(type="graph", content=generate_graph(dfs, titles, path))
        )
        results.append(result)
        self.results = results

        # slide 5
        result = []
        dfs = generate_random_province()
        titles = ["南方21省", "北方10省"]
        result.append(PPTContent(type="text", content="全市场中标-5：新型工业化"))
        result.append(
            PPTContent(
                type="text",
                content="""分省中标金额：
                    南方21省中标金额32亿，同比增长32%，其中：四川、湖北、上海增幅高于12%；广西、安徽、江苏增幅低于12%
                    北方10省中标金额12亿，同比增长12%，其中：吉林、内蒙古、黑龙江增幅高于12%；辽宁、山西、山东增幅低于12%
        """,
            )
        )
        result.append(
            PPTContent(type="graph", content=generate_graph(dfs, titles, path))
        )
        result.append(
            PPTContent(
                type="text",
                content="""分省中标份额：
        南方21省中标份额43%，较去年同期+21pp。其中：江西、湖北、甘肃份额高于21%， 广西、四川、江苏份额低于21% ；湖北、西藏、云南省份额提升， 贵州、新疆、陕西省份额下降
        北方10省中标份额21%，较去年同期-18pp。其中：吉林、北京、河北份额高于12%，天津、河南、内蒙古份额低于12% ；辽宁、山东、黑龙江省份额提升， 陕西、北京省份额下降
                """,
            )
        )
        result.append(
            PPTContent(type="graph", content=generate_graph(dfs, titles, path))
        )
        results.append(result)
        self.results = results
