import datetime
from functools import lru_cache

import numpy as np
import pandas as pd

from common.llm.llama_llm import get_llm
from config.doc_config import report_generate_llm_model
from nl2document.common.models.report_generate_model import (
    get_report_by_id,
    get_report_data_time_params,
    get_report_filter_params,
    get_report_section_data_op,
)
from nl2document.common.msg.report_generate_msg import (
    CreateOrSaveOutlineRequest,
    DataTimeParams,
    ColumnFilterValues,
    OutlineNode,
)
from nl2document.report_generate.report.data_op_executor import (
    exec_data_op,
    generate_report_section,
    generate_report_section_for_update,
)
from nl2document.report_generate.report.report_outline import (
    save_or_create_outline,
    copy_report,
)
from nl2document.report_generate.report.text_op_executor import get_text_op_executor
from nl2document.report_generate.xengine_data.model_data import get_model_all_data


def test_save_or_create_outline():
    req = CreateOrSaveOutlineRequest(
        dataFilterParams=[
            ColumnFilterValues(
                values=["100"],
                columnName="合同签约金额",
                operator="gt",
                columnCode="contract_signing_amount",
            ),
            ColumnFilterValues(
                values=["DICT业务"],
                columnName="业务收入分类",
                operator="in",
                columnCode="business_income_classification",
            ),
        ],
        dataTimeParams=DataTimeParams(
            timeColumn="默认时间维度",
            timeRangeStart="2021-01-03",
            timeRangeEnd="2024-03-31",
        ),
        modelName="dianxin_report_0819",
        reportTitle=f"报告_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}",
        reportIntention="生成一份经营分析报告",
        templateId=1,
        sceneId="",
        creator="",
        reportId=None,
    )
    save_or_create_outline(req)


def test_exec_data_op():
    report = get_report_by_id(31)
    data_time_params = get_report_data_time_params(31)
    data_filter_params = get_report_filter_params(31)
    report_section_data_op = get_report_section_data_op(31, 1)
    for section_data_op in report_section_data_op:
        print(
            exec_data_op(
                report.model_name, data_time_params, data_filter_params, section_data_op
            )
        )


def test_exec_data_op_1_2():
    id = 32
    report = get_report_by_id(id)
    data_time_params = get_report_data_time_params(id)
    data_filter_params = get_report_filter_params(id)
    report_section_data_op = get_report_section_data_op(id, 2)
    for section_data_op in report_section_data_op:
        print(
            exec_data_op(
                report.model_name, data_time_params, data_filter_params, section_data_op
            )
        )


def test_exec_data_op_1_3():
    id = 33
    report = get_report_by_id(id)
    data_time_params = get_report_data_time_params(id)
    data_filter_params = get_report_filter_params(id)
    report_section_data_op = get_report_section_data_op(id, 3)
    for section_data_op in report_section_data_op:
        print(
            exec_data_op(
                report.model_name, data_time_params, data_filter_params, section_data_op
            )
        )


def test_generate_report_section_for_update():
    report = get_report_by_id(68)
    outline_nodes = [OutlineNode(**node) for node in report.outline]
    generate_report_section_for_update(report, outline_nodes, 3)


def test_np_inf():
    params = [1e9, 2e9]
    bins: list[float] = [-np.inf]
    bins.extend(params)
    bins.append(np.inf)
    print(bins)


def test_llm_prompt():
    llm = get_llm(report_generate_llm_model)
    executor = get_text_op_executor(llm)
    inputs = [
        "各省分类2024年签约额总计:A类省的合同签约金额为57.82 亿;B类省的合同签约金额为33.19 亿;C类省的合同签约金额为10.40 亿;其他1的合同签约金额为7787.86 万;总部1的合同签约金额为2225.55 万;总部2的合同签约金额为3.62 亿;总部3的合同签约金额为19.76 万",
        "各省分类2024年签约额总计同比:A类省的增长率为0.18;B类省的增长率为0.00;C类省的增长率为-0.24;其他1的增长率为nan;总部1的增长率为16.39;总部2的增长率为2.15;总部3的增长率为-0.89",
        "各省2024年签约额总计:东北A省,东北B省,东北C省,其他1,华东A省,华东C省,华东D省,华东F省,华中A省,华中B省,华中C省,华中D省,华北A省,华北B省,华北C省,华北D省,华北E省,华南A省,华南B省,华南C省,总部1,总部2,总部3,西北A省,西北B省,西北C省,西北D省,西北E省,西南A省,西南B省,西南C省,西南D省,西南E省的合同签约金额小于10.00 亿;华东B省,华东E省的合同签约金额在10.00 亿到20.00 亿之间",
        "各省2024年签约额总计同比:东北B省,东北C省,华东A省,华东D省,华东F省,华中B省,华中C省,华北B省,华北C省,华南A省,华南B省,华南C省,总部3,西北A省,西北B省,西北E省,西南B省,西南C省,西南D省,西南E省的增长率小于0.00;华东B省,西北C省,西北D省,西南A省的增长率在0.10到0.30之间;东北A省,华东C省,华东E省,华中A省,华中D省,华北A省,华北D省,华北E省,总部1,总部2的增长率大于等于0.30",
    ]

    prompt = """你是一个数据描述的润色及改写工具，根据提供给你的待润色内容仿照预期格式进行数据描述产出。注意，不可对原内容的数据进行篡改，只允许对文本描述进行润色优化，并遵循以下要求：
增长率小于0.00%，描述为“负增长”
增长率大于等于X%，描述为“增长率超过X%”
    参考以下预期格式：
    ```
    2024年1-4月，A类省合同签约额X亿，同比增幅为X%；B类省合同签约额X亿，同比增幅为X%；C类省合同签约额X亿，同比增幅为X%。 南方省合计签约额X亿，同比增幅为X%；北方省合计签约X亿，同比增幅为X%。
江苏、上海、广东、浙江、安徽、湖北、四川省签约额超过X亿，安徽、湖北、湖南、宁夏、四川签约额在X-X亿之间，重庆、福建、江西、贵州、山东签约额在X亿以下。
从签约同比看：江苏、上海、广东、浙江、安徽、湖北、四川签约额同比超过X%，增长较高；安徽、湖北、湖南、宁夏、四川签约同比在X%-X%之间；重庆、福建、江西、贵州、山东签约额增幅低于X%；辽宁、河北、内蒙、山西签约额同比负增长。
    ```
    待润色的内容如下：
    ```
    {text_list}
    ```
    """
    return executor.execute(prompt, [], inputs)


def test_llm_prompt_hangye():
    llm = get_llm(report_generate_llm_model)
    executor = get_text_op_executor(llm)
    inputs = [
        "各行业2024年签约额总计:大企业C,大企业D,大企业E,大企业F,大企业H,大政务A,大政务B,大政务C,大政务F,大民生A,大民生C,大民生D的合同签约金额小于10.00 亿;大企业A,大企业B,大政务D,大政务E,大民生B的合同签约金额在10.00 亿到20.00 亿之间",
        "各行业2024年签约额总计同比:大企业D,大企业E,大企业F,大政务A,大政务C,大政务D,大政务E,大政务F,大民生A,大民生C的增长率小于0.00;大企业B,大政务B的增长率在0.00到0.30之间;大企业A,大企业C,大企业H,大民生B,大民生D的增长率大于等于0.30",
    ]
    prompt = """你是一个数据描述的润色及改写工具，根据提供给你的待润色内容仿照预期格式进行数据描述产出。注意，不可对原内容的数据进行篡改，只允许对文本描述进行润色优化，并遵循以下要求：
增长率小于0.00%，描述为“负增长”
增长率大于等于X%，描述为“增长率超过X%”
    参考以下预期格式：
    ```
分行业看，2024年1-4月卫健、能源化工、住建、文宣4个行业签约额超过20亿，车联网、金融、教育、政务、要客签约额在10亿到20亿之间；交通物流、商客、互二、工业签约额在10亿以下。
从签约额同比看，卫健、能源化工、住建、文宣4个行业同比超过30%；交通物流、商客、互二、工业4个行业同比30%以下；互一、应急、政法公安、农业农村、车联网、金融、教育、政务、要客9个行业负增长。
从环比变化看：能源化工、住建2个行业环比增幅高于15%，保持快速增长趋势；卫健、文宣、交通物流、商客、互二、工业环比低于10%；要客、政务、教育、车联网4个行业环比负增长，需要引起重视。    ```
    待润色的内容如下：
    ```
    {text_list}
    ```
    """
    executor.execute(prompt, [], inputs)


# 在2024年1-4月期间，中国电信累计签约合同额达到248.93亿，同比增长0.56%。与去年同期相比，签约额波动增长0.15个百分点（2023年4月同期签约同比为0.09%）。其中，4月当月签约额为106.04亿，当月同比增长0.09%，环比增长1.08%。整体签约增幅呈现出波动上升的趋势。
# 2024年1-4月，A类省合同签约额57.82亿，同比增幅为0.18%；B类省合同签约额33.19亿，同比增幅为0.00%；C类省合同签约额10.40亿，同比增幅为负增长。其他1类省合同签约额7787.86万，增长率数据不详；总部1类省合同签约额3.62亿，同比增幅为16.39%；总部2类省合同签约额2.22亿，同比增幅为2.15%；总部3类省合同签约额19.76万，同比增幅为负增长。\n\n南方省合计签约额X亿（具体数值未提供），同比增幅为X%（具体数值未提供）；北方省合计签约X亿（具体数值未提供），同比增幅为X%（具体数值未提供）。\n\n江苏、上海、广东、浙江、安徽、湖北、四川省签约额超过10亿；安徽、湖北、湖南、宁夏、四川省签约额在10-20亿之间；东北A省、东北B省、东北C省、其他1、华东A省、华东C省、华东D省、华东F省、华中A省、华中B省、华中C省、华中D省、华北A省、华北B省、华北C省、华北D省、华北E省、华南A省、华南B省、华南C省、总部1、总部2、西北A省、西北B省、西北C省、西北D省、西北E省、西南A省、西南B省、西南C省、西南D省、西南E省的合同签约金额小于10.00亿；华东B省、华东E省的合同签约金额在10.00亿到20.00亿之间。\n\n从签约同比看：东北A省、华东C省、华东E省、华中A省、华中D省、华北A省、华北D省、华北E省、总部1、总部2的签约额同比超过0.30%，增长较高；华东B省、西北C省、西北D省、西南A省的增长率在0.10%到0.30%之间；东北B省、东北C省、华东A省、华东D省、华东F省、华中B省、华中C省、华北B省、华北C省、华南A省、华南B省、华南C省、总部3、西北A省、西北B省、西北E省、西南B省、西南C省、西南D省、西南E省的增长率小于0.00%，呈现负增长。其他1类省增长率数据不详。
# 分行业观察，2024年签约额数据显示，大企业C、大企业D、大企业E、大企业F、大企业H、大政务A、大政务B、大政务C、大政务F、大民生A、大民生C、大民生D的签约金额均低于10亿；而大企业A、大企业B、大政务D、大政务E、大民生B的签约金额则在10亿至20亿之间。\n\n从签约额同比增速来看，大企业D、大企业E、大企业F、大政务A、大政务C、大政务D、大政务E、大政务F、大民生A、大民生C的同比增长率均小于0.00%，呈现负增长态势；大企业B、大政务B的增长率介于0.00%至0.30%之间；大企业A、大企业C、大企业H、大民生B、大民生D的增长率则不低于0.30%，显示出正增长趋势。


def test_llm_prompt_summary():
    llm = get_llm(report_generate_llm_model)
    executor = get_text_op_executor(llm)
    prompt = """你是一个经营分析报告的撰写者，我将会给你提供一些必要的数据以及对应的输出要求，帮助你一步一步的生成最终的报告文档。
经过前几轮的对话，你已经帮我生成了经营分析报告的前几个章节，现在需要你对上文生成最后的总结与建议，前四章节内容如下：
{depends_on}

要求：
1.你需要根据前四个章节，简要总结报告的主要发现和建议，基于分析结果，提出具体的改进建议、应对策略、或进一步分析方向。
2.在建议部分（重要），希望你能充分发挥想象力，结合你知道的常识和知识，给出建议并简单给出原因，比如可以涉及不同地区的经济发展、地理位置、交通等等，在已知内容上进行扩展延伸。
3.请参考如下格式，只需要输出第五章内容即可，总字数请限制在500字以内：
    ```
    ## 总结与建议
    ```
下面请开始撰写：
    """
    depends_on = [
        "在2024年1-4月期间，中国电信累计签约合同额达到248.93亿，同比增长0.56%。与去年同期相比，签约额波动增长0.15个百分点（2023年4月同期签约同比为0.09%）。其中，4月当月签约额为106.04亿，当月同比增长0.09%，环比增长1.08%。整体签约增幅呈现出波动上升的趋势。",
        "2024年1-4月，A类省合同签约额57.82亿，同比增幅为0.18%；B类省合同签约额33.19亿，同比增幅为0.00%；C类省合同签约额10.40亿，同比增幅为负增长。其他1类省合同签约额7787.86万，增长率数据不详；总部1类省合同签约额3.62亿，同比增幅为16.39%；总部2类省合同签约额2.22亿，同比增幅为2.15%；总部3类省合同签约额19.76万，同比增幅为负增长。\n\n南方省合计签约额X亿（具体数值未提供），同比增幅为X%（具体数值未提供）；北方省合计签约X亿（具体数值未提供），同比增幅为X%（具体数值未提供）。\n\n江苏、上海、广东、浙江、安徽、湖北、四川省签约额超过10亿；安徽、湖北、湖南、宁夏、四川省签约额在10-20亿之间；东北A省、东北B省、东北C省、其他1、华东A省、华东C省、华东D省、华东F省、华中A省、华中B省、华中C省、华中D省、华北A省、华北B省、华北C省、华北D省、华北E省、华南A省、华南B省、华南C省、总部1、总部2、西北A省、西北B省、西北C省、西北D省、西北E省、西南A省、西南B省、西南C省、西南D省、西南E省的合同签约金额小于10.00亿；华东B省、华东E省的合同签约金额在10.00亿到20.00亿之间。\n\n从签约同比看：东北A省、华东C省、华东E省、华中A省、华中D省、华北A省、华北D省、华北E省、总部1、总部2的签约额同比超过0.30%，增长较高；华东B省、西北C省、西北D省、西南A省的增长率在0.10%到0.30%之间；东北B省、东北C省、华东A省、华东D省、华东F省、华中B省、华中C省、华北B省、华北C省、华南A省、华南B省、华南C省、总部3、西北A省、西北B省、西北E省、西南B省、西南C省、西南D省、西南E省的增长率小于0.00%，呈现负增长。其他1类省增长率数据不详。"
        "分行业观察，2024年签约额数据显示，大企业C、大企业D、大企业E、大企业F、大企业H、大政务A、大政务B、大政务C、大政务F、大民生A、大民生C、大民生D的签约金额均低于10亿；而大企业A、大企业B、大政务D、大政务E、大民生B的签约金额则在10亿至20亿之间。\n\n从签约额同比增速来看，大企业D、大企业E、大企业F、大政务A、大政务C、大政务D、大政务E、大政务F、大民生A、大民生C的同比增长率均小于0.00%，呈现负增长态势；大企业B、大政务B的增长率介于0.00%至0.30%之间；大企业A、大企业C、大企业H、大民生B、大民生D的增长率则不低于0.30%，显示出正增长趋势。",
    ]
    executor.execute(prompt, depends_on, [])


def test_llm_prompt_section_intention():
    llm = get_llm(report_generate_llm_model)
    input = """2024年1-3月期间，我国电信行业签约合同额整体呈现波动上升的趋势，但多数行业及省份仍面临负增长压力。具体来看，大企业A表现突出，签约额增长率达到或超过15%，而其他行业均呈现负增长。此外，东北、华中、华南等地区省份的签约额普遍低于10亿，且多数省份签约额同比负增长。

针对上述情况，以下提出以下建议：

1. 针对负增长明显的行业，建议加大产品创新和市场拓展力度，提升服务质量，以满足客户需求，增加市场份额。同时，关注行业发展趋势，及时调整经营策略。

2. 对于表现良好的大企业A，建议持续优化产品结构，巩固市场地位，并探索与其他行业的合作机会，实现业务多元化。

3. 针对不同地区的签约额差异，建议结合地理位置、经济发展水平、交通等因素，制定有针对性的市场开发策略。例如，在东北、华中、华南等地区，可以加大基础设施建设投入，提升网络覆盖范围和服务质量，吸引更多客户。

4. 对于签约额同比负增长的省份，建议深入了解当地市场环境，分析原因，制定针对性的应对措施。如加强与当地政府、企业的合作，推动项目落地，提升业务规模。

5. 进一步加强市场调研，关注客户需求变化，优化产品和服务，提升客户满意度。同时，加强内部管理，降低成本，提高运营效率。

6. 探索跨界合作，如与互联网、金融、医疗等行业融合，开发创新产品，拓展业务领域，实现业务增长。

总之，电信行业应关注市场变化，积极调整经营策略，加大创新力度，以实现可持续发展。
    """
    prompt = """你是一个专业、负责、有深度的企业经营分析人员，正在进行报告撰写，请根据以下需求对下述的文本素材进行整理改写,
    限制字数在{min_word}-{max_word}之间
    需求:
    ```
    {intention}
    ```
    文本内容:
    ```
    {input}
    ```
    """
    response = llm.complete(
        prompt.format(
            intention="根据上文给出整体的总结与建议",
            input=input,
            min_word=0,
            max_word=1024,
        )
    )
    print(response.text)


def test_copy_report():
    report_id = 110
    copy_report(report_id)


@lru_cache(maxsize=16)
def get_df_temp_for_test(model_name: str):
    if model_name == "1":
        df1 = pd.DataFrame(
            [
                {"id": 1, "name": "张三", "age": 18},
                {"id": 2, "name": "李四", "age": 19},
                {"id": 3, "name": "王五", "age": 20},
                {"id": 4, "name": "赵六", "age": 21},
                {"id": 5, "name": "钱七", "age": 22},
                {"id": 6, "name": "孙八", "age": 23},
            ]
        )
        return df1
    if model_name == "2":
        df2 = pd.DataFrame(
            [
                {"id": 1, "name": "张三", "age": 21},
                {"id": 2, "name": "李四", "age": 22},
                {"id": 3, "name": "王五", "age": 23},
                {"id": 4, "name": "赵六", "age": 24},
                {"id": 5, "name": "钱七", "age": 25},
                {"id": 6, "name": "孙八", "age": 26},
            ]
        )
        return df2


def test_lru_df():
    df = get_df_temp_for_test("1")
    df = df[df["age"] > 20]
    print(df)

    df_3 = get_df_temp_for_test("1")
    print(df_3)


if __name__ == "__main__":
    df = get_model_all_data("dianxin_report_0819")
    column_mapping = {"项目管控部门名称": "行业", "横向汇总省份v2": "省份"}
    for ori_col, map_col in column_mapping.items():
        df.rename(columns={ori_col: map_col}, inplace=True)
    data_time_params = DataTimeParams(
        timeColumn="默认时间维度", timeRangeStart="2020-01-01", timeRangeEnd="2024-03-31"
    )
    df[data_time_params.timeColumn] = pd.to_datetime(df[data_time_params.timeColumn])
    if data_time_params:
        df = df[
            (df[data_time_params.timeColumn] >= data_time_params.timeRangeStart)
            & (df[data_time_params.timeColumn] <= data_time_params.timeRangeEnd)
        ]
    df = df[(df["默认时间维度"] >= "2024-03-01") & (df["默认时间维度"] <= "2024-03-31")]
    df = df.agg({"合同签约金额": "sum"})
    print(df.iloc[0])
