from dotenv import load_dotenv
from pathlib import Path

from sqlalchemy import Integer, ForeignKey, String, JSON, Text
from sqlalchemy.orm import Mapped, mapped_column

# specify path to easily change env in docker
env_file = Path(__file__).parent / ".env"
print(f"load dotenv {env_file}")
load_dotenv(dotenv_path=env_file, override=True)
# both app_config and doc_config needs load_dotenv

from nl2document.common.models.report_generate_model import (
    ReportTemplate,
    save_and_create_template,
)
from common.db_model.model import engine, Base


def import_template():
    report_template = ReportTemplate(
        id=1,
        name="测试报告模板",
        create_user="admin",
        thumbnail_path="ask-doc/report-generate/report.pdf.png",
        outline=[
            {
                "id": 1,
                "title": "整体签约情况",
                "content": "2024年1-3月，中国电信累计签约合同额331亿元，同比增长3.2%，与去年同期相比下降4pp（2023年4月同期签约同比为7.2%）。4月当月签约77.82亿，当月同比4.02%，环比-1.2%。整体签约增幅呈现缓慢下降的趋势。",
                "allowChildren": False,
                "maxChildrenCount": 0,
            },
            {
                "id": 2,
                "title": "分行业签约",
                "content": """分行业看，2024年1-3月卫健、能源化工、住建、文宣4个行业签约额超过20亿，车联网、金融、教育、政务、要客签约额在10亿到20亿之间；交通物流、商客、互二、工业签约额在10亿以下。（注意所有行业数量加起来=行业总数）
从签约额同比看，卫健、能源化工、住建、文宣4个行业同比超过30%；交通物流、商客、互二、工业4个行业同比30%以下；互一、应急、政法公安、农业农村、车联网、金融、教育、政务、要客9个行业负增长。
从环比变化看：能源化工、住建2个行业环比增幅高于15%，保持快速增长趋势；卫健、文宣、交通物流、商客、互二、工业环比低于10%；要客、政务、教育、车联网4个行业环比负增长，需要引起重视。""",
                "allowChildren": False,
                "maxChildrenCount": 0,
            },
            {
                "id": 3,
                "title": "分省签约",
                "content": """2024年1-3月，A类省合同签约额**亿，同比增幅为**%；B类省合同签约额**亿，同比增幅为**%；C类省合同签约额**亿，同比增幅为**%。 南方省合计签约额**亿，同比增幅为**%；北方省合计签约**亿，同比增幅为**%。
江苏、上海、广东、浙江、安徽、湖北、四川省签约额超过100亿，安徽、湖北、湖南、宁夏、四川签约额在50-100亿之间，重庆、福建、江西、贵州、山东签约额在50亿以下。（注意分类要补全，省份名称要把31省涵盖完整）
从签约同比看：江苏、上海、广东、浙江、安徽、湖北、四川签约额同比超过30%，增长较高；安徽、湖北、湖南、宁夏、四川签约同比在10%-30%之间；重庆、福建、江西、贵州、山东签约额增幅低于10%；辽宁、河北、内蒙、山西签约额同比负增长。
                    """,
                "allowChildren": False,
                "maxChildrenCount": 0,
            },
            {
                "id": 4,
                "title": "总结及建议",
                "content": """大模型总结: 逐个章节总结
                    """,
                "allowChildren": False,
                "maxChildrenCount": 0,
                "dependsOn": [1, 2, 3],
            },
        ],
    )
    Base.metadata.create_all(engine)
    save_and_create_template(report_template)


if __name__ == "__main__":
    import_template()
