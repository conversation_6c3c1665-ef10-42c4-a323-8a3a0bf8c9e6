import datetime
import time
from concurrent.futures import ThreadPoolExecutor, Future
from typing import List, Dict

from nl2document.report_generate.report.graph_executor import (
    build_dependency_graph,
    topological_sort,
)


# 假设 OutlineNode 类定义如下
class OutlineNode:
    def __init__(
        self,
        id,
        title,
        content,
        allowChildren,
        maxChildrenCount,
        children=None,
        dependsOn=None,
    ):
        self.id = id
        self.title = title
        self.content = content
        self.allowChildren = allowChildren
        self.maxChildrenCount = maxChildrenCount
        self.children = children if children is not None else []
        self.dependsOn = dependsOn if dependsOn is not None else []


# 示例树状结构
nodes = [
    OutlineNode(
        id=1, title="一、整体签约情况", content="", allowChildren=False, maxChildrenCount=0
    ),
    OutlineNode(
        id=2, title="二、分行业签约", content="", allowChildren=False, maxChildrenCount=0
    ),
    OutlineNode(
        id=3, title="三、分省签约", content="", allowChildren=False, maxChildrenCount=0
    ),
    OutlineNode(
        id=4,
        title="四、总结及建议",
        content="",
        allowChildren=False,
        maxChildrenCount=0,
        dependsOn=[1, 2, 3],
    ),
]


# 模拟执行节点的函数
def execute_node(node: OutlineNode, results: Dict[int, Future]):
    # 模拟获取依赖节点的结果
    if node.dependsOn:
        print(f"wait depends on")
        dependencies_results = [results[dep_id].result() for dep_id in node.dependsOn]
        # 可以使用这些结果来做进一步计算
        print(
            f"Node {node.id} is using results from dependencies: {dependencies_results}"
        )
    # 假设每个节点返回其 ID 作为结果
    result = f"Result of node {node.id}"
    print(f"Executing node {node.title}, result: {result}")
    time.sleep(1)
    return result


# 主执行函数
def execute_nodes_concurrently(nodes: List[OutlineNode]):
    graph, in_degree = build_dependency_graph(nodes)
    execution_order = topological_sort(nodes, graph, in_degree)
    print(f"Execution order: {execution_order}")
    # 创建 ID 到 Node 对应的映射
    node_map = {node.id: node for node in nodes}
    results = {}  # 用于存储每个节点的 Future 对象

    with ThreadPoolExecutor(max_workers=5) as executor:
        for node_id in execution_order:
            node = node_map[node_id]
            # 提交任务，并将 Future 存储在 results 字典中
            future = executor.submit(execute_node, node, results)
            results[node_id] = future

        # 等待所有任务完成
        for node_id, future in results.items():
            try:
                result = future.result()  # 获取任务结果
                print(f"Node {node_id} executed successfully with result: {result}")
            except Exception as e:
                print(f"Node {node_id} execution failed with exception: {e}")
    for node_id, future in results.items():
        print(node_id, future.result())


if __name__ == "__main__":
    print(f"Executing nodes concurrently...{datetime.datetime.now()}")
    execute_nodes_concurrently(nodes)
    print(f"Execution completed at {datetime.datetime.now()}")
