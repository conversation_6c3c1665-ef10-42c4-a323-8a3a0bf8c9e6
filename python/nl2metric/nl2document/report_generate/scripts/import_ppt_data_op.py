from dotenv import load_dotenv

env_file = "/Users/<USER>/ask-bi/python/nl2metric/.env"
print(f"load dotenv {env_file}")
load_dotenv(dotenv_path=env_file, override=True)
from nl2document.common.models.ppt_generate_model import (
    save_and_create_ppt_data_op_list,
    PPTDataOp,
)


ppt_data_op_list = [
    PPTDataOp(
        template_id=1,
        data_op_id=1,
        name="全市场中标",
        compute_type="总计",
        operator="sum",
        metric="中标金额",
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=2,
        name="全市场中标同比",
        compute_type="总计同比",
        operator="sum",
        metric="中标金额",
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=3,
        name="电信中标",
        compute_type="总计",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=4,
        name="电信中标同比",
        compute_type="总计同比",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=5,
        name="电信中标去年同期同比",
        compute_type="总计去年同期同比",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=177,
        name="移动中标",
        compute_type="总计",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["移动"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=178,
        name="移动中标同比",
        compute_type="总计同比",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["移动"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=179,
        name="移动中标去年同期同比",
        compute_type="总计去年同期同比",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["移动"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=180,
        name="联通中标",
        compute_type="总计",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["联通"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=181,
        name="联通中标同比",
        compute_type="总计同比",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["联通"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=182,
        name="联通中标去年同期同比",
        compute_type="总计去年同期同比",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["联通"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=6,
        name="泛政务领域客户中标金额",
        compute_type="总计",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["农业农村", "住建", "应急", "政法公安", "政务", "要客"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=95,
        name="泛政务领域客户中标金额同比",
        compute_type="总计同比",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["农业农村", "住建", "应急", "政法公安", "政务", "要客"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=96,
        name="泛政务领域客户去年同期同比",
        compute_type="总计去年同期同比",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["农业农村", "住建", "应急", "政法公安", "政务", "要客"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=7,
        name="社会民生领域客户中标金额",
        compute_type="总计",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["商客", "文宣", "互联网一部", "互联网二部", "金融", "教育", "卫健"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=8,
        name="社会民生领域客户中标金额同比",
        compute_type="总计同比",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["商客", "文宣", "互联网一部", "互联网二部", "金融", "教育", "卫健"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=97,
        name="社会民生领域客户去年同期同比",
        compute_type="总计去年同期同比",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["商客", "文宣", "互联网一部", "互联网二部", "金融", "教育", "卫健"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=9,
        name="新型工业化领域客户中标金额",
        compute_type="总计",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["能源化工", "工业", "车联网", "交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=10,
        name="新型工业化领域客户中标金额同比",
        compute_type="总计同比",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["能源化工", "工业", "车联网", "交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=98,
        name="新型工业化领域客户去年同期同比",
        compute_type="总计去年同期同比",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["能源化工", "工业", "车联网", "交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=11,
        name="分行业同比",
        compute_type="总计同比",
        operator="sum",
        metric="中标金额",
        group_by="行业",
        segmentation_options="mean",
        enum_order="desc",
        data_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=12,
        name="全市场中标泛政务图算子",
        compute_type="ppt图模版1",
        operator="sum",
        metric="中标金额",
        group_by="行业",
        data_filter=[
            {
                "values": ["农业农村", "住建", "应急", "政法公安", "政务", "要客"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=13,
        name="全市场中标社会民生图算子",
        compute_type="ppt图模版1",
        operator="sum",
        metric="中标金额",
        group_by="行业",
        data_filter=[
            {
                "values": ["商客", "文宣", "互联网一部", "互联网二部", "金融", "教育", "卫健"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=14,
        name="全市场中标新型工业化图算子",
        compute_type="ppt图模版1",
        operator="sum",
        metric="中标金额",
        group_by="行业",
        data_filter=[
            {
                "values": ["能源化工", "工业", "车联网", "交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=15,
        name="电信全市场中标份额",
        compute_type="占比",
        operator="sum",
        metric="中标金额",
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=99,
        name="电信全市场中标较去年同期",
        compute_type="占比变化",
        operator="sum",
        metric="中标金额",
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=16,
        name="移动全市场中标份额",
        compute_type="占比",
        operator="sum",
        metric="中标金额",
        percentage_filter=[
            {
                "values": ["移动"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=100,
        name="移动全市场中标较去年同期",
        compute_type="占比变化",
        operator="sum",
        metric="中标金额",
        percentage_filter=[
            {
                "values": ["移动"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=17,
        name="联通全市场中标份额",
        compute_type="占比",
        operator="sum",
        metric="中标金额",
        percentage_filter=[
            {
                "values": ["联通"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=101,
        name="联通全市场中标较去年同期",
        compute_type="占比变化",
        operator="sum",
        metric="中标金额",
        percentage_filter=[
            {
                "values": ["联通"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=18,
        name="泛政务全市场中标份额",
        compute_type="占比",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["农业农村", "住建", "应急", "政法公安", "政务", "要客"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=158,
        name="泛政务全市场中标份额较去年同期",
        compute_type="占比变化",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["农业农村", "住建", "应急", "政法公安", "政务", "要客"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=19,
        name="社会民生全市场中标份额",
        compute_type="占比",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["商客", "文宣", "互联网一部", "互联网二部", "金融", "教育", "卫健"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=159,
        name="社会民生全市场中标份额较去年同期",
        compute_type="占比变化",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["商客", "文宣", "互联网一部", "互联网二部", "金融", "教育", "卫健"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=20,
        name="新型工业化全市场中标份额",
        compute_type="占比",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["能源化工", "工业", "车联网", "交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=160,
        name="新型工业化全市场中标份额较去年同期",
        compute_type="占比变化",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["能源化工", "工业", "车联网", "交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=21,
        name="分行业全市场中标份额变化",
        compute_type="占比变化",
        operator="sum",
        metric="中标金额",
        group_by="行业",
        segmentation_options="mean",
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=22,
        name="全市场中标份额泛政务图算子",
        compute_type="ppt图模版2",
        operator="sum",
        metric="中标金额",
        group_by="行业",
        data_filter=[
            {
                "values": ["农业农村", "住建", "应急", "政法公安", "政务", "要客"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=23,
        name="全市场中标份额社会民生图算子",
        compute_type="ppt图模版2",
        operator="sum",
        metric="中标金额",
        group_by="行业",
        data_filter=[
            {
                "values": ["商客", "文宣", "互联网一部", "互联网二部", "金融", "教育", "卫健"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=24,
        name="全市场中标份额新型工业化图算子",
        compute_type="ppt图模版2",
        operator="sum",
        metric="中标金额",
        group_by="行业",
        data_filter=[
            {
                "values": ["能源化工", "工业", "车联网", "交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    #################### Slide 2 ####################
    PPTDataOp(
        template_id=1,
        data_op_id=25,
        name="南方21省中标金额",
        compute_type="总计",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=26,
        name="南方21省中标金额同比",
        compute_type="总计同比",
        operator="sum",
        group_by="省份",
        metric="中标金额",
        segmentation_options="mean",
        enum_order="desc",
        data_filter=[
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=27,
        name="北方10省中标金额",
        compute_type="总计",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=28,
        name="北方10省中标金额同比",
        compute_type="总计同比",
        operator="sum",
        group_by="省份",
        metric="中标金额",
        segmentation_options="mean",
        enum_order="desc",
        data_filter=[
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=29,
        name="全市场中标南方21省图算子",
        compute_type="ppt图模版1",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=30,
        name="全市场中标北方10省图算子",
        compute_type="ppt图模版1",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=31,
        name="南方21省中标份额",
        compute_type="占比",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            }
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=161,
        name="南方21省中标份额变化",
        compute_type="占比变化",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            }
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=32,
        name="南方21省分省份中标份额",
        compute_type="占比",
        operator="sum",
        group_by="省份",
        metric="中标金额",
        segmentation_options="mean",
        enum_order="desc",
        data_filter=[
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            }
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=33,
        name="南方21省分省份中标份额变化",
        compute_type="占比变化",
        operator="sum",
        group_by="省份",
        metric="中标金额",
        segmentation_options="mean",
        data_filter=[
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            }
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=34,
        name="北方10省中标份额",
        compute_type="占比",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            }
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=162,
        name="北方10省中标份额变化",
        compute_type="占比变化",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            }
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=35,
        name="北方10省分省份中标份额",
        compute_type="占比",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        segmentation_options="mean",
        enum_order="desc",
        data_filter=[
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            }
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=36,
        name="北方10省分省份中标份额变化",
        compute_type="占比变化",
        operator="sum",
        metric="中标金额",
        segmentation_options="mean",
        group_by="省份",
        data_filter=[
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            }
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=37,
        name="全市场中标南方21省图算子",
        compute_type="ppt图模版2",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=38,
        name="全市场中标北方10省图算子",
        compute_type="ppt图模版2",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            }
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    #################### Slide 3 ####################
    PPTDataOp(
        template_id=1,
        data_op_id=39,
        name="南方21省中标金额",
        compute_type="总计",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["农业农村", "住建", "应急", "政法公安", "政务", "要客"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=40,
        name="南方21省中标金额同比",
        compute_type="总计同比",
        operator="sum",
        group_by="省份",
        metric="中标金额",
        segmentation_options="mean",
        enum_order="desc",
        data_filter=[
            {
                "values": ["农业农村", "住建", "应急", "政法公安", "政务", "要客"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=41,
        name="北方10省中标金额",
        compute_type="总计",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["农业农村", "住建", "应急", "政法公安", "政务", "要客"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=42,
        name="北方10省中标金额同比",
        compute_type="总计同比",
        operator="sum",
        group_by="省份",
        metric="中标金额",
        segmentation_options="mean",
        enum_order="desc",
        data_filter=[
            {
                "values": ["农业农村", "住建", "应急", "政法公安", "政务", "要客"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=43,
        name="全市场中标南方21省图算子",
        compute_type="ppt图模版1",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["农业农村", "住建", "应急", "政法公安", "政务", "要客"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=44,
        name="全市场中标北方10省图算子",
        compute_type="ppt图模版1",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["农业农村", "住建", "应急", "政法公安", "政务", "要客"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=45,
        name="南方21省中标份额",
        compute_type="占比",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["农业农村", "住建", "应急", "政法公安", "政务", "要客"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=163,
        name="南方21省中标份额变化",
        compute_type="占比变化",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["农业农村", "住建", "应急", "政法公安", "政务", "要客"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=46,
        name="南方21省分省份中标份额",
        compute_type="占比",
        operator="sum",
        group_by="省份",
        segmentation_options="mean",
        enum_order="desc",
        metric="中标金额",
        data_filter=[
            {
                "values": ["农业农村", "住建", "应急", "政法公安", "政务", "要客"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=47,
        name="南方21省分省份中标份额变化",
        compute_type="占比变化",
        operator="sum",
        group_by="省份",
        metric="中标金额",
        segmentation_options="mean",
        data_filter=[
            {
                "values": ["农业农村", "住建", "应急", "政法公安", "政务", "要客"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=48,
        name="北方10省中标份额",
        compute_type="占比",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["农业农村", "住建", "应急", "政法公安", "政务", "要客"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=164,
        name="北方10省中标份额变化",
        compute_type="占比变化",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["农业农村", "住建", "应急", "政法公安", "政务", "要客"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=49,
        name="北方10省分省份中标份额",
        compute_type="占比",
        operator="sum",
        segmentation_options="mean",
        enum_order="desc",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["农业农村", "住建", "应急", "政法公安", "政务", "要客"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=50,
        name="北方10省分省份中标份额变化",
        compute_type="占比变化",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        segmentation_options="mean",
        data_filter=[
            {
                "values": ["农业农村", "住建", "应急", "政法公安", "政务", "要客"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=51,
        name="全市场中标南方21省图算子",
        compute_type="ppt图模版2",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["农业农村", "住建", "应急", "政法公安", "政务", "要客"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=52,
        name="全市场中标北方10省图算子",
        compute_type="ppt图模版2",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["农业农村", "住建", "应急", "政法公安", "政务", "要客"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    #################### Slide 4 ####################
    PPTDataOp(
        template_id=1,
        data_op_id=53,
        name="南方21省中标金额",
        compute_type="总计",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["商客", "文宣", "互联网一部", "互联网二部", "金融", "教育", "卫健"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=54,
        name="南方21省中标金额同比",
        compute_type="总计同比",
        operator="sum",
        group_by="省份",
        metric="中标金额",
        segmentation_options="mean",
        enum_order="desc",
        data_filter=[
            {
                "values": ["商客", "文宣", "互联网一部", "互联网二部", "金融", "教育", "卫健"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=55,
        name="北方10省中标金额",
        compute_type="总计",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["商客", "文宣", "互联网一部", "互联网二部", "金融", "教育", "卫健"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=56,
        name="北方10省中标金额同比",
        compute_type="总计同比",
        operator="sum",
        group_by="省份",
        metric="中标金额",
        segmentation_options="mean",
        enum_order="desc",
        data_filter=[
            {
                "values": ["商客", "文宣", "互联网一部", "互联网二部", "金融", "教育", "卫健"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=57,
        name="全市场中标南方21省图算子",
        compute_type="ppt图模版1",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["商客", "文宣", "互联网一部", "互联网二部", "金融", "教育", "卫健"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=58,
        name="全市场中标北方10省图算子",
        compute_type="ppt图模版1",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["商客", "文宣", "互联网一部", "互联网二部", "金融", "教育", "卫健"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=59,
        name="南方21省中标份额",
        compute_type="占比",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["商客", "文宣", "互联网一部", "互联网二部", "金融", "教育", "卫健"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=165,
        name="南方21省中标份额变化",
        compute_type="占比变化",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["商客", "文宣", "互联网一部", "互联网二部", "金融", "教育", "卫健"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=60,
        name="南方21省分省份中标份额",
        compute_type="占比",
        operator="sum",
        group_by="省份",
        segmentation_options="mean",
        enum_order="desc",
        metric="中标金额",
        data_filter=[
            {
                "values": ["商客", "文宣", "互联网一部", "互联网二部", "金融", "教育", "卫健"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=61,
        name="南方21省分省份中标份额变化",
        compute_type="占比变化",
        operator="sum",
        group_by="省份",
        metric="中标金额",
        segmentation_options="mean",
        data_filter=[
            {
                "values": ["商客", "文宣", "互联网一部", "互联网二部", "金融", "教育", "卫健"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=62,
        name="北方10省中标份额",
        compute_type="占比",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["商客", "文宣", "互联网一部", "互联网二部", "金融", "教育", "卫健"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=166,
        name="北方10省中标份额变化",
        compute_type="占比变化",
        operator="sum",
        metric="中标金额",
        segmentation_options="mean",
        data_filter=[
            {
                "values": ["商客", "文宣", "互联网一部", "互联网二部", "金融", "教育", "卫健"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=63,
        name="北方10省分省份中标份额",
        compute_type="占比",
        operator="sum",
        segmentation_options="mean",
        enum_order="desc",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["商客", "文宣", "互联网一部", "互联网二部", "金融", "教育", "卫健"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=64,
        name="北方10省分省份中标份额变化",
        compute_type="占比变化",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["商客", "文宣", "互联网一部", "互联网二部", "金融", "教育", "卫健"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=65,
        name="全市场中标南方21省图算子",
        compute_type="ppt图模版2",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["商客", "文宣", "互联网一部", "互联网二部", "金融", "教育", "卫健"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=66,
        name="全市场中标北方10省图算子",
        compute_type="ppt图模版2",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["商客", "文宣", "互联网一部", "互联网二部", "金融", "教育", "卫健"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    #################### Slide 5 ####################
    PPTDataOp(
        template_id=1,
        data_op_id=67,
        name="南方21省中标金额",
        compute_type="总计",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["能源化工", "工业", "车联网", "交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=68,
        name="南方21省中标金额同比",
        compute_type="总计同比",
        operator="sum",
        group_by="省份",
        metric="中标金额",
        segmentation_options="mean",
        enum_order="desc",
        data_filter=[
            {
                "values": ["能源化工", "工业", "车联网", "交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=69,
        name="北方10省中标金额",
        compute_type="总计",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["能源化工", "工业", "车联网", "交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=70,
        name="北方10省中标金额同比",
        compute_type="总计同比",
        operator="sum",
        group_by="省份",
        metric="中标金额",
        segmentation_options="mean",
        enum_order="desc",
        data_filter=[
            {
                "values": ["能源化工", "工业", "车联网", "交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=71,
        name="全市场中标南方21省图算子",
        compute_type="ppt图模版1",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["能源化工", "工业", "车联网", "交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=72,
        name="全市场中标北方10省图算子",
        compute_type="ppt图模版1",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["能源化工", "工业", "车联网", "交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=73,
        name="南方21省中标份额",
        compute_type="占比",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["能源化工", "工业", "车联网", "交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=167,
        name="南方21省中标份额变化",
        compute_type="占比变化",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["能源化工", "工业", "车联网", "交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=74,
        name="南方21省分省份中标份额",
        compute_type="占比",
        operator="sum",
        segmentation_options="mean",
        enum_order="desc",
        group_by="省份",
        metric="中标金额",
        data_filter=[
            {
                "values": ["能源化工", "工业", "车联网", "交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=75,
        name="南方21省分省份中标份额变化",
        compute_type="占比变化",
        operator="sum",
        group_by="省份",
        metric="中标金额",
        segmentation_options="mean",
        data_filter=[
            {
                "values": ["能源化工", "工业", "车联网", "交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=76,
        name="北方10省中标份额",
        compute_type="占比",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["能源化工", "工业", "车联网", "交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=168,
        name="北方10省中标份额变化",
        compute_type="占比变化",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["能源化工", "工业", "车联网", "交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=77,
        name="北方10省分省份中标份额",
        compute_type="占比",
        operator="sum",
        segmentation_options="mean",
        enum_order="desc",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["能源化工", "工业", "车联网", "交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=78,
        name="北方10省分省份中标份额变化",
        compute_type="占比变化",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        segmentation_options="mean",
        data_filter=[
            {
                "values": ["能源化工", "工业", "车联网", "交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=79,
        name="全市场中标南方21省图算子",
        compute_type="ppt图模版2",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["能源化工", "工业", "车联网", "交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=80,
        name="全市场中标北方10省图算子",
        compute_type="ppt图模版2",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["能源化工", "工业", "车联网", "交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    #################### Slide 6 ####################
    PPTDataOp(
        template_id=1,
        data_op_id=81,
        name="南方21省中标金额",
        compute_type="总计",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["金融"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=82,
        name="南方21省中标金额同比",
        compute_type="总计同比",
        operator="sum",
        group_by="省份",
        metric="中标金额",
        segmentation_options="mean",
        enum_order="desc",
        data_filter=[
            {
                "values": ["金融"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=83,
        name="北方10省中标金额",
        compute_type="总计",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["金融"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=84,
        name="北方10省中标金额同比",
        compute_type="总计同比",
        operator="sum",
        group_by="省份",
        metric="中标金额",
        segmentation_options="mean",
        enum_order="desc",
        data_filter=[
            {
                "values": ["金融"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=85,
        name="全市场中标南方21省图算子",
        compute_type="ppt图模版1",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["金融"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=86,
        name="全市场中标北方10省图算子",
        compute_type="ppt图模版1",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["金融"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=87,
        name="南方21省中标份额",
        compute_type="占比",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["金融"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=169,
        name="南方21省中标份额变化",
        compute_type="占比变化",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["金融"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=88,
        name="南方21省分省份中标份额",
        compute_type="占比",
        operator="sum",
        segmentation_options="mean",
        enum_order="desc",
        group_by="省份",
        metric="中标金额",
        data_filter=[
            {
                "values": ["金融"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=89,
        name="南方21省分省份中标份额变化",
        compute_type="占比变化",
        operator="sum",
        group_by="省份",
        metric="中标金额",
        segmentation_options="mean",
        data_filter=[
            {
                "values": ["金融"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=90,
        name="北方10省中标份额",
        compute_type="占比",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["金融"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=170,
        name="北方10省中标份额变化",
        compute_type="占比变化",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["金融"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=91,
        name="北方10省分省份中标份额",
        compute_type="占比",
        operator="sum",
        segmentation_options="mean",
        enum_order="desc",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["金融"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=92,
        name="北方10省分省份中标份额变化",
        compute_type="占比变化",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        segmentation_options="mean",
        data_filter=[
            {
                "values": ["金融"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=93,
        name="全市场中标南方21省图算子",
        compute_type="ppt图模版2",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["金融"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=94,
        name="全市场中标北方10省图算子",
        compute_type="ppt图模版2",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["金融"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    #################### Slide 7 ####################
    PPTDataOp(
        template_id=1,
        data_op_id=102,
        name="南方21省中标金额",
        compute_type="总计",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["政务"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=103,
        name="南方21省中标金额同比",
        compute_type="总计同比",
        operator="sum",
        group_by="省份",
        metric="中标金额",
        segmentation_options="mean",
        enum_order="desc",
        data_filter=[
            {
                "values": ["政务"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=104,
        name="北方10省中标金额",
        compute_type="总计",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["政务"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=105,
        name="北方10省中标金额同比",
        compute_type="总计同比",
        operator="sum",
        group_by="省份",
        metric="中标金额",
        segmentation_options="mean",
        enum_order="desc",
        data_filter=[
            {
                "values": ["政务"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=106,
        name="全市场中标南方21省图算子",
        compute_type="ppt图模版1",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["政务"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=107,
        name="全市场中标北方10省图算子",
        compute_type="ppt图模版1",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["政务"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=108,
        name="南方21省中标份额",
        compute_type="占比",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["政务"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=171,
        name="南方21省中标份额变化",
        compute_type="占比变化",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["政务"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=109,
        name="南方21省分省份中标份额",
        compute_type="占比",
        operator="sum",
        segmentation_options="mean",
        enum_order="desc",
        group_by="省份",
        metric="中标金额",
        data_filter=[
            {
                "values": ["政务"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=110,
        name="南方21省分省份中标份额变化",
        compute_type="占比变化",
        operator="sum",
        group_by="省份",
        metric="中标金额",
        segmentation_options="mean",
        data_filter=[
            {
                "values": ["政务"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=111,
        name="北方10省中标份额",
        compute_type="占比",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["政务"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=172,
        name="北方10省中标份额变化",
        compute_type="占比变化",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["政务"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=112,
        name="北方10省分省份中标份额",
        compute_type="占比",
        operator="sum",
        segmentation_options="mean",
        enum_order="desc",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["政务"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=113,
        name="北方10省分省份中标份额变化",
        compute_type="占比变化",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        segmentation_options="mean",
        data_filter=[
            {
                "values": ["政务"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=114,
        name="全市场中标南方21省图算子",
        compute_type="ppt图模版2",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["政务"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=115,
        name="全市场中标北方10省图算子",
        compute_type="ppt图模版2",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["政务"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    #################### Slide 8 ####################
    PPTDataOp(
        template_id=1,
        data_op_id=130,
        name="南方21省中标金额",
        compute_type="总计",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=131,
        name="南方21省中标金额同比",
        compute_type="总计同比",
        operator="sum",
        group_by="省份",
        metric="中标金额",
        segmentation_options="mean",
        enum_order="desc",
        data_filter=[
            {
                "values": ["交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=132,
        name="北方10省中标金额",
        compute_type="总计",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=133,
        name="北方10省中标金额同比",
        compute_type="总计同比",
        operator="sum",
        group_by="省份",
        metric="中标金额",
        segmentation_options="mean",
        enum_order="desc",
        data_filter=[
            {
                "values": ["交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=134,
        name="全市场中标南方21省图算子",
        compute_type="ppt图模版1",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=135,
        name="全市场中标北方10省图算子",
        compute_type="ppt图模版1",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            },
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=136,
        name="南方21省中标份额",
        compute_type="占比",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=175,
        name="南方21省中标份额变化",
        compute_type="占比变化",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=137,
        name="南方21省分省份中标份额",
        compute_type="占比",
        operator="sum",
        segmentation_options="mean",
        enum_order="desc",
        group_by="省份",
        metric="中标金额",
        data_filter=[
            {
                "values": ["交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=138,
        name="南方21省分省份中标份额变化",
        compute_type="占比变化",
        operator="sum",
        group_by="省份",
        metric="中标金额",
        segmentation_options="mean",
        data_filter=[
            {
                "values": ["交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=139,
        name="北方10省中标份额",
        compute_type="占比",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=176,
        name="北方10省中标份额变化",
        compute_type="占比变化",
        operator="sum",
        metric="中标金额",
        data_filter=[
            {
                "values": ["交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=140,
        name="北方10省分省份中标份额",
        compute_type="占比",
        operator="sum",
        segmentation_options="mean",
        enum_order="desc",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=141,
        name="北方10省分省份中标份额变化",
        compute_type="占比变化",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        segmentation_options="mean",
        data_filter=[
            {
                "values": ["交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=142,
        name="全市场中标南方21省图算子",
        compute_type="ppt图模版2",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "贵州",
                    "云南",
                    "新疆",
                    "西藏",
                    "陕西",
                    "江西",
                    "湖北",
                    "甘肃",
                    "湖南",
                    "上海",
                    "安徽",
                    "广西",
                    "浙江",
                    "福建",
                    "宁夏",
                    "四川",
                    "江苏",
                    "青海",
                    "重庆",
                    "广东",
                    "海南",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
    PPTDataOp(
        template_id=1,
        data_op_id=143,
        name="全市场中标北方10省图算子",
        compute_type="ppt图模版2",
        operator="sum",
        metric="中标金额",
        group_by="省份",
        data_filter=[
            {
                "values": ["交通物流"],
                "operator": "in",
                "columnCode": "control_department",
                "columnName": "行业",
                "filterType": 1,
            },
            {
                "values": [
                    "吉林",
                    "内蒙古",
                    "黑龙江",
                    "天津",
                    "辽宁",
                    "山西",
                    "北京",
                    "山东",
                    "河南",
                    "河北",
                ],
                "operator": "in",
                "columnCode": "prov_name",
                "columnName": "省份",
                "filterType": 1,
            },
        ],
        percentage_filter=[
            {
                "values": ["电信", "辰安科技"],
                "operator": "in",
                "columnCode": "operator_split_type",
                "columnName": "中标厂商",
                "filterType": 1,
            }
        ],
    ),
]


def single_department_data_op(index, department):
    op_list = [
        PPTDataOp(
            template_id=1,
            data_op_id=index,
            name="南方21省中标金额",
            compute_type="总计",
            operator="sum",
            metric="中标金额",
            data_filter=[
                {
                    "values": [department],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
                {
                    "values": [
                        "贵州",
                        "云南",
                        "新疆",
                        "西藏",
                        "陕西",
                        "江西",
                        "湖北",
                        "甘肃",
                        "湖南",
                        "上海",
                        "安徽",
                        "广西",
                        "浙江",
                        "福建",
                        "宁夏",
                        "四川",
                        "江苏",
                        "青海",
                        "重庆",
                        "广东",
                        "海南",
                    ],
                    "operator": "in",
                    "columnCode": "prov_name",
                    "columnName": "省份",
                    "filterType": 1,
                },
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                },
            ],
        ),
        PPTDataOp(
            template_id=1,
            data_op_id=index + 1,
            name="南方21省中标金额同比",
            compute_type="总计同比",
            operator="sum",
            group_by="省份",
            metric="中标金额",
            segmentation_options="mean",
            enum_order="desc",
            data_filter=[
                {
                    "values": [department],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
                {
                    "values": [
                        "贵州",
                        "云南",
                        "新疆",
                        "西藏",
                        "陕西",
                        "江西",
                        "湖北",
                        "甘肃",
                        "湖南",
                        "上海",
                        "安徽",
                        "广西",
                        "浙江",
                        "福建",
                        "宁夏",
                        "四川",
                        "江苏",
                        "青海",
                        "重庆",
                        "广东",
                        "海南",
                    ],
                    "operator": "in",
                    "columnCode": "prov_name",
                    "columnName": "省份",
                    "filterType": 1,
                },
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                },
            ],
        ),
        PPTDataOp(
            template_id=1,
            data_op_id=index + 2,
            name="北方10省中标金额",
            compute_type="总计",
            operator="sum",
            metric="中标金额",
            data_filter=[
                {
                    "values": [department],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
                {
                    "values": [
                        "吉林",
                        "内蒙古",
                        "黑龙江",
                        "天津",
                        "辽宁",
                        "山西",
                        "北京",
                        "山东",
                        "河南",
                        "河北",
                    ],
                    "operator": "in",
                    "columnCode": "prov_name",
                    "columnName": "省份",
                    "filterType": 1,
                },
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                },
            ],
        ),
        PPTDataOp(
            template_id=1,
            data_op_id=index + 3,
            name="北方10省中标金额同比",
            compute_type="总计同比",
            operator="sum",
            group_by="省份",
            metric="中标金额",
            segmentation_options="mean",
            enum_order="desc",
            data_filter=[
                {
                    "values": [department],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
                {
                    "values": [
                        "吉林",
                        "内蒙古",
                        "黑龙江",
                        "天津",
                        "辽宁",
                        "山西",
                        "北京",
                        "山东",
                        "河南",
                        "河北",
                    ],
                    "operator": "in",
                    "columnCode": "prov_name",
                    "columnName": "省份",
                    "filterType": 1,
                },
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                },
            ],
        ),
        PPTDataOp(
            template_id=1,
            data_op_id=index + 4,
            name="全市场中标南方21省图算子",
            compute_type="ppt图模版1",
            operator="sum",
            metric="中标金额",
            group_by="省份",
            data_filter=[
                {
                    "values": [department],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
                {
                    "values": [
                        "贵州",
                        "云南",
                        "新疆",
                        "西藏",
                        "陕西",
                        "江西",
                        "湖北",
                        "甘肃",
                        "湖南",
                        "上海",
                        "安徽",
                        "广西",
                        "浙江",
                        "福建",
                        "宁夏",
                        "四川",
                        "江苏",
                        "青海",
                        "重庆",
                        "广东",
                        "海南",
                    ],
                    "operator": "in",
                    "columnCode": "prov_name",
                    "columnName": "省份",
                    "filterType": 1,
                },
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                },
            ],
        ),
        PPTDataOp(
            template_id=1,
            data_op_id=index + 5,
            name="全市场中标北方10省图算子",
            compute_type="ppt图模版1",
            operator="sum",
            metric="中标金额",
            group_by="省份",
            data_filter=[
                {
                    "values": [department],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
                {
                    "values": [
                        "吉林",
                        "内蒙古",
                        "黑龙江",
                        "天津",
                        "辽宁",
                        "山西",
                        "北京",
                        "山东",
                        "河南",
                        "河北",
                    ],
                    "operator": "in",
                    "columnCode": "prov_name",
                    "columnName": "省份",
                    "filterType": 1,
                },
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                },
            ],
        ),
        PPTDataOp(
            template_id=1,
            data_op_id=index + 6,
            name="南方21省中标份额",
            compute_type="占比",
            operator="sum",
            metric="中标金额",
            data_filter=[
                {
                    "values": [department],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
                {
                    "values": [
                        "贵州",
                        "云南",
                        "新疆",
                        "西藏",
                        "陕西",
                        "江西",
                        "湖北",
                        "甘肃",
                        "湖南",
                        "上海",
                        "安徽",
                        "广西",
                        "浙江",
                        "福建",
                        "宁夏",
                        "四川",
                        "江苏",
                        "青海",
                        "重庆",
                        "广东",
                        "海南",
                    ],
                    "operator": "in",
                    "columnCode": "prov_name",
                    "columnName": "省份",
                    "filterType": 1,
                },
            ],
            percentage_filter=[
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                }
            ],
        ),
        PPTDataOp(
            template_id=1,
            data_op_id=index + 7,
            name="南方21省中标份额变化",
            compute_type="占比变化",
            operator="sum",
            metric="中标金额",
            data_filter=[
                {
                    "values": [department],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
                {
                    "values": [
                        "贵州",
                        "云南",
                        "新疆",
                        "西藏",
                        "陕西",
                        "江西",
                        "湖北",
                        "甘肃",
                        "湖南",
                        "上海",
                        "安徽",
                        "广西",
                        "浙江",
                        "福建",
                        "宁夏",
                        "四川",
                        "江苏",
                        "青海",
                        "重庆",
                        "广东",
                        "海南",
                    ],
                    "operator": "in",
                    "columnCode": "prov_name",
                    "columnName": "省份",
                    "filterType": 1,
                },
            ],
            percentage_filter=[
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                }
            ],
        ),
        PPTDataOp(
            template_id=1,
            data_op_id=index + 8,
            name="南方21省分省份中标份额",
            compute_type="占比",
            operator="sum",
            segmentation_options="mean",
            enum_order="desc",
            group_by="省份",
            metric="中标金额",
            data_filter=[
                {
                    "values": [department],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
                {
                    "values": [
                        "贵州",
                        "云南",
                        "新疆",
                        "西藏",
                        "陕西",
                        "江西",
                        "湖北",
                        "甘肃",
                        "湖南",
                        "上海",
                        "安徽",
                        "广西",
                        "浙江",
                        "福建",
                        "宁夏",
                        "四川",
                        "江苏",
                        "青海",
                        "重庆",
                        "广东",
                        "海南",
                    ],
                    "operator": "in",
                    "columnCode": "prov_name",
                    "columnName": "省份",
                    "filterType": 1,
                },
            ],
            percentage_filter=[
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                }
            ],
        ),
        PPTDataOp(
            template_id=1,
            data_op_id=index + 9,
            name="南方21省分省份中标份额变化",
            compute_type="占比变化",
            operator="sum",
            group_by="省份",
            metric="中标金额",
            segmentation_options="mean",
            data_filter=[
                {
                    "values": [department],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
                {
                    "values": [
                        "贵州",
                        "云南",
                        "新疆",
                        "西藏",
                        "陕西",
                        "江西",
                        "湖北",
                        "甘肃",
                        "湖南",
                        "上海",
                        "安徽",
                        "广西",
                        "浙江",
                        "福建",
                        "宁夏",
                        "四川",
                        "江苏",
                        "青海",
                        "重庆",
                        "广东",
                        "海南",
                    ],
                    "operator": "in",
                    "columnCode": "prov_name",
                    "columnName": "省份",
                    "filterType": 1,
                },
            ],
            percentage_filter=[
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                }
            ],
        ),
        PPTDataOp(
            template_id=1,
            data_op_id=index + 10,
            name="北方10省中标份额",
            compute_type="占比",
            operator="sum",
            metric="中标金额",
            data_filter=[
                {
                    "values": [department],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
                {
                    "values": [
                        "吉林",
                        "内蒙古",
                        "黑龙江",
                        "天津",
                        "辽宁",
                        "山西",
                        "北京",
                        "山东",
                        "河南",
                        "河北",
                    ],
                    "operator": "in",
                    "columnCode": "prov_name",
                    "columnName": "省份",
                    "filterType": 1,
                },
            ],
            percentage_filter=[
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                }
            ],
        ),
        PPTDataOp(
            template_id=1,
            data_op_id=index + 11,
            name="北方10省中标份额变化",
            compute_type="占比变化",
            operator="sum",
            metric="中标金额",
            data_filter=[
                {
                    "values": [department],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
                {
                    "values": [
                        "吉林",
                        "内蒙古",
                        "黑龙江",
                        "天津",
                        "辽宁",
                        "山西",
                        "北京",
                        "山东",
                        "河南",
                        "河北",
                    ],
                    "operator": "in",
                    "columnCode": "prov_name",
                    "columnName": "省份",
                    "filterType": 1,
                },
            ],
            percentage_filter=[
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                }
            ],
        ),
        PPTDataOp(
            template_id=1,
            data_op_id=index + 12,
            name="北方10省分省份中标份额",
            compute_type="占比",
            operator="sum",
            segmentation_options="mean",
            enum_order="desc",
            metric="中标金额",
            group_by="省份",
            data_filter=[
                {
                    "values": [department],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
                {
                    "values": [
                        "吉林",
                        "内蒙古",
                        "黑龙江",
                        "天津",
                        "辽宁",
                        "山西",
                        "北京",
                        "山东",
                        "河南",
                        "河北",
                    ],
                    "operator": "in",
                    "columnCode": "prov_name",
                    "columnName": "省份",
                    "filterType": 1,
                },
            ],
            percentage_filter=[
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                }
            ],
        ),
        PPTDataOp(
            template_id=1,
            data_op_id=index + 13,
            name="北方10省分省份中标份额变化",
            compute_type="占比变化",
            operator="sum",
            metric="中标金额",
            group_by="省份",
            segmentation_options="mean",
            data_filter=[
                {
                    "values": [department],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
                {
                    "values": [
                        "吉林",
                        "内蒙古",
                        "黑龙江",
                        "天津",
                        "辽宁",
                        "山西",
                        "北京",
                        "山东",
                        "河南",
                        "河北",
                    ],
                    "operator": "in",
                    "columnCode": "prov_name",
                    "columnName": "省份",
                    "filterType": 1,
                },
            ],
            percentage_filter=[
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                }
            ],
        ),
        PPTDataOp(
            template_id=1,
            data_op_id=index + 14,
            name="全市场中标南方21省图算子",
            compute_type="ppt图模版2",
            operator="sum",
            metric="中标金额",
            group_by="省份",
            data_filter=[
                {
                    "values": [department],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
                {
                    "values": [
                        "贵州",
                        "云南",
                        "新疆",
                        "西藏",
                        "陕西",
                        "江西",
                        "湖北",
                        "甘肃",
                        "湖南",
                        "上海",
                        "安徽",
                        "广西",
                        "浙江",
                        "福建",
                        "宁夏",
                        "四川",
                        "江苏",
                        "青海",
                        "重庆",
                        "广东",
                        "海南",
                    ],
                    "operator": "in",
                    "columnCode": "prov_name",
                    "columnName": "省份",
                    "filterType": 1,
                },
            ],
            percentage_filter=[
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                }
            ],
        ),
        PPTDataOp(
            template_id=1,
            data_op_id=index + 15,
            name="全市场中标北方10省图算子",
            compute_type="ppt图模版2",
            operator="sum",
            metric="中标金额",
            group_by="省份",
            data_filter=[
                {
                    "values": [department],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
                {
                    "values": [
                        "吉林",
                        "内蒙古",
                        "黑龙江",
                        "天津",
                        "辽宁",
                        "山西",
                        "北京",
                        "山东",
                        "河南",
                        "河北",
                    ],
                    "operator": "in",
                    "columnCode": "prov_name",
                    "columnName": "省份",
                    "filterType": 1,
                },
            ],
            percentage_filter=[
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                }
            ],
        ),
    ]
    save_and_create_ppt_data_op_list(op_list)


def single_department_data_op_template2(index, department):
    op_list = [
        PPTDataOp(
            template_id=2,
            data_op_id=index,
            name="全市场中标",
            compute_type="总计",
            operator="sum",
            metric="中标金额",
            data_filter=[
                {
                    "values": [department],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
            ],
        ),
        PPTDataOp(
            template_id=2,
            data_op_id=index + 1,
            name="全市场中标同比",
            compute_type="总计同比",
            operator="sum",
            metric="中标金额",
            data_filter=[
                {
                    "values": [department],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
            ],
        ),
        PPTDataOp(
            template_id=2,
            data_op_id=index + 2,
            name="电信中标",
            compute_type="总计",
            operator="sum",
            metric="中标金额",
            data_filter=[
                {
                    "values": [department],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                },
            ],
        ),
        PPTDataOp(
            template_id=2,
            data_op_id=index + 3,
            name="电信中标金额同比",
            compute_type="总计同比",
            operator="sum",
            metric="中标金额",
            data_filter=[
                {
                    "values": [department],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                },
            ],
        ),
        PPTDataOp(
            template_id=2,
            data_op_id=index + 4,
            name="电信中标金额同比变化",
            compute_type="总计同比波动",
            operator="sum",
            metric="中标金额",
            data_filter=[
                {
                    "values": [department],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                },
            ],
        ),
        PPTDataOp(
            template_id=2,
            data_op_id=index + 5,
            name="电信分地市中标金额同比",
            compute_type="总计同比",
            operator="sum",
            metric="中标金额",
            group_by="地市",
            segmentation_options="mean",
            enum_order="desc",
            data_filter=[
                {
                    "values": [department],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                },
            ],
        ),
        PPTDataOp(
            template_id=2,
            data_op_id=index + 6,
            name="行业分地市图",
            compute_type="ppt图模版1",
            operator="sum",
            metric="中标金额",
            group_by="地市",
            data_filter=[
                {
                    "values": [department],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                },
            ],
        ),
        PPTDataOp(
            template_id=2,
            data_op_id=index + 7,
            name="分地市中标份额",
            compute_type="占比",
            operator="sum",
            group_by="地市",
            metric="中标金额",
            segmentation_options="mean",
            enum_order="desc",
            data_filter=[
                {
                    "values": [department],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
            ],
            percentage_filter=[
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                },
            ],
        ),
        PPTDataOp(
            template_id=2,
            data_op_id=index + 8,
            name="分地市中标份额变化",
            compute_type="占比变化",
            operator="sum",
            segmentation_options="mean",
            enum_order="desc",
            group_by="地市",
            metric="中标金额",
            data_filter=[
                {
                    "values": [department],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
            ],
            percentage_filter=[
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                }
            ],
        ),
        PPTDataOp(
            template_id=2,
            data_op_id=index + 9,
            name="分地市份额图算子",
            compute_type="ppt图模版2",
            operator="sum",
            group_by="地市",
            metric="中标金额",
            data_filter=[
                {
                    "values": [department],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
            ],
            percentage_filter=[
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                }
            ],
        ),
    ]
    save_and_create_ppt_data_op_list(op_list)


if __name__ == "__main__":
    save_and_create_ppt_data_op_list(ppt_data_op_list)
    from collections import OrderedDict

    department_map = OrderedDict(
        {
            # "政务行业拓展部": "政务",
            # "金融行业事业部": "金融",
            # "商业客户拓展部": "商客",
            # "交通物流行业事业部": "交通物流",
            "教育行业事业部": "教育",
            "要客服务事业部": "要客",
            "农业农村行业事业部": "农业农村",
            "卫健行业事业部": "卫健",
            "应急行业事业部": "应急",
            "政法公安行业事业部": "政法公安",
            "文宣行业事业部": "文宣",
            "住建行业事业部": "住建",
            "互联网行业事业部一部": "互联网一部",
            "互联网行业事业部二部": "互联网二部",
            "工业行业事业部": "工业",
            "能源化工团队": "能源化工",
            "车联网创新团队": "车联网",
        }
    )

    index = 183
    for k, v in department_map.items():
        single_department_data_op(index, v)
        index += 16
    template2_index = index  #### 391
    ppt_data_op_list_template2 = [
        PPTDataOp(
            template_id=2,
            data_op_id=template2_index,
            name="全市场中标",
            compute_type="总计同比",
            operator="sum",
            metric="中标金额",
            group_by="地市",
            segmentation_options="mean",
            enum_order="desc",
            data_filter=[
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                }
            ],
        ),
        PPTDataOp(
            template_id=2,
            data_op_id=template2_index + 1,
            name="分地市中标金额图",
            compute_type="ppt图模版1",
            operator="sum",
            metric="中标金额",
            group_by="地市",
            data_filter=[
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                }
            ],
        ),
        PPTDataOp(
            template_id=2,
            data_op_id=template2_index + 2,
            name="分地市中标份额",
            compute_type="占比",
            operator="sum",
            group_by="地市",
            metric="中标金额",
            segmentation_options="mean",
            enum_order="desc",
            percentage_filter=[
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                }
            ],
        ),
        PPTDataOp(
            template_id=2,
            data_op_id=template2_index + 3,
            name="分地市中标份额变化",
            compute_type="占比变化",
            operator="sum",
            group_by="地市",
            metric="中标金额",
            segmentation_options="mean",
            percentage_filter=[
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                }
            ],
        ),
        PPTDataOp(
            template_id=2,
            data_op_id=template2_index + 4,
            name="分地市份额图算子",
            compute_type="ppt图模版2",
            operator="sum",
            metric="中标金额",
            group_by="地市",
            percentage_filter=[
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                }
            ],
        ),
        PPTDataOp(
            template_id=2,
            data_op_id=template2_index + 5,  # 396
            name="泛政务类全市场中标",
            compute_type="总计同比",
            operator="sum",
            metric="中标金额",
            group_by="地市",
            segmentation_options="mean",
            enum_order="desc",
            data_filter=[
                {
                    "values": ["农业农村", "住建", "应急", "政法公安", "政务", "要客"],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                },
            ],
        ),
        PPTDataOp(
            template_id=2,
            data_op_id=template2_index + 6,
            name="泛政务分地市中标金额图",
            compute_type="ppt图模版1",
            operator="sum",
            metric="中标金额",
            group_by="地市",
            data_filter=[
                {
                    "values": ["农业农村", "住建", "应急", "政法公安", "政务", "要客"],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                },
            ],
        ),
        PPTDataOp(
            template_id=2,
            data_op_id=template2_index + 7,
            name="泛政务分地市中标份额",
            compute_type="占比",
            operator="sum",
            group_by="地市",
            metric="中标金额",
            segmentation_options="mean",
            enum_order="desc",
            data_filter=[
                {
                    "values": ["农业农村", "住建", "应急", "政法公安", "政务", "要客"],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
            ],
            percentage_filter=[
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                },
            ],
        ),
        PPTDataOp(
            template_id=2,
            data_op_id=template2_index + 8,
            name="泛政务分地市中标份额变化",
            compute_type="占比变化",
            operator="sum",
            group_by="地市",
            metric="中标金额",
            segmentation_options="mean",
            data_filter=[
                {
                    "values": ["农业农村", "住建", "应急", "政法公安", "政务", "要客"],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
            ],
            percentage_filter=[
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                },
            ],
        ),
        PPTDataOp(
            template_id=2,
            data_op_id=template2_index + 9,
            name="泛政务分地市份额图算子",
            compute_type="ppt图模版2",
            operator="sum",
            metric="中标金额",
            group_by="地市",
            data_filter=[
                {
                    "values": ["农业农村", "住建", "应急", "政法公安", "政务", "要客"],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
            ],
            percentage_filter=[
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                },
            ],
        ),
        PPTDataOp(
            template_id=2,
            data_op_id=template2_index + 10,  # 401
            name="社会民生类全市场中标",
            compute_type="总计同比",
            operator="sum",
            metric="中标金额",
            group_by="地市",
            segmentation_options="mean",
            enum_order="desc",
            data_filter=[
                {
                    "values": ["商客", "文宣", "互联网一部", "互联网二部", "金融", "教育", "卫健"],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                },
            ],
        ),
        PPTDataOp(
            template_id=2,
            data_op_id=template2_index + 11,
            name="社会民生分地市中标金额图",
            compute_type="ppt图模版1",
            operator="sum",
            metric="中标金额",
            group_by="地市",
            data_filter=[
                {
                    "values": ["商客", "文宣", "互联网一部", "互联网二部", "金融", "教育", "卫健"],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                },
            ],
        ),
        PPTDataOp(
            template_id=2,
            data_op_id=template2_index + 12,
            name="社会民生分地市中标份额",
            compute_type="占比",
            operator="sum",
            group_by="地市",
            metric="中标金额",
            segmentation_options="mean",
            enum_order="desc",
            data_filter=[
                {
                    "values": ["商客", "文宣", "互联网一部", "互联网二部", "金融", "教育", "卫健"],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
            ],
            percentage_filter=[
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                },
            ],
        ),
        PPTDataOp(
            template_id=2,
            data_op_id=template2_index + 13,
            name="社会民生分地市中标份额变化",
            compute_type="占比变化",
            operator="sum",
            group_by="地市",
            metric="中标金额",
            segmentation_options="mean",
            data_filter=[
                {
                    "values": ["商客", "文宣", "互联网一部", "互联网二部", "金融", "教育", "卫健"],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
            ],
            percentage_filter=[
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                },
            ],
        ),
        PPTDataOp(
            template_id=2,
            data_op_id=template2_index + 14,
            name="社会民生分地市份额图算子",
            compute_type="ppt图模版2",
            operator="sum",
            metric="中标金额",
            group_by="地市",
            data_filter=[
                {
                    "values": ["商客", "文宣", "互联网一部", "互联网二部", "金融", "教育", "卫健"],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
            ],
            percentage_filter=[
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                },
            ],
        ),
        PPTDataOp(
            template_id=2,
            data_op_id=template2_index + 15,  # 406
            name="新型工业化类全市场中标",
            compute_type="总计同比",
            operator="sum",
            metric="中标金额",
            group_by="地市",
            segmentation_options="mean",
            enum_order="desc",
            data_filter=[
                {
                    "values": ["能源化工", "工业", "车联网", "交通物流"],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                },
            ],
        ),
        PPTDataOp(
            template_id=2,
            data_op_id=template2_index + 16,
            name="新型工业化分地市中标金额图",
            compute_type="ppt图模版1",
            operator="sum",
            metric="中标金额",
            group_by="地市",
            data_filter=[
                {
                    "values": ["能源化工", "工业", "车联网", "交通物流"],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                },
            ],
        ),
        PPTDataOp(
            template_id=2,
            data_op_id=template2_index + 17,
            name="新型工业化分地市中标份额",
            compute_type="占比",
            operator="sum",
            group_by="地市",
            metric="中标金额",
            segmentation_options="mean",
            enum_order="desc",
            data_filter=[
                {
                    "values": ["能源化工", "工业", "车联网", "交通物流"],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
            ],
            percentage_filter=[
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                },
            ],
        ),
        PPTDataOp(
            template_id=2,
            data_op_id=template2_index + 18,
            name="新型工业化分地市中标份额变化",
            compute_type="占比变化",
            operator="sum",
            group_by="地市",
            metric="中标金额",
            segmentation_options="mean",
            data_filter=[
                {
                    "values": ["能源化工", "工业", "车联网", "交通物流"],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
            ],
            percentage_filter=[
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                },
            ],
        ),
        PPTDataOp(
            template_id=2,
            data_op_id=template2_index + 19,
            name="新型工业化分地市份额图算子",
            compute_type="ppt图模版2",
            operator="sum",
            metric="中标金额",
            group_by="地市",
            data_filter=[
                {
                    "values": ["能源化工", "工业", "车联网", "交通物流"],
                    "operator": "in",
                    "columnCode": "control_department",
                    "columnName": "行业",
                    "filterType": 1,
                },
            ],
            percentage_filter=[
                {
                    "values": ["电信", "辰安科技"],
                    "operator": "in",
                    "columnCode": "operator_split_type",
                    "columnName": "中标厂商",
                    "filterType": 1,
                },
            ],
        ),
    ]
    save_and_create_ppt_data_op_list(ppt_data_op_list_template2)

    department_map_template2 = OrderedDict(
        {
            "政务行业拓展部": "政务",
            "金融行业事业部": "金融",
            "商业客户拓展部": "商客",
            "交通物流行业事业部": "交通物流",
            "教育行业事业部": "教育",
            "要客服务事业部": "要客",
            "农业农村行业事业部": "农业农村",
            "卫健行业事业部": "卫健",
            "应急行业事业部": "应急",
            "政法公安行业事业部": "政法公安",
            "文宣行业事业部": "文宣",
            "住建行业事业部": "住建",
            "互联网行业事业部一部": "互联网一部",
            "互联网行业事业部二部": "互联网二部",
            "工业行业事业部": "工业",
            "能源化工团队": "能源化工",
            "车联网创新团队": "车联网",
        }
    )

    index = 411
    for k, v in department_map_template2.items():
        single_department_data_op_template2(index, v)
        index += 10
