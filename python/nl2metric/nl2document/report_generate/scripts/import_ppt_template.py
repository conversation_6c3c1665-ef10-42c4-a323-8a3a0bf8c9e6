from dotenv import load_dotenv

env_file = "/Users/<USER>/ask-bi/python/nl2metric/.env"
print(f"load dotenv {env_file}")
load_dotenv(dotenv_path=env_file, override=True)

from dotenv import load_dotenv

from common.utils.string_utils import class_to_dict
from nl2document.common.models.ppt_generate_model import (
    PPTTemplate,
    PPTTemplateTest,
    test_save_ppt_template_to_db,
    test_get_ppt_template_by_id,
    save_ppt_template_to_db,
)
from nl2document.common.msg.ppt_generate_msg import (
    PPTSlide,
    PPTContentUnit,
    PPTTemplateData,
)

env_file = "/Users/<USER>/ask-bi/python/nl2metric/.env"
print(f"load dotenv {env_file}")
load_dotenv(dotenv_path=env_file, override=True)

if __name__ == "__main__":
    Slide1 = PPTSlide(
        id=1,
        slide_master_id=2,
        contents=[
            PPTContentUnit(
                id=1,
                type="title",
                title="全市场中标-1：整体",
            ),
            PPTContentUnit(
                id=2,
                type="text",
                title="全市场中标金额",
                inputDataOpIds=[
                    1,
                    2,
                    3,
                    4,
                    5,
                    6,
                    95,
                    96,
                    7,
                    8,
                    97,
                    9,
                    10,
                    98,
                    11,
                    177,
                    178,
                    179,
                    180,
                    181,
                    182,
                ],
                inputTextOpIds=[1],
            ),
            PPTContentUnit(
                id=3,
                type="graph",
                inputDataOpIds=[12, 13, 14],
                subTitles=["泛政务", "社会民生", "新型工业化"],
            ),
            PPTContentUnit(
                id=4,
                type="text",
                title="全市场中标份额",
                inputDataOpIds=[
                    15,
                    99,
                    16,
                    100,
                    17,
                    101,
                    18,
                    158,
                    19,
                    159,
                    20,
                    160,
                    21,
                ],
                inputTextOpIds=[2],
            ),
            PPTContentUnit(
                id=5,
                type="graph",
                inputDataOpIds=[22, 23, 24],
                subTitles=["泛政务", "社会民生", "新型工业化"],
            ),
        ],
    )
    Slide2 = PPTSlide(
        id=2,
        slide_master_id=3,
        contents=[
            PPTContentUnit(
                id=1,
                type="title",
                title="全市场中标-2：分区域",
            ),
            PPTContentUnit(
                id=2,
                type="text",
                title="分省中标金额",
                inputDataOpIds=[25, 26, 27, 28],
                inputTextOpIds=[3],
            ),
            PPTContentUnit(
                id=3,
                type="graph",
                inputDataOpIds=[29, 30],
                subTitles=["南方21省", "北方10省"],
            ),
            PPTContentUnit(
                id=4,
                type="text",
                title="分省中标份额",
                inputDataOpIds=[31, 161, 32, 33, 34, 162, 35, 36],
                inputTextOpIds=[4],
            ),
            PPTContentUnit(
                id=5,
                type="graph",
                inputDataOpIds=[37, 38],
                subTitles=["南方21省", "北方10省"],
            ),
        ],
    )
    Slide3 = PPTSlide(
        id=3,
        slide_master_id=4,
        contents=[
            PPTContentUnit(
                id=1,
                type="title",
                title="全市场中标-3：泛政务类",
            ),
            PPTContentUnit(
                id=2,
                type="text",
                title="分省中标金额",
                inputDataOpIds=[39, 40, 41, 42],
                inputTextOpIds=[5],
            ),
            PPTContentUnit(
                id=3,
                type="graph",
                inputDataOpIds=[43, 44],
                subTitles=["南方21省", "北方10省"],
            ),
            PPTContentUnit(
                id=4,
                type="text",
                title="分省中标份额",
                inputDataOpIds=[45, 163, 46, 47, 48, 164, 49, 50],
                inputTextOpIds=[6],
            ),
            PPTContentUnit(
                id=5,
                type="graph",
                inputDataOpIds=[51, 52],
                subTitles=["南方21省", "北方10省"],
            ),
            PPTContentUnit(
                id=6,
                type="title",
                title="泛政务类包含应急、要客、住建、农业农村、政法公安和政务行业6个行业",
            ),
        ],
    )

    Slide4 = PPTSlide(
        id=4,
        slide_master_id=4,
        contents=[
            PPTContentUnit(
                id=1,
                type="title",
                title="全市场中标-4：社会民生类",
            ),
            PPTContentUnit(
                id=2,
                type="text",
                title="分省中标金额",
                inputDataOpIds=[53, 54, 55, 56],
                inputTextOpIds=[7],
            ),
            PPTContentUnit(
                id=3,
                type="graph",
                inputDataOpIds=[57, 58],
                subTitles=["南方21省", "北方10省"],
            ),
            PPTContentUnit(
                id=4,
                type="text",
                title="分省中标份额",
                inputDataOpIds=[59, 165, 60, 61, 62, 166, 63, 64],
                inputTextOpIds=[8],
            ),
            PPTContentUnit(
                id=5,
                type="graph",
                inputDataOpIds=[65, 66],
                subTitles=["南方21省", "北方10省"],
            ),
            PPTContentUnit(
                id=6,
                type="title",
                title="社会民生类包含教育、卫健、文宣、金融、商客、互联网一部和互联网二部7个行业",
            ),
        ],
    )
    Slide5 = PPTSlide(
        id=5,
        slide_master_id=4,
        contents=[
            PPTContentUnit(
                id=1,
                type="title",
                title="全市场中标-5：新型工业化",
            ),
            PPTContentUnit(
                id=2,
                type="text",
                title="分省中标金额",
                inputDataOpIds=[67, 68, 69, 70],
                inputTextOpIds=[9],
            ),
            PPTContentUnit(
                id=3,
                type="graph",
                inputDataOpIds=[71, 72],
                subTitles=["南方21省", "北方10省"],
            ),
            PPTContentUnit(
                id=4,
                type="text",
                title="分省中标份额",
                inputDataOpIds=[73, 167, 74, 75, 76, 168, 77, 78],
                inputTextOpIds=[10],
            ),
            PPTContentUnit(
                id=5,
                type="graph",
                inputDataOpIds=[79, 80],
                subTitles=["南方21省", "北方10省"],
            ),
            PPTContentUnit(
                id=6,
                type="title",
                title="新型工业化包含车联网创新、车联网、能源化工、交通物流和工业5个行业",
            ),
        ],
    )
    Slide6 = PPTSlide(
        id=6,
        slide_master_id=3,
        contents=[
            PPTContentUnit(
                id=1,
                type="title",
                title="全市场中标-6：金融行业事业部",
            ),
            PPTContentUnit(
                id=2,
                type="text",
                title="分省中标金额",
                inputDataOpIds=[81, 82, 83, 84],
                inputTextOpIds=[11],
            ),
            PPTContentUnit(
                id=3,
                type="graph",
                inputDataOpIds=[85, 86],
                subTitles=["南方21省", "北方10省"],
            ),
            PPTContentUnit(
                id=4,
                type="text",
                title="分省中标份额",
                inputDataOpIds=[87, 169, 88, 89, 90, 170, 91, 92],
                inputTextOpIds=[12],
            ),
            PPTContentUnit(
                id=5,
                type="graph",
                inputDataOpIds=[93, 94],
                subTitles=["南方21省", "北方10省"],
            ),
        ],
    )
    Slide7 = PPTSlide(
        id=7,
        slide_master_id=3,
        contents=[
            PPTContentUnit(
                id=1,
                type="title",
                title="全市场中标-7：政务行业拓展部",
            ),
            PPTContentUnit(
                id=2,
                type="text",
                title="分省中标金额",
                inputDataOpIds=[
                    102,
                    103,
                    104,
                    105,
                ],
                inputTextOpIds=[13],
            ),
            PPTContentUnit(
                id=3,
                type="graph",
                inputDataOpIds=[106, 107],
                subTitles=["南方21省", "北方10省"],
            ),
            PPTContentUnit(
                id=4,
                type="text",
                title="分省中标份额",
                inputDataOpIds=[108, 171, 109, 110, 111, 172, 112, 113],
                inputTextOpIds=[14],
            ),
            PPTContentUnit(
                id=5,
                type="graph",
                inputDataOpIds=[114, 115],
                subTitles=["南方21省", "北方10省"],
            ),
        ],
    )

    Slide8 = PPTSlide(
        id=8,
        slide_master_id=3,
        contents=[
            PPTContentUnit(
                id=1,
                type="title",
                title="全市场中标-8：交通物流行业事业部",
            ),
            PPTContentUnit(
                id=2,
                type="text",
                title="分省中标金额",
                inputDataOpIds=[130, 131, 132, 133],
                inputTextOpIds=[17],
            ),
            PPTContentUnit(
                id=3,
                type="graph",
                inputDataOpIds=[134, 135],
                subTitles=["南方21省", "北方10省"],
            ),
            PPTContentUnit(
                id=4,
                type="text",
                title="分省中标份额",
                inputDataOpIds=[136, 175, 137, 138, 139, 176, 140, 141],
                inputTextOpIds=[18],
            ),
            PPTContentUnit(
                id=5,
                type="graph",
                inputDataOpIds=[142, 143],
                subTitles=["南方21省", "北方10省"],
            ),
        ],
    )
    from collections import OrderedDict

    department_map = OrderedDict(
        {
            # "政务行业拓展部": "政务",
            # "金融行业事业部": "金融",
            # "商业客户拓展部": "商客",
            # "交通物流行业事业部": "交通物流",
            "教育行业事业部": "教育",
            "要客服务事业部": "要客",
            "农业农村行业事业部": "农业农村",
            "卫健行业事业部": "卫健",
            "应急行业事业部": "应急",
            "政法公安行业事业部": "政法公安",
            "文宣行业事业部": "文宣",
            "住建行业事业部": "住建",
            "互联网行业事业部一部": "互联网一部",
            "互联网行业事业部二部": "互联网二部",
            "工业行业事业部": "工业",
            "能源化工团队": "能源化工",
            "车联网创新团队": "车联网",
        }
    )

    def generate_one_slide(data_op_id, text_op_id, index, title):
        Slide = PPTSlide(
            id=index,
            slide_master_id=3,
            contents=[
                PPTContentUnit(
                    id=1,
                    type="title",
                    title=f"全市场中标-{index}：{title}",
                ),
                PPTContentUnit(
                    id=2,
                    type="text",
                    title="分省中标金额",
                    inputDataOpIds=[
                        data_op_id,
                        data_op_id + 1,
                        data_op_id + 2,
                        data_op_id + 3,
                    ],
                    inputTextOpIds=[text_op_id],
                ),
                PPTContentUnit(
                    id=3,
                    type="graph",
                    inputDataOpIds=[data_op_id + 4, data_op_id + 5],
                    subTitles=["南方21省", "北方10省"],
                ),
                PPTContentUnit(
                    id=4,
                    type="text",
                    title="分省中标份额",
                    inputDataOpIds=[
                        data_op_id + 6,
                        data_op_id + 7,
                        data_op_id + 8,
                        data_op_id + 9,
                        data_op_id + 10,
                        data_op_id + 11,
                        data_op_id + 12,
                        data_op_id + 13,
                    ],
                    inputTextOpIds=[text_op_id + 1],
                ),
                PPTContentUnit(
                    id=5,
                    type="graph",
                    inputDataOpIds=[data_op_id + 14, data_op_id + 15],
                    subTitles=["南方21省", "北方10省"],
                ),
            ],
        )
        return Slide

    ppt_list = [Slide1, Slide2, Slide3, Slide4, Slide5, Slide6, Slide7, Slide8]

    data_op_id = 183
    text_op_id = 19
    index = 9
    for k, v in department_map.items():
        ppt_list.append(generate_one_slide(data_op_id, text_op_id, index, k))
        data_op_id += 16
        text_op_id += 2
        index += 1

    ppt1 = PPTTemplateData(slides=ppt_list)
    template1 = PPTTemplate(
        id=1,
        name="商情中标分析模板（for集团）",
        path="ask-doc/ppt-generate/template/template_group.pptx",
        thumbnail="ask-doc/ppt_generate/template/template_1.png",
        preview_path="ask-doc/ppt-generate/template/template_group.pdf",
        slides=class_to_dict(ppt1),
    )
    save_ppt_template_to_db(template1)

    Slide1 = PPTSlide(
        id=1,
        slide_master_id=2,
        contents=[
            PPTContentUnit(
                id=1,
                type="title",
                title="全市场中标-1：整体",
            ),
            PPTContentUnit(
                id=2,
                type="text",
                title="全市场中标金额",
                inputDataOpIds=[
                    1,
                    2,
                    3,
                    4,
                    5,
                    6,
                    95,
                    96,
                    7,
                    8,
                    97,
                    9,
                    10,
                    98,
                    11,
                    177,
                    178,
                    179,
                    180,
                    181,
                    182,
                ],
                inputTextOpIds=[1],
            ),
            PPTContentUnit(
                id=3,
                type="graph",
                inputDataOpIds=[12, 13, 14],
                subTitles=["泛政务", "社会民生", "新型工业化"],
            ),
            PPTContentUnit(
                id=4,
                type="text",
                title="全市场中标份额",
                inputDataOpIds=[
                    15,
                    99,
                    16,
                    100,
                    17,
                    101,
                    18,
                    158,
                    19,
                    159,
                    20,
                    160,
                    21,
                ],
                inputTextOpIds=[2],
            ),
            PPTContentUnit(
                id=5,
                type="graph",
                inputDataOpIds=[22, 23, 24],
                subTitles=["泛政务", "社会民生", "新型工业化"],
            ),
        ],
    )
    Slide2 = PPTSlide(
        id=2,
        slide_master_id=3,
        contents=[
            PPTContentUnit(
                id=1,
                type="title",
                title="全市场中标-2：分地市",
            ),
            PPTContentUnit(
                id=2,
                type="text",
                title="分地市中标金额",
                inputDataOpIds=[391],
                inputTextOpIds=[45],
            ),
            PPTContentUnit(
                id=3,
                type="graph",
                inputDataOpIds=[392],
                subTitles=[],
            ),
            PPTContentUnit(
                id=4,
                type="text",
                title="分地市中标份额",
                inputDataOpIds=[393, 394],
                inputTextOpIds=[46],
            ),
            PPTContentUnit(
                id=5,
                type="graph",
                inputDataOpIds=[395],
                subTitles=[],
            ),
        ],
    )

    Slide3 = PPTSlide(
        id=3,
        slide_master_id=3,
        contents=[
            PPTContentUnit(
                id=1,
                type="title",
                title="全市场中标-3：泛政务类",
            ),
            PPTContentUnit(
                id=2,
                type="text",
                title="分地市中标金额",
                inputDataOpIds=[396],
                inputTextOpIds=[47],
            ),
            PPTContentUnit(
                id=3,
                type="graph",
                inputDataOpIds=[397],
                subTitles=[],
            ),
            PPTContentUnit(
                id=4,
                type="text",
                title="分地市中标份额",
                inputDataOpIds=[398, 399],
                inputTextOpIds=[48],
            ),
            PPTContentUnit(
                id=5,
                type="graph",
                inputDataOpIds=[400],
                subTitles=[],
            ),
        ],
    )

    Slide4 = PPTSlide(
        id=4,
        slide_master_id=3,
        contents=[
            PPTContentUnit(
                id=1,
                type="title",
                title="全市场中标-4：社会民生类",
            ),
            PPTContentUnit(
                id=2,
                type="text",
                title="分地市中标金额",
                inputDataOpIds=[401],
                inputTextOpIds=[49],
            ),
            PPTContentUnit(
                id=3,
                type="graph",
                inputDataOpIds=[402],
                subTitles=[],
            ),
            PPTContentUnit(
                id=4,
                type="text",
                title="分地市中标份额",
                inputDataOpIds=[403, 404],
                inputTextOpIds=[50],
            ),
            PPTContentUnit(
                id=5,
                type="graph",
                inputDataOpIds=[405],
                subTitles=[],
            ),
        ],
    )

    Slide5 = PPTSlide(
        id=5,
        slide_master_id=3,
        contents=[
            PPTContentUnit(
                id=1,
                type="title",
                title="全市场中标-5：新型工业化",
            ),
            PPTContentUnit(
                id=2,
                type="text",
                title="分地市中标金额",
                inputDataOpIds=[406],
                inputTextOpIds=[51],
            ),
            PPTContentUnit(
                id=3,
                type="graph",
                inputDataOpIds=[407],
                subTitles=[],
            ),
            PPTContentUnit(
                id=4,
                type="text",
                title="分地市中标份额",
                inputDataOpIds=[408, 409],
                inputTextOpIds=[52],
            ),
            PPTContentUnit(
                id=5,
                type="graph",
                inputDataOpIds=[410],
                subTitles=[],
            ),
        ],
    )

    ppt_list = [Slide1, Slide2, Slide3, Slide4, Slide5]

    from collections import OrderedDict

    department_map_template2 = OrderedDict(
        {
            "政务行业拓展部": "政务",
            "金融行业事业部": "金融",
            "商业客户拓展部": "商客",
            "交通物流行业事业部": "交通物流",
            "教育行业事业部": "教育",
            "要客服务事业部": "要客",
            "农业农村行业事业部": "农业农村",
            "卫健行业事业部": "卫健",
            "应急行业事业部": "应急",
            "政法公安行业事业部": "政法公安",
            "文宣行业事业部": "文宣",
            "住建行业事业部": "住建",
            "互联网行业事业部一部": "互联网一部",
            "互联网行业事业部二部": "互联网二部",
            "工业行业事业部": "工业",
            "能源化工团队": "能源化工",
            "车联网创新团队": "车联网",
        }
    )

    def generate_one_slide_template2(data_op_id, text_op_id, index, title):
        Slide = PPTSlide(
            id=index,
            slide_master_id=3,
            contents=[
                PPTContentUnit(
                    id=1,
                    type="title",
                    title=f"全市场中标-{index}：{title}",
                ),
                PPTContentUnit(
                    id=2,
                    type="text",
                    title="分地市中标金额",
                    inputDataOpIds=[
                        data_op_id,
                        data_op_id + 1,
                        data_op_id + 2,
                        data_op_id + 3,
                        data_op_id + 4,
                        data_op_id + 5,
                    ],
                    inputTextOpIds=[text_op_id],
                ),
                PPTContentUnit(
                    id=3,
                    type="graph",
                    inputDataOpIds=[data_op_id + 6],
                    subTitles=[],
                ),
                PPTContentUnit(
                    id=4,
                    type="text",
                    title="分地市中标份额",
                    inputDataOpIds=[
                        data_op_id + 7,
                        data_op_id + 8,
                    ],
                    inputTextOpIds=[text_op_id + 1],
                ),
                PPTContentUnit(
                    id=5,
                    type="graph",
                    inputDataOpIds=[data_op_id + 9],
                    subTitles=[],
                ),
            ],
        )
        return Slide

    department_map_template2 = OrderedDict(
        {
            "政务行业拓展部": "政务",
            "金融行业事业部": "金融",
            "商业客户拓展部": "商客",
            "交通物流行业事业部": "交通物流",
            "教育行业事业部": "教育",
            "要客服务事业部": "要客",
            "农业农村行业事业部": "农业农村",
            "卫健行业事业部": "卫健",
            "应急行业事业部": "应急",
            "政法公安行业事业部": "政法公安",
            "文宣行业事业部": "文宣",
            "住建行业事业部": "住建",
            "互联网行业事业部一部": "互联网一部",
            "互联网行业事业部二部": "互联网二部",
            "工业行业事业部": "工业",
            "能源化工团队": "能源化工",
            "车联网创新团队": "车联网",
        }
    )
    data_op_id_template2 = 411
    text_op_id_template2 = 53  # 53
    index = 6
    for k, v in department_map_template2.items():
        ppt_list.append(
            generate_one_slide_template2(
                data_op_id_template2, text_op_id_template2, index, k
            )
        )
        data_op_id_template2 += 10
        text_op_id_template2 += 2
        index += 1
    ppt2 = PPTTemplateData(slides=ppt_list)
    template2 = PPTTemplate(
        id=2,
        name="商情中标分析模版（for省）",
        path="ask-doc/ppt-generate/template/template_group.pptx",
        thumbnail="ask-doc/ppt_generate/template/template_1.png",
        preview_path="ask-doc/ppt-generate/template/template_group.pdf",
        slides=class_to_dict(ppt2),
    )
    save_ppt_template_to_db(template2)
