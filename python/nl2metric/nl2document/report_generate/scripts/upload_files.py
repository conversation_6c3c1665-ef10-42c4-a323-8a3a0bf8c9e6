import sys

sys.path.append("/ask-bi/python/nl2metric")
from common.fs.storage import create_s3_client

if __name__ == "__main__":
    local_pptx_path = "/ask-bi/python/nl2metric/nl2document/report_generate/scripts/template_group.pptx"
    remote_path = "ppt-generate/template/template_group.pptx"
    create_s3_client().upload_file(
        local_pptx_path,
        "ask-doc",
        remote_path,
    )

    local_pdf_path = "/ask-bi/python/nl2metric/nl2document/report_generate/scripts/template_group.pdf"
    remote_path = "ppt-generate/template/template_group.pdf"
    create_s3_client().upload_file(
        local_pdf_path,
        "ask-doc",
        remote_path,
    )

    local_png_path = (
        "/ask-bi/python/nl2metric/nl2document/report_generate/scripts/template_1.png"
    )
    remote_path = "ppt_generate/template/template_1.png"
    create_s3_client().upload_file(
        local_png_path,
        "ask-doc",
        remote_path,
    )
