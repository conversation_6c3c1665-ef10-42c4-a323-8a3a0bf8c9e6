from dotenv import load_dotenv

env_file = "/Users/<USER>/ask-bi/python/nl2metric/.env"
print(f"load dotenv {env_file}")
load_dotenv(dotenv_path=env_file, override=True)

from nl2document.common.models.ppt_generate_model import (
    PPTTextOp,
    save_and_create_ppt_text_op,
)

ppt_text_op_list = [
    PPTTextOp(
        name="全市场中标金额格式",
        prompt="""
        请严格按照以下的格式两种格式之一，选择合适的格式，将待润色的内容中的数字填入，不要漏掉任何行业，如没有则不填，不可以改变任何数据，注意时间都是1到润色内容中的月份，只直接输出润色结果：
        格式一：
        截至X月全市场累计中标XX亿，同比xx%；电信中标XX亿，同比 XX%（去年同期 XX%）；移动中标XX亿，同比 XX%（去年同期 XX%）；联通中标XX亿，同比 XX%（去年同期 XX%）
        泛政务领域客户中标金额XX亿，同比XX%（去年同期 XX%）；社会民生领域客户中标金额XX亿，同比XX%（去年同期 XX%）；新型工业化领域客户中标金额XX亿，同比XX%（去年同期 XX%）
        分行业看：XX、XX、XX、XX等X个行业增幅高于xx%； XX、XX、XX、XX等X个行业增幅低于xx%；XX、XX、XX、XX等X个行业负增长
        
        格式二：
        截至X月全市场累计中标XX亿，同比xx%；电信中标XX亿，同比 XX%（去年同期 XX%）；移动中标XX亿，同比 XX%（去年同期 XX%）；联通中标XX亿，同比 XX%（去年同期 XX%）
        泛政务领域客户中标金额XX亿，同比XX%（去年同期 XX%）；社会民生领域客户中标金额XX亿，同比XX%（去年同期 XX%）；新型工业化领域客户中标金额XX亿，同比XX%（去年同期 XX%）
        分行业看：XX、XX、XX、XX等X个行业正增长；XX、XX、XX、XX等X个行业负增长
        
        参考样列一：
        输入：
        时间：2024-04;
        全市场中标为120153.17 亿;全市场中标同比下降0.15%;电信中标为5494.42 亿;电信中标同比增长0.25%;电信中标去年同期同比增长0.45%;移动中标为3217.21 亿;移动中标同比增长2.32%;移动中标去年同期同比增长0.12%;联通中标为1426.32 亿;联通中标同比下降1.09%;联通中标去年同期同比下降2.31%;
        泛政务领域客户中标金额为42312.75 亿;泛政务领域客户中标金额同比下降0.64%;泛政务领域客户去年同期同比下降1.47%;
        社会民生领域客户中标金额为49524.37 亿;社会民生领域客户中标金额同比增长0.12%;社会民生领域客户去年同期同比增长0.39%;
        新型工业化领域客户中标金额为28316.05 亿;新型工业化领域客户中标金额同比增长0.13%;新型工业化领域客户去年同期同比下降1.19%;
        分行业同比:各行业的整体分行业同比为-0.15%;互联网一部,政务,商客,车联网,交通物流,政法公安共6个行业的分行业同比正增长;能源化工,卫健,互联网二部,工业,要客,教育,农业农村,文宣,金融,住建,应急共11个行业的分行业同比负增长
        
        输出：
        截至4月全市场累计中标120153.17亿，同比下降0.15%；电信中标5494.42亿，同比增长0.25%（去年同期增长0.45%）；移动中标3217.21亿，同比增长2.32%（去年同期增长0.12%）；联通中标1426.32亿，同比下降1.09%（去年同期下降2.31%）。
        泛政务领域客户中标金额42312.75亿，同比下降0.64%（去年同期下降1.47%）；社会民生领域客户中标金额49524.37亿，同比增长0.12%（去年同期增长0.39%）；新型工业化领域客户中标金额28316.05亿，同比增长0.13%（去年同期下降1.19%）。
        分行业看：互联网一部、政务、商客、车联网、交通物流、政法公安等6个行业正增长；能源化工、卫健、互联网二部、工业、要客、教育、农业农村、文宣、金融、住建、应急等11个行业负增长。
        
        请直接输出结果，不要在前面加任何多余的文字""",
        template_id=1,
        text_op_id=1,
        input_data_op_ids=[
            1,
            2,
            3,
            4,
            5,
            6,
            95,
            96,
            7,
            8,
            97,
            9,
            10,
            98,
            11,
            177,
            178,
            179,
            180,
            181,
            182,
        ],
        type="润色",
    ),
    PPTTextOp(
        name="全市场中标金额格式",
        prompt="""
        请严格按照以下的格式两种格式之一，选择合适的格式，将待润色的内容中的数字填入，不要漏掉任何行业，如没有则不填，不可以改变任何数据，只直接输出润色结果：
        格式一：
        截至X月电信全市场中标份额XX%,较去年同期 XXpp（移动份额XX%、较去年同期 XXpp；联通份额XX%、较去年同期 XXpp）
        泛政务全市场份额XX%（较去年同期 XXpp）；社会民生全市场份额XX%（较去年同期 XXpp）；新型工业化全市场份额XX%（较去年同期 XXpp）
        分行业看：XX、XX、XX、XX等行业份额较去年同期增幅高于 XXpp； XX、XX、XX、XX等行业份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等行业份额负增长 
        
        格式二：
        截至X月电信全市场中标份额XX%,较去年同期 XXpp（移动份额XX%、较去年同期 XXpp；联通份额XX%、较去年同期 XXpp）
        泛政务全市场份额XX%（较去年同期 XXpp）；社会民生全市场份额XX%（较去年同期 XXpp）；新型工业化全市场份额XX%（较去年同期 XXpp）
        分行业看：XX、XX、XX、XX等行业份额正增长；XX、XX、XX、XX等行业份额负增长 
        
        参考样列一：
        输入：
        时间：2024-03;
        电信全市场中标份额为4.54%;电信全市场中标较去年同期提升0.05pp;移动全市场中标份额为4.54%;移动全市场中标较去年同期提升0.05pp;联通全市场中标份额为4.47%;联通全市场中标较去年同期下降0.11pp;
        泛政务全市场中标份额为4.47%;泛政务全市场中标份额较去年同期提升0.03pp;社会民生全市场中标份额为4.60%;社会民生全市场中标份额较去年同期提升0.15pp;新型工业化全市场中标份额为4.55%;新型工业化全市场中标份额较去年同期下降0.09pp;
        分行业全市场中标份额变化:各行业的整体分行业全市场中标份额变化为0.05%;互联网一部,互联网二部,交通物流,住建,卫健,应急,教育,能源化工,要客共9个行业的分行业全市场中标份额变化大于等于0.05%;农业农村,商客,工业,政务,政法公安,文宣,车联网,金融共8个行业的分行业全市场中标份额变化负增长
        
        输出：
        截至3月电信全市场中标份额4.54%，较去年同期提升0.05pp（移动份额4.54%、较去年同期提升0.05pp；联通份额4.47%、较去年同期下降0.11pp）。
        泛政务全市场份额4.47%（较去年同期提升0.03pp）；社会民生全市场份额4.60%（较去年同期提升0.15pp）；新型工业化全市场份额4.55%（较去年同期下降0.09pp）。
        分行业看：互联网一部、互联网二部、交通物流、住建、卫健、应急、教育、能源化工、要客等9个行业份额较去年同期增幅高于0.05pp；农业农村、商客、工业、政务、政法公安、文宣、车联网、金融等8个行业份额负增长。
        
        请直接输出结果，不要在前面加任何多余的文字
   """,
        template_id=1,
        text_op_id=2,
        input_data_op_ids=[15, 99, 16, 100, 17, 101, 18, 158, 19, 159, 20, 160, 21],
        type="润色",
    ),
    PPTTextOp(
        name="全市场中标分区域金额格式",
        prompt="""
    请严格按照以下几种格式之一，选择合适的格式，将待润色的内容中的数字填入，不要漏掉任何省份，如没有则不填，不可以改变任何数据，只直接输出润色结果：
    格式一：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    格式二：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅为正；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅为正；xx、xx、xx等x个省份增幅为负
    格式三：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅为正%；xx、xx、xx等x个省份增幅为负
    格式四：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%；xx、xx、xx等x个省份增幅为正%；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    
    参考样列一：
    输入：
    时间：2024-03;
    南方21省中标金额为61239.77 亿;南方21省中标金额同比:各省份的整体南方21省中标金额同比为0.32%;江苏,广东,新疆,广西,重庆,湖南,云南,福建,江西共9个省份的南方21省中标金额同比大于等于0.32%;宁夏共1个省份的南方21省中标金额同比在0.00%到0.32%之间;陕西,青海,浙江,湖北,西藏,上海,海南,贵州,四川,甘肃,安徽共11个省份的南方21省中标金额同比负增长;
    北方10省中标金额为28889.31 亿;北方10省中标金额同比:各省份的整体北方10省中标金额同比为-1.20%;辽宁,吉林共2个省份的北方10省中标金额同比正增长;黑龙江,河南,内蒙古,河北,天津,北京,山西,山东共8个省份的北方10省中标金额同比负增长
    输出：
    南方21省中标金额61239.77亿，同比增长0.32%，其中：江苏、广东、新疆、广西、重庆、湖南、云南、福建、江西等9个省份增幅高于0.32%；宁夏等1个省份增幅在0.00%到0.32%之间；陕西、青海、浙江、湖北、西藏、上海、海南、贵州、四川、甘肃、安徽等11个省份增幅为负。
    北方10省中标金额28889.31亿，同比下降1.20%，其中：辽宁、吉林等2个省份增幅为正；黑龙江、河南、内蒙古、河北、天津、北京、山西、山东等8个省份增幅为负。
    
    请直接输出结果，不要在前面加任何多余的文字
""",
        template_id=1,
        text_op_id=3,
        input_data_op_ids=[25, 26, 27, 28],
        type="润色",
    ),
    PPTTextOp(
        name="全市场中标分区域金额格式",
        prompt="""
        请严格按照以下的几种格式之一，选择合适的格式，将待润色的内容中的数字填入，不要漏掉任何省份，如没有则不填，不可以改变任何数据，只直接输出润色结果：
        格式一：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        格式二：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        格式三：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长
        格式四：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长        
    
        参考样列一：
        输入：
        时间：2024-03;
        南方21省中标份额为4.57%;南方21省中标份额变化提升0.07pp;南方21省分省份中标份额:上海,广西,江苏,浙江,甘肃,福建,贵州,陕西,青海共9个省份的南方21省分省份中标份额高于4.57%;云南,四川,宁夏,安徽,广东,新疆,江西,海南,湖北,湖南,西藏,重庆共12个省份的南方21省分省份中标份额低于4.57%;
        南方21省分省份中标份额变化:各省份的整体南方21省分省份中标份额变化为0.07pp;上海,广东,江苏,浙江,海南,湖北,甘肃,福建,贵州,重庆,青海共11个省份的南方21省分省份中标份额变化大于等于0.07pp;新疆,西藏共2个省份的南方21省分省份中标份额变化在0.00pp到0.07pp之间;云南,四川,宁夏,安徽,广西,江西,湖南,陕西共8个省份的南方21省分省份中标份额变化负增长;
        北方10省中标份额为4.49%;北方10省中标份额变化下降0.05pp;北方10省分省份中标份额:内蒙古,北京,吉林,河南,黑龙江共5个省份的北方10省分省份中标份额高于4.49%;天津,山东,山西,河北,辽宁共5个省份的北方10省分省份中标份额低于4.49%;
        北方10省分省份中标份额变化:各省份的整体北方10省分省份中标份额变化为-0.05pp;北京,吉林,河北,河南共4个省份的北方10省分省份中标份额变化正增长;内蒙古,天津,山东,山西,辽宁,黑龙江共6个省份的北方10省分省份中标份额变化负增长
        输出：
        南方21省中标份额4.57%，较去年同期提升0.07pp。其中：上海、广西、江苏、浙江、甘肃、福建、贵州、陕西、青海等9个省份份额高于4.57%，云南、四川、宁夏、安徽、广东、新疆、江西、海南、湖北、湖南、西藏、重庆等12个省份份额低于4.57%；上海、广东、江苏、浙江、海南、湖北、甘肃、福建、贵州、重庆、青海等11个省份份额较去年同期增幅高于0.07pp；新疆、西藏等2个省份份额较去年同期增幅在0.00pp到0.07pp之间；云南、四川、宁夏、安徽、广西、江西、湖南、陕西等8个省份份额负增长。
        北方10省中标份额4.49%，较去年同期下降0.05pp。其中：内蒙古、北京、吉林、河南、黑龙江等5个省份份额高于4.49%，天津、山东、山西、河北、辽宁等5个省份份额低于4.49%；北京、吉林、河北、河南等4个省份份额正增长；内蒙古、天津、山东、山西、辽宁、黑龙江等6个省份份额负增长。
    请直接输出结果，不要在前面加任何多余的文字
""",
        template_id=1,
        text_op_id=4,
        input_data_op_ids=[31, 161, 32, 33, 34, 162, 35, 36],
        type="润色",
    ),
    PPTTextOp(
        name="全市场中标分区域金额格式",
        prompt="""
    请严格按照以下几种格式之一，选择合适的格式，将待润色的内容中的数字填入，不要漏掉任何省份，如没有则不填，不可以改变任何数据，只直接输出润色结果：
    格式一：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    格式二：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅为正；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅为正；xx、xx、xx等x个省份增幅为负
    格式三：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅为正%；xx、xx、xx等x个省份增幅为负
    格式四：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%；xx、xx、xx等x个省份增幅为正%；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    
    参考样列一：
    输入：
    时间：2024-03;
    南方21省中标金额为61239.77 亿;南方21省中标金额同比:各省份的整体南方21省中标金额同比为0.32%;江苏,广东,新疆,广西,重庆,湖南,云南,福建,江西共9个省份的南方21省中标金额同比大于等于0.32%;宁夏共1个省份的南方21省中标金额同比在0.00%到0.32%之间;陕西,青海,浙江,湖北,西藏,上海,海南,贵州,四川,甘肃,安徽共11个省份的南方21省中标金额同比负增长;
    北方10省中标金额为28889.31 亿;北方10省中标金额同比:各省份的整体北方10省中标金额同比为-1.20%;辽宁,吉林共2个省份的北方10省中标金额同比正增长;黑龙江,河南,内蒙古,河北,天津,北京,山西,山东共8个省份的北方10省中标金额同比负增长
    输出：
    南方21省中标金额61239.77亿，同比增长0.32%，其中：江苏、广东、新疆、广西、重庆、湖南、云南、福建、江西等9个省份增幅高于0.32%；宁夏等1个省份增幅在0.00%到0.32%之间；陕西、青海、浙江、湖北、西藏、上海、海南、贵州、四川、甘肃、安徽等11个省份增幅为负。
    北方10省中标金额28889.31亿，同比下降1.20%，其中：辽宁、吉林等2个省份增幅为正；黑龙江、河南、内蒙古、河北、天津、北京、山西、山东等8个省份增幅为负。
    
    请直接输出结果，不要在前面加任何多余的文字

        """,
        template_id=1,
        text_op_id=5,
        input_data_op_ids=[39, 40, 41, 42],
        type="润色",
    ),
    PPTTextOp(
        name="全市场中标分区域金额格式",
        prompt="""
        请严格按照以下的几种格式之一，选择合适的格式，将待润色的内容中的数字填入，不要漏掉任何省份，如没有则不填，不可以改变任何数据，只直接输出润色结果：
        格式一：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        格式二：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        格式三：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长
        格式四：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长        
    
        参考样列一：
        输入：
        时间：2024-03;
        南方21省中标份额为4.57%;南方21省中标份额变化提升0.07pp;南方21省分省份中标份额:上海,广西,江苏,浙江,甘肃,福建,贵州,陕西,青海共9个省份的南方21省分省份中标份额高于4.57%;云南,四川,宁夏,安徽,广东,新疆,江西,海南,湖北,湖南,西藏,重庆共12个省份的南方21省分省份中标份额低于4.57%;
        南方21省分省份中标份额变化:各省份的整体南方21省分省份中标份额变化为0.07pp;上海,广东,江苏,浙江,海南,湖北,甘肃,福建,贵州,重庆,青海共11个省份的南方21省分省份中标份额变化大于等于0.07pp;新疆,西藏共2个省份的南方21省分省份中标份额变化在0.00pp到0.07pp之间;云南,四川,宁夏,安徽,广西,江西,湖南,陕西共8个省份的南方21省分省份中标份额变化负增长;
        北方10省中标份额为4.49%;北方10省中标份额变化下降0.05pp;北方10省分省份中标份额:内蒙古,北京,吉林,河南,黑龙江共5个省份的北方10省分省份中标份额高于4.49%;天津,山东,山西,河北,辽宁共5个省份的北方10省分省份中标份额低于4.49%;
        北方10省分省份中标份额变化:各省份的整体北方10省分省份中标份额变化为-0.05pp;北京,吉林,河北,河南共4个省份的北方10省分省份中标份额变化正增长;内蒙古,天津,山东,山西,辽宁,黑龙江共6个省份的北方10省分省份中标份额变化负增长
        输出：
        南方21省中标份额4.57%，较去年同期提升0.07pp。其中：上海、广西、江苏、浙江、甘肃、福建、贵州、陕西、青海等9个省份份额高于4.57%，云南、四川、宁夏、安徽、广东、新疆、江西、海南、湖北、湖南、西藏、重庆等12个省份份额低于4.57%；上海、广东、江苏、浙江、海南、湖北、甘肃、福建、贵州、重庆、青海等11个省份份额较去年同期增幅高于0.07pp；新疆、西藏等2个省份份额较去年同期增幅在0.00pp到0.07pp之间；云南、四川、宁夏、安徽、广西、江西、湖南、陕西等8个省份份额负增长。
        北方10省中标份额4.49%，较去年同期下降0.05pp。其中：内蒙古、北京、吉林、河南、黑龙江等5个省份份额高于4.49%，天津、山东、山西、河北、辽宁等5个省份份额低于4.49%；北京、吉林、河北、河南等4个省份份额正增长；内蒙古、天津、山东、山西、辽宁、黑龙江等6个省份份额负增长。
    请直接输出结果，不要在前面加任何多余的文字
        """,
        template_id=1,
        text_op_id=6,
        input_data_op_ids=[45, 163, 46, 47, 48, 164, 49, 50],
        type="润色",
    ),
    PPTTextOp(
        name="全市场中标分区域金额格式",
        prompt="""
    请严格按照以下几种格式之一，选择合适的格式，将待润色的内容中的数字填入，不要漏掉任何省份，如没有则不填，不可以改变任何数据，只直接输出润色结果：
    格式一：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    格式二：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅为正；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅为正；xx、xx、xx等x个省份增幅为负
    格式三：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅为正%；xx、xx、xx等x个省份增幅为负
    格式四：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%；xx、xx、xx等x个省份增幅为正%；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    
    参考样列一：
    输入：
    时间：2024-03;
    南方21省中标金额为61239.77 亿;南方21省中标金额同比:各省份的整体南方21省中标金额同比为0.32%;江苏,广东,新疆,广西,重庆,湖南,云南,福建,江西共9个省份的南方21省中标金额同比大于等于0.32%;宁夏共1个省份的南方21省中标金额同比在0.00%到0.32%之间;陕西,青海,浙江,湖北,西藏,上海,海南,贵州,四川,甘肃,安徽共11个省份的南方21省中标金额同比负增长;
    北方10省中标金额为28889.31 亿;北方10省中标金额同比:各省份的整体北方10省中标金额同比为-1.20%;辽宁,吉林共2个省份的北方10省中标金额同比正增长;黑龙江,河南,内蒙古,河北,天津,北京,山西,山东共8个省份的北方10省中标金额同比负增长
    输出：
    南方21省中标金额61239.77亿，同比增长0.32%，其中：江苏、广东、新疆、广西、重庆、湖南、云南、福建、江西等9个省份增幅高于0.32%；宁夏等1个省份增幅在0.00%到0.32%之间；陕西、青海、浙江、湖北、西藏、上海、海南、贵州、四川、甘肃、安徽等11个省份增幅为负。
    北方10省中标金额28889.31亿，同比下降1.20%，其中：辽宁、吉林等2个省份增幅为正；黑龙江、河南、内蒙古、河北、天津、北京、山西、山东等8个省份增幅为负。
    
    请直接输出结果，不要在前面加任何多余的文字

        """,
        template_id=1,
        text_op_id=7,
        input_data_op_ids=[53, 54, 55, 56],
        type="润色",
    ),
    PPTTextOp(
        name="全市场中标分区域金额格式",
        prompt="""
        请严格按照以下的几种格式之一，选择合适的格式，将待润色的内容中的数字填入，不要漏掉任何省份，如没有则不填，不可以改变任何数据，只直接输出润色结果：
        格式一：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        格式二：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        格式三：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长
        格式四：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长        
    
        参考样列一：
        输入：
        时间：2024-03;
        南方21省中标份额为4.57%;南方21省中标份额变化提升0.07pp;南方21省分省份中标份额:上海,广西,江苏,浙江,甘肃,福建,贵州,陕西,青海共9个省份的南方21省分省份中标份额高于4.57%;云南,四川,宁夏,安徽,广东,新疆,江西,海南,湖北,湖南,西藏,重庆共12个省份的南方21省分省份中标份额低于4.57%;
        南方21省分省份中标份额变化:各省份的整体南方21省分省份中标份额变化为0.07pp;上海,广东,江苏,浙江,海南,湖北,甘肃,福建,贵州,重庆,青海共11个省份的南方21省分省份中标份额变化大于等于0.07pp;新疆,西藏共2个省份的南方21省分省份中标份额变化在0.00pp到0.07pp之间;云南,四川,宁夏,安徽,广西,江西,湖南,陕西共8个省份的南方21省分省份中标份额变化负增长;
        北方10省中标份额为4.49%;北方10省中标份额变化下降0.05pp;北方10省分省份中标份额:内蒙古,北京,吉林,河南,黑龙江共5个省份的北方10省分省份中标份额高于4.49%;天津,山东,山西,河北,辽宁共5个省份的北方10省分省份中标份额低于4.49%;
        北方10省分省份中标份额变化:各省份的整体北方10省分省份中标份额变化为-0.05pp;北京,吉林,河北,河南共4个省份的北方10省分省份中标份额变化正增长;内蒙古,天津,山东,山西,辽宁,黑龙江共6个省份的北方10省分省份中标份额变化负增长
        输出：
        南方21省中标份额4.57%，较去年同期提升0.07pp。其中：上海、广西、江苏、浙江、甘肃、福建、贵州、陕西、青海等9个省份份额高于4.57%，云南、四川、宁夏、安徽、广东、新疆、江西、海南、湖北、湖南、西藏、重庆等12个省份份额低于4.57%；上海、广东、江苏、浙江、海南、湖北、甘肃、福建、贵州、重庆、青海等11个省份份额较去年同期增幅高于0.07pp；新疆、西藏等2个省份份额较去年同期增幅在0.00pp到0.07pp之间；云南、四川、宁夏、安徽、广西、江西、湖南、陕西等8个省份份额负增长。
        北方10省中标份额4.49%，较去年同期下降0.05pp。其中：内蒙古、北京、吉林、河南、黑龙江等5个省份份额高于4.49%，天津、山东、山西、河北、辽宁等5个省份份额低于4.49%；北京、吉林、河北、河南等4个省份份额正增长；内蒙古、天津、山东、山西、辽宁、黑龙江等6个省份份额负增长。
    请直接输出结果，不要在前面加任何多余的文字
        """,
        template_id=1,
        text_op_id=8,
        input_data_op_ids=[59, 165, 60, 61, 62, 166, 63, 64],
        type="润色",
    ),
    PPTTextOp(
        name="全市场中标分区域金额格式",
        prompt="""
    请严格按照以下几种格式之一，选择合适的格式，将待润色的内容中的数字填入，不要漏掉任何省份，如没有则不填，不可以改变任何数据，只直接输出润色结果：
    格式一：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    格式二：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅为正；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅为正；xx、xx、xx等x个省份增幅为负
    格式三：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅为正%；xx、xx、xx等x个省份增幅为负
    格式四：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%；xx、xx、xx等x个省份增幅为正%；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    
    参考样列一：
    输入：
    时间：2024-03;
    南方21省中标金额为61239.77 亿;南方21省中标金额同比:各省份的整体南方21省中标金额同比为0.32%;江苏,广东,新疆,广西,重庆,湖南,云南,福建,江西共9个省份的南方21省中标金额同比大于等于0.32%;宁夏共1个省份的南方21省中标金额同比在0.00%到0.32%之间;陕西,青海,浙江,湖北,西藏,上海,海南,贵州,四川,甘肃,安徽共11个省份的南方21省中标金额同比负增长;
    北方10省中标金额为28889.31 亿;北方10省中标金额同比:各省份的整体北方10省中标金额同比为-1.20%;辽宁,吉林共2个省份的北方10省中标金额同比正增长;黑龙江,河南,内蒙古,河北,天津,北京,山西,山东共8个省份的北方10省中标金额同比负增长
    输出：
    南方21省中标金额61239.77亿，同比增长0.32%，其中：江苏、广东、新疆、广西、重庆、湖南、云南、福建、江西等9个省份增幅高于0.32%；宁夏等1个省份增幅在0.00%到0.32%之间；陕西、青海、浙江、湖北、西藏、上海、海南、贵州、四川、甘肃、安徽等11个省份增幅为负。
    北方10省中标金额28889.31亿，同比下降1.20%，其中：辽宁、吉林等2个省份增幅为正；黑龙江、河南、内蒙古、河北、天津、北京、山西、山东等8个省份增幅为负。
    
    请直接输出结果，不要在前面加任何多余的文字

        """,
        template_id=1,
        text_op_id=9,
        input_data_op_ids=[67, 68, 69, 70],
        type="润色",
    ),
    PPTTextOp(
        name="全市场中标分区域金额格式",
        prompt="""
        请严格按照以下的几种格式之一，选择合适的格式，将待润色的内容中的数字填入，不要漏掉任何省份，如没有则不填，不可以改变任何数据，只直接输出润色结果：
        格式一：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        格式二：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        格式三：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长
        格式四：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长        
    
        参考样列一：
        输入：
        时间：2024-03;
        南方21省中标份额为4.57%;南方21省中标份额变化提升0.07pp;南方21省分省份中标份额:上海,广西,江苏,浙江,甘肃,福建,贵州,陕西,青海共9个省份的南方21省分省份中标份额高于4.57%;云南,四川,宁夏,安徽,广东,新疆,江西,海南,湖北,湖南,西藏,重庆共12个省份的南方21省分省份中标份额低于4.57%;
        南方21省分省份中标份额变化:各省份的整体南方21省分省份中标份额变化为0.07pp;上海,广东,江苏,浙江,海南,湖北,甘肃,福建,贵州,重庆,青海共11个省份的南方21省分省份中标份额变化大于等于0.07pp;新疆,西藏共2个省份的南方21省分省份中标份额变化在0.00pp到0.07pp之间;云南,四川,宁夏,安徽,广西,江西,湖南,陕西共8个省份的南方21省分省份中标份额变化负增长;
        北方10省中标份额为4.49%;北方10省中标份额变化下降0.05pp;北方10省分省份中标份额:内蒙古,北京,吉林,河南,黑龙江共5个省份的北方10省分省份中标份额高于4.49%;天津,山东,山西,河北,辽宁共5个省份的北方10省分省份中标份额低于4.49%;
        北方10省分省份中标份额变化:各省份的整体北方10省分省份中标份额变化为-0.05pp;北京,吉林,河北,河南共4个省份的北方10省分省份中标份额变化正增长;内蒙古,天津,山东,山西,辽宁,黑龙江共6个省份的北方10省分省份中标份额变化负增长
        输出：
        南方21省中标份额4.57%，较去年同期提升0.07pp。其中：上海、广西、江苏、浙江、甘肃、福建、贵州、陕西、青海等9个省份份额高于4.57%，云南、四川、宁夏、安徽、广东、新疆、江西、海南、湖北、湖南、西藏、重庆等12个省份份额低于4.57%；上海、广东、江苏、浙江、海南、湖北、甘肃、福建、贵州、重庆、青海等11个省份份额较去年同期增幅高于0.07pp；新疆、西藏等2个省份份额较去年同期增幅在0.00pp到0.07pp之间；云南、四川、宁夏、安徽、广西、江西、湖南、陕西等8个省份份额负增长。
        北方10省中标份额4.49%，较去年同期下降0.05pp。其中：内蒙古、北京、吉林、河南、黑龙江等5个省份份额高于4.49%，天津、山东、山西、河北、辽宁等5个省份份额低于4.49%；北京、吉林、河北、河南等4个省份份额正增长；内蒙古、天津、山东、山西、辽宁、黑龙江等6个省份份额负增长。
    请直接输出结果，不要在前面加任何多余的文字
        """,
        template_id=1,
        text_op_id=10,
        input_data_op_ids=[73, 167, 74, 75, 76, 168, 77, 78],
        type="润色",
    ),
    PPTTextOp(
        name="全市场中标分区域金额格式",
        prompt="""
    请严格按照以下几种格式之一，选择合适的格式，将待润色的内容中的数字填入，不要漏掉任何省份，如没有则不填，不可以改变任何数据，只直接输出润色结果：
    格式一：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    格式二：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅为正；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅为正；xx、xx、xx等x个省份增幅为负
    格式三：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅为正%；xx、xx、xx等x个省份增幅为负
    格式四：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%；xx、xx、xx等x个省份增幅为正%；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    
    参考样列一：
    输入：
    时间：2024-03;
    南方21省中标金额为61239.77 亿;南方21省中标金额同比:各省份的整体南方21省中标金额同比为0.32%;江苏,广东,新疆,广西,重庆,湖南,云南,福建,江西共9个省份的南方21省中标金额同比大于等于0.32%;宁夏共1个省份的南方21省中标金额同比在0.00%到0.32%之间;陕西,青海,浙江,湖北,西藏,上海,海南,贵州,四川,甘肃,安徽共11个省份的南方21省中标金额同比负增长;
    北方10省中标金额为28889.31 亿;北方10省中标金额同比:各省份的整体北方10省中标金额同比为-1.20%;辽宁,吉林共2个省份的北方10省中标金额同比正增长;黑龙江,河南,内蒙古,河北,天津,北京,山西,山东共8个省份的北方10省中标金额同比负增长
    输出：
    南方21省中标金额61239.77亿，同比增长0.32%，其中：江苏、广东、新疆、广西、重庆、湖南、云南、福建、江西等9个省份增幅高于0.32%；宁夏等1个省份增幅在0.00%到0.32%之间；陕西、青海、浙江、湖北、西藏、上海、海南、贵州、四川、甘肃、安徽等11个省份增幅为负。
    北方10省中标金额28889.31亿，同比下降1.20%，其中：辽宁、吉林等2个省份增幅为正；黑龙江、河南、内蒙古、河北、天津、北京、山西、山东等8个省份增幅为负。
    
    请直接输出结果，不要在前面加任何多余的文字

        """,
        template_id=1,
        text_op_id=11,
        input_data_op_ids=[81, 82, 83, 84],
        type="润色",
    ),
    PPTTextOp(
        name="全市场中标分区域金额格式",
        prompt="""
        请严格按照以下的几种格式之一，选择合适的格式，将待润色的内容中的数字填入，不要漏掉任何省份，如没有则不填，不可以改变任何数据，只直接输出润色结果：
        格式一：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        格式二：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        格式三：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长
        格式四：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长        
    
        参考样列一：
        输入：
        时间：2024-03;
        南方21省中标份额为4.57%;南方21省中标份额变化提升0.07pp;南方21省分省份中标份额:上海,广西,江苏,浙江,甘肃,福建,贵州,陕西,青海共9个省份的南方21省分省份中标份额高于4.57%;云南,四川,宁夏,安徽,广东,新疆,江西,海南,湖北,湖南,西藏,重庆共12个省份的南方21省分省份中标份额低于4.57%;
        南方21省分省份中标份额变化:各省份的整体南方21省分省份中标份额变化为0.07pp;上海,广东,江苏,浙江,海南,湖北,甘肃,福建,贵州,重庆,青海共11个省份的南方21省分省份中标份额变化大于等于0.07pp;新疆,西藏共2个省份的南方21省分省份中标份额变化在0.00pp到0.07pp之间;云南,四川,宁夏,安徽,广西,江西,湖南,陕西共8个省份的南方21省分省份中标份额变化负增长;
        北方10省中标份额为4.49%;北方10省中标份额变化下降0.05pp;北方10省分省份中标份额:内蒙古,北京,吉林,河南,黑龙江共5个省份的北方10省分省份中标份额高于4.49%;天津,山东,山西,河北,辽宁共5个省份的北方10省分省份中标份额低于4.49%;
        北方10省分省份中标份额变化:各省份的整体北方10省分省份中标份额变化为-0.05pp;北京,吉林,河北,河南共4个省份的北方10省分省份中标份额变化正增长;内蒙古,天津,山东,山西,辽宁,黑龙江共6个省份的北方10省分省份中标份额变化负增长
        输出：
        南方21省中标份额4.57%，较去年同期提升0.07pp。其中：上海、广西、江苏、浙江、甘肃、福建、贵州、陕西、青海等9个省份份额高于4.57%，云南、四川、宁夏、安徽、广东、新疆、江西、海南、湖北、湖南、西藏、重庆等12个省份份额低于4.57%；上海、广东、江苏、浙江、海南、湖北、甘肃、福建、贵州、重庆、青海等11个省份份额较去年同期增幅高于0.07pp；新疆、西藏等2个省份份额较去年同期增幅在0.00pp到0.07pp之间；云南、四川、宁夏、安徽、广西、江西、湖南、陕西等8个省份份额负增长。
        北方10省中标份额4.49%，较去年同期下降0.05pp。其中：内蒙古、北京、吉林、河南、黑龙江等5个省份份额高于4.49%，天津、山东、山西、河北、辽宁等5个省份份额低于4.49%；北京、吉林、河北、河南等4个省份份额正增长；内蒙古、天津、山东、山西、辽宁、黑龙江等6个省份份额负增长。
    请直接输出结果，不要在前面加任何多余的文字
        """,
        template_id=1,
        text_op_id=12,
        input_data_op_ids=[87, 169, 88, 89, 90, 170, 91, 92],
        type="润色",
    ),
    PPTTextOp(
        name="全市场中标分区域金额格式",
        prompt="""
    请严格按照以下几种格式之一，选择合适的格式，将待润色的内容中的数字填入，不要漏掉任何省份，如没有则不填，不可以改变任何数据，只直接输出润色结果：
    格式一：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    格式二：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅为正；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅为正；xx、xx、xx等x个省份增幅为负
    格式三：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅为正%；xx、xx、xx等x个省份增幅为负
    格式四：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%；xx、xx、xx等x个省份增幅为正%；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    
    参考样列一：
    输入：
    时间：2024-03;
    南方21省中标金额为61239.77 亿;南方21省中标金额同比:各省份的整体南方21省中标金额同比为0.32%;江苏,广东,新疆,广西,重庆,湖南,云南,福建,江西共9个省份的南方21省中标金额同比大于等于0.32%;宁夏共1个省份的南方21省中标金额同比在0.00%到0.32%之间;陕西,青海,浙江,湖北,西藏,上海,海南,贵州,四川,甘肃,安徽共11个省份的南方21省中标金额同比负增长;
    北方10省中标金额为28889.31 亿;北方10省中标金额同比:各省份的整体北方10省中标金额同比为-1.20%;辽宁,吉林共2个省份的北方10省中标金额同比正增长;黑龙江,河南,内蒙古,河北,天津,北京,山西,山东共8个省份的北方10省中标金额同比负增长
    输出：
    南方21省中标金额61239.77亿，同比增长0.32%，其中：江苏、广东、新疆、广西、重庆、湖南、云南、福建、江西等9个省份增幅高于0.32%；宁夏等1个省份增幅在0.00%到0.32%之间；陕西、青海、浙江、湖北、西藏、上海、海南、贵州、四川、甘肃、安徽等11个省份增幅为负。
    北方10省中标金额28889.31亿，同比下降1.20%，其中：辽宁、吉林等2个省份增幅为正；黑龙江、河南、内蒙古、河北、天津、北京、山西、山东等8个省份增幅为负。
    
    请直接输出结果，不要在前面加任何多余的文字

        """,
        template_id=1,
        text_op_id=13,
        input_data_op_ids=[
            102,
            103,
            104,
            105,
        ],
        type="润色",
    ),
    PPTTextOp(
        name="全市场中标分区域金额格式",
        prompt="""
        请严格按照以下的几种格式之一，选择合适的格式，将待润色的内容中的数字填入，不要漏掉任何省份，如没有则不填，不可以改变任何数据，只直接输出润色结果：
        格式一：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        格式二：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        格式三：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长
        格式四：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长        
    
        参考样列一：
        输入：
        时间：2024-03;
        南方21省中标份额为4.57%;南方21省中标份额变化提升0.07pp;南方21省分省份中标份额:上海,广西,江苏,浙江,甘肃,福建,贵州,陕西,青海共9个省份的南方21省分省份中标份额高于4.57%;云南,四川,宁夏,安徽,广东,新疆,江西,海南,湖北,湖南,西藏,重庆共12个省份的南方21省分省份中标份额低于4.57%;
        南方21省分省份中标份额变化:各省份的整体南方21省分省份中标份额变化为0.07pp;上海,广东,江苏,浙江,海南,湖北,甘肃,福建,贵州,重庆,青海共11个省份的南方21省分省份中标份额变化大于等于0.07pp;新疆,西藏共2个省份的南方21省分省份中标份额变化在0.00pp到0.07pp之间;云南,四川,宁夏,安徽,广西,江西,湖南,陕西共8个省份的南方21省分省份中标份额变化负增长;
        北方10省中标份额为4.49%;北方10省中标份额变化下降0.05pp;北方10省分省份中标份额:内蒙古,北京,吉林,河南,黑龙江共5个省份的北方10省分省份中标份额高于4.49%;天津,山东,山西,河北,辽宁共5个省份的北方10省分省份中标份额低于4.49%;
        北方10省分省份中标份额变化:各省份的整体北方10省分省份中标份额变化为-0.05pp;北京,吉林,河北,河南共4个省份的北方10省分省份中标份额变化正增长;内蒙古,天津,山东,山西,辽宁,黑龙江共6个省份的北方10省分省份中标份额变化负增长
        输出：
        南方21省中标份额4.57%，较去年同期提升0.07pp。其中：上海、广西、江苏、浙江、甘肃、福建、贵州、陕西、青海等9个省份份额高于4.57%，云南、四川、宁夏、安徽、广东、新疆、江西、海南、湖北、湖南、西藏、重庆等12个省份份额低于4.57%；上海、广东、江苏、浙江、海南、湖北、甘肃、福建、贵州、重庆、青海等11个省份份额较去年同期增幅高于0.07pp；新疆、西藏等2个省份份额较去年同期增幅在0.00pp到0.07pp之间；云南、四川、宁夏、安徽、广西、江西、湖南、陕西等8个省份份额负增长。
        北方10省中标份额4.49%，较去年同期下降0.05pp。其中：内蒙古、北京、吉林、河南、黑龙江等5个省份份额高于4.49%，天津、山东、山西、河北、辽宁等5个省份份额低于4.49%；北京、吉林、河北、河南等4个省份份额正增长；内蒙古、天津、山东、山西、辽宁、黑龙江等6个省份份额负增长。
    请直接输出结果，不要在前面加任何多余的文字
        """,
        template_id=1,
        text_op_id=14,
        input_data_op_ids=[108, 171, 109, 110, 111, 172, 112, 113],
        type="润色",
    ),
    PPTTextOp(
        name="全市场中标分区域金额格式",
        prompt="""
    请严格按照以下几种格式之一，选择合适的格式，将待润色的内容中的数字填入，不要漏掉任何省份，如没有则不填，不可以改变任何数据，只直接输出润色结果：
    格式一：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    格式二：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅为正；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅为正；xx、xx、xx等x个省份增幅为负
    格式三：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅为正%；xx、xx、xx等x个省份增幅为负
    格式四：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%；xx、xx、xx等x个省份增幅为正%；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    
    参考样列一：
    输入：
    时间：2024-03;
    南方21省中标金额为61239.77 亿;南方21省中标金额同比:各省份的整体南方21省中标金额同比为0.32%;江苏,广东,新疆,广西,重庆,湖南,云南,福建,江西共9个省份的南方21省中标金额同比大于等于0.32%;宁夏共1个省份的南方21省中标金额同比在0.00%到0.32%之间;陕西,青海,浙江,湖北,西藏,上海,海南,贵州,四川,甘肃,安徽共11个省份的南方21省中标金额同比负增长;
    北方10省中标金额为28889.31 亿;北方10省中标金额同比:各省份的整体北方10省中标金额同比为-1.20%;辽宁,吉林共2个省份的北方10省中标金额同比正增长;黑龙江,河南,内蒙古,河北,天津,北京,山西,山东共8个省份的北方10省中标金额同比负增长
    输出：
    南方21省中标金额61239.77亿，同比增长0.32%，其中：江苏、广东、新疆、广西、重庆、湖南、云南、福建、江西等9个省份增幅高于0.32%；宁夏等1个省份增幅在0.00%到0.32%之间；陕西、青海、浙江、湖北、西藏、上海、海南、贵州、四川、甘肃、安徽等11个省份增幅为负。
    北方10省中标金额28889.31亿，同比下降1.20%，其中：辽宁、吉林等2个省份增幅为正；黑龙江、河南、内蒙古、河北、天津、北京、山西、山东等8个省份增幅为负。
    
    请直接输出结果，不要在前面加任何多余的文字

        """,
        template_id=1,
        text_op_id=15,
        input_data_op_ids=[116, 117, 118, 119],
        type="润色",
    ),
    PPTTextOp(
        name="全市场中标分区域金额格式",
        prompt="""
        请严格按照以下的几种格式之一，选择合适的格式，将待润色的内容中的数字填入，不要漏掉任何省份，如没有则不填，不可以改变任何数据，只直接输出润色结果：
        格式一：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        格式二：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        格式三：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长
        格式四：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长        
    
        参考样列一：
        输入：
        时间：2024-03;
        南方21省中标份额为4.57%;南方21省中标份额变化提升0.07pp;南方21省分省份中标份额:上海,广西,江苏,浙江,甘肃,福建,贵州,陕西,青海共9个省份的南方21省分省份中标份额高于4.57%;云南,四川,宁夏,安徽,广东,新疆,江西,海南,湖北,湖南,西藏,重庆共12个省份的南方21省分省份中标份额低于4.57%;
        南方21省分省份中标份额变化:各省份的整体南方21省分省份中标份额变化为0.07pp;上海,广东,江苏,浙江,海南,湖北,甘肃,福建,贵州,重庆,青海共11个省份的南方21省分省份中标份额变化大于等于0.07pp;新疆,西藏共2个省份的南方21省分省份中标份额变化在0.00pp到0.07pp之间;云南,四川,宁夏,安徽,广西,江西,湖南,陕西共8个省份的南方21省分省份中标份额变化负增长;
        北方10省中标份额为4.49%;北方10省中标份额变化下降0.05pp;北方10省分省份中标份额:内蒙古,北京,吉林,河南,黑龙江共5个省份的北方10省分省份中标份额高于4.49%;天津,山东,山西,河北,辽宁共5个省份的北方10省分省份中标份额低于4.49%;
        北方10省分省份中标份额变化:各省份的整体北方10省分省份中标份额变化为-0.05pp;北京,吉林,河北,河南共4个省份的北方10省分省份中标份额变化正增长;内蒙古,天津,山东,山西,辽宁,黑龙江共6个省份的北方10省分省份中标份额变化负增长
        输出：
        南方21省中标份额4.57%，较去年同期提升0.07pp。其中：上海、广西、江苏、浙江、甘肃、福建、贵州、陕西、青海等9个省份份额高于4.57%，云南、四川、宁夏、安徽、广东、新疆、江西、海南、湖北、湖南、西藏、重庆等12个省份份额低于4.57%；上海、广东、江苏、浙江、海南、湖北、甘肃、福建、贵州、重庆、青海等11个省份份额较去年同期增幅高于0.07pp；新疆、西藏等2个省份份额较去年同期增幅在0.00pp到0.07pp之间；云南、四川、宁夏、安徽、广西、江西、湖南、陕西等8个省份份额负增长。
        北方10省中标份额4.49%，较去年同期下降0.05pp。其中：内蒙古、北京、吉林、河南、黑龙江等5个省份份额高于4.49%，天津、山东、山西、河北、辽宁等5个省份份额低于4.49%；北京、吉林、河北、河南等4个省份份额正增长；内蒙古、天津、山东、山西、辽宁、黑龙江等6个省份份额负增长。
    请直接输出结果，不要在前面加任何多余的文字
        """,
        template_id=1,
        text_op_id=16,
        input_data_op_ids=[122, 173, 123, 124, 125, 174, 126, 127],
        type="润色",
    ),
    PPTTextOp(
        name="全市场中标分区域金额格式",
        prompt="""
    请严格按照以下几种格式之一，选择合适的格式，将待润色的内容中的数字填入，不要漏掉任何省份，如没有则不填，不可以改变任何数据，只直接输出润色结果：
    格式一：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    格式二：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅为正；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅为正；xx、xx、xx等x个省份增幅为负
    格式三：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅为正%；xx、xx、xx等x个省份增幅为负
    格式四：
    南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%；xx、xx、xx等x个省份增幅为正%；xx、xx、xx等x个省份增幅为负
    北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
    
    参考样列一：
    输入：
    时间：2024-03;
    南方21省中标金额为61239.77 亿;南方21省中标金额同比:各省份的整体南方21省中标金额同比为0.32%;江苏,广东,新疆,广西,重庆,湖南,云南,福建,江西共9个省份的南方21省中标金额同比大于等于0.32%;宁夏共1个省份的南方21省中标金额同比在0.00%到0.32%之间;陕西,青海,浙江,湖北,西藏,上海,海南,贵州,四川,甘肃,安徽共11个省份的南方21省中标金额同比负增长;
    北方10省中标金额为28889.31 亿;北方10省中标金额同比:各省份的整体北方10省中标金额同比为-1.20%;辽宁,吉林共2个省份的北方10省中标金额同比正增长;黑龙江,河南,内蒙古,河北,天津,北京,山西,山东共8个省份的北方10省中标金额同比负增长
    输出：
    南方21省中标金额61239.77亿，同比增长0.32%，其中：江苏、广东、新疆、广西、重庆、湖南、云南、福建、江西等9个省份增幅高于0.32%；宁夏等1个省份增幅在0.00%到0.32%之间；陕西、青海、浙江、湖北、西藏、上海、海南、贵州、四川、甘肃、安徽等11个省份增幅为负。
    北方10省中标金额28889.31亿，同比下降1.20%，其中：辽宁、吉林等2个省份增幅为正；黑龙江、河南、内蒙古、河北、天津、北京、山西、山东等8个省份增幅为负。
    
    请直接输出结果，不要在前面加任何多余的文字

        """,
        template_id=1,
        text_op_id=17,
        input_data_op_ids=[130, 131, 132, 133],
        type="润色",
    ),
    PPTTextOp(
        name="全市场中标分区域金额格式",
        prompt="""
        请严格按照以下的几种格式之一，选择合适的格式，将待润色的内容中的数字填入，不要漏掉任何省份，如没有则不填，不可以改变任何数据，只直接输出润色结果：
        格式一：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        格式二：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        格式三：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长
        格式四：
        南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长
        北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长        
    
        参考样列一：
        输入：
        时间：2024-03;
        南方21省中标份额为4.57%;南方21省中标份额变化提升0.07pp;南方21省分省份中标份额:上海,广西,江苏,浙江,甘肃,福建,贵州,陕西,青海共9个省份的南方21省分省份中标份额高于4.57%;云南,四川,宁夏,安徽,广东,新疆,江西,海南,湖北,湖南,西藏,重庆共12个省份的南方21省分省份中标份额低于4.57%;
        南方21省分省份中标份额变化:各省份的整体南方21省分省份中标份额变化为0.07pp;上海,广东,江苏,浙江,海南,湖北,甘肃,福建,贵州,重庆,青海共11个省份的南方21省分省份中标份额变化大于等于0.07pp;新疆,西藏共2个省份的南方21省分省份中标份额变化在0.00pp到0.07pp之间;云南,四川,宁夏,安徽,广西,江西,湖南,陕西共8个省份的南方21省分省份中标份额变化负增长;
        北方10省中标份额为4.49%;北方10省中标份额变化下降0.05pp;北方10省分省份中标份额:内蒙古,北京,吉林,河南,黑龙江共5个省份的北方10省分省份中标份额高于4.49%;天津,山东,山西,河北,辽宁共5个省份的北方10省分省份中标份额低于4.49%;
        北方10省分省份中标份额变化:各省份的整体北方10省分省份中标份额变化为-0.05pp;北京,吉林,河北,河南共4个省份的北方10省分省份中标份额变化正增长;内蒙古,天津,山东,山西,辽宁,黑龙江共6个省份的北方10省分省份中标份额变化负增长
        输出：
        南方21省中标份额4.57%，较去年同期提升0.07pp。其中：上海、广西、江苏、浙江、甘肃、福建、贵州、陕西、青海等9个省份份额高于4.57%，云南、四川、宁夏、安徽、广东、新疆、江西、海南、湖北、湖南、西藏、重庆等12个省份份额低于4.57%；上海、广东、江苏、浙江、海南、湖北、甘肃、福建、贵州、重庆、青海等11个省份份额较去年同期增幅高于0.07pp；新疆、西藏等2个省份份额较去年同期增幅在0.00pp到0.07pp之间；云南、四川、宁夏、安徽、广西、江西、湖南、陕西等8个省份份额负增长。
        北方10省中标份额4.49%，较去年同期下降0.05pp。其中：内蒙古、北京、吉林、河南、黑龙江等5个省份份额高于4.49%，天津、山东、山西、河北、辽宁等5个省份份额低于4.49%；北京、吉林、河北、河南等4个省份份额正增长；内蒙古、天津、山东、山西、辽宁、黑龙江等6个省份份额负增长。
    请直接输出结果，不要在前面加任何多余的文字
        """,
        template_id=1,
        text_op_id=18,
        input_data_op_ids=[136, 175, 137, 138, 139, 176, 140, 141],
        type="润色",
    ),
]

if __name__ == "__main__":

    def generate_one_slide_text_op(data_op_id, text_op_id):
        text_op_list = [
            PPTTextOp(
                name="全市场中标分区域金额格式",
                prompt="""
        请严格按照以下几种格式之一，选择合适的格式，将待润色的内容中的数字填入，不要漏掉任何省份，如没有则不填，不可以改变任何数据，只直接输出润色结果：
        格式一：
        南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
        北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
        格式二：
        南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅为正；xx、xx、xx等x个省份增幅为负
        北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅为正；xx、xx、xx等x个省份增幅为负
        格式三：
        南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负
        北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅为正%；xx、xx、xx等x个省份增幅为负
        格式四：
        南方21省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%；xx、xx、xx等x个省份增幅为正%；xx、xx、xx等x个省份增幅为负
        北方10省中标金额xx亿，同比增长xx%，其中：xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅高于xx%； xx、xx、xx等x个省份增幅低于xx%；xx、xx、xx等x个省份增幅为负

        参考样列一：
        输入：
        时间：2024-03;
        南方21省中标金额为61239.77 亿;南方21省中标金额同比:各省份的整体南方21省中标金额同比为0.32%;江苏,广东,新疆,广西,重庆,湖南,云南,福建,江西共9个省份的南方21省中标金额同比大于等于0.32%;宁夏共1个省份的南方21省中标金额同比在0.00%到0.32%之间;陕西,青海,浙江,湖北,西藏,上海,海南,贵州,四川,甘肃,安徽共11个省份的南方21省中标金额同比负增长;
        北方10省中标金额为28889.31 亿;北方10省中标金额同比:各省份的整体北方10省中标金额同比为-1.20%;辽宁,吉林共2个省份的北方10省中标金额同比正增长;黑龙江,河南,内蒙古,河北,天津,北京,山西,山东共8个省份的北方10省中标金额同比负增长
        输出：
        南方21省中标金额61239.77亿，同比增长0.32%，其中：江苏、广东、新疆、广西、重庆、湖南、云南、福建、江西等9个省份增幅高于0.32%；宁夏等1个省份增幅在0.00%到0.32%之间；陕西、青海、浙江、湖北、西藏、上海、海南、贵州、四川、甘肃、安徽等11个省份增幅为负。
        北方10省中标金额28889.31亿，同比下降1.20%，其中：辽宁、吉林等2个省份增幅为正；黑龙江、河南、内蒙古、河北、天津、北京、山西、山东等8个省份增幅为负。

        请直接输出结果，不要在前面加任何多余的文字

            """,
                template_id=1,
                text_op_id=text_op_id,
                input_data_op_ids=[
                    data_op_id,
                    data_op_id + 1,
                    data_op_id + 2,
                    data_op_id + 3,
                ],
                type="润色",
            ),
            PPTTextOp(
                name="全市场中标分区域金额格式",
                prompt="""
            请严格按照以下的几种格式之一，选择合适的格式，将待润色的内容中的数字填入，不要漏掉任何省份，如没有则不填，不可以改变任何数据，只直接输出润色结果：
            格式一：
            南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
            北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
            格式二：
            南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长
            北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
            格式三：
            南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额较去年同期增幅高于XXpp；XX、XX、XX、XX等x个省份份额较去年同期增幅低于 XXpp；XX、XX、XX、XX等x个省份份额负增长
            北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长
            格式四：
            南方21省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长
            北方10省中标份额xx%，较去年同期 XXpp。其中：xx、xx、xx等x个省份份额高于xx%，xx、xx、xx等x个省份份额低于xx% ；XX、XX、XX、XX等x个省份份额正增长；XX、XX、XX、XX等x个省份份额负增长        

            参考样列一：
            输入：
            时间：2024-03;
            南方21省中标份额为4.57%;南方21省中标份额变化提升0.07pp;南方21省分省份中标份额:上海,广西,江苏,浙江,甘肃,福建,贵州,陕西,青海共9个省份的南方21省分省份中标份额高于4.57%;云南,四川,宁夏,安徽,广东,新疆,江西,海南,湖北,湖南,西藏,重庆共12个省份的南方21省分省份中标份额低于4.57%;
            南方21省分省份中标份额变化:各省份的整体南方21省分省份中标份额变化为0.07pp;上海,广东,江苏,浙江,海南,湖北,甘肃,福建,贵州,重庆,青海共11个省份的南方21省分省份中标份额变化大于等于0.07pp;新疆,西藏共2个省份的南方21省分省份中标份额变化在0.00pp到0.07pp之间;云南,四川,宁夏,安徽,广西,江西,湖南,陕西共8个省份的南方21省分省份中标份额变化负增长;
            北方10省中标份额为4.49%;北方10省中标份额变化下降0.05pp;北方10省分省份中标份额:内蒙古,北京,吉林,河南,黑龙江共5个省份的北方10省分省份中标份额高于4.49%;天津,山东,山西,河北,辽宁共5个省份的北方10省分省份中标份额低于4.49%;
            北方10省分省份中标份额变化:各省份的整体北方10省分省份中标份额变化为-0.05pp;北京,吉林,河北,河南共4个省份的北方10省分省份中标份额变化正增长;内蒙古,天津,山东,山西,辽宁,黑龙江共6个省份的北方10省分省份中标份额变化负增长
            输出：
            南方21省中标份额4.57%，较去年同期提升0.07pp。其中：上海、广西、江苏、浙江、甘肃、福建、贵州、陕西、青海等9个省份份额高于4.57%，云南、四川、宁夏、安徽、广东、新疆、江西、海南、湖北、湖南、西藏、重庆等12个省份份额低于4.57%；上海、广东、江苏、浙江、海南、湖北、甘肃、福建、贵州、重庆、青海等11个省份份额较去年同期增幅高于0.07pp；新疆、西藏等2个省份份额较去年同期增幅在0.00pp到0.07pp之间；云南、四川、宁夏、安徽、广西、江西、湖南、陕西等8个省份份额负增长。
            北方10省中标份额4.49%，较去年同期下降0.05pp。其中：内蒙古、北京、吉林、河南、黑龙江等5个省份份额高于4.49%，天津、山东、山西、河北、辽宁等5个省份份额低于4.49%；北京、吉林、河北、河南等4个省份份额正增长；内蒙古、天津、山东、山西、辽宁、黑龙江等6个省份份额负增长。
        请直接输出结果，不要在前面加任何多余的文字
            """,
                template_id=1,
                text_op_id=text_op_id + 1,
                input_data_op_ids=[
                    data_op_id + 6,
                    data_op_id + 7,
                    data_op_id + 8,
                    data_op_id + 9,
                    data_op_id + 10,
                    data_op_id + 11,
                    data_op_id + 12,
                    data_op_id + 13,
                ],
                type="润色",
            ),
        ]
        data_op_id += 16
        text_op_id += 2
        ppt_text_op_list.extend(text_op_list)

    from collections import OrderedDict

    department_map = OrderedDict(
        {
            # "政务行业拓展部": "政务",
            # "金融行业事业部": "金融",
            # "商业客户拓展部": "商客",
            # "交通物流行业事业部": "交通物流",
            "教育行业事业部": "教育",
            "要客服务事业部": "要客",
            "农业农村行业事业部": "农业农村",
            "卫健行业事业部": "卫健",
            "应急行业事业部": "应急",
            "政法公安行业事业部": "政法公安",
            "文宣行业事业部": "文宣",
            "住建行业事业部": "住建",
            "互联网行业事业部一部": "互联网一部",
            "互联网行业事业部二部": "互联网二部",
            "工业行业事业部": "工业",
            "能源化工团队": "能源化工",
            "车联网创新团队": "车联网",
        }
    )
    data_op_id = 183
    text_op_id = 19
    for k, v in department_map.items():
        generate_one_slide_text_op(data_op_id, text_op_id)
        data_op_id += 16
        text_op_id += 2

    save_and_create_ppt_text_op(ppt_text_op_list)

    template2_index = text_op_id  #### 45

    ppt_text_op_list_template2 = [
        PPTTextOp(
            name="分地市中标金额",
            prompt="""
            请严格按照以下格式，将待润色的内容中的数字填入，不要漏掉任何省份，如没有则不填，不可以改变任何数据，只直接输出润色结果：
            xx、xx、xx等XX个地市增幅高于xx%；：xx、xx、xx等xx个地市增幅低于xx%
            
            示例：
            时间：2024-03;
            全市场中标:各地市的整体全市场中标为-15.5%;巴中市,甘孜藏族自治州,资阳市,广安市,宜宾市,绵阳市,达州市共7个地市的全市场中标正增长;攀枝花市,内江市,凉山彝族自治州,泸州市,眉山市,广元市,德阳市,南充市,乐山市,阿坝藏族羌族自治州,自贡市,成都市,雅安市,遂宁市共14个地市的全市场中标负增长
            输出：
            巴中市、甘孜藏族自治州、资阳市、广安市、宜宾市、绵阳市、达州市等7个地市增幅高于0%；攀枝花市、内江市、凉山彝族自治州、泸州市、眉山市、广元市、德阳市、南充市、乐山市、阿坝藏族羌族自治州、自贡市、成都市、雅安市、遂宁市等14个地市增幅低于0%
            
            请直接输出结果，不要在前面加任何多余的文字
        """,
            template_id=2,
            text_op_id=template2_index,
            input_data_op_ids=[391],
            type="润色",
        ),
        PPTTextOp(
            name="分地市中标份额",
            prompt="""
        请严格按照以下格式，将待润色的内容中的数字填入，不要漏掉任何省份，如没有则不填，不可以改变任何数据，只直接输出润色结果：
        xx、xx、xx地市份额高于xx%；xx、xx、xx份额低于xx%
        xx、xx、xx等地市份额较去年同期提升； xx、xx、xx等地市份额较去年同期下降
        
        示例：
        时间：2024-03;
        分地市中标份额:内江市,凉山彝族自治州,南充市,宜宾市,广安市,泸州市,甘孜藏族自治州,眉山市,绵阳市,资阳市共10个地市的分地市中标份额高于4.2%;乐山市,巴中市,广元市,德阳市,成都市,攀枝花市,自贡市,达州市,遂宁市,阿坝藏族羌族自治州,雅安市共11个地市的分地市中标份额低于4.2%;
        分地市中标份额变化:各地市的整体分地市中标份额变化为-0.7pp;宜宾市,巴中市,广安市,甘孜藏族自治州,资阳市,达州市共6个地市的分地市中标份额变化正增长;乐山市,内江市,凉山彝族自治州,南充市,广元市,德阳市,成都市,攀枝花市,泸州市,眉山市,绵阳市,自贡市,遂宁市,阿坝藏族羌族自治州,雅安市共15个地市的分地市中标份额变化负增长
        输出：
        内江市、凉山彝族自治州、南充市、宜宾市、广安市、泸州市、甘孜藏族自治州、眉山市、绵阳市、资阳市等10个地市份额高于4.2%；乐山市、巴中市、广元市、德阳市、成都市、攀枝花市、自贡市、达州市、遂宁市、阿坝藏族羌族自治州、雅安市等11个地市份额低于4.2%
        宜宾市、巴中市、广安市、甘孜藏族自治州、资阳市、达州市等地市份额较去年同期提升；乐山市、内江市、凉山彝族自治州、南充市、广元市、德阳市、成都市、攀枝花市、泸州市、眉山市、绵阳市、自贡市、遂宁市、阿坝藏族羌族自治州、雅安市等地市份额较去年同期下降
        请直接输出结果，不要在前面加任何多余的文字
    """,
            template_id=2,
            text_op_id=template2_index + 1,
            input_data_op_ids=[393, 394],
            type="润色",
        ),
        PPTTextOp(
            name="分地市中标金额",
            prompt="""
                请严格按照以下格式，将待润色的内容中的数字填入，不要漏掉任何省份，如没有则不填，不可以改变任何数据，只直接输出润色结果：
                xx、xx、xx等XX个地市增幅高于xx%；：xx、xx、xx等xx个地市增幅低于xx%
                示例：
                时间：2024-03;
                泛政务类全市场中标:各地市的整体泛政务类全市场中标为-5.3%;广安市,甘孜藏族自治州,乐山市,宜宾市,德阳市,达州市,绵阳市共7个地市的泛政务类全市场中标正增长;资阳市,攀枝花市,泸州市,眉山市,成都市,遂宁市,凉山彝族自治州,内江市,广元市,雅安市,自贡市,阿坝藏族羌族自治州,南充市共13个地市的泛政务类全市场中标负增长
                输出：
                广安市、甘孜藏族自治州、乐山市、宜宾市、德阳市、达州市、绵阳市等7个地市增幅高于0%；资阳市、攀枝花市、泸州市、眉山市、成都市、遂宁市、凉山彝族自治州、内江市、广元市、雅安市、自贡市、阿坝藏族羌族自治州、南充市等13个地市增幅低于0%

                请直接输出结果，不要在前面加任何多余的文字
            """,
            template_id=2,
            text_op_id=template2_index + 2,
            input_data_op_ids=[396],
            type="润色",
        ),
        PPTTextOp(
            name="分地市中标份额",
            prompt="""
            请严格按照以下格式，将待润色的内容中的数字填入，不要漏掉任何省份，如没有则不填，不可以改变任何数据，只直接输出润色结果：
            xx、xx、xx地市份额高于xx%；xx、xx、xx份额低于xx%
            xx、xx、xx等地市份额较去年同期提升； xx、xx、xx等地市份额较去年同期下降
            示例：
            时间：2024-03;
            泛政务分地市中标份额:乐山市,内江市,凉山彝族自治州,宜宾市,巴中市,广安市,泸州市,甘孜藏族自治州,眉山市共9个地市的泛政务分地市中标份额高于4.2%;南充市,广元市,德阳市,成都市,攀枝花市,绵阳市,自贡市,资阳市,达州市,遂宁市,阿坝藏族羌族自治州,雅安市共12个地市的泛政务分地市中标份额低于4.2%;
            泛政务分地市中标份额变化:各地市的整体泛政务分地市中标份额变化为0.0pp;乐山市,宜宾市,巴中市,广安市,德阳市,甘孜藏族自治州,达州市共7个地市的泛政务分地市中标份额变化大于等于0.0pp;内江市,凉山彝族自治州,南充市,广元市,成都市,攀枝花市,泸州市,眉山市,绵阳市,自贡市,资阳市,遂宁市,阿坝藏族羌族自治州,雅安市共14个地市的泛政务分地市中标份额变化负增长
            输出：
            乐山市、内江市、凉山彝族自治州、宜宾市、巴中市、广安市、泸州市、甘孜藏族自治州、眉山市等9个地市份额高于4.2%；南充市、广元市、德阳市、成都市、攀枝花市、绵阳市、自贡市、资阳市、达州市、遂宁市、阿坝藏族羌族自治州、雅安市等12个地市份额低于4.2%
            乐山市、宜宾市、巴中市、广安市、德阳市、甘孜藏族自治州、达州市等地市份额较去年同期提升；内江市、凉山彝族自治州、南充市、广元市、成都市、攀枝花市、泸州市、眉山市、绵阳市、自贡市、资阳市、遂宁市、阿坝藏族羌族自治州、雅安市等地市份额较去年同期下降
            请直接输出结果，不要在前面加任何多余的文字
        """,
            template_id=2,
            text_op_id=template2_index + 3,
            input_data_op_ids=[398, 399],
            type="润色",
        ),
        PPTTextOp(
            name="分地市中标金额",
            prompt="""
            请严格按照以下格式，将待润色的内容中的数字填入，不要漏掉任何省份，如没有则不填，不可以改变任何数据，只直接输出润色结果：
            xx、xx、xx等XX个地市增幅高于xx%；：xx、xx、xx等xx个地市增幅低于xx%
            示例：
            时间：2024-03;
            社会民生类全市场中标:各地市的整体社会民生类全市场中标为-20.7%;宜宾市,甘孜藏族自治州,资阳市,自贡市,绵阳市,攀枝花市,阿坝藏族羌族自治州,凉山彝族自治州共8个地市的社会民生类全市场中标正增长;达州市,眉山市,南充市,广元市,巴中市,内江市,广安市,德阳市,泸州市,雅安市,乐山市,遂宁市,成都市共13个地市的社会民生类全市场中标负增长
            输出：
            宜宾市、甘孜藏族自治州、资阳市、自贡市、绵阳市、攀枝花市、阿坝藏族羌族自治州、凉山彝族自治州等8个地市增幅高于0%；达州市、眉山市、南充市、广元市、巴中市、内江市、广安市、德阳市、泸州市、雅安市、乐山市、遂宁市、成都市等13个地市增幅低于0%

            请直接输出结果，不要在前面加任何多余的文字
        """,
            template_id=2,
            text_op_id=template2_index + 4,
            input_data_op_ids=[401],
            type="润色",
        ),
        PPTTextOp(
            name="分地市中标份额",
            prompt="""
        请严格按照以下格式，将待润色的内容中的数字填入，不要漏掉任何省份，如没有则不填，不可以改变任何数据，只直接输出润色结果：
        xx、xx、xx地市份额高于xx%；xx、xx、xx份额低于xx%
        xx、xx、xx等地市份额较去年同期提升； xx、xx、xx等地市份额较去年同期下降
        示例：
        时间：2024-03;
        社会民生分地市中标份额:内江市,凉山彝族自治州,南充市,宜宾市,广安市,眉山市,绵阳市,自贡市,资阳市,达州市,阿坝藏族羌族自治州共11个地市的社会民生分地市中标份额高于4.5%;乐山市,巴中市,广元市,德阳市,成都市,攀枝花市,泸州市,甘孜藏族自治州,遂宁市,雅安市共10个地市的社会民生分地市中标份额低于4.5%;
        社会民生分地市中标份额变化:各地市的整体社会民生分地市中标份额变化为-1.2pp;宜宾市,甘孜藏族自治州,眉山市,绵阳市,自贡市,资阳市,阿坝藏族羌族自治州共7个地市的社会民生分地市中标份额变化正增长;乐山市,内江市,凉山彝族自治州,南充市,巴中市,广元市,广安市,德阳市,成都市,攀枝花市,泸州市,达州市,遂宁市,雅安市共14个地市的社会民生分地市中标份额变化负增长
        输出：
        内江市、凉山彝族自治州、南充市、宜宾市、广安市、眉山市、绵阳市、自贡市、资阳市、达州市、阿坝藏族羌族自治州份额高于4.5%；乐山市、巴中市、广元市、德阳市、成都市、攀枝花市、泸州市、甘孜藏族自治州、遂宁市、雅安市份额低于4.5%
        宜宾市、甘孜藏族自治州、眉山市、绵阳市、自贡市、资阳市、阿坝藏族羌族自治州等地市份额较去年同期提升；乐山市、内江市、凉山彝族自治州、南充市、巴中市、广元市、广安市、德阳市、成都市、攀枝花市、泸州市、达州市、遂宁市、雅安等地市份额较去年同期下降

        请直接输出结果，不要在前面加任何多余的文字
    """,
            template_id=2,
            text_op_id=template2_index + 5,
            input_data_op_ids=[403, 404],
            type="润色",
        ),
        PPTTextOp(
            name="分地市中标金额",
            prompt="""
            请严格按照以下格式，将待润色的内容中的数字填入，不要漏掉任何省份，如没有则不填，不可以改变任何数据，只直接输出润色结果：
            xx、xx、xx等XX个地市增幅高于xx%；：xx、xx、xx等xx个地市增幅低于xx%
            示例：
            时间：2024-03;
            新型工业化类全市场中标:各地市的整体新型工业化类全市场中标为-18.7%;广元市,内江市,资阳市,泸州市,南充市,广安市,凉山彝族自治州,达州市共8个地市的新型工业化类全市场中标正增长;攀枝花市,宜宾市,德阳市,眉山市,乐山市,绵阳市,甘孜藏族自治州,雅安市,成都市,自贡市,遂宁市,阿坝藏族羌族自治州共12个地市的新型工业化类全市场中标负增长
            输出：
            广元市、内江市、资阳市、泸州市、南充市、广安市、凉山彝族自治州、达州市等8个地市增幅高于0%；攀枝花市、宜宾市、德阳市、眉山市、乐山市、绵阳市、甘孜藏族自治州、雅安市、成都市、自贡市、遂宁市、阿坝藏族羌族自治州等12个地市增幅低于0%

            请直接输出结果，不要在前面加任何多余的文字
        """,
            template_id=2,
            text_op_id=template2_index + 6,
            input_data_op_ids=[406],
            type="润色",
        ),
        PPTTextOp(
            name="分地市中标份额",
            prompt="""
        请严格按照以下格式，将待润色的内容中的数字填入，不要漏掉任何省份，如没有则不填，不可以改变任何数据，只直接输出润色结果：
        xx、xx、xx地市份额高于xx%；xx、xx、xx份额低于xx%
        xx、xx、xx等地市份额较去年同期提升； xx、xx、xx等地市份额较去年同期下降
        示例：
        时间：2024-03;
        新型工业化分地市中标份额:内江市,凉山彝族自治州,南充市,宜宾市,广元市,泸州市,资阳市共7个地市的新型工业化分地市中标份额高于3.6%;乐山市,巴中市,广安市,德阳市,成都市,攀枝花市,甘孜藏族自治州,眉山市,绵阳市,自贡市,达州市,遂宁市,阿坝藏族羌族自治州,雅安市共14个地市的新型工业化分地市中标份额低于3.6%;
        新型工业化分地市中标份额变化:各地市的整体新型工业化分地市中标份额变化为-0.8pp;内江市,凉山彝族自治州,南充市,巴中市,广元市,广安市,泸州市,资阳市,达州市共9个地市的新型工业化分地市中标份额变化正增长;乐山市,宜宾市,德阳市,成都市,攀枝花市,甘孜藏族自治州,眉山市,绵阳市,自贡市,遂宁市,阿坝藏族羌族自治州,雅安市共12个地市的新型工业化分地市中标份额变化负增长
        输出：
        内江市、凉山彝族自治州、南充市、宜宾市、广元市、泸州市、资阳市地市份额高于3.6%；乐山市、巴中市、广安市、德阳市、成都市、攀枝花市、甘孜藏族自治州、眉山市、绵阳市、自贡市、达州市、遂宁市、阿坝藏族羌族自治州、雅安市地市份额低于3.6%
        内江市、凉山彝族自治州、南充市、巴中市、广元市、广安市、泸州市、资阳市、达州市等地市份额较去年同期提升；乐山市、宜宾市、德阳市、成都市、攀枝花市、甘孜藏族自治州、眉山市、绵阳市、自贡市、遂宁市、阿坝藏族羌族自治州、雅安市等地市份额较去年同期下降
        请直接输出结果，不要在前面加任何多余的文字
    """,
            template_id=2,
            text_op_id=template2_index + 7,
            input_data_op_ids=[408, 409],
            type="润色",
        ),
    ]

    department_map_template2 = OrderedDict(
        {
            "政务行业拓展部": "政务",
            "金融行业事业部": "金融",
            "商业客户拓展部": "商客",
            "交通物流行业事业部": "交通物流",
            "教育行业事业部": "教育",
            "要客服务事业部": "要客",
            "农业农村行业事业部": "农业农村",
            "卫健行业事业部": "卫健",
            "应急行业事业部": "应急",
            "政法公安行业事业部": "政法公安",
            "文宣行业事业部": "文宣",
            "住建行业事业部": "住建",
            "互联网行业事业部一部": "互联网一部",
            "互联网行业事业部二部": "互联网二部",
            "工业行业事业部": "工业",
            "能源化工团队": "能源化工",
            "车联网创新团队": "车联网",
        }
    )

    def generate_one_slide_text_op_template2(data_op_id, text_op_id):
        text_op_list = [
            PPTTextOp(
                name="分地市中标金额",
                prompt="""
                        请严格按照以下格式，将待润色的内容中的数字填入，不要漏掉任何省份，如没有则不填，不可以改变任何数据，只直接输出润色结果：
                        1-xx月全市场中标XX亿，同比xx%；电信中标XX亿，同比增长XX%，较去年同期+/-XXpp
                        xx、xx、xx等地市增幅高于xx%；：xx、xx、xx等地市增幅低于xx%
                        示例：
                        时间：2024-03;
                        全市场中标为181.9 亿;
                        全市场中标同比增长18.2%;
                        电信中标为8.7 亿;
                        电信中标金额同比增长8.3%;
                        电信中标金额同比变化下降0.6pp;
                        电信分地市中标金额同比:各地市的整体电信分地市中标金额同比为8.3%;广安市,泸州市共2个地市的电信分地市中标金额同比大于等于8.3%;内江市,凉山彝族自治州,自贡市,成都市,德阳市,眉山市,绵阳市,阿坝藏族羌族自治州,雅安市共9个地市的电信分地市中标金额同比负增长
                        输出：
                        1-3月全市场中标181.9亿，同比18.2%；电信中标8.7亿，同比增长8.3%，较去年同期-0.6pp
                        广安市、泸州等地市增幅高于8.3%；内江市、凉山彝族自治州、自贡市、成都市、德阳市、眉山市、绵阳市、阿坝藏族羌族自治州、雅安等地市增幅低于8.3%
                        请直接输出结果，不要在前面加任何多余的文字
                    """,
                template_id=2,
                text_op_id=text_op_id,
                input_data_op_ids=[
                    data_op_id,
                    data_op_id + 1,
                    data_op_id + 2,
                    data_op_id + 3,
                    data_op_id + 4,
                    data_op_id + 5,
                ],
                type="润色",
            ),
            PPTTextOp(
                name="分地市中标份额",
                prompt="""
                    请严格按照以下格式，将待润色的内容中的数字填入，不要漏掉任何省份，如没有则不填，不可以改变任何数据，只直接输出润色结果，不输出时间：
                    xx、xx、xx地市份额高于xx%；xx、xx、xx份额低于xx%
                    xx、xx、xx等地市份额较去年同期提升； xx、xx、xx等地市份额较去年同期下降
                    示例：
                    时间：2024-03;
                    分地市中标份额:内江市,凉山彝族自治州,宜宾市,广安市,攀枝花市,泸州市共6个地市的分地市中标份额高于4.8%;乐山市,南充市,巴中市,广元市,德阳市,成都市,甘孜藏族自治州,眉山市,绵阳市,自贡市,资阳市,达州市,遂宁市,阿坝藏族羌族自治州,雅安市共15个地市的分地市中标份额低于4.8%;
                    分地市中标份额变化:各地市的整体分地市中标份额变化为-0.4pp;乐山市,南充市,宜宾市,巴中市,广元市,广安市,攀枝花市,泸州市,甘孜藏族自治州,资阳市,达州市,遂宁市共12个地市的分地市中标份额变化正增长;内江市,凉山彝族自治州,德阳市,成都市,眉山市,绵阳市,自贡市,阿坝藏族羌族自治州,雅安市共9个地市的分地市中标份额变化负增长
                    输出：
                    内江市、凉山彝族自治州、宜宾市、广安市、攀枝花市、泸州市地市份额高于4.8%；乐山市、南充市、巴中市、广元市、德阳市、成都市、甘孜藏族自治州、眉山市、绵阳市、自贡市、资阳市、达州市、遂宁市、阿坝藏族羌族自治州、雅安市地市份额低于4.8%
                    乐山市、南充市、宜宾市、巴中市、广元市、广安市、攀枝花市、泸州市、甘孜藏族自治州、资阳市、达州市、遂宁市等地市份额较去年同期提升；内江市、凉山彝族自治州、德阳市、成都市、眉山市、绵阳市、自贡市、阿坝藏族羌族自治州、雅安市等地市份额较去年同期下降
                """,
                template_id=2,
                text_op_id=text_op_id + 1,
                input_data_op_ids=[data_op_id + 7, data_op_id + 8],
                type="润色",
            ),
        ]
        ppt_text_op_list_template2.extend(text_op_list)

    data_op_id_template2 = 411
    text_op_id_template2 = template2_index + 8  # 53
    for k, v in department_map_template2.items():
        generate_one_slide_text_op_template2(data_op_id_template2, text_op_id_template2)
        data_op_id_template2 += 10
        text_op_id_template2 += 2

    save_and_create_ppt_text_op(ppt_text_op_list_template2)
