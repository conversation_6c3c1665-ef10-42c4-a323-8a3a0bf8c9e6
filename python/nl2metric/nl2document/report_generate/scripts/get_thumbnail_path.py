import os.path

import pdfplumber

from common.fs.storage import create_s3_client


def get_thumbnail_path(pdf_path: str) -> str:
    pdf = pdfplumber.open(pdf_path)
    img = pdf.pages[0].to_image().original.resize((200, 200))
    img_path = f"{pdf_path}.png"
    base_name = os.path.basename(img_path)
    img.save(img_path)
    remote_path = f"ppt_generate/template/template_1.png"
    create_s3_client().upload_file(
        img_path,
        "ask-doc",
        remote_path,
    )
    return remote_path


if __name__ == "__main__":
    print(get_thumbnail_path("/Users/<USER>/Downloads/template_group.pdf"))
