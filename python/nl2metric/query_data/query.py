import json
from typing import Dict, List, Optional
from urllib.parse import quote, urljoin

import requests
from pydantic import BaseModel

from common.utils.string_utils import class_to_dict
from config.app_config import ASK_BI_HOST
from nl2time_query.nl2time_query import TimeQueryParams


def metric_to_sql_data(
    metrics: List[str],
    where: str,
    time_query: Optional[TimeQueryParams],
    group_bys: List[str],
    order_bys: List[str],
    model_id: str,
):
    url = urljoin(ASK_BI_HOST, "api/metrics/metric2sql2data")
    params = {
        "metricNames": metrics,
        "where": where,
        "modelId": model_id,
        "timeQueryParams": json.dumps(class_to_dict(time_query)),
        "groupBys": group_bys,
        "orderBys": order_bys,
    }
    response = requests.get(url, params=params)
    response.raise_for_status()
    json_response = response.json()
    rows_metadata = json_response["data"]["rowsMetadata"]
    rows = json_response["data"]["rows"]
    name_to_labels = {}
    for m in rows_metadata:
        name_to_labels[m["value"]["name"]] = m["value"]["label"]
    ret = []
    for row in rows:
        row_data = {}
        for key, value in row.items():
            row_data[name_to_labels[key]] = value
        ret.append(row_data)
    return ret


def format_query_data(rows: List[Dict]) -> str:
    if not rows:
        return ""
    if len(rows) == 1 and len(rows[0]) == 1:
        return str(list(rows[0].values())[0])
    if len(rows) == 1:
        return json.dumps(rows[0], indent=2, ensure_ascii=False)
    return json.dumps(rows[0], indent=2, ensure_ascii=False)
