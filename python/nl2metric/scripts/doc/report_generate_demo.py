import json
import re
from uuid import uuid4


class ReportGenerator:
    """
    根据JSON模板和数据生成Markdown报告的服务。
    """

    def __init__(self, template, data_context):
        """
        初始化报告生成器。

        Args:
            template (dict): 报告模板的JSON对象。
            data_context (dict): 用于填充模板的上下文数据。
        """
        self.template = template
        self.data_context = data_context
        self.report_lines = []
        # 模拟系统方法注册表
        self.system_operations = {
            "fetchKpiDataForQuarter": self._mock_fetch_kpi_data,
            "getTeamQuarterlyMetrics": self._mock_get_team_metrics,
            "getMarketTrendsAndCompetitorAnalysis": self._mock_get_market_analysis,
        }

    def _get_value_from_path(self, path_str, context):
        """
        从上下文中根据点分路径获取实际值。
        path_str: e.g., "reportData.teams"
        """
        current_value = context
        try:
            for key in path_str.split("."):
                if isinstance(current_value, dict):
                    current_value = current_value.get(key)
                elif isinstance(current_value, list) and key.isdigit():
                    current_value = current_value[int(key)]
                else:
                    # 尝试将key作为属性访问（针对简单对象）
                    current_value = getattr(current_value, key, None)

                if current_value is None:
                    # print(f"[警告] 路径解析中 '{key}' 在 '{path_str}' 未找到或值为None。")
                    return None  # Path resolution failed or led to None
        except (KeyError, IndexError, AttributeError, TypeError) as e:
            # print(f"[警告] 解析路径 '{path_str}' 时出错: {e}")
            return None
        return current_value

    def _resolve_variables(self, template_string, context):
        """
        解析字符串中的变量占位符 {{variable.path.to.value}}。
        """
        if not isinstance(template_string, str):
            return template_string  # 如果不是字符串，直接返回

        def replace_match(match):
            path = match.group(1).strip()
            # Use the new helper to get the actual value
            resolved_value = self._get_value_from_path(path, context)

            if resolved_value is None:
                # print(f"[警告] 变量 '{path}' 在上下文中未找到或路径中某部分为None。")
                return f"{{{{Undefined: {path}}}}}"
            return str(
                resolved_value
            )  # Always convert to string for template substitution

        return re.sub(r"\{\{(.*?)\}\}", replace_match, template_string)

    def _evaluate_condition(self, condition_string, context):
        """
        评估触发条件。这是一个非常简化的实现。
        实际应用中可能需要更复杂的表达式求值器。
        例如: "{{data.showSectionA}} === true"
        """
        if not condition_string:
            return True

        resolved_condition = self._resolve_variables(condition_string, context)
        # 简单实现：假设条件是 "value === expected" 或 "value" (隐式为true)
        try:
            py_condition = resolved_condition.replace("===", "==").replace("!==", "!=")

            if py_condition.lower() == "true":
                return True
            if py_condition.lower() == "false":
                return False  # Python False for string "false"

            # This simplified comparison logic is kept as is,
            # as the NameError is unlikely to originate from here with current usage.
            # A robust solution would use a safe expression evaluation library.
            if (
                "==" in py_condition
                or "!=" in py_condition
                or ">" in py_condition
                or "<" in py_condition
            ):
                # Simplified: assume if it's a comparison, it's true for demo purposes
                # print(f"Evaluating condition (simplified): {py_condition} -> True")
                return True
            return bool(
                resolved_condition
            )  # Fallback: truthiness of the resolved string
        except Exception as e:
            # print(f"[警告] 评估条件 '{resolved_condition}' 失败: {e}")
            return False

    def _mock_fetch_kpi_data(self, params, context):
        # print(f"[模拟数据操作] fetchKpiDataForQuarter 调用参数: {params}")
        quarter = params.get("quarter", "未知季度")
        return {
            "summary": f"{quarter} KPI数据总结：整体表现良好，营收增长20%。",
            "details": {
                "revenue": 120000,
                "profitMargin": 0.25,
                "customerAcquisition": 500,
            },
            "comparisonTarget": f"上一{quarter}",
        }

    def _mock_get_team_metrics(self, params, context):
        team_id = params.get("teamId", "未知团队ID")
        quarter = params.get("quarter", "未知季度")
        # print(f"[模拟数据操作] getTeamQuarterlyMetrics 调用参数: teamId={team_id}, quarter={quarter}")
        return {
            "metricsSummary": f"{team_id} 在 {quarter} 完成了 {params.get('metrics', [])} 等指标。",
            "dataSource": "内部统计系统",
            "details": {
                "completedTasks": 150,
                "bugsFixed": 30,
                "clientSatisfaction": 4.5,
            },
        }

    def _mock_get_market_analysis(self, params, context):
        category = params.get("productCategory", "通用类别")
        # print(f"[模拟数据操作] getMarketTrendsAndCompetitorAnalysis 调用参数: productCategory={category}")
        return {
            "trends": f"{category}市场的主要趋势是智能化和个性化。",
            "competitors": f"主要竞争对手包括X公司和Y公司，它们最近发布了新产品Z。",
        }

    def _execute_data_operation(self, operation_id, params_template, context):
        """
        执行数据操作。
        """
        if operation_id not in self.system_operations:
            # print(f"[警告] 未找到数据操作ID: {operation_id}")
            return {"error": f"Operation ID '{operation_id}' not found."}

        operation_func = self.system_operations[operation_id]

        resolved_params = {}
        if isinstance(params_template, dict):
            for key, value_template in params_template.items():
                # Resolve variables within parameter values if they are strings
                if isinstance(value_template, str):
                    resolved_params[key] = self._resolve_variables(
                        value_template, context
                    )
                else:
                    resolved_params[
                        key
                    ] = value_template  # Keep non-string params as is
        elif isinstance(params_template, str):
            try:
                resolved_params_str = self._resolve_variables(params_template, context)
                resolved_params = json.loads(resolved_params_str)
            except json.JSONDecodeError as e:
                # print(f"[警告] 解析数据操作参数JSON模板失败 ('{params_template}'): {e}")
                return {
                    "error": f"Failed to parse params template for '{operation_id}'."
                }
            except Exception as e:
                # print(f"[警告] 解析数据操作参数模板时发生未知错误 ('{params_template}'): {e}")
                return {
                    "error": f"Unknown error parsing params template for '{operation_id}'."
                }

        try:
            return operation_func(resolved_params, context)
        except Exception as e:
            # print(f"[错误] 执行数据操作 '{operation_id}' 失败: {e}")
            return {"error": f"Error executing operation '{operation_id}': {str(e)}"}

    def _process_paragraph(self, paragraph_config, context, current_level_num=1):
        """
        递归处理单个段落及其子段落。
        """
        trigger_condition = paragraph_config.get("triggerCondition")
        if not self._evaluate_condition(trigger_condition, context):
            return

        title_template = paragraph_config.get("paragraphTitle", "")
        title = self._resolve_variables(title_template, context)
        if title:
            self.report_lines.append(f"{'#' * current_level_num} {title}")

        content_type = paragraph_config.get("contentType")

        if content_type == "fixedContent":
            fixed_config = paragraph_config.get("fixedContent", {})
            rich_text_template = fixed_config.get("richText", "")
            rich_text = self._resolve_variables(rich_text_template, context)
            if rich_text:
                self.report_lines.append(rich_text)
                self.report_lines.append("")

        elif content_type == "aiGenerated":
            ai_config = paragraph_config.get("aiConfig", {})
            operation_result = {}

            if "dataOperationId" in ai_config:
                op_id = ai_config["dataOperationId"]
                op_params_template = ai_config.get(
                    "dataOperationParams", ai_config.get("dataOperationParamsTemplate")
                )
                operation_result = self._execute_data_operation(
                    op_id, op_params_template, context
                )

            ai_context = {**context, "operationResult": operation_result}
            requirements_template = ai_config.get("requirements", "")
            requirements = self._resolve_variables(requirements_template, ai_context)

            generated_text = f"[模拟AI生成内容]\n**要求**:\n```\n{requirements}\n```"
            if ai_config.get("referenceExemplar"):
                ref_exemplar = self._resolve_variables(
                    ai_config["referenceExemplar"], ai_context
                )
                generated_text += f"\n**参考范文片段**:\n```\n{ref_exemplar[:100]}...\n```"

            self.report_lines.append(generated_text)
            self.report_lines.append("")

        iteration_settings = paragraph_config.get("iterationSettings")
        if iteration_settings:
            source_list_var_template = iteration_settings.get(
                "sourceListVariable"
            )  # e.g., "{{reportData.teams}}"
            item_alias = iteration_settings.get("itemAlias")
            child_template_config = iteration_settings.get("childParagraphTemplate")

            if source_list_var_template and item_alias and child_template_config:
                # Directly resolve the path to get the list object, avoid eval
                path_str = source_list_var_template.strip(
                    "{}"
                ).strip()  # "reportData.teams"
                list_data = self._get_value_from_path(path_str, context)

                if not isinstance(list_data, list):
                    # print(f"[警告] 迭代源 '{source_list_var_template}' ('{path_str}') 解析结果不是列表: {type(list_data)}. 使用空列表替代。")
                    list_data = []

                for index, item_data in enumerate(list_data):
                    item_context = {
                        **context,
                        item_alias: item_data,
                        "iterator": {"index": index},
                    }

                    current_child_config = json.loads(json.dumps(child_template_config))

                    current_child_config["paragraphId"] = self._resolve_variables(
                        current_child_config.get("paragraphIdPrefix", "child_")
                        + str(index),
                        item_context,
                    )
                    current_child_config["paragraphTitle"] = self._resolve_variables(
                        current_child_config.get("paragraphTitleTemplate", ""),
                        item_context,
                    )

                    if (
                        current_child_config.get("contentType") == "aiGenerated"
                        and "aiConfig" in current_child_config
                    ):
                        ai_conf = current_child_config["aiConfig"]
                        if "requirementsTemplate" in ai_conf:
                            ai_conf["requirements"] = self._resolve_variables(
                                ai_conf["requirementsTemplate"], item_context
                            )
                        if "referenceExemplarTemplate" in ai_conf:
                            ai_conf["referenceExemplar"] = self._resolve_variables(
                                ai_conf["referenceExemplarTemplate"], item_context
                            )
                        # Note: dataOperationParamsTemplate within child's aiConfig will be handled by _execute_data_operation

                    elif (
                        current_child_config.get("contentType") == "fixedContent"
                        and "fixedContent" in current_child_config
                    ):
                        fc_conf = current_child_config["fixedContent"]
                        if "richTextTemplate" in fc_conf:
                            fc_conf["richText"] = self._resolve_variables(
                                fc_conf["richTextTemplate"], item_context
                            )

                    self._process_paragraph(
                        current_child_config, item_context, current_level_num + 1
                    )

        static_children = paragraph_config.get("children", [])
        for child_config in static_children:
            self._process_paragraph(child_config, context, current_level_num + 1)

    def generate_report(self):
        """
        生成完整的Markdown报告。
        """
        self.report_lines = []
        if "templateInfo" in self.template and "title" in self.template["templateInfo"]:
            report_title = self._resolve_variables(
                self.template["templateInfo"]["title"], self.data_context
            )
            self.report_lines.append(f"# {report_title}")
            self.report_lines.append(
                f"_{self._resolve_variables(self.template['templateInfo'].get('description',''), self.data_context)}_"
            )
            self.report_lines.append("---")

        for paragraph_config in self.template.get("templateContent", []):
            self._process_paragraph(
                paragraph_config, self.data_context, paragraph_config.get("level", 1)
            )

        return "\n".join(self.report_lines)


# --- 示例模板和数据 ---
EXAMPLE_TEMPLATE = {
    "templateId": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
    "templateVersion": "1.3.0",
    "createdAt": "2025-05-22T10:00:00Z",
    "updatedAt": "2025-05-22T11:55:00Z",
    "templateInfo": {
        "title": "季度业务回顾与展望报告模板 (含数据操作)",
        "name": "QuarterlyBusinessReview_Q2_2025_v3_dataOps",
        "templateType": "Custom",
        "description": "用于生成季度业务回顾报告，通过数据操作ID调用系统方法获取动态数据辅助AI生成。",
        "fieldScopeMax": 10000,
        "textStyle": "正式商务",
        "introduction": "此模板旨在标准化季度回顾报告的结构和内容，确保信息全面且一致。",
    },
    "templateContent": [
        {
            "paragraphId": "sec_introduction",
            "level": 1,
            "paragraphTitle": "1. 引言 ({{reportContext.quarter}})",
            "contentType": "fixedContent",
            "fixedContent": {
                "richText": "<h2>1. 引言</h2><p>本报告旨在对 {{reportContext.quarter}} 的业务表现进行全面回顾，总结成就，分析挑战，并对下一季度的工作重点进行规划。感谢 {{reportContext.authorName}} 的辛勤整理。</p>"
            },
            "children": [
                {
                    "paragraphId": "subsec_intro_purpose",
                    "level": 2,
                    "paragraphTitle": "1.1 报告目的与范围",
                    "contentType": "fixedContent",
                    "fixedContent": {"richText": "<p>详细阐述本季度回顾的目的、覆盖的业务范围和关键评估维度。</p>"},
                    "children": [],
                }
            ],
        },
        {
            "paragraphId": "sec_kpi_overview",
            "level": 1,
            "paragraphTitle": "2. 关键绩效指标 (KPI) 总览",
            "contentType": "aiGenerated",
            "aiConfig": {
                "dataOperationId": "fetchKpiDataForQuarter",
                "dataOperationParams": {
                    "quarter": "{{reportContext.quarter}}",
                    "region": "{{reportContext.regionId}}",
                    "metrics": ["revenue", "profitMargin", "customerAcquisition"],
                },
                "requirements": "基于以下由系统方法 'fetchKpiDataForQuarter' 获取的KPI数据：\n指标概览: {{operationResult.summary}}\n详细数据: {{operationResult.details}}\n\n请生成一段关于本季度关键绩效指标（KPI）完成情况的总结性概述。应包括总体达成率、与 {{operationResult.comparisonTarget}} 对比的显著增长的指标和未达预期的指标。语言风格要求客观、数据驱动。",
                "knowledgeBaseIds": ["kb_company_kpi_definitions"],
                "enableWebSearch": False,  # Corrected to Python False
            },
            "children": [],
        },
        {
            "paragraphId": "sec_team_performance_main",
            "level": 1,
            "paragraphTitle": "3. 团队与项目表现",
            "triggerCondition": "{{reportData.includeTeamPerformanceSection}} === true",
            "contentType": "fixedContent",
            "fixedContent": {
                "richText": "<h3>3. 团队与项目表现回顾</h3><p>本部分将逐一介绍各团队在本季度的主要贡献和项目进展。</p>"
            },
            "children": [
                {
                    "paragraphId": "subsec_team_iteration_host",
                    "level": 2,
                    "paragraphTitle": "3.1 各团队详细表现",
                    "contentType": "fixedContent",
                    "fixedContent": {"richText": "<p>以下为各团队的具体表现报告：</p>"},
                    "iterationSettings": {
                        "sourceListVariable": "{{reportData.teams}}",
                        "itemAlias": "team",
                        "childParagraphTemplate": {
                            "paragraphIdPrefix": "team_report_",
                            "paragraphTitleTemplate": "{{iterator.index + 1}}. {{team.name}} 团队报告",
                            "contentType": "aiGenerated",
                            "aiConfig": {
                                "dataOperationId": "getTeamQuarterlyMetrics",
                                "dataOperationParamsTemplate": '{ "teamId": "{{team.id}}", "quarter": "{{reportContext.quarter}}", "metrics": ["completedTasks", "bugsFixed", "clientSatisfaction"] }',
                                "requirementsTemplate": "团队 {{team.name}} (ID: {{team.id}}) 在 {{reportContext.quarter}} 的表现如下：\n系统获取指标: {{operationResult.metricsSummary}}\n\n请结合团队自述链接 {{team.selfReportLink}} 和上述系统指标，总结团队的主要成就、关键项目进展以及遇到的挑战。数据来源：{{operationResult.dataSource}}。",
                                "knowledgeBaseIds": [
                                    "kb_team_performance_metrics",
                                    "kb_project_updates",
                                ],
                            },
                        },
                    },
                    "children": [],
                },
                {
                    "paragraphId": "subsec_team_collaboration_summary",
                    "level": 2,
                    "paragraphTitle": "3.2 跨团队协作总结",
                    "contentType": "aiGenerated",
                    "aiConfig": {
                        "requirements": "基于各团队的报告和 {{reportData.collaborationToolStats.summary}} 数据，总结本季度跨团队协作的整体情况，包括成功案例和待改进领域。",
                        "knowledgeBaseIds": ["kb_collaboration_best_practices"],
                    },
                    "children": [],
                },
            ],
        },
        {
            "paragraphId": "sec_product_updates_main",
            "level": 1,
            "paragraphTitle": "4. 产品更新与迭代",
            "contentType": "fixedContent",
            "fixedContent": {"richText": "<h3>4. 产品更新与迭代概览</h3>"},
            "children": [
                {
                    "paragraphId": "subsec_prod_iteration_host",
                    "level": 2,
                    "paragraphTitle": "4.1 主要产品迭代详情",
                    "contentType": "fixedContent",
                    "fixedContent": {"richText": "<p>以下是本季度各主要产品的迭代详情：</p>"},
                    "iterationSettings": {
                        "sourceListVariable": "{{reportData.products}}",
                        "itemAlias": "product",
                        "childParagraphTemplate": {
                            "paragraphIdPrefix": "prod_update_",
                            "paragraphTitleTemplate": "{{product.name}} (版本 {{product.version}})",
                            "contentType": "fixedContent",
                            "fixedContent": {
                                "richTextTemplate": "<h4>{{product.name}}</h4><ul><li><strong>新功能:</strong> {{product.newFeatures}}</li><li><strong>优化改进:</strong> {{product.optimizations}}</li><li><strong>用户反馈亮点:</strong> {{product.feedbackHighlights}}</li></ul>"
                            },
                        },
                    },
                    "children": [],
                },
                {
                    "paragraphId": "subsec_prod_roadmap",
                    "level": 2,
                    "paragraphTitle": "4.2 下季度产品路线图展望",
                    "contentType": "aiGenerated",
                    "aiConfig": {
                        "dataOperationId": "getMarketTrendsAndCompetitorAnalysis",
                        "dataOperationParams": {
                            "productCategory": "{{reportContext.mainProductCategory}}"
                        },
                        "requirements": "结合当前产品表现和以下市场分析结果：\n市场趋势：{{operationResult.trends}}\n竞品动态：{{operationResult.competitors}}\n\n为下季度的产品开发制定初步的路线图和优先级建议。",
                        "enableWebSearch": False,  # Corrected to Python False
                    },
                    "children": [],
                },
            ],
        },
        {
            "paragraphId": "sec_conclusion",
            "level": 1,
            "paragraphTitle": "5. 总结与展望",
            "contentType": "aiGenerated",
            "aiConfig": {
                "requirements": "基于以上所有章节内容，撰写一份全面的总结，并对下一季度提出3-5项关键行动建议和期望达成的目标。总结应强调战略一致性。",
                "relatedParagraphs": [
                    "sec_introduction",
                    "sec_kpi_overview",
                    "sec_team_performance_main",
                    "sec_product_updates_main",
                ],
            },
            "children": [],
        },
    ],
}

EXAMPLE_DATA_CONTEXT = {
    "reportContext": {
        "quarter": "2025年第二季度",
        "authorName": "AI报告助手",
        "regionId": "APAC",
        "mainProductCategory": "企业级SaaS",
        "marketAnalysisLink": "http://example.com/market_analysis_q2_2025",
    },
    "reportData": {
        "kpiMetrics": {
            "revenueGrowth": 0.15,
            "customerChurnRate": 0.02,
            "netPromoterScore": 55,
        },
        "includeTeamPerformanceSection": True,
        "teams": [
            {
                "id": "team_alpha",
                "name": "阿尔法",
                "achievementsSummary": "完成了X项目，超额完成KPI 10%",
                "projects": ["项目X (已完成)", "项目Y (进行中)"],
                "selfReportLink": "http://example.com/reports/alpha_q2",
            },
            {
                "id": "team_beta",
                "name": "贝塔",
                "achievementsSummary": "发布了新功能Z，用户反馈良好",
                "projects": ["功能Z (已上线)", "维护任务 (持续)"],
                "selfReportLink": "http://example.com/reports/beta_q2",
            },
        ],
        "collaborationToolStats": {"summary": "本季度跨团队消息总数10000条，共享文档500份。"},
        "products": [
            {
                "name": "核心产品A",
                "version": "2.5.0",
                "newFeatures": "智能推荐引擎、增强的安全性",
                "optimizations": "性能提升15%、UI/UX改进",
                "feedbackHighlights": "用户对新推荐引擎评价很高。",
            },
            {
                "name": "辅助工具B",
                "version": "1.2.1",
                "newFeatures": "增加了导出PDF功能",
                "optimizations": "修复了若干已知bug",
                "feedbackHighlights": "导出功能受到小型企业欢迎。",
            },
        ],
    },
}

if __name__ == "__main__":
    print("开始生成报告...\n")
    generator = ReportGenerator(EXAMPLE_TEMPLATE, EXAMPLE_DATA_CONTEXT)
    markdown_report = generator.generate_report()

    print("--- 生成的Markdown报告 ---")
    print(markdown_report)
    print("\n--- 报告生成完毕 ---")

    # 可以选择将报告写入文件
    # with open("generated_report.md", "w", encoding="utf-8") as f:
    #     f.write(markdown_report)
    # print("\n报告已保存到 generated_report.md")
