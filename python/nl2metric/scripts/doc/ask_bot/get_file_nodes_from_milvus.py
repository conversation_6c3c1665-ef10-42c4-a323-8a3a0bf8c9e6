import json
import os

from pymilvus import MilvusClient
from config import doc_config

uri = doc_config.milvus_uri
client = MilvusClient(uri)
save_path = "./milvus_nodes"

no_sentence_node = False


def create_dir_if_not_exist(path: str):
    if not os.path.exists(path):
        os.makedirs(path)


def save_file_nodes(file_id: str):
    res = client.query(
        collection_name=doc_config.milvus_collection_name,
        filter=f" file_id=='{file_id}'",
    )

    file_name = res[0]["file_name"]
    target_file = f"{save_path}/{file_name}_milvus_nodes.json"
    result_dict = []

    for item in res:
        node_content = json.loads(item["_node_content"])
        content = node_content["text"]
        node_id = node_content["id_"]
        temp = {
            "part_name": item["part_name"],
            "node_id": node_id,
            "content": content,
            "parent_node": item.get("parent_node", ""),
        }
        if no_sentence_node:
            if temp["parent_node"] != "":
                continue
        result_dict.append(temp)

    with open(target_file, "w") as f:
        json.dump(result_dict, f, indent=4, ensure_ascii=False)


if __name__ == "__main__":
    create_dir_if_not_exist(save_path)
    print("milvus_uri: ", uri)
    print("collection_name: ", doc_config.milvus_collection_name)
    print("ask_doc_index_excel_split: ", doc_config.ask_doc_index_excel_split)
    save_file_nodes("2118")
