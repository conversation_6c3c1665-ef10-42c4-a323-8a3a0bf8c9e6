import json
import argparse
import logging
from typing import List, Dict, Any, Optional

from pymilvus import (
    connections,
    utility,
    Collection,
    CollectionSchema,
    FieldSchema,
    DataType,
    MilvusException,
)

# --- Configuration ---
# Default Milvus connection parameters (can be overridden by CLI arguments)
DEFAULT_MILVUS_ALIAS = "default"
DEFAULT_MILVUS_HOST = "**************"
DEFAULT_MILVUS_PORT = "19530"

# Default file for export/import
DEFAULT_DATA_FILE = "export_data.jsonl"

# Batch size for reading/writing to Milvus
DEFAULT_BATCH_SIZE = 1000

# --- Logger Setup ---
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def convert_to_native_types(obj):
    """Convert float32 and other special types to Python native types."""
    if isinstance(obj, dict):
        return {key: convert_to_native_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_to_native_types(item) for item in obj]
    elif hasattr(obj, "dtype"):  # Handle float32 and other numeric types
        return float(obj)
    elif isinstance(obj, (int, float, str, bool, type(None))):
        return obj
    else:
        return str(obj)  # Convert any other types to string


# --- Milvus Connection Helper ---
def connect_to_milvus(
    alias: str,
    host: str,
    port: str,
    user: Optional[str] = None,
    password: Optional[str] = None,
):
    """Establishes a connection to the Milvus server."""
    try:
        logger.info(
            f"Attempting to connect to Milvus at {host}:{port} with alias '{alias}'"
        )
        if connections.has_connection(alias):
            logger.info(f"Connection with alias '{alias}' already exists. Reusing.")
            return True

        connection_params = {"host": host, "port": port}
        if user and password:
            connection_params["user"] = user
            connection_params["password"] = password
            logger.info("Connecting with user and password.")

        connections.connect(alias=alias, **connection_params)
        logger.info("Successfully connected to Milvus.")
        return True
    except Exception as e:
        logger.error(f"Failed to connect to Milvus: {e}")
        raise


# --- Core Export Functionality ---
def export_collection_to_file(
    alias: str,
    collection_name: str,
    output_file: str,
    batch_size: int = DEFAULT_BATCH_SIZE,
):
    """Exports all data from a Milvus collection to a JSON Lines file."""
    if not utility.has_collection(collection_name, using=alias):
        logger.error(f"Source collection '{collection_name}' does not exist.")
        return

    logger.info(
        f"Starting export from collection '{collection_name}' to '{output_file}'..."
    )
    collection = Collection(collection_name, using=alias)

    try:
        # Check if collection is loaded, if not, load it.
        # Some Milvus versions might require loading before certain operations.
        # For query_iterator, it might not be strictly necessary but is good practice.
        is_loaded = False
        try:
            # list_loaded_collections might not exist in older pymilvus, or behave differently.
            # A more direct way is to check collection.partitions, if any are loaded.
            # However, a simple load attempt is often sufficient.
            if collection.name not in utility.list_loaded_collections(
                using=alias
            ):  # PyMilvus 2.4+
                logger.info(f"Loading collection '{collection_name}'...")
                collection.load()
                logger.info(f"Collection '{collection_name}' loaded.")
            else:
                logger.info(
                    f"Collection '{collection_name}' is already loaded or load status check passed."
                )
            is_loaded = True
        except (
            AttributeError
        ):  # Handle older pymilvus versions not having list_loaded_collections
            logger.info(
                f"Attempting to load collection '{collection_name}' (older PyMilvus or direct check failed)."
            )
            collection.load()  # Attempt load
            logger.info(f"Collection '{collection_name}' load attempted.")
            is_loaded = True  # Assume loaded or continue
        except Exception as e:
            logger.warning(
                f"Could not ensure collection '{collection_name}' is loaded: {e}. Proceeding with query."
            )
    except Exception as e:
        logger.error(f"Error during collection loading: {e}")
        raise

    total_exported_count = 0
    primary_key_field = collection.schema.primary_field.name
    logger.info(f"Primary key field of source collection: '{primary_key_field}'")
    logger.info(f"Schema fields: {[f.name for f in collection.schema.fields]}")

    try:
        with open(output_file, "w", encoding="utf-8") as f:
            query_iter = collection.query_iterator(
                batch_size=batch_size, output_fields=["*"]  # Fetches all fields
            )

            while True:
                results_batch = query_iter.next()
                if not results_batch:
                    break

                for hit in results_batch:
                    hit = convert_to_native_types(
                        hit
                    )  # Convert types before JSON serialization
                    f.write(json.dumps(hit) + "\n")
                    total_exported_count += 1

                logger.info(f"Exported {total_exported_count} entities so far...")

            if hasattr(query_iter, "close"):
                query_iter.close()

        logger.info(
            f"Successfully exported {total_exported_count} entities from '{collection_name}' to '{output_file}'."
        )
    except Exception as e:
        logger.error(f"Error during export: {e}")
        raise


# --- Core Import Functionality ---
def import_data_to_collection(
    alias: str,
    input_file: str,
    target_collection_name: str,
    pk_field_name: str,
    vector_field_name: str,
    vector_dim: int,
    other_fields_schema_defs: Optional[List[Dict[str, Any]]] = None,
    auto_id_pk: bool = False,
    pk_dtype: str = "VARCHAR",
    pk_max_length: Optional[int] = 65535,
    metric_type: str = "L2",
    index_field_name: Optional[str] = None,
    index_type: str = "AUTOINDEX",
    index_params: Optional[Dict[str, Any]] = None,
    consistency_level: str = "Bounded",
    batch_size: int = DEFAULT_BATCH_SIZE,
    overwrite_collection: bool = False,
):
    """Imports data from a JSON Lines file into a Milvus collection."""
    if index_field_name is None:
        index_field_name = vector_field_name

    if utility.has_collection(target_collection_name, using=alias):
        logger.info(f"Target collection '{target_collection_name}' already exists.")
        if overwrite_collection:
            logger.warning(
                f"Overwriting collection '{target_collection_name}' as requested."
            )
            utility.drop_collection(target_collection_name, using=alias)
            logger.info(f"Collection '{target_collection_name}' dropped.")
        else:
            logger.warning(
                f"Using existing collection '{target_collection_name}'. Ensure schema compatibility."
            )

    if not utility.has_collection(target_collection_name, using=alias):
        logger.info(f"Creating new collection '{target_collection_name}'...")
        fields = []
        # Primary Key Field
        if pk_dtype.upper() == "VARCHAR":
            fields.append(
                FieldSchema(
                    name=pk_field_name,
                    dtype=DataType.VARCHAR,
                    is_primary=True,
                    auto_id=auto_id_pk,
                    max_length=pk_max_length,
                )
            )
        elif pk_dtype.upper() == "INT64":
            fields.append(
                FieldSchema(
                    name=pk_field_name,
                    dtype=DataType.INT64,
                    is_primary=True,
                    auto_id=auto_id_pk,
                )
            )
        else:
            raise ValueError(
                f"Unsupported pk_dtype: {pk_dtype}. Must be VARCHAR or INT64."
            )

        # Vector Field
        fields.append(
            FieldSchema(
                name=vector_field_name, dtype=DataType.FLOAT_VECTOR, dim=vector_dim
            )
        )

        # Other Scalar Fields
        if other_fields_schema_defs:
            for field_def in other_fields_schema_defs:
                dtype_str = field_def["dtype"].upper()
                try:
                    milvus_dtype = getattr(DataType, dtype_str)
                except AttributeError:
                    raise ValueError(
                        f"Unsupported DataType '{dtype_str}' for field '{field_def['name']}'."
                    )

                field_params = {
                    "name": field_def["name"],
                    "dtype": milvus_dtype,
                }
                if milvus_dtype == DataType.VARCHAR:
                    field_params["max_length"] = field_def.get("max_length", 65535)
                elif milvus_dtype == DataType.ARRAY:  # <<< MODIFIED SECTION FOR ARRAY
                    element_dtype_str = field_def.get("element_type", "").upper()
                    if not element_dtype_str:
                        raise ValueError(
                            f"Missing 'element_type' for ARRAY field '{field_def['name']}'."
                        )
                    try:
                        milvus_element_dtype = getattr(DataType, element_dtype_str)
                    except AttributeError:
                        raise ValueError(
                            f"Unsupported element_type '{element_dtype_str}' for ARRAY field '{field_def['name']}'."
                        )

                    field_params["element_type"] = milvus_element_dtype

                    max_capacity_val = field_def.get("max_capacity")
                    if max_capacity_val is None:
                        raise ValueError(
                            f"Missing 'max_capacity' for ARRAY field '{field_def['name']}'."
                        )
                    field_params["max_capacity"] = int(max_capacity_val)
                # <<< END OF MODIFIED SECTION FOR ARRAY

                # Add other dtype specific params if needed (e.g. for JSON)
                fields.append(FieldSchema(**field_params))

        schema = CollectionSchema(
            fields=fields,
            description=f"Imported data for {target_collection_name}",
            enable_dynamic_field=True,
        )

        collection = Collection(
            target_collection_name,
            schema=schema,
            using=alias,
            consistency_level=consistency_level,
        )
        logger.info(
            f"Collection '{target_collection_name}' created successfully with schema: {[f.name for f in fields]}"
        )
        if schema.enable_dynamic_field:
            logger.info("Dynamic fields are enabled for this collection.")
    else:
        collection = Collection(target_collection_name, using=alias)
        logger.info(
            f"Using existing collection '{target_collection_name}'. Schema: {[f.name for f in collection.schema.fields]}"
        )

    logger.info(
        f"Starting import from '{input_file}' to collection '{target_collection_name}'..."
    )

    data_batch: List[Dict[str, Any]] = []
    total_inserted_count = 0

    try:
        with open(input_file, "r", encoding="utf-8") as f:
            for line_num, line in enumerate(f, 1):
                try:
                    node_data = json.loads(line)
                    data_batch.append(node_data)
                except json.JSONDecodeError:
                    logger.warning(
                        f"Skipping malformed JSON line {line_num}: {line.strip()}"
                    )
                    continue

                if len(data_batch) >= batch_size:
                    try:
                        insert_result = collection.insert(data_batch)
                        total_inserted_count += len(insert_result.primary_keys)
                    except MilvusException as mke:
                        logger.error(
                            f"Schema mismatch during insert: {mke}. Data example: {data_batch[0] if data_batch else 'N/A'}"
                        )
                        logger.error(
                            f"Ensure all fields in your data file are defined in the target collection's schema OR dynamic schema is enabled and working as expected."
                        )
                        raise
                    except Exception as e:
                        logger.error(f"Error inserting batch: {e}")
                    finally:
                        logger.info(
                            f"Processed batch of {len(data_batch)}. Total inserted (attempted/successful): {total_inserted_count}"
                        )
                        data_batch = []

            if data_batch:
                try:
                    insert_result = collection.insert(data_batch)
                    total_inserted_count += len(insert_result.primary_keys)
                except MilvusException as mke:
                    logger.error(
                        f"Schema mismatch during final insert: {mke}. Data example: {data_batch[0] if data_batch else 'N/A'}"
                    )
                    raise
                except Exception as e:
                    logger.error(f"Error inserting final batch: {e}")
                finally:
                    logger.info(
                        f"Processed final batch of {len(data_batch)}. Total inserted (attempted/successful): {total_inserted_count}"
                    )

        logger.info("Flushing collection to ensure data persistence...")
        collection.flush()
        logger.info(
            f"Collection '{target_collection_name}' flushed. Total entities in collection: {collection.num_entities}"
        )

        has_index = False
        if collection.has_index():  # Check if any index exists
            for index_info in collection.indexes:
                # In PyMilvus 2.x, index_info.field_name is a string, not a list
                if index_info.field_name == index_field_name:
                    has_index = True
                    logger.info(
                        f"Index already exists on field '{index_field_name}'. Index params: {index_info.to_dict().get('index_param')}"
                    )
                    break

        if not has_index:
            logger.info(
                f"Creating index on field '{index_field_name}' with type '{index_type}'..."
            )
            current_index_params = {}  # Default to empty dict
            if index_params is not None:  # User provided params
                current_index_params = index_params
            elif index_type == "IVF_FLAT":
                current_index_params = {"nlist": 128}
            elif index_type == "HNSW":
                current_index_params = {"M": 16, "efConstruction": 200}
            # AUTOINDEX and FLAT typically don't require specific 'params' (handled by index_type or metric_type)

            try:
                collection.create_index(
                    field_name=index_field_name,
                    index_params={
                        "index_type": index_type,
                        "metric_type": metric_type,
                        "params": current_index_params,
                    },
                )
                logger.info(f"Index created successfully on '{index_field_name}'.")
            except Exception as e:
                logger.error(f"Failed to create index: {e}")

        logger.info("Loading collection after import and index creation...")
        collection.load()
        logger.info(f"Collection '{target_collection_name}' loaded.")
        logger.info(
            f"Successfully imported data to '{target_collection_name}'. Final count of entities in collection: {collection.num_entities}."
        )

    except FileNotFoundError:
        logger.error(f"Input file '{input_file}' not found.")
    except Exception as e:
        logger.error(f"Error during import: {e}")
        raise


# --- Main CLI Handler ---
if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Milvus Collection Export/Import Utility"
    )
    parser.add_argument(
        "--host", type=str, default=DEFAULT_MILVUS_HOST, help="Milvus server host."
    )
    parser.add_argument(
        "--port", type=str, default=DEFAULT_MILVUS_PORT, help="Milvus server port."
    )
    parser.add_argument(
        "--user",
        type=str,
        default=None,
        help="Milvus username (for Zilliz Cloud or RBAC).",
    )
    parser.add_argument("--password", type=str, default=None, help="Milvus password.")
    parser.add_argument(
        "--alias",
        type=str,
        default=DEFAULT_MILVUS_ALIAS,
        help="Milvus connection alias.",
    )

    subparsers = parser.add_subparsers(dest="action", required=True)

    # Export arguments
    parser_export = subparsers.add_parser(
        "export", help="Export data from a collection."
    )
    parser_export.add_argument(
        "-c",
        "--collection",
        type=str,
        required=True,
        help="Name of the source Milvus collection.",
    )
    parser_export.add_argument(
        "-o",
        "--output",
        type=str,
        default=DEFAULT_DATA_FILE,
        help="Output JSON Lines file path.",
    )
    parser_export.add_argument(
        "--batch_size",
        type=int,
        default=DEFAULT_BATCH_SIZE,
        help="Batch size for querying Milvus.",
    )

    # Import arguments
    parser_import = subparsers.add_parser(
        "import", help="Import data into a collection."
    )
    parser_import.add_argument(
        "-i",
        "--input",
        type=str,
        default=DEFAULT_DATA_FILE,
        help="Input JSON Lines file path.",
    )
    parser_import.add_argument(
        "-tc",
        "--target_collection",
        default="new_collection",
        type=str,
        required=True,
        help="Name of the target Milvus collection.",
    )
    parser_import.add_argument(
        "--overwrite",
        action="store_true",
        help="Overwrite target collection if it exists.",
    )
    parser_import.add_argument(
        "--pk_field",
        type=str,
        required=True,
        help="Name of the primary key field in the data and target schema.",
    )
    parser_import.add_argument(
        "--pk_dtype",
        type=str,
        default="VARCHAR",
        choices=["VARCHAR", "INT64"],
        help="Primary key data type.",
    )
    parser_import.add_argument(
        "--pk_max_len", type=int, default=64, help="Max length for VARCHAR primary key."
    )
    parser_import.add_argument(
        "--pk_auto_id",
        action="store_true",
        help="Enable auto_id for the primary key if creating collection (ignores PK values from file if true).",
    )
    parser_import.add_argument(
        "--vector_field",
        type=str,
        default="embedding",
        help="Name of the vector embedding field.",
    )
    parser_import.add_argument(
        "--vector_dim",
        type=int,
        default=1792,
        help="Dimension of the vector embeddings.",
    )
    parser_import.add_argument(
        "--other_fields",
        type=str,
        help="JSON string defining other scalar fields for schema creation. "
        'Example: \'[{"name": "text", "dtype": "VARCHAR", "max_length": 1000}, {"name": "page_num", "dtype": "INT32"}, {"name": "tags", "dtype": "ARRAY", "element_type": "VARCHAR", "max_capacity": 100}]\' '
        "Supported dtypes: BOOL, INT8, INT16, INT32, INT64, FLOAT, DOUBLE, VARCHAR, JSON, ARRAY.",
    )
    parser_import.add_argument(
        "--index_field",
        type=str,
        help="Name of the field to build index on (defaults to vector_field).",
    )
    parser_import.add_argument(
        "--index_type",
        type=str,
        default="FLAT",
        help="Milvus index type (e.g., AUTOINDEX, IVF_FLAT, HNSW, FLAT).",
    )
    parser_import.add_argument(
        "--metric_type",
        type=str,
        default="IP",
        help="Metric type for vector index (e.g., L2, IP).",
    )
    parser_import.add_argument(
        "--index_params",
        type=str,
        help="JSON string for index parameters (e.g., '{\"nlist\": 128}').",
    )
    parser_import.add_argument(
        "--consistency_level",
        type=str,
        default="Bounded",
        help="Consistency level for the collection.",
    )
    parser_import.add_argument(
        "--batch_size",
        type=int,
        default=DEFAULT_BATCH_SIZE,
        help="Batch size for inserting data into Milvus.",
    )

    args = parser.parse_args()

    # Establish Milvus connection
    connect_to_milvus(args.alias, args.host, args.port, args.user, args.password)

    if args.action == "export":
        export_collection_to_file(
            alias=args.alias,
            collection_name=args.collection,
            output_file=args.output,
            batch_size=args.batch_size,
        )
    elif args.action == "import":
        other_fields_schema_defs = None
        if args.other_fields:
            try:
                other_fields_schema_defs = json.loads(args.other_fields)
                if not isinstance(other_fields_schema_defs, list):
                    raise ValueError(
                        "--other_fields must be a JSON list of field definitions."
                    )
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON for --other_fields: {e}")
                exit(1)
            except ValueError as e:
                logger.error(e)
                exit(1)

        idx_params = None
        if args.index_params:
            try:
                idx_params = json.loads(args.index_params)
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON for --index_params: {e}")
                exit(1)

        import_data_to_collection(
            alias=args.alias,
            input_file=args.input,
            target_collection_name=args.target_collection,
            pk_field_name=args.pk_field,
            pk_dtype=args.pk_dtype,
            pk_max_length=args.pk_max_len,
            auto_id_pk=args.pk_auto_id,
            vector_field_name=args.vector_field,
            vector_dim=args.vector_dim,
            other_fields_schema_defs=other_fields_schema_defs,
            metric_type=args.metric_type,
            index_field_name=args.index_field
            if args.index_field
            else args.vector_field,
            index_type=args.index_type,
            index_params=idx_params,
            consistency_level=args.consistency_level,
            batch_size=args.batch_size,
            overwrite_collection=args.overwrite,
        )

    logger.info("Operation finished.")
