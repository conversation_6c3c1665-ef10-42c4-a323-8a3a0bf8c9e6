from docx import Document
import json
import hashlib


def docx_to_json(docx_path):
    doc = Document(docx_path)
    result = {
        "reportID": "606530bc9ea7d564ec3eebbed70b88d325cdb2231e2d17276ef96328f94b6890",
        "chapter": [],
    }

    stack = [result]  # 使用栈来跟踪当前层级

    for para in doc.paragraphs:
        text = para.text.strip()
        if not text:
            continue

        # 根据段落样式判断层级
        if para.style.name.startswith("Heading 1"):
            level = 1
        elif para.style.name.startswith("Heading 2"):
            level = 2
        elif para.style.name.startswith("Heading 3"):
            level = 3
        elif para.style.name.startswith("Heading 4"):
            level = 4
        elif para.style.name.startswith("Heading 5"):
            level = 5
        elif para.style.name.startswith("Heading 6"):
            level = 6
        else:
            continue  # 跳过非标题段落

        # 创建节点ID
        node_id = hashlib.sha256(text.encode("utf-8")).hexdigest()

        # 创建新节点
        new_node = {"id": node_id, "title": text, "description": "", "subChapter": []}

        # 调整栈以匹配当前层级
        while len(stack) > level:
            stack.pop()

        # 将新节点添加到父节点的subChapter中
        parent = stack[-1]
        if "subChapter" not in parent:
            parent["subChapter"] = []
        parent["subChapter"].append(new_node)

        # 将新节点压入栈
        stack.append(new_node)

    return result


# 转换docx文件为json结构
json_data = docx_to_json("./杭州一网统管大纲模版_v2.docx")

# 保存json文件
output_file_name = "outline.json"
with open(output_file_name, "w", encoding="utf-8") as f:
    json.dump(json_data, f, ensure_ascii=False, indent=4)

print(f"文件 {output_file_name} 已成功保存。")
