from datetime import datetime, <PERSON><PERSON><PERSON>
import os
from tempfile import TemporaryDirectory
from typing import List
from sqlalchemy import select
from nl2document.common.models.model import CommonDocumentModel, engine
from nl2document.common.models.base_model import DocumentModel, FileStatus
from sqlalchemy.orm import Session

from common.fs.fs import get_s3_file_system
from nl2document.builder.index_builder import DocumentAddRequest, IndexBuilderBase
from common.logging.logger import get_logger

logger = get_logger(__name__)
index_builder = IndexBuilderBase()


def get_old_documents():
    with Session(engine) as session:
        stmt = select(DocumentModel).where(
            DocumentModel.gmt_modified < datetime.now().date() - timedelta(days=1)
        )
        docs = list(session.scalars(stmt))
        return docs


def check_download_valid(docs: List[DocumentModel]):
    download_failed_docs = []
    for doc in docs:
        with TemporaryDirectory() as temp_dir:
            file_path = os.path.join(temp_dir, str(doc.id))
            get_s3_file_system().download(doc.source_path, file_path)
            if not is_file_valid(file_path):
                download_failed_docs.append(doc.file_name)
    if download_failed_docs:
        with open("download_failed_docs.txt", "a", encoding="utf-8") as f:
            f.write("\n".join(download_failed_docs) + "\n")


def is_file_valid(file_path: str) -> bool:
    return os.path.exists(file_path) and os.path.getsize(file_path) > 0


def main():
    docs = get_old_documents()
    check_download_valid(docs)
    failed_docs = []
    for doc in docs:
        temp_doc = CommonDocumentModel(
            id=str(doc.id),
            file_name=doc.name,
            file_type=doc.file_type,
            source_url=doc.source_path,
            file_status=doc.file_status,
        )
        document_request = DocumentAddRequest(source_url=doc.source_path)
        logger.info(f"add document {doc.source_path} start")
        try:
            index_builder._add_document(temp_doc, document_request)
            logger.info(f"add document {doc.source_path} success")
        except Exception as e:
            failed_docs.append(doc.source_path)
            logger.error(f"add document {doc.source_path} failed, error: {e}")
            continue

    if failed_docs:
        with open("failed_docs.txt", "a", encoding="utf-8") as f:
            f.write("\n".join(failed_docs) + "\n")


if __name__ == "__main__":
    main()
