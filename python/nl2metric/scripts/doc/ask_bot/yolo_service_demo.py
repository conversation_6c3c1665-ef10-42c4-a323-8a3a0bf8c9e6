from ultralytics import YOLO
from doclayout_yolo import YOLOv10
import torch


def get_device(device: str = None):
    # return torch.device("cuda:0")
    if not device:
        return torch.device("cuda" if torch.cuda.is_available() else "cpu")

    if device == "cpu":
        return torch.device("cpu")

    elif device.startswith("gpu:"):
        _, gpu_id = device.split(":")
        return torch.device(f"cuda:{gpu_id}")

    else:
        return torch.device("cuda" if torch.cuda.is_available() else "cpu")


def ouryolo_rec(model_path: str, img_path: str, device):
    model = YOLO(model_path).to(device)

    yolo_reaults = model(img_path, conf=0.5)[0]
    bbox = yolo_reaults.boxes.xyxy.cpu().numpy().astype(int).tolist()
    labels = yolo_reaults.boxes.cls.cpu().numpy().astype(int).tolist()
    confs = yolo_reaults.boxes.conf.cpu().numpy().tolist()
    for box, label_id, conf in zip(bbox, labels, confs):
        print(f"ouryolo results: {box}, {label_id}, {conf}")


def doclayout_rec(model_path: str, img_path: str, device):
    model = YOLOv10(model_path).to(device)
    yolo_reaults = model(img_path, imgsz=1024, conf=0.5)[0]
    bbox = yolo_reaults.boxes.xyxy.cpu().numpy().astype(int).tolist()
    labels = yolo_reaults.boxes.cls.cpu().numpy().astype(int).tolist()
    confs = yolo_reaults.boxes.conf.cpu().numpy().tolist()

    for box, label_id, conf in zip(bbox, labels, confs):
        print(f"ouryolo results: {box}, {label_id}, {conf}")


if __name__ == "__main__":
    our_model_path = "/ask_doc_ocr/AskDoc_OCR/runs/detect/train39/weights/best.pt"
    doclayout_model_path = "/ask_doc_ocr/AskDoc_OCR/runs/detect/train39/weights/doclayout_yolo_docstructbench_imgsz1024.pt"
    img_path = "/test_image.png"
    device = get_device()
    print(f"device: {device}")
    # ouryolo_rec(our_model_path, img_path, device)
    doclayout_rec(doclayout_model_path, img_path, device)
