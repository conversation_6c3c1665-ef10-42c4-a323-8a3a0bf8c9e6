import requests
import json

"""
CREATE TABLE `bj_telecom_files` (
  `file_id` varchar(64) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_type` varchar(64) NOT NULL COMMENT '文件类型',
  `upload_status` enum('UPLOAD_COMPLETED','INDEX_BUILDING','INDEX_BUILD_FAILED','INDEX_BUILD_SUCCESS','INDEX_BUILD_RETRY') NOT NULL DEFAULT 'UPLOAD_COMPLETED' COMMENT '文件状态（上传完毕，索引构建中，索引构建失败，索引构建成功, 等待重新构建）',
  `removed` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已下架，在新增文件后，修改该状态为0，下架文件时，修改该状态为1',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  `meta_info` json DEFAULT NULL COMMENT '文件的元信息，存储为JSON格式',
  PRIMARY KEY (`file_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
"""

import mysql.connector


# 建立数据库连接
def get_database_connection():
    connection = mysql.connector.connect(
        host="**************", user="root", password="123", database="askdoc_test"
    )
    return connection


# 查询所有文件ID
def get_all_file_ids():
    connection = get_database_connection()
    cursor = connection.cursor()

    query = "SELECT file_id FROM bj_telecom_files"
    cursor.execute(query)

    file_ids = [row[0] for row in cursor.fetchall()]

    cursor.close()
    connection.close()

    return file_ids


def remove_files(file_ids):
    url = "http://**************:29099/bj-telecom/askdoc/file-removed"

    headers = {
        "User-Agent": "Apifox/1.0.0 (https://apifox.com)",
        "Content-Type": "application/json",
        "Accept": "*/*",
        "Host": "**************:29099",
        "Connection": "keep-alive",
    }

    data = {"fileIds": file_ids}

    print(data)
    response = requests.post(url, headers=headers, data=json.dumps(data))
    print(response.text)
    return response.text


# Fetch duplicate file IDs for duplicate file names
def get_duplicate_file_ids_to_delete():
    connection = get_database_connection()
    cursor = connection.cursor()

    # Query to fetch all duplicate file_ids and their file_names (where removed = 0)
    query = """
        SELECT file_id, file_name
        FROM bj_telecom_files
        WHERE file_name IN (
            SELECT file_name 
            FROM bj_telecom_files 
            WHERE removed = 0
            GROUP BY file_name 
            HAVING COUNT(*) > 1
        ) AND removed = 0
    """
    cursor.execute(query)
    all_duplicates = (
        cursor.fetchall()
    )  # [(file_id1, file_name1), (file_id2, file_name1), ...]

    # Query to fetch the file_id to retain for each duplicate file_name (where removed = 0)
    retain_query = """
        SELECT MAX(file_id) AS file_id, file_name
        FROM bj_telecom_files
        WHERE file_name IN (
            SELECT file_name 
            FROM bj_telecom_files 
            WHERE removed = 0
            GROUP BY file_name 
            HAVING COUNT(*) > 1
        ) AND removed = 0
        GROUP BY file_name
    """
    cursor.execute(retain_query)
    retained_files = {
        row[1]: row[0] for row in cursor.fetchall()
    }  # {file_name1: file_id1, ...}

    # Determine file_ids to delete (all duplicates except the retained ones)
    to_delete = [
        file_id
        for file_id, file_name in all_duplicates
        if retained_files[file_name] != file_id
    ]

    cursor.close()
    connection.close()

    return to_delete


if __name__ == "__main__":
    file_ids = get_duplicate_file_ids_to_delete()
    print("file_ids:", file_ids)
    # print(remove_files(file_ids))
    # print(file_ids)
    # print(len(file_ids))
