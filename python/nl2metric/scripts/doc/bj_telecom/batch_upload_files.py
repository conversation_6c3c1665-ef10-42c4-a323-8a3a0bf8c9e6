import glob
import requests
import os

"""
curl --location 'http://192.168.111.51:8080/File/UploadBJTelecom' \
--form 'File=@"/Users/<USER>/Downloads/new_doc_parse/商城/中国电信云产品（安全类+甄选商城）资费标准V1.8（发布版）.pdf"'
"""


def upload_file(file_path: str, file_name: str) -> None:
    print(file_path, file_name)
    url = "http://192.168.111.51:8080/File/UploadBJTelecom"
    files = {"File": (file_name, open(file_path, "rb"))}
    response = requests.post(url, files=files)
    print(response.text)


def main() -> None:
    base_dir = "/Users/<USER>/Documents/0.work/2024/6.北京电信知识库/问小政整理-by-鸿信/"
    for file_path in glob.glob(f"{base_dir}/**/*", recursive=True):
        if os.path.isfile(file_path):
            file_name = os.path.basename(file_path)
            upload_file(file_path, file_name)


if __name__ == "__main__":
    # upload_file("/Users/<USER>/Documents/0.work/2024/6.北京电信知识库/问小政整理-by-鸿信/天翼云3.0/附件/附件5：(省示范)北京电信天翼云会议业务合同模板.docx", "附件5：(省示范)北京电信天翼云会议业务合同模板.docx")

    xlsx_files = [
        "/Users/<USER>/Documents/0.work/2024/6.北京电信知识库/资费Excel/等保助手-资费.xlsx",
    ]
    for file_path in xlsx_files:
        upload_file(file_path, os.path.basename(file_path))
    # main()
