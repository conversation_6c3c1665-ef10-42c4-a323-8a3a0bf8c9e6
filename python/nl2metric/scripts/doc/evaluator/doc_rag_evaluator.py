import asyncio
import logging
import os
from typing import List, <PERSON><PERSON>, Dict

from tqdm import tqdm
import time

from scripts.doc.evaluator.models.config import Config
from scripts.doc.evaluator.models.evaluation import EvaluationResult
from scripts.doc.evaluator.utils.result_writer import ResultWriter
from scripts.doc.evaluator.evaluators.doc_index import DocIndexEvaluator
from scripts.doc.evaluator.evaluators.bj_telecom import BJTelecomEvaluator
from scripts.doc.evaluator.models.exceptions import EvaluationError


class AsyncEvaluatorManager:
    """异步评估器"""

    def __init__(self, config: Config):
        self.config = config
        self.evaluator = None
        self.result_writer = ResultWriter(config)
        self._init_evaluator()
        self.results = []
        self.failed_count = 0
        self.semaphore = asyncio.Semaphore(self.config.performance.query_concurrency)

    def _init_evaluator(self):
        """初始化评估器"""
        if self.config.evaluator.mode == "doc_index":
            self.evaluator = DocIndexEvaluator(
                model_name=self.config.api.model_name, config=self.config
            )
        elif self.config.evaluator.mode == "bj_telecom":
            self.evaluator = BJTelecomEvaluator(
                model_name=self.config.api.model_name, config=self.config
            )
        else:
            raise ValueError(f"不支持的评估模式: {self.config.evaluator.mode}")

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.evaluator.init_session()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        if self.results:
            self.result_writer.generate_report(self.results)
        else:
            logging.error("没有成功的评估结果")
        if self.evaluator:
            await self.evaluator.close()

    async def _create_task(
        self, question: str, correct_answer: str, doc_ids: List[str]
    ) -> EvaluationResult:
        """创建单个评估任务"""
        try:
            (
                actual_answer,
                retrieved_docs,
                latency,
            ) = await self.evaluator.query_documents(question, doc_ids)
            (score, reason), rag_metrics = await asyncio.gather(
                self.evaluator.evaluate_answer_quality(
                    question=question,
                    correct_answer=correct_answer,
                    actual_answer=actual_answer,
                ),
                self.evaluator.evaluate_rag_metrics(
                    question, actual_answer, retrieved_docs
                ),
            )
            return EvaluationResult(
                question=question,
                correct_answer=correct_answer,
                actual_answer=actual_answer,
                score=score,
                reason=reason,
                latency=latency,
                retrieved_docs=retrieved_docs,
                rag_metrics=rag_metrics,
            )

        except Exception as e:
            logging.exception(f"创建评估任务失败: {e}")
            raise EvaluationError(f"创建评估任务失败: {e}")

    async def _bounded_evaluate(
        self, question: str, correct_answer: str, doc_ids: List[str]
    ):
        try:
            async with self.semaphore:
                result = await self._create_task(question, correct_answer, doc_ids)
                self.results.append(result)
                return None
        except Exception as e:
            self.failed_count += 1
            logging.error(f"评估任务失败: {e}")
            return e

    async def evaluate(self, test_cases: List[Tuple[str, str, List[str]]]):
        """执行评估任务"""
        if not test_cases:
            raise ValueError("测试用例不能为空")

        tasks = []

        # 创建所有任务
        for question, correct_answer, doc_ids in test_cases:
            task = asyncio.create_task(
                self._bounded_evaluate(question, correct_answer, doc_ids)
            )
            tasks.append(task)

        # 使用tqdm显示进度
        with tqdm(total=len(tasks), desc="评估进度") as pbar:
            for coro in asyncio.as_completed(tasks):
                error = await coro
                if error:
                    logging.error(f"任务执行失败: {error}")
                pbar.update(1)

        if self.failed_count:
            logging.warning(f"共有 {self.failed_count}/{len(test_cases)} 个任务失败")


def setup_logging(log_file: str = "evaluation.log"):
    """配置日志

    Args:
        log_file: 日志文件路径
    """
    log_format = "%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s"
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(log_file, encoding="utf-8"),
        ],
    )


async def main():
    """主函数：运行评估流程"""
    setup_logging()

    try:
        config = Config.from_env()
        logging.info(f"\n {config} ")

        async with AsyncEvaluatorManager(config) as manager:
            # 加载并执行任务
            test_cases = manager.evaluator.read_test_cases(config.files.test_cases)
            await manager.evaluate(test_cases)

    except KeyboardInterrupt:
        logging.info("评估被用户中断")
    except Exception as e:
        logging.exception(f"程序执行失败: {e}")
    finally:
        logging.info("评估完成")


if __name__ == "__main__":
    asyncio.run(main())
