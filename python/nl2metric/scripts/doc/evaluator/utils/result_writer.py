import pandas as pd
import logging
from datetime import datetime
from typing import List
import os
import markdown2
from ..models.evaluation import EvaluationResult, EvaluationStats, LatencyStats
from ..models.exceptions import ResultWriteError
from collections import defaultdict


class ResultWriter:
    """结果写入器"""

    def __init__(self, config):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config
        self.csv_file = config.files.output_csv
        self.html_file = config.files.output_html

    def _render_markdown(self, text: str) -> str:
        """渲染Markdown文本"""
        try:
            if pd.isna(text):
                return ""
            # 配置markdown扩展
            extras = {
                "code-friendly": None,
                "fenced-code-blocks": None,
                "tables": None,
                "break-on-newline": None,
                "html-classes": {
                    "table": "table table-bordered",
                    "pre": "code-block",
                    "code": "inline-code",
                },
            }
            return markdown2.markdown(str(text), extras=extras)
        except Exception as e:
            self.logger.error(f"解析Markdown文本失败: {e}")
            return str(text)

    def write_results_to_csv(self, results: List[EvaluationResult]) -> pd.DataFrame:
        """写入评估结果到CSV文件"""
        try:
            # 清理旧的输出文件
            if os.path.exists(self.csv_file):
                os.remove(self.csv_file)
                logging.info(f"已清理旧的输出文件: {self.csv_file}")
            df = pd.DataFrame([r.to_dict() for r in results])
            df.to_csv(self.csv_file, index=False)
            self.logger.info(f"评估结果已保存到: {self.csv_file}")
            return df
        except Exception as e:
            self.logger.error(f"写入CSV文件失败: {e}")
            raise ResultWriteError(f"写入CSV文件失败: {e}")

    def _calculate_stats(self, results: List[EvaluationResult]) -> EvaluationStats:
        """计算评估统计信息"""
        try:
            if not results:
                return EvaluationStats.empty()

            total_count = len(results)
            success_count = sum(1 for r in results if r.score >= 6)  # 及格分数为6分
            avg_score = sum(r.score for r in results) / total_count
            avg_latency = sum(r.latency for r in results) / total_count

            # 计算分数分布
            score_distribution = {}
            for score in range(11):  # 0-10分
                count = sum(1 for r in results if r.score == score)
                if count > 0:  # 只记录有数据的分数
                    score_distribution[score] = count

            # 计算延迟统计
            latencies = sorted(r.latency for r in results)
            latency_stats = LatencyStats(
                min=min(latencies),
                max=max(latencies),
                avg=avg_latency,
                p95=latencies[int(len(latencies) * 0.95)],
                p99=latencies[int(len(latencies) * 0.99)],
            )

            # 计算RAG指标平均值
            rag_metrics_sum = defaultdict(float)
            rag_metrics_count = defaultdict(int)
            for r in results:
                if r.rag_metrics:
                    for metric, value in r.rag_metrics.items():
                        rag_metrics_sum[metric] += value.score or 0
                        rag_metrics_count[metric] += 1

            avg_rag_metrics = {
                metric: value / rag_metrics_count[metric]
                for metric, value in rag_metrics_sum.items()
                if rag_metrics_count[metric] > 0
            }

            return EvaluationStats(
                total_count=total_count,
                success_count=success_count,
                fail_count=total_count - success_count,
                avg_score=avg_score,
                avg_latency=avg_latency,
                score_distribution=score_distribution,
                latency_stats=latency_stats,
                avg_rag_metrics=avg_rag_metrics,
            )

        except Exception as e:
            self.logger.error(f"计算统计信息失败: {e}")
            raise ResultWriteError(f"计算统计信息失败: {e}")

    def _get_custom_style(self) -> str:
        """获取自定义样式"""
        return """
        <style>
            :root {
                --primary-color: #007bff;
                --secondary-color: #6c757d;
                --success-color: #28a745;
                --info-color: #17a2b8;
                --warning-color: #ffc107;
                --danger-color: #dc3545;
            }
            
            .table { font-size: 14px; width: 100%; }
            .table th { 
                background: #f8f9fa; 
                position: sticky; 
                top: 0; 
                z-index: 1;
            }
            .table td { 
                max-width: 300px; 
                overflow: auto;
                white-space: pre-wrap;
                word-break: break-word;
            }
            
            .stats-panel {
                margin-bottom: 20px;
                padding: 15px;
                background: #f8f9fa;
                border-radius: 4px;
            }
            .stats-item {
                display: inline-block;
                margin-right: 20px;
                padding: 8px 15px;
                background: white;
                border-radius: 4px;
                border: 1px solid #dee2e6;
            }
            .stats-label { font-size: 12px; color: var(--secondary-color); }
            .stats-value { font-size: 18px; font-weight: bold; color: var(--primary-color); }
            
            .stats-title {
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 10px;
                color: #495057;
            }
            .score-distribution {
                display: flex;
                flex-direction: column;
                gap: 10px;
            }
            .score-bar {
                display: flex;
                align-items: center;
                gap: 8px;
            }
            .score-label { min-width: 40px; font-weight: bold; }
            .score-progress {
                width: 200px;
                height: 20px;
                background: #e9ecef;
                border-radius: 10px;
                overflow: hidden;
            }
            .score-progress-bar {
                height: 100%;
                border-radius: 10px;
                transition: width 0.3s ease;
            }
            .score-value {
                min-width: 60px;
                text-align: right;
                color: #6c757d;
            }
            .column-toggles {{
                background: #f8f9fa;
                padding: 15px;
                border-radius: 4px;
                margin-bottom: 20px;
            }}
            .form-switch {{
                padding-left: 2.5em;
            }}
            .form-check-input:checked {{
                background-color: var(--primary-color);
                border-color: var(--primary-color);
            }}
            
            /* RAG指标说明样式 */
            .metrics-description {
                margin-top: 2rem;
                padding: 1.5rem;
                background: #f8f9fa;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            }
            
            .metrics-description h3 {
                color: var(--primary-color);
                margin-bottom: 1.5rem;
                font-size: 1.25rem;
            }
            
            .metric-item {
                margin-bottom: 1.5rem;
                padding-bottom: 1.5rem;
                border-bottom: 1px solid #e9ecef;
            }
            
            .metric-item:last-child {
                border-bottom: none;
                margin-bottom: 0;
                padding-bottom: 0;
            }
            
            .metric-item h4 {
                color: #495057;
                font-size: 1.1rem;
                margin-bottom: 0.75rem;
            }
            
            .metric-item p {
                color: #6c757d;
                margin-bottom: 0.5rem;
            }
            
            .metric-item ul {
                margin: 0.5rem 0 0 1.5rem;
                padding: 0;
                color: #6c757d;
            }
            
            .metric-item li {
                margin-bottom: 0.25rem;
            }
        </style>
        """

    def _generate_html_report(
        self, df: pd.DataFrame, html_file: str, results: List[EvaluationResult]
    ):
        """生成HTML格式报告"""
        try:
            stats: EvaluationStats = self._calculate_stats(results)

            # 渲染Markdown列
            markdown_columns = ["question", "correct_answer", "actual_answer", "reason"]
            for col in markdown_columns:
                if col in df.columns:
                    df[col] = df[col].apply(self._render_markdown)

            # 格式化分数
            def format_score(score):
                colors = {
                    10: "#28a745",  # 深绿色 - 完美
                    9: "#34c759",  # 绿色 - 极好
                    8: "#17a2b8",  # 青色 - 优秀
                    7: "#5856d6",  # 紫色 - 良好
                    6: "#007aff",  # 蓝色 - 及格
                    5: "#28a745",  # 绿色
                    4: "#17a2b8",  # 青色
                    3: "#ffc107",  # 黄色
                    2: "#fd7e14",  # 橙色
                    1: "#dc3545",  # 红色
                }
                return f'<span style="color: {colors.get(int(score), "#000")}">{score}</span>'

            if "score" in df.columns:
                df["score"] = df["score"].apply(format_score)

            # 格式化延迟
            if "latency" in df.columns:
                df["latency"] = df["latency"].apply(
                    lambda x: f"{float(x):.2f}s" if pd.notna(x) else ""
                )

            # 定义默认隐藏的列
            hidden_columns = ["retrieved_docs"]

            # 生成列控制按钮的HTML
            column_toggles = []
            for i, col in enumerate(df.columns):
                toggle_id = f"toggle_{col.lower().replace(' ', '_')}"
                is_checked = col not in hidden_columns
                column_toggles.append(
                    f"""
                    <div class="form-check form-switch d-inline-block me-3">
                        <input class="form-check-input" type="checkbox" id="{toggle_id}" 
                               data-column="{i}" {' checked' if is_checked else ''}>
                        <label class="form-check-label" for="{toggle_id}">{col}</label>
                    </div>
                """
                )

            template = f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <title>RAG评估结果</title>
                    <meta charset="utf-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1">
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                    <link href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap5.min.css" rel="stylesheet">
                    {self._get_custom_style()}
                    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
                    <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
                    <script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap5.min.js"></script>
                    <script>
                        $(document).ready(function() {{
                            // 初始化DataTable
                            var table = $('#evaluation_results').DataTable({{
                                pageLength: 25,
                                order: [[3, 'desc']],
                                language: {{
                                    search: "搜索:",
                                    lengthMenu: "显示 _MENU_ 条记录",
                                    info: "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
                                    paginate: {{
                                        first: "首页",
                                        previous: "上页",
                                        next: "下页",
                                        last: "末页"
                                    }}
                                }},
                                // 设置初始列可见性
                                columnDefs: [
                                    {{
                                        targets: {[list(df.columns).index(col) for col in hidden_columns]},
                                        visible: false
                                    }}
                                ]
                            }});

                            // 列显示/隐藏控制
                            $('.form-check-input').on('change', function() {{
                                var columnIdx = $(this).data('column');
                                table.column(columnIdx).visible($(this).prop('checked'));
                            }});

                            // 全选/取消全选按钮
                            $('#toggle_all').on('change', function() {{
                                var checked = $(this).prop('checked');
                                // 先更新所有复选框状态
                                $('.form-check-input').prop('checked', checked);
                                // 然后更新列的可见性
                                table.columns().visible(checked);
                                // 最后确保指定列保持隐藏
                                {[f"table.column({list(df.columns).index(col)}).visible(false);" for col in hidden_columns]}
                                {[f"$('#toggle_{col.lower().replace(' ', '_')}').prop('checked', false);" for col in hidden_columns]}
                            }});

                            // 初始化时确保正确的复选框状态
                            {[f"$('#toggle_{col.lower().replace(' ', '_')}').prop('checked', false);" for col in hidden_columns]}
                        }});
                    </script>
                </head>
                <body>
                    <div class="container-fluid mt-3">
                        <h2>RAG评估结果</h2>
                        {stats.to_html()}
                        {self._get_rag_metrics_description()}
                        
                        <div class="column-toggles">
                            <h5 class="mb-3">显示/隐藏列</h5>
                            <div class="form-check form-switch d-inline-block me-3">
                                <input class="form-check-input" type="checkbox" id="toggle_all" checked>
                                <label class="form-check-label" for="toggle_all">全选/取消全选</label>
                            </div>
                            {''.join(column_toggles)}
                        </div>

                        <div class="table-responsive">
                            {df.to_html(
                                escape=False,
                                index=False,
                                table_id='evaluation_results',
                                classes='table table-striped table-bordered'
                            )}
                        </div>
                    </div>
                </body>
                </html>
            """

            with open(html_file, "w", encoding="utf-8") as f:
                f.write(template)

        except Exception as e:
            logging.error(f"生成HTML报告失败: {e}")
            raise ResultWriteError(f"生成HTML报告失败: {e}")

    def generate_report(self, results: List[EvaluationResult]):
        """生成评估报告"""
        try:
            df = self.write_results_to_csv(results)

            if not results:
                logging.warning("没有评估结果可供生成报告")
                return

            self._generate_html_report(df, self.html_file, results)
            logging.info(f"HTML报告已保存到: {self.html_file}")

        except Exception as e:
            logging.exception(f"生成报告失败: {e}")
            raise ResultWriteError(f"生成报告失败: {e}")

    def _get_rag_metrics_description(self) -> str:
        """获取RAG评估指标说明"""
        return """
        <div class="metrics-description">
            <h3>RAG评估指标说明</h3>
            <div class="metric-item">
                <h4>答案相关性 (Answer Relevance)</h4>
                <p>评估生成答案与用户问题的相关程度。高分表示答案准确回应了用户问题，低分可能需要：</p>
                <ul>
                    <li>优化提示模板设计</li>
                    <li>改进示例质量</li>
                    <li>增强情境学习能力</li>
                </ul>
            </div>
            
            <div class="metric-item">
                <h4>忠实度 (Faithfulness)</h4>
                <p>评估答案对事实的准确性，避免"幻觉"。低分可能需要：</p>
                <ul>
                    <li>调整模型参数</li>
                    <li>优化上下文利用</li>
                    <li>考虑更换或微调模型</li>
                </ul>
            </div>
            
            <div class="metric-item">
                <h4>上下文相关性 (Context Relevance)</h4>
                <p>评估检索文本与问题的相关程度。低分可能需要：</p>
                <ul>
                    <li>调整检索片段大小</li>
                    <li>优化Top-K参数</li>
                    <li>改进嵌入模型</li>
                </ul>
            </div>
        </div>
        """
