import re
from abc import ABC, abstractmethod
import aiohttp
import asyncio
import time
from typing import List, Tuple, Dict, Optional, Any

from llama_index.core import PromptTemplate, Response
from llama_index.core.evaluation import EvaluationResult
from llama_index.core.schema import NodeWithScore, TextNode
from llama_index.llms.openai import OpenAI
from llama_index.llms.openai.utils import ALL_AVAILABLE_MODELS, CHAT_MODELS

from ..models.config import Config
import logging

from llama_index.core.evaluation.answer_relevancy import (
    AnswerRelevancyEvaluator,
)
from llama_index.core.evaluation.faithfulness import (
    FaithfulnessEvaluator,
)
from llama_index.core.evaluation.context_relevancy import (
    ContextRelevancyEvaluator,
)

DEFAULT_EVALUATION_PROMPT = """您是一位专业的RAG系统评估专家，需要对以下问答对进行严格评估。请基于事实和客观标准进行分析。

待评估问答：
问题：{question}
标准答案：{correct_answer}
实际答案：{actual_answer}

评估说明：
1. 如果提供了标准答案，请严格对比标准答案与实际答案的差异进行评分
2. 如果标准答案为空，则基于以下标准评分：
   - 答案是否符合问题的要求
   - 答案的内容是否合理、准确
   - 答案的表达是否专业、清晰

评估维度（总分10分）：
1. 信息准确性（4分）
- 实际答案中的信息是否与标准答案一致
- 专业术语使用是否与标准答案保持一致
- 是否存在与标准答案相矛盾的内容
- 评分标准（分子不能超过分母）：
  * 4/4分：与标准答案完全一致，无任何偏差
  * 3/4分：基本一致，有1-2处小的差异
  * 2/4分：部分一致，有明显差异
  * 1/4分：大部分不一致
  * 0/4分：完全不一致或答非所问

2. 内容完整性（4分）
- 是否完整覆盖了标准答案中的所有关键信息点
- 信息的组织结构是否与标准答案相当
- 是否遗漏了标准答案中的重要内容
- 评分标准（分子不能超过分母）：
  * 4/4分：完整覆盖标准答案的所有要点
  * 3/4分：覆盖主要要点，有小的遗漏
  * 2/4分：遗漏部分关键要点
  * 1/4分：严重遗漏，仅包含少量要点
  * 0/4分：几乎没有覆盖标准答案要点

3. 表达质量（2分）
- 语言表达是否与标准答案同等专业
- 回答结构是否与标准答案同等清晰
- 是否比标准答案多出无关内容
- 评分标准（分子不能超过分母）：
  * 2/2分：表达与标准答案同等专业清晰
  * 1/2分：表达基本清晰，但不如标准答案专业
  * 0/2分：表达混乱或有大量冗余

评分要求：
1. 分数必须是整数，各维度分数之和等于总分
2. 必须严格按照各维度的评分标准打分，分子不能超过分母
3. 每个维度的分析要具体指出与标准答案的差异
4. 评分要客观公正，不轻易给满分
5. 如果标准答案为空，则基于问题本身和实际答案的合理性评分
6. 各维度得分相加不能超过10分

请严格按照以下格式输出（不要添加任何其他内容）：

分数： [准确性得分/4 + 完整性得分/4 + 表达得分/2]
原因： 准确性([x/4分])：[具体说明与标准答案的差异] | 完整性([x/4分])：[具体说明与标准答案的差异] | 表达([x/2分])：[具体说明与标准答案的差异]

"""


def _parse_evaluation_response(response: str) -> Tuple[Optional[int], Optional[str]]:
    """解析评估响应

    Args:
        response: 评估响应文本

    Returns:
        Tuple[Optional[int], Optional[str]]: (分数, 原因)
    """
    try:
        lines = response.strip().split("\n")

        # 查找分数行
        score_line = None
        for line in lines:
            if "分数：" in line or "评分：" in line:
                score_line = line
                break

        if not score_line:
            raise ValueError("未找到分数行")

        # 提取分数，处理形如 [3/4 + 2/4 + 2/2] 的格式
        score_text = score_line.replace("分数：", "").replace("评分：", "").strip()
        if "[" in score_text and "]" in score_text:
            # 提取方括号中的内容
            score_expr = score_text.strip("[]")
            # 分割各部分分数
            score_parts = score_expr.replace(" ", "").split("+")
            total_score = 0
            for part in score_parts:
                if "/" in part:
                    # 处理分数形式 (如 "3/4")
                    num, den = map(int, part.split("/"))
                    # 检查分子是否超过分母
                    if num > den:
                        raise ValueError(f"分子({num})超过分母({den})")
                    total_score += num
                else:
                    # 处理整数形式
                    total_score += int(part)
            score = total_score
        else:
            # 兼容旧格式：直接提取数字
            score_text = "".join(c for c in score_text if c.isdigit())
            score = int(score_text)

        # 查找原因
        reason_start = False
        reason_lines = []
        for line in lines:
            if "原因：" in line:
                reason_start = True
                line = line.replace("原因：", "").strip()
                if line:  # 如果原因和"原因："在同一行
                    reason_lines.append(line)
                continue

            if reason_start and line.strip():
                reason_lines.append(line.strip())

        reason = " ".join(reason_lines)

        # 验证结果
        if not (0 <= score <= 10):
            raise ValueError(f"分数 {score} 超出范围 [0-10]")

        if not reason:
            raise ValueError("未找到评分原因")

        # 检查原因中的分数格式
        # 检查准确性和完整性分数 (0-4分)
        for part in ["准确性", "完整性"]:
            matches = re.findall(rf"\[(\d+)/4分\]", reason)
            for match in matches:
                score_value = int(match)
                if score_value > 4:
                    raise ValueError(f"{part}分数 {score_value} 超出范围 [0-4]")

        # 检查表达分数 (0-2分)
        matches = re.findall(r"\[(\d+)/2分\]", reason)
        for match in matches:
            score_value = int(match)
            if score_value > 2:
                raise ValueError(f"表达分数 {score_value} 超出范围 [0-2]")

        return score, reason

    except Exception as e:
        logging.error(f"解析评估响应失败: {e}, 响应内容: {response}")
        # 如果是分子超过分母的错误，返回修正后的分数
        if "分子超过分母" in str(e):
            return 0, f"评分无效：{str(e)}，得分按0分处理"
        return None, f"解析失败: {str(e)}"


class BaseDocEvaluator(ABC):
    """文档评测基类，包含 RAG 三元组评估"""

    def __init__(self, model_name: str = "yi", config: Config = None):
        ALL_AVAILABLE_MODELS[
            config.evaluator.openai.model_name
        ] = config.evaluator.openai.context_size
        CHAT_MODELS[
            config.evaluator.openai.model_name
        ] = config.evaluator.openai.context_size
        self.logger = logging.getLogger(self.__class__.__name__)
        self.model_name = model_name
        self.session: Optional[aiohttp.ClientSession] = None
        self.concurrency = config.performance.query_concurrency if config else 5
        self._llm = OpenAI(
            api_base=config.evaluator.openai.api_base,
            api_key=config.evaluator.openai.api_key,
            model=config.evaluator.openai.model_name,
            max_tokens=config.evaluator.openai.max_tokens,
            temperature=config.evaluator.openai.temperature,
        )
        self._evaluation_prompt_template = DEFAULT_EVALUATION_PROMPT
        self._context_relevance = ContextRelevancyEvaluator(llm=self._llm)
        self._answer_relevance = AnswerRelevancyEvaluator(llm=self._llm)
        self._evaluate_faithfulness = FaithfulnessEvaluator(llm=self._llm)

    def get_llm(self) -> OpenAI:
        """获取 LLM 实例"""
        return self._llm

    async def init_session(self):
        """初始化session"""
        if self.session is None:
            self.session = aiohttp.ClientSession()

    async def close(self):
        """关闭会话"""
        if self.session:
            await self.session.close()
            self.session = None

    async def query_documents(
        self, query: str, doc_ids: List[str] = None
    ) -> Tuple[str, List[Dict[str, Any]], float]:
        try:
            start = time.time()
            answer, doc_nodes = await self._do_raw_query(query, doc_ids)
            return answer, doc_nodes, time.time() - start
        except Exception as e:
            logging.error(f"查询失败: {e}")
            raise

    @abstractmethod
    async def _do_raw_query(
        self, query: str, doc_ids: List[str] = None
    ) -> Tuple[str, List[Dict[str, Any]]]:
        """执行原始查询

        Args:
            query: 查询文本
            doc_ids: 文档ID列表，可选

        Returns:
            Tuple[str, List[Dict[str, Any]]]:
                - 回答内容
                - 召回文档列表
        """
        raise NotImplementedError("子类必须实现_do_raw_query方法")

    async def evaluate_rag_metrics(
        self, query: str, generated_answer: str, retrieved_docs: List[Dict[str, Any]]
    ) -> Dict[str, EvaluationResult]:
        """评估 RAG 三元组指标

        Args:
            query: 用户查询
            generated_answer: 生成的答案
            retrieved_docs: 检索到的文档列表

        Returns:
            Dict[str, float]: RAG 评估指标
        """
        source_nodes = []
        for doc in retrieved_docs:
            node = TextNode(text=doc["content"])
            source_nodes.append(NodeWithScore(node=node))
        resp: Response = Response(response=generated_answer, source_nodes=source_nodes)
        answer_relevance, faithfulness, context_relevance = await asyncio.gather(
            self.evaluate_answer_relevance(query, resp),
            self.evaluate_faithfulness(generated_answer, resp),
            self.evaluate_context_relevance(query, resp),
        )
        return {
            "answer_relevance": answer_relevance,
            "faithfulness": faithfulness,
            "context_relevance": context_relevance,
        }

    @abstractmethod
    def read_test_cases(self, file_path: str) -> List[Tuple[str, str, List[str]]]:
        """读取测试用例

        Args:
            file_path: 测试用例文件路径

        Returns:
            List[Tuple[str, str, List[str]]]: 测试用例列表，每个元素为(问题, 正确答案, 文档ID列表)
        """
        raise NotImplementedError("子类必须实现read_test_cases方法")

    async def evaluate_answer_relevance(
        self, query: str, resp: Response
    ) -> EvaluationResult:
        """评估答案相关性"""
        return await self._answer_relevance.aevaluate_response(query, resp)

    async def evaluate_faithfulness(
        self, generated_answer: str, resp: Response
    ) -> EvaluationResult:
        """"""
        return await self._evaluate_faithfulness.aevaluate_response(
            generated_answer, resp
        )

    async def evaluate_context_relevance(
        self, query: str, resp: Response
    ) -> EvaluationResult:
        return await self._context_relevance.aevaluate_response(query, resp)

    async def evaluate_answer_quality(
        self, question: str, correct_answer: str, actual_answer: str
    ) -> Tuple[int, str]:
        """评估答案质量

        Args:
            question: 问题
            correct_answer: 标准答案
            actual_answer: 实际答案

        Returns:
            Tuple[int, str]: (分数, 原因)
        """
        self.logger.info(f"开始评估问题: {question[:50]}...")
        prompt = PromptTemplate(self._evaluation_prompt_template).partial_format(
            question=question,
            correct_answer=correct_answer,
            actual_answer=actual_answer,
        )
        response = await self._llm.apredict(prompt)
        score, reason = _parse_evaluation_response(response)

        if score is None:
            return 0, reason or "评估失败"

        self.logger.info(f"评估完成，得分: {score}")
        return score, reason
