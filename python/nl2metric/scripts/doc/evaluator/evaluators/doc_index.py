from typing import List, Tuple, Dict, Any

from nl2document.common.msg.docservice import QueryDocumentResponse
from .base import BaseDocEvaluator
import logging
import pandas as pd
from ..models.exceptions import DataProcessError
from ..models.config import Config


class DocIndexEvaluator(BaseDocEvaluator):
    """DocIndex文档评测实现，包含 RAG 三元组评估"""

    def __init__(
        self, model_name: str = "yi", library: str = "string", config: Config = None
    ):
        super().__init__(model_name, config)
        self.library = library
        self.url = (
            config.api.doc_index_url
            if config
            else "http://127.0.0.1:9099/api/doc_index/query_document"
        )

    async def _do_raw_query(
        self, query: str, doc_ids: List[str] = None
    ) -> Tuple[str, List[Dict[str, Any]]]:
        """实际的查询实现"""
        await self.init_session()

        payload = {
            "query": query,
            "library": self.library,
            "model_type": self.model_name,
            "stream": False,
        }

        # 使用传入的 doc_ids 参数
        if doc_ids:
            payload["ids"] = doc_ids

        headers = {"Content-Type": "application/json", "Accept": "application/json"}

        async with self.session.post(
            self.url, headers=headers, json=payload
        ) as response:
            if response.status != 200:
                raise ValueError(f"请求失败，状态码: {response.status}")

            result = await response.json()
            response_model = QueryDocumentResponse(**result)

            if response_model.code != 0:
                raise ValueError(f"请求失败: {response_model.msg}")

            retrieved_docs = []
            if (
                response_model.data.sourceNodes
                and response_model.data.sourceNodes.textNodes
            ):
                for node in response_model.data.sourceNodes.textNodes:
                    retrieved_docs.append(
                        {
                            "id": node.nodeId,
                            "content": node.content,
                            "score": 0,  # DocIndex接口也没有返回相似度分数
                            "file_name": node.fileName,
                            "page": node.page,
                        }
                    )

            return response_model.data.content, retrieved_docs

    def read_test_cases(self, file_path: str) -> List[Tuple[str, str, List[str]]]:
        """读取DocIndex格式的测试用例"""
        try:
            df = pd.read_csv(file_path)
            required_columns = ["问题描述", "问题答复", "doc_ids"]
            if not all(col in df.columns for col in required_columns):
                raise ValueError(f"CSV文件中缺少必要的列: {', '.join(required_columns)}")

            def parse_doc_ids(ids_str: str) -> List[str]:
                try:
                    if pd.isna(ids_str):
                        return []
                    return [id.strip() for id in str(ids_str).split(",") if id.strip()]
                except Exception as e:
                    logging.warning(f"解析文档ID失败: {e}, 使用空列表")
                    return []

            result = [
                (row["问题描述"], row["问题答复"], parse_doc_ids(row["doc_ids"]))
                for _, row in df.iterrows()
            ]

            logging.info(f"读取到 {len(result)} 条测试用例")
            return result
        except Exception as e:
            raise DataProcessError(f"读取CSV文件失败: {e}")
