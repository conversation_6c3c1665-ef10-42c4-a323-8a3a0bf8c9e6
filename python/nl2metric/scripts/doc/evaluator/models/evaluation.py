import json
from dataclasses import dataclass
from typing import Dict, List, Any

from llama_index.core.evaluation import EvaluationResult
from pydantic import BaseModel, Field


class LatencyStats(BaseModel):
    """延迟统计"""

    min: float = Field(..., description="最小延迟(秒)")
    max: float = Field(..., description="最大延迟(秒)")
    avg: float = Field(..., description="平均延迟(秒)")
    p95: float = Field(..., description="P95延迟(秒)")
    p99: float = Field(..., description="P99延迟(秒)")


@dataclass
class EvaluationResult:
    """评估结果数据类"""

    question: str  # 问题
    correct_answer: str  # 正确答案
    actual_answer: str  # 实际答案
    score: float  # 评分
    reason: str  # 评分理由
    latency: float  # 查询延迟
    retrieved_docs: List[Dict[str, Any]] = None  # 召回的文档列表
    rag_metrics: Dict[str, EvaluationResult] = None  # RAG 三元组评估指标

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "question": self.question,
            "correct_answer": self.correct_answer,
            "actual_answer": self.actual_answer,
            "score": self.score,
            "reason": self.reason,
            "latency": self.latency,
            "答案相关性": self.rag_metrics.get("answer_relevance").score or 0,
            "忠实度": self.rag_metrics.get("faithfulness").score or 0,
            "上下文相关性": self.rag_metrics.get("context_relevance").score or 0,
            "retrieved_docs": json.dumps(
                self.retrieved_docs, indent=4, ensure_ascii=False
            ),
        }

    def _format_retrieved_docs(self) -> str:
        """格式化召回文档信息"""
        if not self.retrieved_docs:
            return "无召回文档"
        doc_infos = []
        for doc in self.retrieved_docs:
            doc_info = [
                f"文档ID: {doc.get('id', 'N/A')}",
                f"文件名: {doc.get('file_name', 'N/A')}",
                f"页码: {doc.get('page', 'N/A')}",
                f"相似度: {doc.get('score', 0.0):.3f}",
                f"内容: {doc.get('content', '')[:200]}...",
            ]
            doc_infos.append("\n".join(doc_info))
        return "\n---\n".join(doc_infos)


def _get_color(score):
    # 这里只是一个示例，实际应用中需要根据实际情况来定义颜色
    colors = {
        10: "28a745",
        9: "34c759",
        8: "17a2b8",
        7: "5856d6",
        6: "007aff",
        5: "ffc107",
        4: "ff9500",
        3: "ff3b30",
        2: "dc3545",
        1: "8e8e93",
        0: "636366",
    }
    return colors.get(score, "gray")


class EvaluationStats(BaseModel):
    """评估统计结果"""

    total_count: int = Field(..., description="总评估数量")
    success_count: int = Field(..., description="成功数量")
    fail_count: int = Field(..., description="失败数量")
    avg_score: float = Field(..., description="平均分数")
    avg_latency: float = Field(..., description="平均延迟(秒)")
    score_distribution: Dict[int, int] = Field(..., description="分数分布")
    latency_stats: LatencyStats = Field(..., description="延迟统计")
    avg_rag_metrics: Dict[str, float] = Field(
        default_factory=lambda: {
            "answer_relevance": 0.0,
            "faithfulness": 0.0,
            "context_relevance": 0.0,
        },
        description="平均 RAG 指标",
    )

    @classmethod
    def from_results(cls, results: List[EvaluationResult]) -> "EvaluationStats":
        if not results:
            raise ValueError("结果列表不能为空")

        total_count = len(results)
        latencies = [r.latency for r in results]
        sorted_latencies = sorted(latencies)

        # 计算平均 RAG 指标
        rag_metrics_sum = {
            "answer_relevance": 0.0,
            "faithfulness": 0.0,
            "context_relevance": 0.0,
        }

        for result in results:
            if result.rag_metrics:
                for metric, value in result.rag_metrics.items():
                    rag_metrics_sum[metric] += value.score or 0

        avg_rag_metrics = {
            metric: value / total_count for metric, value in rag_metrics_sum.items()
        }

        return cls(
            total_count=total_count,
            success_count=sum(1 for r in results if r.score > 0),
            fail_count=sum(1 for r in results if r.score == 0),
            avg_score=sum(r.score for r in results) / total_count,
            avg_latency=sum(latencies) / total_count,
            score_distribution={
                score: sum(1 for r in results if r.score == score)
                for score in range(11)
            },
            latency_stats=LatencyStats(
                min=min(latencies),
                max=max(latencies),
                avg=sum(latencies) / total_count,
                p95=sorted_latencies[int(total_count * 0.95)],
                p99=sorted_latencies[int(total_count * 0.99)],
            ),
            avg_rag_metrics=avg_rag_metrics,
        )

    def to_markdown(self) -> str:
        """生成Markdown格式的统计报告"""
        sections = [
            self._basic_stats_section(),
            self._score_distribution_section(),
            self._latency_stats_section(),
        ]
        return "\n\n".join(sections)

    def _basic_stats_section(self) -> str:
        return (
            "# 评估结果统计报告\n\n"
            "## 1. 基础统计\n"
            "| 指标 | 数值 |\n"
            "|------|------|\n"
            f"| 总评估数量 | {self.total_count} |\n"
            f"| 成功数量 | {self.success_count} |\n"
            f"| 失败数量 | {self.fail_count} |\n"
            f"| 平均分数 | {self.avg_score:.2f} |\n"
            f"| 平均延迟 | {self.avg_latency:.2f}s |"
        )

    def _score_distribution_section(self) -> str:
        rows = ["## 2. 分数分布", "| 分数 | 数量 | 比例 |", "|------|------|------|"]

        for score, count in sorted(self.score_distribution.items()):
            percentage = (count / self.total_count) * 100
            rows.append(f"| {score}分 | {count} | {percentage:.1f}% |")

        return "\n".join(rows)

    def _latency_stats_section(self) -> str:
        return (
            "## 3. 延迟统计\n"
            "| 指标 | 数值(秒) |\n"
            "|------|----------|\n"
            f"| 最小延迟 | {self.latency_stats.min:.2f} |\n"
            f"| 最大延迟 | {self.latency_stats.max:.2f} |\n"
            f"| 平均延迟 | {self.latency_stats.avg:.2f} |\n"
            f"| P95延迟 | {self.latency_stats.p95:.2f} |\n"
            f"| P99延迟 | {self.latency_stats.p99:.2f} |"
        )

    def to_html(self) -> str:
        """生成HTML格式的统计报告"""
        return f"""
        <div class="evaluation-report">
            {self._basic_stats_panel()}
            {self._score_distribution_panel()}
            {self._latency_stats_panel()}
        </div>
        """

    def _basic_stats_panel(self) -> str:
        success_rate = (self.success_count / self.total_count) * 100
        return f"""
        <div class="stats-panel basic-stats">
            <h2>基础统计</h2>
            <div class="stats-grid">
                <div class="stats-item">
                    <div class="stats-label">总评估数量</div>
                    <div class="stats-value">{self.total_count}</div>
                </div>
                <div class="stats-item">
                    <div class="stats-label">成功数量</div>
                    <div class="stats-value success">{self.success_count}</div>
                </div>
                <div class="stats-item">
                    <div class="stats-label">失败数量</div>
                    <div class="stats-value fail">{self.fail_count}</div>
                </div>
                <div class="stats-item">
                    <div class="stats-label">成功率</div>
                    <div class="stats-value">{success_rate:.1f}%</div>
                </div>
                <div class="stats-item">
                    <div class="stats-label">平均分数</div>
                    <div class="stats-value">{self.avg_score:.2f}</div>
                </div>
                <div class="stats-item">
                    <div class="stats-label">平均延迟</div>
                    <div class="stats-value">{self.avg_latency:.2f}s</div>
                </div>
            </div>
        </div>
        """

    def _score_distribution_panel(self) -> str:
        # 使用列表推导式简化生成过程
        distribution_items = [
            f"""
            <div class="score-bar">
                <div class="score-label">{score}分</div>
                <div class="score-progress">
                    <div class="score-progress-bar" style="width: {percentage}%; background-color: #{_get_color(score)}">
                    </div>
                </div>
                <div class="score-value">{percentage:.1f}%</div>
            </div>
            """
            for score, count in sorted(self.score_distribution.items())
            if (percentage := (count / self.total_count) * 100)
        ]

        return f"""
        <div class="score-distribution">
            {''.join(distribution_items)}
        </div>
        """

    # 假设有一个方法用于获取不同分数的颜色

    def _latency_stats_panel(self) -> str:
        return f"""
        <div class="stats-panel latency-stats">
            <h2>延迟统计</h2>
            <div class="stats-grid">
                <div class="stats-item">
                    <div class="stats-label">最小延迟</div>
                    <div class="stats-value">{self.latency_stats.min:.2f}s</div>
                </div>
                <div class="stats-item">
                    <div class="stats-label">最大延迟</div>
                    <div class="stats-value">{self.latency_stats.max:.2f}s</div>
                </div>
                <div class="stats-item">
                    <div class="stats-label">平均延迟</div>
                    <div class="stats-value">{self.latency_stats.avg:.2f}s</div>
                </div>
                <div class="stats-item">
                    <div class="stats-label">P95延迟</div>
                    <div class="stats-value">{self.latency_stats.p95:.2f}s</div>
                </div>
                <div class="stats-item">
                    <div class="stats-label">P99延迟</div>
                    <div class="stats-value">{self.latency_stats.p99:.2f}s</div>
                </div>
            </div>
        </div>

        <div class="stats-panel latency-stats">
            <h2>RAG评估指标</h2>
            <div class="stats-grid">
                <div class="stats-item">
                    <div class="stats-label">答案相关性</div>
                    <div class="stats-value">{self.avg_rag_metrics.get('answer_relevance', 0):.3f}</div>
                </div>
                <div class="stats-item">
                    <div class="stats-label">忠实度</div>
                    <div class="stats-value">{self.avg_rag_metrics.get('faithfulness', 0):.3f}</div>
                </div>
                <div class="stats-item">
                    <div class="stats-label">上下文相关性</div>
                    <div class="stats-value">{self.avg_rag_metrics.get('context_relevance', 0):.3f}</div>
                </div>
            </div>
        </div>
        """
