from pathlib import Path

import pandas as pd
from dotenv import load_dotenv
from llama_index.core.indices.utils import embed_nodes
from llama_index.core.schema import TextNode


# specify path to easily change env in docker
env_file = Path(__file__).parent.parent / ".env"
load_dotenv(dotenv_path=env_file, override=True)
# both app_config and doc_config needs load_doten
from common.llm.embedding import get_embedding_model
from config import doc_config
from nl2document.common.vector.vector_store import get_vector_store

embed_model, _, _ = get_embedding_model(doc_config.doc_embedding_model)


# pandas read csv file
def read_csv_file(file_path) -> pd.DataFrame:
    return pd.read_csv(file_path)


if __name__ == "__main__":
    df = read_csv_file("副本问小政常见问题V2024829 - Sheet3.csv")
    nodes = []
    for index, row in df.iterrows():
        # print(row['问题描述'], row['问题答复'])
        nodes.append(
            TextNode(
                text=f"question: {row['问题描述']} \n answer: {row['问题答复']}",
                metadata={"file_id": "test_file_id"},
            )
        )

    # for node in nodes:
    #     print(node.get_content())

    id_to_embed_map = embed_nodes(nodes, embed_model, show_progress=True)
    for node in nodes:
        node.embedding = id_to_embed_map[node.node_id]
    vector_store = get_vector_store()
    vector_store.add(nodes)
    print("done")
