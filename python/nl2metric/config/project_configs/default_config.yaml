default:
  enable_time_dimension: True
  qwen3_agent: &qwen3_agent
    model_name: "qwen3_32b"
    openai_api_base: "http://123.181.192.99:29002/v1"
  qwen3_agent_nothink: &qwen3_agent_nothink
    <<: *qwen3_agent
    extra_body:
      chat_template_kwargs:
        enable_thinking: False
  agent_model: &agent_model
    # ds-qwen_agent 14b
    <<: *qwen3_agent
  ner_model:
    <<: *qwen3_agent_nothink
  condense_model:
    <<: *qwen3_agent_nothink
  judge_model:
    <<: *agent_model
  base_model: &base_model
    model_name: "qwen3_32b"
    openai_api_base: "http://8.147.105.203:8889/v1"
  base_model_nothink:
    <<:  *base_model
    extra_body:
      chat_template_kwargs:
        enable_thinking: False
  code_model:
    <<: *base_model
  special_subchain_params:
    nl2intent:
      type: "nl2intent_by_tag_v2"
      model_type: "qwen3_agent_nothink"
    nl2metric_group_bys:
      model_type: "qwen3_agent_nothink"
    nl2metric_metrics:
      model_type: "qwen3_agent_nothink"
    nl2metric_order_bys:
      model_type: "qwen3_agent_nothink"
    nl2metric_where:
      model_type: "qwen3_agent_nothink"
    attr_analysis_time:
      model_type: "qwen3_agent_nothink"
    nl2time_dimension:
      model_type: "qwen3_agent_nothink"
    nl2metric_time_query:
      type: "nl2metric_time_query_v2"
      model_type: "qwen3_agent_nothink"
