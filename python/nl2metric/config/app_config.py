#! coding: utf-8

import os
import getpass
from distutils.util import strtobool
from os.path import dirname, join
from pathlib import Path
from urllib.parse import urljoin

from langchain.globals import set_debug

from common.jinja2.utils import register_filters


register_filters()
jaeger_url = os.environ.get("JAEGER_URL", "http://192.168.110.16:14268/api/traces")
jaeger_sample = float(os.environ.get("JAEGER_SAMPLE", 0.1))
disable_json_logger = strtobool(os.environ.get("DISABLE_JSON_LOGGER", "true"))
log_root = str(os.environ.get("LOG_DIR", os.path.join(os.getcwd(), "logs")))
elk_log_root = str(os.environ.get("ELK_LOG_DIR", os.path.join(os.getcwd(), "logs")))
run_dir = os.environ.get("RUN_DIR", os.getcwd())
DEFAULT_BIND_PORT = 9099
SHARED_SERVICE_PORT = 9199
EMBEDDING_SERVICE_PORT = 9299
APP_NAME = "nl2metric"

NL2METRIC_PROJECT_ROOT = dirname(dirname(__file__))
PYTHON_PROJECT_ROOT = dirname(NL2METRIC_PROJECT_ROOT)

DBT_PROJECT_DIR = os.environ.get(
    "DBT_PROJECT_DIR", join(dirname(PYTHON_PROJECT_ROOT), "dbt_bank_test")
)

PROJECT_ROOT = dirname(PYTHON_PROJECT_ROOT)

# only for langfuse display
HOST_USERNAME = getpass.getuser()
CLUSTER_ID = os.environ.get("CLUSTER_ID", HOST_USERNAME)

TEST_CONFIG_PATH = os.environ.get(
    "TEST_CONFIG_PATH",
    join(
        dirname(PYTHON_PROJECT_ROOT),
        "src/server/MetricStore/metric2sql/jiaohang-tests.yml",
    ),
)
SEMANTIC_MODEL_PATH = os.environ.get(
    "SEMANTIC_MODEL_PATH",
    join(
        dirname(PYTHON_PROJECT_ROOT),
        "src/server/MetricStore/data/jiaohang-model.yml",
    ),
)

ENABLE_PROMPT_STUDIO = strtobool(os.environ.get("ENABLE_PROMPT_STUDIO", "False"))

DATABASE_URL = os.environ.get(
    "DATABASE_URL",
    "mysql://root:AE3~sByGLG-.Prhwdpgb@**************:60155/askbi_mix?allowPublicKeyRetrieval=true",
)
AI_DATA_OPERATOR_DATABASE_URL = os.environ.get(
    "AI_DATA_OPERATOR_DATABASE_URL",
    "mysql://root:AE3~sByGLG-.Prhwdpgb@**************:60155/ai_data_operator?allowPublicKeyRetrieval=true",
)

embedding_model = os.environ.get("embedding_model", "HuggingFace")
embedding_service_model = os.environ.get("embedding_service_model", "BGE")
bge_model_path = os.environ.get(
    "bge_model_path",
    "/data2/public_file/LLM_model/b5c9d86d763d9945f7c0a73e549a4a39c423d520",
)
bge_rerank_model_name_or_path = os.environ.get(
    "bge_rerank_model_name_or_path", "BAAI/bge-reranker-large"
)
hugging_face_embedding_model_path = os.environ.get(
    "hugging_face_embedding_model_path",
    "shibing624/text2vec-base-chinese-paraphrase",
)
LLM_BASE_ADDRESS = os.environ.get("LLM_CHAT_URL_HOST", "http://**************:50010")
LLM_BASE_URL = urljoin(LLM_BASE_ADDRESS, "api/llm/chat")
LLM_BASE_MODEL = str(os.environ.get("llmbase_model", "gpt-3.5-turbo"))
ENABLE_PRE_FILTER = strtobool(os.environ.get("enable_pre_filter", "y"))
ENABLE_TIME_FILTER = strtobool(os.environ.get("enable_time_filter", "n"))
ASK_BI_HOST = os.environ.get("ASK_BI_HOST", "http://127.0.0.1:8000")
MIXTRAL_VLLM_ADDRESS = os.environ.get(
    "mixtral_vllm_address", "http://*************:30010"
)
VLLM_MIXTRAL_MODEL_NAME = "/model/mixtral-8-7b"

YI_34B_ADDRESS = os.environ.get("yi_1.5_34b_address", "http://**************:10042")
YI_34B_MODEL_NAME = "yi"

YI_34B_16K_ADDRESS = os.environ.get(
    "yi_1.5_34b_16k_address", "http://*************:8888"
)

QWEN3_8B_MODEL_NAME = os.environ.get("qwen3_8b_model_name", "qwen3")
QWEN3_8B_ADDRESS = os.environ.get("qwen3_8b_address", "http://***********:8886")

DEEPSEEK_14B_MODEL_NAME = "deepseek"
DEEPSEEK_14B_ADDRESS = os.environ.get(
    "deepseek_14b_address", "http://*************:9999"
)

DEEPSEEK_AGENT_14B_MODEL_NAME = "deepseek"
DEEPSEEK_AGENT_14B_ADDRESS = os.environ.get(
    "deepseek_agent_14b_address", "http://*************:7777"
)

DEEPSEEK_R1_671B_MODEL_NAME = os.environ.get(
    "deepseek_r1_671b_model_name", "DeepSeek-R1-671b"
)
DEEPSEEK_R1_671B_API_KEY = os.environ.get(
    "deepseek_r1_671b_api_key", "sk-mbwLzVpFncmL2QcS8VEM3Q"
)
DEEPSEEK_R1_671B_ADDRESS = os.environ.get(
    "deepseek_r1_671b_address", "http://************:80"
)

BAOWU_GLM_4_9B_ADDRESS = os.environ.get(
    "baowu_glm_4_9b_address", "http://**************:8087"
)
PRE_GLM_4_9B_ADDRESS = os.environ.get(
    "pre_glm_4_9b_address", "http://**************:8089"
)
PRE_GLM_4_9B_AGENT_ADDRESS = os.environ.get(
    "pre_glm_4_9b_agent_address", "http://**************:8090"
)
MODEL_TYPE_GLM_4_9b_kxc_ADDRESS = os.environ.get(
    "model_type_glm_4_9b_kxc_address", "http://**************:8088"
)
QWQ_32B_MODEL_NAME = "./QwQ-32B"
QWQ_32B_ADDRESS = os.environ.get("qwq_32b_address", "http://**************:2000")


MODEL_TYPE_VLLM_MIXTRAL = "vllm-mixtral-8x7b-chat"
MODEL_TYPE_POE_MIXTRAL = "poe-mixtral-8x7b-chat"
MODEL_TYPE_QWEN_72B = "poe-qwen-72b-chat"
MODEL_TYPE_GPT35 = "gpt-3.5-turbo"
MODEL_TYPE_GPT4 = "gpt-4-turbo-preview"
MODEL_TYPE_O1 = "o1"
MODEL_TYPE_AZURE_GPT = "azure-gpt"
MODEL_TYPE_ZHIPU_GLM4 = "zhipu-glm-4"
MODEL_TYPE_ZHIPU_GLM3_32B = "zhipu-chatglm3_32b"
MODEL_TYPE_YI_34B = "yi_1.5_34b"
MODEL_TYPE_YI_34B_16K = "yi_1.5_34b_16k"
MODEL_TYPE_BAICHUAN = "baichuan2"
MODEL_TYPE_QIANFAN_ERNIE_SPEED_128K = "ERNIE-Speed-128K"
MODEL_TYPE_MOCK = "mock"
MODEL_TYPE_BAOWU_GLM_4_9B = "baowu-glm-4-9b"
MODEL_TYPE_PRE_GLM_4_9B = "pre-glm-4-9b"
MODEL_TYPE_PRE_GLM_4_9B_AGENT = "pre-glm-4-9b-agent"
MODEL_TYPE_CHAT_LAW_7B = "chat_law_7b"
MODEL_TYPE_GLM_4_9b_kxc = "pre-glm-4-9b-kxc"
MODEL_TYPE_DEEPSEEK_14B = "deepseek-14b"
MODEL_TYPE_DEEPSEEK_R1_671B = "deepseek-r1-671b"
MODEL_TYPE_DEEPSEEK_AGENT_14B = "deepseek-agent-14b"
MODEL_TYPE_QWQ_32B = "qwq_32b"
MODEL_TYPE_QWEN3_8B = "qwen3_8b"

MODEL_TYPE_FAKE_PASSTHROUGH = "fake_passthrough"

DIMENSION_VALUE_FILTER_THRESHOLD = int(
    os.environ.get("DIMENSION_VALUE_FILTER_THRESHOLD", "5")
)

CHAT_MODEL_TYPE = os.environ.get("CHAT_MODEL_TYPE", MODEL_TYPE_DEEPSEEK_14B)
AGENT_MODEL_TYPE = os.environ.get("AGENT_MODEL_TYPE", MODEL_TYPE_DEEPSEEK_AGENT_14B)
CODE_MODEL_TYPE = "Coder"
CODE_MODEL_TYPE_NAME = os.environ.get("CODE_MODEL_TYPE_NAME", DEEPSEEK_14B_MODEL_NAME)
CODE_MODEL_TYPE_ADDRESS = os.environ.get(
    "CODE_MODEL_TYPE_ADDRESS", DEEPSEEK_14B_ADDRESS
)
CODE_MODEL_TYPE_TEMPERATURE = float(os.environ.get("CODE_MODEL_TYPE_TEMPERATURE", "0"))
CODE_MODEL_TYPE_MAX_TOKENS = int(os.environ.get("CODE_MODEL_TYPE_MAX_TOKENS", "8192"))

CACHE_REFRESH_INTERVAL_SECONDS = int(os.environ.get("CACHE_REFRESH_INTERVAL", "600"))
DEFAULT_MODEL_TYPE = os.environ.get("DEFAULT_MODEL_TYPE", MODEL_TYPE_VLLM_MIXTRAL)

PROJECT_NAME = os.environ.get("project_name", "示例项目")
MIXTRAL_VLLM_URL = urljoin(
    MIXTRAL_VLLM_ADDRESS,
    "v1/chat/completions",
)

NL2ALL_NAME = os.environ.get("NL2ALL_NAME", "电信经分BI")  # 中原银行、示例项目、天弘

AZURE_API_KEY = os.environ.get("AZURE_API_KEY", "********************************")
AZURE_DEPLOYMENT_NAME = os.environ.get("AZURE_DEPLOYMENT_NAME", "turbo-16k")
AZURE_API_VERSION = os.environ.get("AZURE_API_VERSION", "2023-05-15")
AZURE_API_TYPE = os.environ.get("AZURE_API_TYPE", "azure")
AZURE_ENDPOINT = os.environ.get("AZURE_ENDPOINT", "https://data2.openai.azure.com")
OPENAI_PROXY = os.environ.get("OPENAI_PROXY")


ZHIPU_API_KEY = os.environ.get(
    "ZHIPU_API_KEY", "********************************.tXiNFopvX6VA8PxI"
)

ALIYUN_ACCESS_KEY = os.environ.get("ALIYUN_ACCESS_KEY", "LTAI5tE9cCGRqBfP9mT6uMM4")
ALIYUN_ACCESS_ID = os.environ.get("ALIYUN_ACCESS_ID", "******************************")
WEB_SEARCH_TOOL = os.environ.get("WEB_SEARCH_TOOL", "aliyun").lower()
BOCHA_WEB_SEARCH_API_KEY = os.environ.get(
    "BOCHA_API_KEY", "sk-d1e6d6ed1ff349659bef8aed3814f433"
)

GPT_35_API_KEY = os.environ.get(
    "GPT_35_API_KEY", "***************************************************"
)
GPT_4_API_KEY = os.environ.get(
    "GPT_4_API_KEY", "***************************************************"
)
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY", "")
BAICHUAN_API_KEY = os.environ.get("BAICHUAN_API_KEY", "YOUR_API_KEY")
BAICHUAN_ADDRESS = os.environ.get("BAICHUAN_ADDRESS", "http://*************:8888")
BAICHUAN_API_BASE = urljoin(
    BAICHUAN_ADDRESS,
    "v1/chat/completions",
)
QIANFAN_ACCESS_KEY = os.environ.get("QIANFAN_ACCESS_KEY", "your_iam_ak")
QIANFAN_SECRET_KEY = os.environ.get("QIANFAN_SECRET_KEY", "your_iam_sk")

ENABLE_QUESTION_DECOMPOSITION = strtobool(
    os.environ.get("ENABLE_QUESTION_DECOMPOSITION", "n")
)

LANGCHAIN_DEBUG = strtobool(os.environ.get("langchain_debug", "y"))
set_debug(LANGCHAIN_DEBUG)

FILE_PROMPT_SELECTOR_DIR = os.environ.get("FILE_PROMPT_SELECTOR_DIR", "./")

AGENT_NAME = os.environ.get("AGENT_NAME", "数巅科技")


def get_default_project_id():
    from common.db_model.model import get_semantic_project_by_name

    return get_semantic_project_by_name(PROJECT_NAME).id


ENABLE_ASK_BI_METASTORE = strtobool(os.environ.get("ENABLE_ASK_BI_METASTORE", "True"))

ENABLE_LANGFUSE = strtobool(os.environ.get("ENABLE_LANGFUSE", "True"))

ENABLE_PROGRESS_STATUS = strtobool(os.environ.get("ENABLE_PROGRESS_STATUS", "True"))

LANGFUSE_PUBLIC_KEY = os.environ.get(
    "LANGFUSE_PUBLIC_KEY", "pk-lf-7373ae64-e54c-44ee-bd06-58423cd8056d"
)
LANGFUSE_SECRET_KEY = os.environ.get(
    "LANGFUSE_SECRET_KEY", "******************************************"
)
LANGFUSE_HOST = os.environ.get("LANGFUSE_HOST", "http://**************:3000")

MAX_CONCURRENCY = int(os.environ.get("MAX_CONCURRENCY", 8))

PARALLEL_INTENT = strtobool(os.environ.get("PARALLEL_INTENT", "True"))
if PARALLEL_INTENT and MAX_CONCURRENCY < 6:
    MAX_CONCURRENCY = 6

ENABLE_LANGCHAIN_DEBUG_LOGGER = strtobool(
    os.environ.get("ENABLE_LANGCHAIN_DEBUG_LOGGER", "False")
)

ENABLE_ASKBI = strtobool(os.environ.get("ENABLE_ASKBI", "True"))

ENABLE_NL2DOCUMENT = strtobool(os.environ.get("ENABLE_NL2DOCUMENT", "False"))

ENABLE_NL2DOCUMENT_BUILDER = strtobool(
    os.environ.get("ENABLE_NL2DOCUMENT_BUILDER", "False")
)

ENABLE_REPORT_GENERATE = strtobool(os.environ.get("ENABLE_REPORT_GENERATE", "True"))
if ENABLE_REPORT_GENERATE:
    print("report_generate is enabled")
ENABLE_YISHUITONG = strtobool(os.environ.get("ENABLE_YISHUITONG", "False"))
if ENABLE_YISHUITONG:
    print("yishuitong is enabled")

ENABLE_LLM_CONFIDENCE_CHECK = strtobool(
    os.environ.get("ENABLE_LLM_CONFIDENCE_CHECK", "False")
)
LLM_CONFIDENCE_SCORE = float(os.environ.get("LLM_CONFIDENCE_SCORE", 0))

USE_ASYNC_LANGCHAIN_LOG = strtobool(os.environ.get("USE_ASYNC_LANGCHAIN_LOG", "False"))

EMBEDDING_STORE_PATH = os.environ.get("EMBEDDING_STORE_PATH", "./embedding_store")

PRE_HEAT_PROJECTS = os.environ.get("PRE_HEAT_PROJECTS", "")
PRE_HEAT_MODEL_NAMES = os.environ.get("PRE_HEAT_MODEL_NAMES", "")

VLLM_MODEL_ADD_V1_SUFFIX = bool(
    strtobool(os.environ.get("VLLM_MODEL_ADD_V1_SUFFIX", "True"))
)
VLLM_MODEL_NAME = os.environ.get("VLLM_MODEL_NAME", "")
VLLM_MODEL_URL = os.environ.get("VLLM_MODEL_URL", "")
VLLM_MODEL_API_KEY = os.environ.get("VLLM_MODEL_API_KEY", "EMPTY")
VLLM_MODEL_TEMPERATURE = float(os.environ.get("VLLM_MODEL_TEMPERATURE", 0.01))
VLLM_MODEL_MAX_TOKEN = int(os.environ.get("VLLM_MODEL_MAX_TOKEN", 8192))

CHAT_LAW_7B_NAME = os.environ.get("CHAT_LAW_7B_NAME", "chat-law-7b")
CHAT_LAW_7B_ADDRESS = os.environ.get(
    "CHAT_LAW_7B_ADDRESS", "http://**************:40011"
)

# baowu has 3000+ dimension value
DIMENSION_VALUE_INDEX_THRESHOLD = int(
    os.environ.get("DIMENSION_VALUE_INDEX_THRESHOLD", 3000)
)

embedding_api_base = os.environ.get(
    "embedding_api_base", "http://**************:9997/v1"
)
embedding_model_name = os.environ.get(
    "embedding_model_name", "alime-embedding-large-zh"
)
embedding_api_key = os.environ.get("embedding_api_key", "EMPTY")
embedding_api_dimension_num = int(os.environ.get("embedding_api_dimension_num", 1024))
# JIEBA_EXACT_MATCH_THRESHOLD = int(os.environ.get("JIEBA_EXACT_MATCH_THRESHOLD", 6))

PARAM_EXTRACT_RETRY_LIMIT = int(os.environ.get("PARAM_EXTRACT_RETRY_LIMIT", 3))

general_parser_chunk_size = int(os.environ.get("general_parser_chunk_size", 500))
general_parser_chunk_overlap = int(os.environ.get("general_parser_chunk_overlap", 50))

ASYNC_CACHED_SERVER_PATH = os.environ.get("ASYNC_CACHED_SERVER_PATH", "cached_server")

ENABLE_API_WORKER = strtobool(os.environ.get("ENABLE_API_WORKER", "False"))
ENABLE_HINT_API = strtobool(os.environ.get("ENABLE_HINT_API", "False"))
API_WORKER_NUM = int(os.environ.get("API_WORKER_NUM", 8))
API_WORKER_THREAD_NUM = int(os.environ.get("API_WORKER_THREAD_NUM", 2))

ZHIPU_WEB_SEARCH_API_KEY = os.environ.get(
    "ZHIPU_WEB_SEARCH_API_KEY", "0a5a2bd9f63440c99a858112fce31072.GWB3SyRD0s15yQUV"
)

llm_model_api_base = os.environ.get(
    "llm_model_api_base", "http://**************:2000/v1"
)
llm_model_api_key = os.environ.get("llm_model_api_key", "xxx")
llm_model_id = os.environ.get("llm_model_id", "./QwQ-32B")


xengine_backend_host = os.environ.get("xengine_backend_host", "**************")
xengine_backend_port = int(os.environ.get("xengine_backend_port", 32566))
xengine_username = os.environ.get("xengine_username", "admin")
xengine_password = os.environ.get("xengine_password", "xxx")
xengine_database = os.environ.get("xengine_database", "dipeak")
doc_endpoint = os.environ.get("doc_endpoint", "http://127.0.0.1:9099")

AGENT_CSV_PATH = os.environ.get("agent_csv_path", "agent_csv/")
AGENT_CSV_TTL = int(os.environ.get("agent_csv_ttl", 7 * 24 * 3600))
AGENT_RESULT_TTL = int(os.environ.get("agent_result_ttl", 7 * 24 * 3600))
AGENT_COT_TTL = int(os.environ.get("agent_cot_ttl", 3600))
AGENT_COT_WAIT_TTL = int(os.environ.get("agent_cot_wait_ttl", 600))
MANUAL_SELECT_TTL = int(os.environ.get("manual_select_ttl", 3600))
AGENT_NEWFILE_PATH = os.environ.get("agent_newfile_path", "agent_result/")
AGENT_CSV_DISPLAY_ROWS = int(os.environ.get("agent_csv_display_rows", 12))
ENABLE_DUMP_MEMORY = strtobool(os.environ.get("ENABLE_DUMP_MEMORY", "False"))
chart_insight_model_type = os.environ.get(
    "chart_insight_model_type", MODEL_TYPE_DEEPSEEK_14B
)
