from typing import List, Dict

from langchain_core.runnables import RunnableConfig
from pydantic import BaseModel

from common.types.base import CHAIN_META, ChainMeta
import yaml

from vector_store.data_models import (
    AgentType,
    HintTagType,
    TermTypeEnum,
    PROJECT_HINT,
)
from vector_store.hint_search_engine import (
    hint_search_engine,
    QueryResult,
    HintSearchResult,
)

from langchain_core.runnables import RunnableParallel, RunnableLambda


class HintRetrieverData(BaseModel):
    # "agent", "extra_info", "tags", "type"
    agents: List[AgentType]
    extra_info: str
    tags: List[HintTagType]
    type: TermTypeEnum


def retrieve_business_term(
    question: str,
    condense_query_after: bool,
    config: RunnableConfig,
) -> dict[AgentType, List[str]]:
    # Extract config values
    project_id = config[CHAIN_META][ChainMeta.PROJECT_ID]
    scene_id = config[CHAIN_META][ChainMeta.MODEL_ID]
    limit = (
        config[CHAIN_META]
        .get(ChainMeta.ADDITIONAL_INFO, {})
        .get("hint_dynamic_limit", 3)
    )
    dense_weight = (
        config[CHAIN_META]
        .get(ChainMeta.ADDITIONAL_INFO, {})
        .get("hint_dense_weight", 0.5)
    )
    sparse_weight = (
        config[CHAIN_META]
        .get(ChainMeta.ADDITIONAL_INFO, {})
        .get("hint_sparse_weight", 0.5)
    )
    if sparse_weight + dense_weight != 1:
        raise ValueError("sparse_weight + dense_weight != 1")
    # Build filters
    filter = []
    semantic_scene_ids = [PROJECT_HINT]
    if scene_id != PROJECT_HINT and scene_id:
        semantic_scene_ids.append(scene_id)
    filter.append(f"semantic_scene_id in {semantic_scene_ids}")
    if project_id:
        filter.append(f"semantic_project_id == '{project_id}'")

    # Define filter strings
    fixed_filter = " and ".join(filter + ["type == 'fixed'"])
    dynamic_filter = " and ".join(filter + ["type == 'dynamic'"])

    # Define individual search functions
    def fixed_search(_: str, agent_type: AgentType) -> list[HintRetrieverData]:
        fixed_list: QueryResult = hint_search_engine.query(
            filter=f"{fixed_filter} and ARRAY_CONTAINS(agents, '{agent_type.value}')",
            output_fields=["agents", "extra_info", "tags", "type"],
        )
        return [HintRetrieverData(**item) for item in fixed_list.items]

    def dynamic_search(question: str, agent_type: AgentType) -> list[HintRetrieverData]:
        hybrid_results: HintSearchResult = hint_search_engine.hybrid_search(
            question,
            filter=f"{dynamic_filter} and ARRAY_CONTAINS(agents, '{agent_type.value}')",
            limit=limit,
            sparse_weight=sparse_weight,
            dense_weight=dense_weight,
            output_fields=["agents", "extra_info", "tags", "type"],
        )
        return [
            HintRetrieverData(**item.get("entity", {})) for item in hybrid_results.items
        ]

    agent_types: List[AgentType] = (
        [
            agent_type_enum
            for agent_type_enum in AgentType
            if agent_type_enum != AgentType.AGENT_TYPE_CONDENSE
        ]
        if condense_query_after
        else [AgentType.AGENT_TYPE_CONDENSE]
    )
    parallel_runner = RunnableParallel(
        {
            agent_type: RunnableParallel(
                fixed=RunnableLambda(fixed_search).bind(agent_type=agent_type),
                dynamic=RunnableLambda(dynamic_search).bind(agent_type=agent_type),
            )
            for agent_type in agent_types
        }
    )
    results = parallel_runner.invoke(question)
    agent_hints = format_hints_for_specific_agents_yaml(results)
    return agent_hints


def format_hints_for_specific_agents_yaml(
    data: Dict[str, Dict[str, List[HintRetrieverData]]]
) -> Dict[str, str]:
    """
    处理指定 AgentType 的提示信息，将其按 HintTagType 分类合并，
    并将结果格式化为 YAML 字符串。extra_info 会被添加到文本后。
    对于 fixed 类型的 extra_info，会进行排序。
    Args:
        data (dict): 包含不同类型规则和提示的字典。
                     外层 key 是 agent_type.value (str)，内层 key 是 term_type.value (str)。
    Returns:
        dict: 字典，其中 key 是 agent_hint，value 是该 agent 的 YAML 字符串表示。
    """
    output_yaml_dict = {}
    for agent_type_enum in AgentType:
        agent_key = agent_type_enum.value
        agent_data = data.get(agent_key, {})
        if isinstance(agent_data, dict):
            agent_rules_by_tag: Dict[str, List[str]] = {}
            dynamic_items: List[HintRetrieverData] = agent_data.get(
                TermTypeEnum.DYNAMIC.value, []
            )
            fixed_items: List[HintRetrieverData] = agent_data.get(
                TermTypeEnum.FIXED.value, []
            )
            # Combine and categorize items by tag
            all_agent_items = dynamic_items + fixed_items
            for tag_type_enum in HintTagType:
                tag_key = tag_type_enum.value
                tagged_dynamic_extra_infos: List[str] = []
                tagged_fixed_extra_infos: List[str] = []
                for item in all_agent_items:
                    # Check if 'tags' is a list and contains the current tag_key
                    if tag_key in [tag.value for tag in item.tags]:
                        if item.extra_info:
                            if item.type == TermTypeEnum.DYNAMIC:
                                tagged_dynamic_extra_infos.append(item.extra_info)
                            elif item.type == TermTypeEnum.FIXED:
                                tagged_fixed_extra_infos.append(item.extra_info)

                # Sort fixed extra_infos for readability
                filtered_fixed_extra_infos = sorted(list(set(tagged_fixed_extra_infos)))
                filtered_dynamic_extra_infos = list(
                    set(tagged_dynamic_extra_infos)
                )  # Remove duplicates for dynamic, but no specific order needed
                # Combine fixed and dynamic, fixed first
                combined_extra_infos = (
                    filtered_fixed_extra_infos + filtered_dynamic_extra_infos
                )
                if combined_extra_infos:
                    agent_rules_by_tag[f"[{tag_key}规则hint]"] = combined_extra_infos
            if agent_rules_by_tag:
                # Convert the categorized dictionary to YAML string
                # Use default_flow_style=False for block style and explicit_start=False to avoid "---"
                # sort_keys=False to maintain the order of HintTagType
                yaml_string = yaml.dump(
                    agent_rules_by_tag,
                    indent=2,
                    allow_unicode=True,
                    default_flow_style=False,
                    explicit_start=False,
                    explicit_end=False,
                    sort_keys=False,  # Maintain the order of tags as defined in HintTagType
                )
                # Remove single quotes around strings and trim whitespace
                output_yaml_dict[f"{agent_key}_hint"] = yaml_string.replace(
                    "'", ""
                ).strip()
            else:
                output_yaml_dict[
                    f"{agent_key}_hint"
                ] = ""  # If no hints for this agent, return empty string
    return output_yaml_dict
