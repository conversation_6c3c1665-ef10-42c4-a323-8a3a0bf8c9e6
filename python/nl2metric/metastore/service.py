from cache_updater.async_cached import async_cached
from cache_updater.cached import cached
from common.logging.logger import get_logger
from metastore.ask_bi_metastore import AskBIMetaStore
from metastore.custom_json import CustomAskBIMetaStore
from metastore.db_metastore import DatabaseAppStore, DatabaseMetaStore
from typing import Optional

logger = get_logger(__name__)


def need_refresh_ask_bi_metastore(old_cache: Optional[AskBIMetaStore]):
    if not old_cache:
        logger.info("need_refresh_ask_bi_metastore no old_cache")
        return True
    return old_cache.need_refresh()


@async_cached("{project_id}", need_refresh_ask_bi_metastore)
def get_ask_bi_metastore(project_id: str):
    return AskBIMetaStore(project_id)


@async_cached("")
def get_db_appstore():
    return DatabaseAppStore()


@cached("{project_id}")
def get_db_metastore(project_id: str):
    return DatabaseMetaStore(project_id)


def need_refresh_custom_ask_bi_metastore(old_cache: Optional[AskBIMetaStore]):
    if not old_cache:
        logger.info("need_refresh_custom_ask_bi_metastore no old_cache")
        return True
    return old_cache.need_refresh()


@async_cached("{project_id}", need_refresh_custom_ask_bi_metastore)
def get_custom_ask_bi_metastore(project_id: str):
    return CustomAskBIMetaStore(project_id)
