from config import app_config
from metastore.base import BaseMetaStore
from metastore.custom_yaml import get_custom_metastore

_use_yaml_meta = False
_use_json_meta = False
_use_ask_bi_meta = app_config.ENABLE_ASK_BI_METASTORE


def set_use_yaml_meta(enabled: bool):
    global _use_yaml_meta
    _use_yaml_meta = enabled


def set_use_json_meta(enabled: bool):
    global _use_json_meta
    _use_json_meta = enabled


def set_use_ask_bi_meta(enabled: bool):
    global _use_ask_bi_meta
    _use_ask_bi_meta = enabled


def get_metastore(project_id: str) -> BaseMetaStore:
    from metastore.service import get_ask_bi_metastore, get_db_metastore

    if _use_json_meta:
        # this is for ut, better not import it by default
        from metastore.service import get_custom_ask_bi_metastore

        return get_custom_ask_bi_metastore(project_id)

    if _use_ask_bi_meta:
        return get_ask_bi_metastore(project_id)

    return get_db_metastore(project_id)


def get_cached_metastore(project_id: str) -> BaseMetaStore:
    from metastore.service import get_ask_bi_metastore, get_db_metastore

    if _use_yaml_meta:
        return get_custom_metastore()

    if _use_ask_bi_meta:
        return get_ask_bi_metastore.get_cached(project_id)

    return get_db_metastore.get_cached(project_id)
