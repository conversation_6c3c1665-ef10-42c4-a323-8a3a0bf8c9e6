from typing import List, Dict

from cachetools import TTLCache, cached

from common.logging.logger import get_logger
from config import app_config
from metastore.base import (
    Metric,
    BaseMetaStore,
    TimeDimensionDatum,
    TIME_DIMENSION_FORMAT_TO_ISO,
    TimeDimensionType,
)
from metastore.service import get_ask_bi_metastore
from tools.sql_executor import MysqlClient

logger = get_logger(__name__)

cache = TTLCache(maxsize=1000, ttl=1800)


def get_metrics_latest_time(metrics: List[Metric], project_id: str) -> Dict[str, str]:
    """
    获取指标最新时间
    """
    metrics_latest_time: Dict[str, str] = {}
    for metric in metrics:
        if not metric.is_bi_metric():
            metrics_latest_time[metric.name] = ""
        metrics_latest_time[metric.name] = get_metric_table_latest_time(
            metric.model_names[0], metric.get_metric_column_name(), project_id
        )
    return metrics_latest_time


@cached(cache=cache)
def get_metric_table_latest_time(
    table_name: str, col_name: str, project_id: str
) -> str:
    """
    获取指标最新时间
    """
    meta_store: BaseMetaStore = get_ask_bi_metastore(project_id)
    if not meta_store:
        return ""
    time_dimension: TimeDimensionDatum = meta_store.get_time_dimension_datum(table_name)
    time_col = time_dimension.timeDimensionName
    if not time_col:
        return ""
    if time_dimension.timeDimensionType == TimeDimensionType.string:
        time_format = TIME_DIMENSION_FORMAT_TO_ISO.get(
            time_dimension.timeDimensionFormat
        )
        time_col = f"str_to_date({time_col}, '{time_format}')"
    query = f"select date_format(max({time_col}), '%Y-%m-%d') from {table_name}"
    if col_name:
        query = f"{query} where {col_name} is not null"
    try:
        client = MysqlClient.get_instance_by_params(
            host=app_config.xengine_backend_host,
            port=app_config.xengine_backend_port,
            user=app_config.xengine_username,
            passwd=app_config.xengine_password,
            database=app_config.xengine_database,
        )
        _, result = client.query(query)
        if result and len(result) > 0 and len(result[0]) > 0:
            return result[0][0]
        else:
            return ""
    except Exception as e:
        logger.warning(
            f"failed to get_table_latest_time for {table_name} {col_name} -> {e}"
        )
        return ""
