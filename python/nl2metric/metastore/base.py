import itertools
import json
import copy
import re
import jieba

from abc import ABC, abstractmethod
from collections import defaultdict
from common.logging.logger import get_logger
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Union, Set, Any, Iterable, Tuple

from pydantic import BaseModel, Field

from common.types.exceptions import (
    ProjectNotExist,
    ModelNotExist,
    MetricsNotExist,
    DimensionsNotExist,
)

logger = get_logger(__name__)


def merge_dimensions(dimensions):
    dimensions_dict = {}
    for d in dimensions:
        if d.name in dimensions_dict:
            dimensions_dict[d.name].model_names.append(d.model_name)
            dimensions_dict[d.name].values.extend(d.values)
        else:
            d_copied = copy.deepcopy(d)
            d_copied.model_name = None
            d_copied.model_names = [
                d.model_name,
            ]
            dimensions_dict[d.name] = d_copied
    for d in dimensions_dict.values():
        d.model_names = list(set(d.model_names))
        d.values = list(set(d.values))
    return dimensions_dict


class Metric(BaseModel):
    name: str
    label: str
    synonyms: Optional[List[str]] = Field(default_factory=list)
    model_names: List[str] = Field(default_factory=list)
    description: Optional[str] = None
    type: Optional[str] = None
    type_params: Optional[Dict[str, Any]] = None
    raw_json: Optional[Dict] = None

    def __str__(self):
        output_lines = []
        if self.raw_json:
            raw_data = self.raw_json
            # List of fields to potentially display from raw_json
            fields_to_display = {
                # 'id': '原始JSON ID',
                "name": "名称",
                # 'label': '标签',
                "synonyms": "别名",
                "description": "描述",
                "type": "类型",
                # 'formatTemplate': '格式模板',
                # 'rank': '排名',
                # 'keypoint': '是否关键点',
                "createByMeasure": "是否由度量创建",
                "isCumulative": "是否累积",
                # 'updatedAt': '更新时间',
                "displayExpr": "指标运算表达式",
            }
            # Add simple fields if they exist and are not None/empty
            for key, label in fields_to_display.items():
                value = raw_data.get(key)
                if (
                    value is not None
                    and value != "N/A"
                    and (isinstance(value, list) and value)
                    or (not isinstance(value, list))
                ):
                    output_lines.append(f"{label}: {value}")
            # Handle complex fields with JSON formatting if they exist and are not None/empty
            complex_fields = {
                "typeParams": "类型参数",
                "filter": "过滤条件",
                "config": "配置",
                "meta": "元数据",
            }
            for key, label in complex_fields.items():
                value = raw_data.get(key)
                if value is not None and value != {}:  # Check for None and empty dict
                    output_lines.append(
                        f"{label}: {json.dumps(value, indent=2, ensure_ascii=False)}"
                    )
        else:
            # Fallback to original structure if raw_json is not available
            if self.label:
                output_lines.append(f"中文名: {self.label}")
            if self.synonyms:
                output_lines.append(f"同义词: {self.synonyms}")
            if self.type:
                output_lines.append(f"类型: {self.type}")
            if self.description:
                output_lines.append(f"描述: {self.description}")
            if self.type_params:
                output_lines.append(f"配置/过滤条件: {self.type_params}")
        # Join the lines with newlines
        return "\n".join(output_lines)

    @property
    def prompt_description(self):
        prompt_description = {self.label}
        if self.synonyms:
            prompt_description.update(self.synonyms)
        return ";".join(sorted(prompt_description))

    @property
    def name_description(self):
        return f"name: {self.name}, description: {self.prompt_description}"

    @property
    def label_and_synonyms(self):
        if not self.synonyms:
            return [self.label]
        return itertools.chain([self.label], self.synonyms)

    def is_doc_metric(self):
        metric_config = self.raw_json.get("config")
        if metric_config:
            return metric_config.get("specific_for_doc", False)
        return False

    # Currently, we only support simple metric
    def get_metric_column_name(self) -> str:
        if self.type == "simple":
            display_expr = self.raw_json.get("displayExpr", "")
            if display_expr:
                start_idx = display_expr.rfind(".")
                end_idx = display_expr.rfind(")")
                if start_idx != -1 and end_idx != -1:
                    col_name = display_expr[start_idx + 1 : end_idx].strip()
                    return col_name

        return ""

    def is_bi_metric(self):
        return (
            self.type in ["simple", "ratio", "list", "derived", "rank"]
            and not self.is_doc_metric()
        )

    @property
    def type_descp(self):
        type_descp_dict = {
            "simple": "原子指标",
            "ratio": "比值指标",
            "derived": "派生指标",
            "rank": "排名指标",
            "list": "列表指标",
        }
        return type_descp_dict.get(self.type, self.type)


class Measure(BaseModel):
    name: str
    label: str
    synonyms: Optional[List[str]] = Field(default_factory=list)
    description: Optional[str] = None
    create_metric: bool
    model_name: Optional[str] = None


class NL2MetricFewShot(BaseModel):
    project_id: str
    metrics: List[Dict]
    dimensions: List[Dict]
    think: str
    result: Dict
    labels: Dict
    scene: str


def decode_dimension_value_str(raw_str):
    raw_str = raw_str.strip()
    if not raw_str:
        return None, set()
    SYNONYM_SPLITTER = "\1\1\1"
    parts = raw_str.split(SYNONYM_SPLITTER)
    if not parts:
        return None, set()
    dimension_value = parts[0].strip()
    if not dimension_value:
        return None, set()
    synonyms = set()
    for i in range(1, len(parts)):
        synonym = parts[i].strip()
        if not synonym:
            continue
        if synonym != dimension_value:
            synonyms.add(synonym)
    return dimension_value, synonyms


def _gen_dimension_value_key(name, is_synonym):
    # TODO(bhx): langfuse cannot process Tuple, why?
    # return (name, is_synonym)
    return f"{name}\1\1\1{is_synonym}"


class DimensionValue(BaseModel):
    name: str

    # A: s1, s2, B: s1
    # for DimensionValue A: is_synonym = False, synonyms = (s1, s2)
    # for DimensionValue B: is_synonym = False, synonyms = (s1)
    # for DimensionValue s1: is_synonym = True, synonyms = (A, B)
    # for DimensionValue s2: is_synonym = True, synonyms = (A)
    is_synonym: bool = False
    synonyms: Optional[Union[Set[str], List[str]]] = None

    # currently used by baowu
    # if self is part of many other dimension values
    is_high_risk: bool = False
    jieba_cut: Optional[List[str]] = None

    # currently used in BaseMetaStore::_dimensions_all_model
    @property
    def key(self):
        return _gen_dimension_value_key(self.name, self.is_synonym)

    @property
    # we deleted the property description
    # add this just in case we miss some calling dimension_value.description
    def description(self):
        return self.name

    @property
    def name_and_synonyms(self):
        if not self.synonyms:
            return [self.name]
        return itertools.chain([self.name], self.synonyms)

    @staticmethod
    def from_list(l: List[str]) -> List["DimensionValue"]:
        if not l:
            return {}
        ret_real_values = {}
        ret_synonyms_values = {}
        assert isinstance(l, list)
        for raw_str in l:
            raw_str = str(raw_str)
            if not raw_str:
                continue
            dimension_value, synonyms = decode_dimension_value_str(raw_str)
            if not dimension_value:
                logger.info(
                    f"decode {raw_str} found no dimension value, output synonyms {synonyms}"
                )
                continue
            ret_real_values[dimension_value] = DimensionValue(
                name=dimension_value,
                is_synonym=False,
                synonyms=synonyms,
                jieba_cut=jieba.cut(dimension_value),
            )
            for synonym in synonyms:
                if synonym in ret_synonyms_values:
                    ret_synonyms_values[synonym].synonyms.add(dimension_value)
                else:
                    ret_synonyms_values[synonym] = DimensionValue(
                        name=synonym,
                        is_synonym=True,
                        synonyms={
                            dimension_value,
                        },
                        jieba_cut=jieba.cut(synonym),
                    )
        key_ret = {}
        for dv in ret_real_values.values():
            if dv.synonyms is not None:
                dv.synonyms = sorted(list(dv.synonyms))
            key_ret[dv.key] = dv
        for dv in ret_synonyms_values.values():
            if dv.synonyms is not None:
                dv.synonyms = sorted(list(dv.synonyms))
            key_ret[dv.key] = dv
        for dv in key_ret.values():
            if len(dv.name) > 2:
                continue
            cnt = 0
            for dv2 in key_ret.values():
                if dv.name in dv2.name:
                    cnt += 1
                    if cnt >= 50:
                        break
            if cnt >= 50:
                dv.is_high_risk = True

        return key_ret


class DimensionValueWithMeta(BaseModel):
    dimension_value: DimensionValue
    score: Optional[float] = None

    # otherwise there are too many print in log
    class Config:
        json_encoders = {
            DimensionValue: lambda v: v.name,
        }

    @property
    def name(self):
        return self.dimension_value.name

    @property
    def key(self):
        return self.dimension_value.key

    @property
    def is_synonym(self):
        return self.dimension_value.is_synonym

    @property
    def synonyms(self):
        return self.dimension_value.synonyms


class Dimension(BaseModel):
    name: str
    label: Union[str, Set[str]]  # Set[str] is tmp value during merge dimensions
    synonyms: Optional[List[str]] = Field(default_factory=list)
    description: Optional[str] = None
    type: Optional[str] = None
    expr: Optional[str] = None
    # TODO(bhx): erase model_name
    model_name: Optional[str] = None
    model_names: Optional[List[str]] = None
    # For dimension merged from different models, such as in BaseMetaStore::_dimensions_all_model
    # we may have DimensionValue with same name and different key
    distinct_values: Dict[str, DimensionValue] = Field(default_factory=dict)
    _high_risk_dimension_value_strs: Optional[Set[str]] = None

    def __str__(self):
        return (
            f"中文名:{self.label}	同义词:{self.synonyms} "
            f"描述:{self.description}	表达式:{self.expr}	类型:{self.type}"
        )

    def __init__(self, *args: Any, **kwargs: Any):
        if len(args) > 0:
            # in case we pass values/distinct_values in args
            raise RuntimeError(f"donnot use args {args}")
        if "values" in kwargs and "distinct_values" in kwargs:
            raise RuntimeError(
                f"cannot use both values {kwargs['values']} and distinct_values {kwargs['distinct_values']}"
            )
        if "values" in kwargs:
            values = kwargs.pop("values")
            if isinstance(values, Dict):
                kwargs["distinct_values"] = values
            elif isinstance(values, List):
                distinct_values = {}
                for v in values:
                    distinct_values[v.key] = v
                kwargs["distinct_values"] = distinct_values
            elif isinstance(values, DimensionValue):
                kwargs["distinct_values"] = {values.key: values}
            else:
                raise RuntimeError(f"illegal values {values} of type {type(values)}")
        super().__init__(**kwargs)

    def is_time(self):
        return bool(
            self.type == "time"
            or self.type == "virtual-time"
            or self.type == "time_default"
        )

    def set_values(self, distinct_values):
        self.distinct_values = distinct_values
        self._high_risk_dimension_value_strs = None

    @property
    def values(self):
        return self.distinct_values.values()

    def _merge_values(self, values_in: Dict[str, DimensionValue]):
        for key, value in values_in.items():
            dimension_value = copy.deepcopy(value)
            if key in self.distinct_values:
                if dimension_value.synonyms:
                    new_synonyms = (
                        self.distinct_values[key].synonyms + dimension_value.synonyms
                    )
                    new_synonyms = sorted(list(set(new_synonyms)))
                    self.distinct_values[key].synonyms = new_synonyms
            else:
                self.distinct_values[key] = dimension_value

    def merge(self, d):
        if self.name != d.name:
            logger.error(f"merge wrong dimension name, {self.name} vs. {d.name}")
            return
        self.model_names.append(d.model_name)
        self._merge_values(d.distinct_values)
        self.label.add(d.label)
        if not self.synonyms:
            self.synonyms = copy.deepcopy(d.synonyms)
        elif d.synonyms:
            for s in d.synonyms:
                if s not in self.synonyms:
                    self.synonyms.append(s)
        if not self._high_risk_dimension_value_strs:
            self._high_risk_dimension_value_strs = copy.deepcopy(
                d._high_risk_dimension_value_strs
            )
        elif d._high_risk_dimension_value_strs:
            self._high_risk_dimension_value_strs.update(
                d._high_risk_dimension_value_strs
            )

    @property
    def key(self):
        return self.model_name, self.name

    @property
    def prompt_description(self):
        prompt_description = {self.label}
        if self.synonyms:
            prompt_description.update(self.synonyms)
        return ";".join(sorted(prompt_description))

    @property
    def high_risk_dimension_value_strs(self):
        if self._high_risk_dimension_value_strs is None:
            self._high_risk_dimension_value_strs = set()
            for dv in self.values:
                if dv.is_high_risk:
                    self._high_risk_dimension_value_strs.add(dv.name)
        return self._high_risk_dimension_value_strs

    def add_value(self, value: DimensionValue):
        if value.key not in self.distinct_values:
            self.distinct_values[value.key] = value

    def search_value(self, name: str, is_synonym: Optional[bool]):
        if is_synonym is None:
            result = self.distinct_values.get(
                _gen_dimension_value_key(name, False), None
            )
            if not result:
                result = self.distinct_values.get(
                    _gen_dimension_value_key(name, True), None
                )
            return result
        return self.distinct_values.get(
            _gen_dimension_value_key(name, is_synonym), None
        )

    @property
    def values_to_json(self):
        return json.dumps([v.name for v in self.values], ensure_ascii=False)


class TimeDimensionType(Enum):
    string = "string"
    date = "date"
    datetime = "datetime"


class TimeDimensionFormat(Enum):
    y1 = "yyyy"
    m1 = "yyyyMM"
    m2 = "yyyy-MM"
    m3 = "yyyy/MM"
    d1 = "yyyyMMDD"
    d2 = "yyyyMMdd"
    d3 = "yyyy_MM_DD"
    d4 = "yyyy_MM_dd"
    d5 = "yyyy/MM/DD"
    d6 = "yyyy/MM/dd"
    d7 = "yyyy-MM-DD"
    d8 = "yyyy-MM-dd"


TIME_DIMENSION_FORMAT_TO_ISO = {
    TimeDimensionFormat.y1: "%Y",
    TimeDimensionFormat.m1: "%Y%m",
    TimeDimensionFormat.m2: "%Y-%m",
    TimeDimensionFormat.m3: "%Y/%m",
    TimeDimensionFormat.d1: "%Y%m%d",
    TimeDimensionFormat.d2: "%Y%m%d",
    TimeDimensionFormat.d3: "%Y_%m_%d",
    TimeDimensionFormat.d4: "%Y_%m_%d",
    TimeDimensionFormat.d5: "%Y/%m/%d",
    TimeDimensionFormat.d6: "%Y/%m/%d",
    TimeDimensionFormat.d7: "%Y-%m-%d",
    TimeDimensionFormat.d8: "%Y-%m-%d",
}

ISO_TIME_FORMATS = set(TIME_DIMENSION_FORMAT_TO_ISO.values())


def safe_match_time_format(time_str):
    for fmt_enum, fmt_str in TIME_DIMENSION_FORMAT_TO_ISO.items():
        try:
            datetime.strptime(time_str, fmt_str)
            return fmt_enum.value
        except ValueError:
            continue
    return ""


def format_time(input_time: str, target_format: TimeDimensionFormat) -> str:
    target_format = TimeDimensionFormat(target_format)
    input_time = re.sub(r"[年月日]", "-", input_time)  # 替换 "年", "月", "日" 为 "-"
    input_time = re.sub(r"[^\d\-/:]", "", input_time)  # 删除其他非数字字符
    input_time = input_time.strip()
    input_time = input_time.strip("-")  # 2023年2月  ->  2023-2-

    parsed_time = None
    for fmt in ISO_TIME_FORMATS:
        try:
            parsed_time = datetime.strptime(input_time, fmt)
            break
        except ValueError:
            continue

    if not parsed_time:
        raise ValueError(f"format_time cannot parse {input_time}")

    target_format_str = TIME_DIMENSION_FORMAT_TO_ISO[target_format]
    return parsed_time.strftime(target_format_str)


class TimeGranularityType(Enum):
    # total should be min, for get_time_dimension_format
    total = "total"
    day = "day"
    month = "month"
    quarter = "quarter"
    year = "year"

    @classmethod
    def get_max_granularity(cls, l, r):
        granularity_order = list(TimeGranularityType)
        return granularity_order[
            max(
                granularity_order.index(l),
                granularity_order.index(r),
            )
        ]


class TimeDimensionDatum(BaseModel):
    timeDimensionName: str
    timeDimensionType: TimeDimensionType
    timeDimensionFormat: TimeDimensionFormat
    timeGranularityMin: TimeGranularityType


class BaseMetaStore(ABC):
    def __init__(self, project_id: str):
        self._project_id = project_id
        self._load()

    def _load(self) -> None:
        all_metrics = {}
        all_dimensions: Dict[str, Dict[str, Dimension]] = defaultdict(dict)
        ignored_dimensions: Dict[str, Dict[str, Dimension]] = defaultdict(dict)
        self._time_dimension_datum = self._fetch_time_dimension_datum()

        for metric in self._fetch_metrics().values():
            all_metrics[metric.name] = metric
        # treat external_report the same as metric
        # frontend guarantees that external_report name and metric name different
        for external_report_metric in self._fetch_external_reports().values():
            all_metrics[external_report_metric.name] = external_report_metric
        dimensions_fetch, ignored_dimensions_fetch = self._fetch_dimensions()
        for model_name, app_dimensions in dimensions_fetch.items():
            for dimension in app_dimensions.values():
                all_dimensions[model_name][dimension.name] = dimension
        for model_name, app_dimensions in ignored_dimensions_fetch.items():
            for dimension in app_dimensions.values():
                ignored_dimensions[model_name][dimension.name] = dimension
        all_metrics_copy = all_metrics.copy()
        for metric_name, metric_temp in all_metrics_copy.items():
            if metric_temp.label.endswith("内部"):
                all_metrics.pop(metric_name)
        self._metrics: Dict[str, Metric] = all_metrics
        self._dimensions: Dict[str, Dict[str, Dimension]] = all_dimensions
        self._ignored_dimensions: Dict[str, Dict[str, Dimension]] = ignored_dimensions
        self._nl_metric_few_shot: Dict[
            str, List[dict]
        ] = self._fetch_nl_metric_few_shot()
        self._model_id_list = self._fetch_model_list()

        def _merge_dimensions(dimensions):
            dimensions_all_model = {}
            for dimensions_per_model in dimensions.values():
                for d in dimensions_per_model.values():
                    if d.name in dimensions_all_model:
                        dimensions_all_model[d.name].merge(d)
                    else:
                        d_copied = copy.deepcopy(d)
                        d_copied.model_name = None
                        d_copied.model_names = [
                            d.model_name,
                        ]
                        if d_copied.label:
                            d_copied.label = {d_copied.label}
                        else:
                            d_copied.label = set()
                        dimensions_all_model[d.name] = d_copied
            for d in dimensions_all_model.values():
                d.model_names = list(set(d.model_names))
                d.label = ",".join(d.label)
            return dimensions_all_model

        self._dimensions_all_model = _merge_dimensions(self._dimensions)
        self._ignored_dimensions_all_model = _merge_dimensions(self._ignored_dimensions)

        self._metrics_label_and_synonyms = {}
        for metric in self._metrics.values():
            for word in metric.label_and_synonyms:
                if word not in self._metrics_label_and_synonyms:
                    self._metrics_label_and_synonyms[word] = [metric]
                else:
                    self._metrics_label_and_synonyms[word].append(metric)
        for metrics in self._metrics_label_and_synonyms.values():
            metrics.sort(key=lambda m: m.label)

    def _get_metrics_from_measure(
        self, model_name: str, measure: Measure
    ) -> List[Metric]:
        ret = []
        if measure.create_metric:
            ret.append(
                Metric(
                    name=measure.name,
                    label=measure.label,
                    synonyms=measure.synonyms,
                    description=measure.description,
                    model_names=[model_name],
                )
            )
        if False:
            asc_name = f"{measure.name}_rank_asc"
            ret.append(
                Metric(
                    name=asc_name,
                    label=f"{measure.label}升序排名",
                    synonyms=(
                        [f"{s}升序排名" for s in measure.synonyms]
                        if measure.synonyms
                        else None
                    ),
                    description=f"{measure.description}升序排名，自动生成",
                    model_names=[measure.model_name],
                )
            )
            desc_name = f"{measure.name}_rank_desc"
            ret.append(
                Metric(
                    name=desc_name,
                    label=f"{measure.label}降序排名",
                    synonyms=(
                        [f"{s}降序排名" for s in measure.synonyms]
                        if measure.synonyms
                        else None
                    ),
                    description=f"{measure.description}降序排名，自动生成",
                    model_names=[measure.model_name],
                )
            )
        return ret

    @abstractmethod
    def _fetch_metrics(self) -> Dict[str, Metric]:
        raise NotImplementedError

    @abstractmethod
    def _fetch_external_reports(self) -> Dict[str, Metric]:
        raise NotImplementedError

    @abstractmethod
    def _fetch_measures(self) -> Dict[str, Dict[str, Measure]]:
        raise NotImplementedError

    @abstractmethod
    def _fetch_dimensions(
        self,
    ) -> Tuple[Dict[str, Dict[str, Dimension]], Dict[str, Dict[str, Dimension]]]:
        raise NotImplementedError

    @abstractmethod
    def _fetch_time_dimension_datum(
        self,
    ) -> Dict[str, TimeDimensionDatum]:
        raise NotImplementedError

    @abstractmethod
    def _fetch_nl_metric_few_shot(self) -> Dict[str, List[dict]]:
        raise NotImplementedError

    @abstractmethod
    def _fetch_model_list(self) -> Dict[str, str]:
        raise NotImplementedError

    def get_metric(self, metric_name: str) -> Metric:
        return self._metrics[metric_name]

    def safe_get_metric(self, metric_name: str) -> Metric:
        return self._metrics.get(metric_name)

    def list_metrics_by_model_name(self, model_name: str) -> Dict[str, Metric]:
        if not model_name:
            ret = self._metrics
        else:
            ret: Dict[str, Metric] = {}
            for metric_name, metric in self._metrics.items():
                if metric.model_names and model_name in metric.model_names:
                    ret[metric_name] = metric
        # if there is no metrics, we should retry fetch data again, in case that
        # the model is added newly
        if not ret:
            raise MetricsNotExist(
                self._project_id,
                self._model_id_list,
                model_name,
                f"No metrics exist in model {model_name}",
            )
        return ret

    def list_metrics_label_and_synonyms_by_model_name(
        self, model_name: str
    ) -> Dict[str, Metric]:
        if not model_name:
            result = self._metrics_label_and_synonyms
        else:
            result = {}
            for key, metrics in self._metrics_label_and_synonyms.items():
                model_metrics = []
                for metric in metrics:
                    if metric.model_names and model_name in metric.model_names:
                        model_metrics.append(metric)
                if model_metrics:
                    result[key] = model_metrics
        if not result:
            raise MetricsNotExist(
                self._project_id,
                self._model_id_list,
                model_name,
                f"No metrics exist in model {model_name} ",
            )
        return result

    def list_metrics(self) -> List[Metric]:
        return list(self._metrics.values())

    def get_dimension(
        self, model_name: str, dimension_name: str
    ) -> Optional[Dimension]:
        # 这里就是要抛出异常，如果不需要抛出异常的话就用safe_get_dimension
        if not model_name:
            return self._dimensions_all_model[dimension_name]
        else:
            if model_name not in self._dimensions:
                raise RuntimeError(
                    f"get_dimension model_name {model_name} does not exist"
                )
            return self._dimensions[model_name][dimension_name]

    def safe_get_dimension(
        self, model_name: str, dimension_name: str
    ) -> Optional[Dimension]:
        try:
            if not model_name:
                return self._dimensions_all_model[dimension_name]
            else:
                return self._dimensions[model_name].get(dimension_name)
        except Exception as e:
            return None

    def safe_get_ignored_dimension(
        self, model_name: str, dimension_name: str
    ) -> Optional[Dimension]:
        try:
            if not model_name:
                return self._ignored_dimensions_all_model[dimension_name]
            else:
                return self._ignored_dimensions[model_name].get(dimension_name)
        except Exception as e:
            return None

    def list_dimensions_by_model_name(
        self, model_name: str, dimension_type: str = None
    ) -> Dict[str, Dimension]:
        if not model_name:
            ret = self._dimensions_all_model
        else:
            # if there is no dimensions, we should retry fetch data again, in case that
            # the model is added newly
            ret = self._dimensions.get(model_name, None)
            if not ret:
                raise DimensionsNotExist(
                    self._project_id,
                    self._model_id_list,
                    model_name,
                    f" model_name {model_name} does not exist",
                )
        if dimension_type:
            ret = {
                name: dimension
                for name, dimension in ret.items()
                if dimension.type == dimension_type
            }
        return ret

    def list_ignored_dimensions_by_model_name(
        self, model_name: str, dimension_type: str = None
    ) -> Dict[str, Dimension]:
        if not model_name:
            ret = self._ignored_dimensions_all_model
        else:
            # if there is no dimensions, we should retry fetch data again, in case that
            # the model is added newly
            ret = self._ignored_dimensions.get(model_name, None)
            if not ret:
                raise DimensionsNotExist(
                    self._project_id,
                    self._model_id_list,
                    model_name,
                    f" model_name {model_name} does not exist",
                )
        if dimension_type:
            ret = {
                name: dimension
                for name, dimension in ret.items()
                if dimension.type == dimension_type
            }
        return ret

    def list_dimensions_by_dimension_labels(
        self, model_name: str, dimension_labels: Set
    ) -> Dict[str, Dimension]:
        if not model_name or not dimension_labels:
            raise RuntimeError(
                f"list_dimensions_by_dimension_labels needs non-empty "
                f"model_name {model_name} and dimension_labels {dimension_labels}"
            )
        ret: Dict[str, Dimension] = {}
        for dimension_name, dimension in self._dimensions[model_name].items():
            if dimension.label in dimension_labels:
                ret[dimension_name] = dimension
        return ret

    def list_dimensions(self) -> List[Dimension]:
        ret: List[Dimension] = []
        for name, value in self._dimensions.items():
            ret.extend(value.values())
        return ret

    def get_nl_metric_few_shot(self, scene) -> List[dict]:
        if scene in self._nl_metric_few_shot:
            return self._nl_metric_few_shot[scene]
        return []

    def get_time_dimension_format(self, model_name: str, time_granularity: str) -> str:
        time_dimension_datum = self._time_dimension_datum[model_name]
        time_granularity_enum = TimeGranularityType.get_max_granularity(
            TimeGranularityType(time_granularity),
            time_dimension_datum.timeGranularityMin,
        )
        assert time_granularity_enum != TimeGranularityType.total
        time_dimension_type = time_dimension_datum.timeDimensionType
        time_dimension_format = time_dimension_datum.timeDimensionFormat

        if time_dimension_type == TimeDimensionType.string:
            if time_granularity_enum == TimeGranularityType.quarter:
                return f"{time_dimension_format.value[:4]}-Qi"
            elif time_granularity_enum == TimeGranularityType.year:
                return time_dimension_format.value[:4]

            if time_dimension_format == TimeDimensionFormat.y1:
                return time_dimension_format.value
            elif time_dimension_format == TimeDimensionFormat.m1:
                # Format 'yyyyMM'
                if time_granularity_enum in [
                    TimeGranularityType.day,
                    TimeGranularityType.month,
                ]:
                    return time_dimension_format.value

            elif time_dimension_format in [
                TimeDimensionFormat.m2,
                TimeDimensionFormat.m3,
            ]:
                # Format 'yyyy-MM' or 'yyyy/MM'
                if time_granularity_enum in [
                    TimeGranularityType.day,
                    TimeGranularityType.month,
                ]:
                    return time_dimension_format.value

            elif time_dimension_format in [
                TimeDimensionFormat.d1,
                TimeDimensionFormat.d2,
            ]:
                # Format 'yyyyMMDD' or 'yyyyMMdd'
                if time_granularity_enum in [TimeGranularityType.day]:
                    return time_dimension_format.value
                elif time_granularity_enum == TimeGranularityType.month:
                    return time_dimension_format.value[:6]

            elif time_dimension_format in [
                TimeDimensionFormat.d3,
                TimeDimensionFormat.d4,
                TimeDimensionFormat.d5,
                TimeDimensionFormat.d6,
                TimeDimensionFormat.d7,
                TimeDimensionFormat.d8,
            ]:
                if time_granularity_enum in [TimeGranularityType.day]:
                    return time_dimension_format.value
                elif time_granularity_enum == TimeGranularityType.month:
                    return time_dimension_format.value[:7]

            raise ValueError(
                f"Unexpected time_dimension_format {time_dimension_format} and time_granularity {time_granularity_enum}"
            )

        else:
            raise ValueError(f"Unsupported time dimension type: {time_dimension_type}")

    def get_time_dimension_datum(self, model_name: str) -> TimeDimensionDatum:
        empty = TimeDimensionDatum(
            timeDimensionName="",
            timeDimensionType=TimeDimensionType.string,
            timeDimensionFormat=TimeDimensionFormat.y1,
            timeGranularityMin=TimeGranularityType.total,
        )
        try:
            return self._time_dimension_datum.get(model_name, empty)
        except Exception as e:
            logger.warning(
                f"get_time_dimension_datum model_name {model_name} failed {e}"
            )
            return empty

    def safe_get_time_dimension_format(
        self, model_name: str, time_granularity: str
    ) -> str:
        try:
            return self.get_time_dimension_format(model_name, time_granularity)
        except Exception as e:
            logger.error(
                f"get_time_dimension_format model_name {model_name} time_granularity {time_granularity} failed {e}"
            )
            return ""


class Model(BaseModel):
    id: str
    table_name: str
    label: str
    description: Optional[str] = None
    agg_time_dimension: Optional[str] = None
    table_meta_id: Optional[str] = None
    semantic_project_name: str
    semantic_project_id: str


class Project(BaseModel):
    id: str
    name: str


class BaseAppStore(ABC):
    def __init__(self):
        logger.debug(f"BaseAppStore creating...")
        (
            self._projects_by_name,
            self._projects_by_id,
            self._models_by_name,
            self._models_by_id,
            self._models_by_project_id,
        ) = self._fetch_projects_and_models()
        logger.info(f"BaseAppStore created")

    @abstractmethod
    def _fetch_projects_and_models(self):
        raise NotImplementedError

    def safe_get_project_by_name(self, project_name: str):
        return self._projects_by_name.get(project_name, None)

    def get_project_by_name(self, project_name: str):
        return self._projects_by_name[project_name]

    def get_project_by_id(self, project_id: str):
        project = self._projects_by_id.get(project_id, None)
        if project == None:
            raise ProjectNotExist(project_id, f"project_id {project_id} does not exist")
        return project

    def get_model_by_name(self, project_id: str, model_name: str):
        return self._models_by_name[project_id][model_name]

    def get_model_by_id(self, model_id: str):
        model = self._models_by_id.get(model_id, None)
        if model == None:
            raise ModelNotExist(model_id, f"model_id {model_id} does not exist")
        return model

    def get_models_by_project_id(self, project_id: str):
        models = self._models_by_project_id.get(project_id, None)
        if models == None:
            raise ProjectNotExist(project_id, f"project_id {project_id} does not exist")
        return models


def verify_meta(project_id: str, appstore: BaseAppStore, metastore: BaseMetaStore):
    if project_id not in appstore._projects_by_id:
        return False, [f"project_id {project_id} does not exist"]
    models = appstore.get_models_by_project_id(project_id)
    if not models:
        return False, [f"project_id {project_id} have no models"]
    msgs = []
    for model in models:
        model_name = model.table_name
        try:
            metrics = metastore.list_metrics_by_model_name(model_name)
            if not metrics:
                msgs.append(
                    f"project_id {project_id} model {model_name} have no metrics"
                )
        except Exception as e:
            msgs.append(
                f"project_id {project_id} model {model_name} list_metrics_by_model_name failed {e}"
            )
        try:
            dimensions = metastore.list_dimensions_by_model_name(model_name)
            if not dimensions:
                msgs.append(
                    f"project_id {project_id} model {model_name} have no dimensions"
                )
        except Exception as e:
            msgs.append(
                f"project_id {project_id} model {model_name} list_dimensions_by_model_name failed {e}"
            )
    if msgs:
        return False, msgs
    else:
        return True, None
