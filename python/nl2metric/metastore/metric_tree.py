import json
from typing import List, Optional, Set

from pydantic import BaseModel

from common.db_model.model import (
    SemanticMetricTreeNode,
    get_semantic_metric_tree_nodes,
    get_semantic_models,
)
from common.logging.logger import get_logger
from cache_updater.cached import cached
from common.utils.string_utils import ObjectEncoder
from metastore import get_metastore
from metastore.base import Metric

logger = get_logger(__name__)


class MetricTreeNode(BaseModel):
    class Config:
        arbitrary_types_allowed = True

    name: str
    metric: Metric
    data: Optional[SemanticMetricTreeNode] = None
    children: Optional[List["MetricTreeNode"]] = None

    def to_json(self, indent=None):
        return json.dumps(self, cls=ObjectEncoder, indent=indent, ensure_ascii=False)


class MetricTreeManager:
    def __init__(self, project_id):
        logger.debug(f"MetricTreeManager for project_id {project_id} creating...")
        self._metrics_map = {}
        self._project_id = project_id
        tree_nodes = get_semantic_metric_tree_nodes(self._project_id)
        self._metrics_map = {
            m.name: m for m in get_metastore(project_id=project_id).list_metrics()
        }
        self._nodes = {n.metric_name: n for n in tree_nodes}
        logger.info(f"MetricTreeManager for project_id {project_id} created")

    def build_tree(self, metric_name: str) -> MetricTreeNode:
        visited = set()
        return self._build_node(metric_name, visited)

    def _build_node(
        self, metric_name: str, visited: Set[str]
    ) -> Optional[MetricTreeNode]:
        metric = self._metrics_map[metric_name]
        if metric_name in visited:
            return None
        visited.add(metric_name)
        if metric_name not in self._nodes:
            return MetricTreeNode(name=metric_name, metric=metric)
        node = self._nodes[metric_name]
        children_nodes = []
        for child_name in node.children_names:
            child_node = self._build_node(child_name, visited)
            if child_node:
                children_nodes.append(child_node)
        return MetricTreeNode(
            name=metric_name, data=node, children=children_nodes, metric=metric
        )


@cached("{project_id}")
def get_metric_tree_manager(project_id: str):
    return MetricTreeManager(project_id)
