import json
import os

import yaml
from compute_metric_score import compute_confidence, get_rerank_model
from tqdm import tqdm

from config.app_config import get_default_project_id
from pre_filter.service import get_pre_filter_service


def get_metric_list_from_question(question):
    metrics = pre_filter.retrieve_metrics(question)
    return [metrics[i].label for i in range(len(metrics))]


def test_compute_metric_score():
    test_result = []
    for test_data in tqdm(tests_data):
        question = test_data["question"]
        metric_list = get_metric_list_from_question(question)
        question_metric_list = [
            [question, metric_list[i]] for i in range(len(metric_list))
        ]
        scores = compute_confidence(rerank_model, question_metric_list)
        sorted_indices = sorted(
            range(len(scores)), key=lambda x: scores[x], reverse=True
        )[:5]
        test_result.append(
            {
                "question": question,
                "label": test_data["queryParams"]["metricNames"],
                "metric_score": [
                    [
                        metric_list[idx],
                        scores[idx],
                        metric_list[idx] in question,
                    ]
                    for idx in sorted_indices
                ],
            }
        )

    with open("./test_result.json", "w") as f:
        json.dump(test_result, f, ensure_ascii=False, indent=4)


if __name__ == "__main__":
    rerank_model = get_rerank_model()
    file_path = os.path.join(
        os.path.dirname(__file__), "..", "tests/test_nl2metric.yml"
    )
    tests_data = yaml.safe_load(open(file_path, "r"))["tests"]
    project_id = get_default_project_id()
    pre_filter = get_pre_filter_service(project_id, None)
    test_compute_metric_score()
