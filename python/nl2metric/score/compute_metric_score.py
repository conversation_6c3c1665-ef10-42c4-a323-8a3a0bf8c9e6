import torch
from FlagEmbedding import <PERSON><PERSON><PERSON><PERSON>
from transformers import AutoModelForSequenceClassification, AutoTokenizer

from common.utils.concurrent_utils import do_once
from config.app_config import bge_rerank_model_name_or_path


class rerankModel(FlagReranker):
    def __init__(
        self, model_name_or_path, use_fp16: bool = False, disable_gpu: bool = True
    ):
        self.tokenizer = AutoTokenizer.from_pretrained(model_name_or_path)
        self.model = AutoModelForSequenceClassification.from_pretrained(
            model_name_or_path
        )
        if use_fp16:
            self.model.half()
        if disable_gpu:
            self.device = torch.device("cpu")
            self.num_gpus = 0
        else:
            if torch.cuda.is_available():
                self.device = torch.device("cuda")
            else:
                self.device = torch.device("cpu")
            self.num_gpus = 1
        self.model = self.model.to(self.device)
        self.model.eval()


@do_once
def get_rerank_model(use_fp16: bool = False, disable_gpu: bool = True):
    reranker = rerankModel(
        bge_rerank_model_name_or_path, use_fp16=use_fp16, disable_gpu=disable_gpu
    )
    return reranker


def compute_confidence(
    rerank_model, question_metric_list: list[str, str]
) -> list[float]:
    logits = rerank_model.compute_score(question_metric_list)
    confidence = torch.nn.functional.sigmoid(torch.tensor(logits)).tolist()
    return confidence
