SHELL = /bin/bash

test_icbc_pre_filter:
	PYTHONPATH=${PYTHONPATH}:$(shell pwd) \
	 SEMANTIC_MODEL_PATH="$(shell pwd)/../../src/server/MetricStore/data/icbc-sdfh-model.yml" \
	 TEST_CONFIG_PATH="$(shell pwd)/../../src/server/MetricStore/metric2sql/icbc-sdfh-tests.yml" \
	  pytest -s tests/test_pre_filter.py

test_jiaohang_pre_filter:
	PYTHONPATH=${PYTHONPATH}:$(shell pwd) \
	 SEMANTIC_MODEL_PATH="$(shell pwd)/../../src/server/MetricStore/data/jiaohang-model.yml" \
	 TEST_CONFIG_PATH="$(shell pwd)/../../src/server/MetricStore/metric2sql/jiaohang-tests.yml" \
	  pytest -s tests/test_pre_filter.py

test_icbc:
	PYTHONPATH=${PYTHONPATH}:$(shell pwd) \
	 SEMANTIC_MODEL_PATH="$(shell pwd)/../../src/server/MetricStore/data/icbc-sdfh-model.yml" \
	 TEST_CONFIG_PATH="$(shell pwd)/../../src/server/MetricStore/metric2sql/icbc-sdfh-tests.yml" \
	  python tests/test_nl2metric.py -b icbc -m mixtral-8x7b-chat

test_icbc_mixtral:
	PYTHONPATH=${PYTHONPATH}:$(shell pwd) \
	 SEMANTIC_MODEL_PATH="$(shell pwd)/../../src/server/MetricStore/data/icbc-sdfh-model.yml" \
	 TEST_CONFIG_PATH="$(shell pwd)/../../src/server/MetricStore/metric2sql/icbc-sdfh-tests.yml" \
	  python tests/test_nl2metric.py -b icbc -m huggingface-mixtral

test_jiaohang:
	PYTHONPATH=${PYTHONPATH}:$(shell pwd) \
	 SEMANTIC_MODEL_PATH="$(shell pwd)/../../src/server/MetricStore/data/jiaohang-model.yml" \
	 TEST_CONFIG_PATH="$(shell pwd)/../../src/server/MetricStore/metric2sql/jiaohang-tests.yml" \
	  python tests/test_nl2metric.py -b bocomm

test_jiaohang_mixtral:
	PYTHONPATH=${PYTHONPATH}:$(shell pwd) \
	 SEMANTIC_MODEL_PATH="$(shell pwd)/../../src/server/MetricStore/data/jiaohang-model.yml" \
	 TEST_CONFIG_PATH="$(shell pwd)/../../src/server/MetricStore/metric2sql/jiaohang-tests.yml" \
	  python tests/test_nl2metric.py -b bocomm -m huggingface-mixtral


build:
	DOCKER_BUILDKIT=1 docker build . -f Dockerfile

flake:
  PYTHONPATH=${PYTHONPATH}:$(shell pwd) flake8 .
