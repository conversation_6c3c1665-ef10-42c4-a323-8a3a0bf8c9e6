from langchain_core.runnables import RunnableConfig

from backend_stage_reporter.reporter import ProgressRunStatus
from common.types.base import CHAIN_META, ChainMeta
from nl2agent.tools.base_tool import BaseTool, register_tool
from nl2agent.dag.table_node_data import TableNodeData
from nl2agent.types import OrderByType
from typing import Type
from pydantic import BaseModel, Field, field_validator


class OrderInput(BaseModel):
    column_name: str = Field(description="排序列名")
    type: OrderByType = Field(description="排序类型，可选asc、desc")
    limit: int = Field(default=0, description="排序后取的数据数量，例如5意味着取排序后的前5条数据")

    @field_validator("type", mode="before")
    def validate_order_type(cls, value):
        if isinstance(value, str):
            return OrderByType(value.upper())
        return OrderByType(value)


@register_tool(name="order")
class OrderTool(BaseTool):
    name: str = "order"
    description: str = "动态模拟工具子工具，对临时表排序"
    args_schema: Type[BaseModel] = OrderInput

    def _run(
        self,
        column_name: str,
        type: OrderByType,
        config: RunnableConfig,
        limit: int = 0,
    ):
        try:
            input = self.dag_executor.get_node(self.target.parent_node_names[0])
            self.target.node_data = TableNodeData()
            ascending = bool(type == OrderByType.ASC)
            target_data = input.node_data.data.sort_values(
                by=column_name, ascending=ascending
            )
            if limit > 0:
                target_data = target_data.head(limit)
            self.target.node_data.data = target_data.reset_index(drop=True)
            self.target.node_data.schema = input.node_data.copy_schema()
        except Exception as e:
            reporter = config[CHAIN_META][ChainMeta.AGENT_REPORTER]
            nl2agent_steps_list_tuple = (
                self.target.param_key,
                [ProgressRunStatus.FAILED, e],
            )
            reporter.report(
                breadcrumbs=self.breadcrumbs,
                config=config,
                nl2agent_steps_list_tuple=nl2agent_steps_list_tuple,
            )
            raise e
        reporter = config[CHAIN_META][ChainMeta.AGENT_REPORTER]
        nl2agent_steps_list_tuple = (
            self.target.param_key,
            [ProgressRunStatus.SUCCEED, self.target],
        )
        reporter.report(
            breadcrumbs=self.breadcrumbs,
            config=config,
            nl2agent_steps_list_tuple=nl2agent_steps_list_tuple,
        )
        return self.target

    def verify_degree(self, degree):
        return degree == 1
