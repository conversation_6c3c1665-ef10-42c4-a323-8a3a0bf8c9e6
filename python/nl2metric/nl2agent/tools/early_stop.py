from langchain.tools import BaseTool as LangChainBaseTool
from pydantic import BaseModel
from typing import List, Type
from nl2agent.tools.base_tool import register_tool


class EarlyStopInput(BaseModel):
    pass


@register_tool(name="early_stop")
class EarlyStopTool(LangChainBaseTool):
    name: str = "early_stop"
    description: str = "提前停止。当且仅当仅使用工具即可回答用户问题，不需要进一步处理或分析时，必须使用提前终止"
    args_schema: Type[BaseModel] = EarlyStopInput

    def _run(self):
        pass
