from abc import ABC
from common.types.base import (
    CHAIN_META,
    ChainMeta,
    ChainRuntime,
)
from common.types.exception_cacher import _exception_to_code, ResponseCode
from common.types.exceptions import NeedManualSelect
from langchain.tools import BaseTool as LangChainBaseTool
from langchain_core.runnables import <PERSON>nableLambda, RunnableConfig, RunnableParallel
from nl2agent.dag.node import Node
from nl2agent.dag.dag_executor import DagExecutor
from pydantic import BaseModel
from pydantic.v1 import BaseModel as BaseModelV1
from typing import Optional, List

REGISTRY_TOOLS = {}


def get_tool(tool_name):
    if tool_name not in REGISTRY_TOOLS:
        raise ValueError(f"Tool {tool_name} not registered.")
    return REGISTRY_TOOLS[tool_name]


def register_tool(name, allow_overwrite=False):
    def decorator(cls):
        if not issubclass(cls, LangChainBaseTool):
            raise ValueError(f"Tool {name} must be subclass of BaseTool.")

        if name in REGISTRY_TOOLS:
            if not allow_overwrite:
                raise ValueError(
                    f"Tool {name} already registered with {REGISTRY_TOOLS[name].__name__}."
                )
        if issubclass(cls, BaseModel):
            if cls.model_fields["name"].default and (
                cls.model_fields["name"].default != name
            ):
                raise ValueError(
                    f'{cls.__name__}.name="{cls.name}" conflicts with @register_tool(name="{name}").'
                )
        if issubclass(cls, BaseModelV1):
            if cls.name and (cls.name != name):
                raise ValueError(
                    f'{cls.__name__}.name="{cls.name}" conflicts with @register_tool(name="{name}").'
                )
        cls.name = name
        REGISTRY_TOOLS[name] = cls
        return cls

    return decorator


def _run_tool(input, tool_call, config: RunnableConfig):
    tool_cls = get_tool(tool_call["name"])
    tool = tool_cls()
    # verify param
    args_schema: BaseModel = tool.args_schema
    if args_schema is not None:
        input_item = args_schema.parse_obj(tool_call["args"])
        # get verified kwargs
        tool_kw_params = input_item.dict()
    else:
        tool_kw_params = tool_call["args"]

    if "tool_chain" in tool_call:
        chain = tool_call["tool_chain"]
    else:
        chain = tool
        # 把tool存下来，要不然下次人工选择的时候状态就丢了
        tool_call["tool_chain"] = tool
    result = {
        "name": tool_call["name"],
        "args": tool_kw_params,
        "original_tool_call": tool_call,
        "breadcrumbs": tool_call["breadcrumbs"],
    }

    reporter = config[CHAIN_META][ChainMeta.AGENT_REPORTER]
    # report running
    reporter.report(
        breadcrumbs=result["breadcrumbs"],
        tool_type=tool_call["name"],
        config=config,
    )

    try:
        result["code"] = 0
        result["result"] = chain.invoke(tool_kw_params, config=config)
    except NeedManualSelect as e:
        err = _exception_to_code(e)
        result["code"] = int(err["code"])
        result["result"] = e.manual_selects
    except Exception as e:
        err = _exception_to_code(e)
        result["code"] = int(err["code"])
        result["result"] = err["data"]
    code = result["code"]
    if tool_call["name"] in ("fast_lookup", "bi") and config[CHAIN_META][
        ChainMeta.RUN_TIME
    ].get(ChainRuntime.PARAMS_EXTRACT_DATA, []):
        # hack， doc metric case, 不需要前端展示
        code = ResponseCode.NOT_NEED_BI_RESULT.value

    # report result
    reporter.report(
        breadcrumbs=result["breadcrumbs"],
        code=code,
        data=result["result"],
        config=config,
    )
    return result


def run_tools(tool_calls, config: RunnableConfig):
    if not tool_calls:
        return None
    elif len(tool_calls) == 1:
        tool_call = tool_calls[0]
        return [_run_tool(None, tool_call, config)]
    else:
        chains = {
            i: RunnableLambda(_run_tool, name=f"{tool_call['name']}_{i}").bind(
                tool_call=tool_call
            )
            for i, tool_call in enumerate(tool_calls)
        }
        result_dict = RunnableParallel(chains).invoke(None, config=config)
        return [result_dict[k] for k in sorted(result_dict.keys())]


class BaseTool(LangChainBaseTool, ABC):
    dag_executor: Optional[DagExecutor] = None
    target: Optional[Node] = None
    breadcrumbs: Optional[List[str]] = None

    def verify_degree(self, degree):
        raise NotImplementedError("verify_degree not supported")

    def table_description(self, kwargs):
        return ""

    def verify_result(self, target: Node):
        if target.node_data.data.shape[0] == 0 and target.node_data.data.shape[1] == 0:
            return False, "no rows and columns"
        try:
            target.node_data.verify()
        except Exception as e:
            return False, f"node_data.verify failed {e}"

        return True, None

    def manual_fill_params(self, kw_params):
        if not self.target:
            return
        if self.target.tool_kw_params:
            return

        args_schema: BaseModel = self.args_schema
        input_item = args_schema.parse_obj(kw_params)
        # get verified args and kwargs
        field_order = list(args_schema.__fields__.keys())
        self.target.tool_params = [getattr(input_item, field) for field in field_order]
        self.target.tool_kw_params = input_item.dict()
