from common.types.base import CHAIN_META, ChainMeta, ChainRuntime
from langchain.tools import BaseTool as LangChainBaseTool
from langchain_core.pydantic_v1 import BaseModel, Field
from langchain_core.runnables import RunnableConfig
from nl2agent.dag.node import Node
from nl2agent.tools.base_tool import register_tool
from nl2agent.tools.lookup_data import FastLookupTool
from nl2intent.types import Intent, intent2type
from typing import List, Type
from common.trace import tracer


class MetricAttrInput(BaseModel):
    query: str = Field(description="用户输入的查询")


@register_tool(name="metric_attr")
class MetricAttrTool(LangChainBaseTool):
    name: str = "metric_attr"
    description: str = "归因工具。能力范围：维度下钻，只能用于指标异动分析"
    args_schema: Type[BaseModel] = MetricAttrInput
    fastlookup_tool: FastLookupTool = Field(
        default_factory=lambda: FastLookupTool(
            target=Node(param_key="attr_fast_lookup_target", tool_name="fast_lookup"),
            do_query=False,
            extra_additional_info={
                "always_check_manual_select": True,
                "enable_group_bys": False,
                "enable_where": True,
                "enable_time_query": False,
                "enable_time_dimension": False,
                "enable_metrics": True,
                "enable_order_bys": False,
            },
        )
    )

    def _run(self, query: str, config: RunnableConfig):
        early_stop = config[CHAIN_META][ChainMeta.RUN_TIME].get(
            ChainRuntime.EARLY_STOP, False
        )

        from http_router.api.query_metric_attr import query_metric_attr

        results = query_metric_attr(
            trace_id=tracer.create_trace_id(),
            question=query,
            project_name=config[CHAIN_META][ChainMeta.PROJECT_NAME],
            project_id=config[CHAIN_META][ChainMeta.PROJECT_ID],
            model_name=config[CHAIN_META][ChainMeta.MODEL_NAME],
            model_label=config[CHAIN_META][ChainMeta.MODEL_LABEL],
            model_type=config[CHAIN_META][ChainMeta.LOOKUP_DATA_MODEL_TYPE],
            model_id=config[CHAIN_META][ChainMeta.MODEL_ID],
            user_id=config[CHAIN_META].get(ChainMeta.USER_ID, None),
            fastlookup_tool=self.fastlookup_tool,
            manual_selects_result=config[CHAIN_META].get(
                ChainMeta.MANUAL_SELECTS_RESULT, {}
            ),
            additional_info=config[CHAIN_META].get(ChainMeta.ADDITIONAL_INFO, None),
            do_query=True,
            agent_input=config[CHAIN_META][ChainMeta.RUN_TIME].get(
                ChainRuntime.AGENT_BRAIN_INPUT, None
            ),
        )
        if not results:
            raise RuntimeError(f"query_metric_attr question {query} git nothing")
        metric_attr_result = {}
        for model_id, result in results.items():
            attribution_analysis = result["result"]
            type = intent2type(Intent.attribution_analysis)
            if "type" in attribution_analysis:
                type = attribution_analysis["type"]
            metric_attr_result[model_id] = {
                "type": type,
                "attribution_analysis": attribution_analysis,
                "agent_msg": result["agent_msg"],
            }
        return metric_attr_result
