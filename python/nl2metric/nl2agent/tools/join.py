import pandas as pd
from langchain_core.runnables import RunnableConfig

from backend_stage_reporter.reporter import ProgressRunStatus
from common.types.base import CHAIN_META, ChainMeta
from nl2agent.types import JoinType
from nl2agent.tools.base_tool import BaseTool, register_tool
from nl2agent.dag.node import Node
from nl2agent.dag.table_node_data import TableNodeData
from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Union, Type


MERGE_HOW = {
    JoinType.INNER_JOIN: "inner",
    JoinType.LEFT_JOIN: "left",
    JoinType.RIGHT_JOIN: "right",
    JoinType.FULL_OUTER_JOIN: "outer",
    JoinType.CROSS_JOIN: "cross",
}


def _rename_on(on, rename):
    if not rename:
        return on
    if isinstance(on, str):
        return rename.get(on, on)
    else:
        return [rename.get(item, item) for item in on]


def _verify_suffix(suffix, all_column_names):
    orig_suffix = suffix
    for i in range(100):
        need_new = False
        for col in all_column_names:
            if col.endswith(suffix):
                need_new = True
                break
        if need_new:
            suffix = f"{orig_suffix}{i}"
        else:
            return suffix
    raise RuntimeError(f"suffix verify failed")


def join(
    l: Node,
    r: Node,
    target: Node,
    join_type: JoinType,
    l_on: Optional[Union[str, List[str]]],
    r_on: Optional[Union[str, List[str]]],
    l_rename: Optional[Dict[str, str]] = None,
    r_rename: Optional[Dict[str, str]] = None,
):
    if l_on:
        l_on = _rename_on(l_on, l_rename)
        if isinstance(l_on, list) and (len(set(l_on)) != len(l_on)):
            raise RuntimeError(f"invalid l_on {l_on}")
    elif join_type != JoinType.CROSS_JOIN:
        raise RuntimeError(f"join_type {join_type} needs l_on")
    if r_on:
        r_on = _rename_on(r_on, r_rename)
        if isinstance(r_on, list) and (len(set(r_on)) != len(r_on)):
            raise RuntimeError(f"invalid r_on {r_on}")
    elif join_type != JoinType.CROSS_JOIN:
        raise RuntimeError(f"join_type {join_type} needs r_on")

    # rename table
    if l_rename:
        l.node_data.rename(l_rename)
    if r_rename:
        r.node_data.rename(r_rename)

    all_column_names = set(l.node_data.column_names)
    all_column_names.update(r.node_data.column_names)
    l_suffix = _verify_suffix(f"_{target.name}l", all_column_names)
    r_suffix = _verify_suffix(f"_{target.name}r", all_column_names)

    # join table
    target.node_data.data = pd.merge(
        l.node_data.data,
        r.node_data.data,
        left_on=l_on,
        right_on=r_on,
        how=MERGE_HOW[join_type],
        suffixes=(l_suffix, r_suffix),
    )

    # get node_data.schema
    target.node_data.schema = []
    l_schema = l.node_data.columns_dict
    r_schema = r.node_data.columns_dict
    for target_col in target.node_data.data.columns:
        if target_col.endswith(l_suffix):
            orig_col = l_schema[target_col[: -len(l_suffix)]]
        elif target_col.endswith(r_suffix):
            orig_col = r_schema[target_col[: -len(r_suffix)]]
        elif target_col in l_schema:
            orig_col = l_schema[target_col]
        else:
            orig_col = r_schema[target_col]
        target.node_data.schema.append(orig_col.copy(target_col))


def auto_join(nodes: List[Node], target: Node):
    if not nodes:
        return
    elif len(nodes) == 1:
        target.node_data.data = nodes[0].node_data.data.copy(deep=True)
        target.node_data.schema = nodes[0].node_data.copy_schema()
        return

    common_col = None
    for node in nodes:
        if common_col is None:
            common_col = set(node.node_data.non_calculable_column_names())
        else:
            common_col = common_col.intersection(
                node.node_data.non_calculable_column_names()
            )
    if common_col:
        common_col = list(common_col)
        # inner join
        join(
            nodes[0],
            nodes[1],
            target,
            JoinType.INNER_JOIN,
            l_on=common_col,
            r_on=common_col,
        )
        for i in range(2, len(nodes)):
            l_table = target.deepcopy()
            r_table = nodes[i].deepcopy()
            join(
                l_table,
                r_table,
                target,
                JoinType.INNER_JOIN,
                l_on=common_col,
                r_on=common_col,
            )
        if not target.node_data.data.empty:
            return

    # cross join
    join(
        nodes[0],
        nodes[1],
        target,
        JoinType.CROSS_JOIN,
        l_on=None,
        r_on=None,
    )
    for i in range(2, len(nodes)):
        l_table = target.deepcopy()
        r_table = nodes[i].deepcopy()
        join(
            l_table,
            r_table,
            target,
            JoinType.CROSS_JOIN,
            l_on=None,
            r_on=None,
        )


class AutoJoinInput(BaseModel):
    # no input needed
    pass


@register_tool(name="auto_join")
class AutoJoinTool(BaseTool):
    name: str = "auto_join"
    description: str = "自动将多个表join到一起。如果这些表有公共的列，就会使用这些列作inner_join，否则直接做cross_join"
    args_schema: Type[BaseModel] = AutoJoinInput

    def _run(self):
        self.target.node_data = TableNodeData()
        nodes = [
            self.dag_executor.get_node(node_name)
            for node_name in self.target.parent_node_names
        ]
        auto_join(nodes, self.target)
        return self.target

    def verify_degree(self, degree):
        return degree > 0


class JoinInput(BaseModel):
    l: str = Field(description="左表")
    r: str = Field(description="右表")
    join_type: JoinType = Field(
        description="连接类型，可选left_join, right_join, full_join, inner_join"
    )
    l_on: Optional[Union[str, List[str]]] = Field(default=None, description="左表连接列")
    r_on: Optional[Union[str, List[str]]] = Field(default=None, description="右表连接列")
    l_rename: Optional[Dict[str, str]] = Field(
        default=None, description="左表连接列重命名，字典中分别是新列名，旧列名"
    )
    r_rename: Optional[Dict[str, str]] = Field(
        default=None, description="右表连接列重命名，字典中分别是新列名，旧列名"
    )


@register_tool(name="join")
class JoinTool(BaseTool):
    name: str = "join"
    description: str = "动态模拟工具子工具，对两个表进行连接"
    args_schema: Type[BaseModel] = JoinInput

    def _run(
        self,
        l: str,
        r: str,
        join_type: JoinType,
        config: RunnableConfig,
        l_on: Optional[Union[str, List[str]]] = None,
        r_on: Optional[Union[str, List[str]]] = None,
        l_rename: Optional[Dict[str, str]] = None,
        r_rename: Optional[Dict[str, str]] = None,
    ):
        try:
            # deepcopy to avoid changing original node:
            # 1. rename will change node
            # 2. date_offset will change node
            # deepcopy makes self join much easier
            self.target.node_data = TableNodeData()
            l_node = self.dag_executor.get_node(l).deepcopy()
            l_node.node_data.create_tmp_column(l_on)
            r_node = self.dag_executor.get_node(r).deepcopy()
            r_node.node_data.create_tmp_column(r_on)
            join(
                l=l_node,
                r=r_node,
                target=self.target,
                join_type=join_type,
                l_on=l_on,
                r_on=r_on,
                l_rename=l_rename,
                r_rename=r_rename,
            )
            self.target.node_data.clean_tmp_column()
        except Exception as e:
            reporter = config[CHAIN_META][ChainMeta.AGENT_REPORTER]
            nl2agent_steps_list_tuple = (
                self.target.param_key,
                [ProgressRunStatus.FAILED, e],
            )
            reporter.report(
                breadcrumbs=self.breadcrumbs,
                config=config,
                nl2agent_steps_list_tuple=nl2agent_steps_list_tuple,
            )
            raise e
        reporter = config[CHAIN_META][ChainMeta.AGENT_REPORTER]
        nl2agent_steps_list_tuple = (
            self.target.param_key,
            [ProgressRunStatus.SUCCEED, self.target],
        )
        reporter.report(
            breadcrumbs=self.breadcrumbs,
            config=config,
            nl2agent_steps_list_tuple=nl2agent_steps_list_tuple,
        )
        return self.target

    def verify_degree(self, degree):
        return degree == 2 or degree == 1  # self join
