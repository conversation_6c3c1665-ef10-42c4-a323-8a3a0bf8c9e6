import json

from langchain_core.messages import ChatMessage

from common.utils.llm_utils import create_llm_model_by_project_config
from config.project_config import get_project_config
from nl2agent.tools.base_tool import BaseTool, register_tool
from common.llm.general import create_chat_model
from typing import Type, Dict
from pydantic import BaseModel, Field
from langchain_core.runnables import RunnableConfig, RunnableLambda, RunnableParallel
from langchain_core.output_parsers import StrOutputParser
from common.types.base import (
    CHAIN_META,
    ChainMeta,
    ChainRuntime,
    ParamsExtractStage,
)
from nl2agent.common.agent_reporter import reporter_run_chain
from typing import Optional, List

from tools.doc_tool import RetrieverDocument
from tools.web_tools import WebSearcherTool


# ATTATION: chat工具的入参是框架拼进去的，模型不感知
# 所以args_schema不能有入参，_run需要入参
# 直接使用上面的ChatInput会导致参数校验失败，所以args_schema是None
# 但是不加args_schema的话convert_to_openai_function又会出错
# 所有这里只能分成两个类了


class ChatInputOnlyForDisplay(BaseModel):
    pass


def chat_preprocess(input: dict, config: RunnableConfig):
    history = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.HISTORY_MESSAGES]
    if not history:
        history = []
    prompt_selector = config[CHAIN_META][ChainMeta.PROMPT_SELECTOR]
    messages = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.MESSAGES_RECORD]
    extra_info = [
        m.content for m in messages if (isinstance(m, ChatMessage) and m.role == "tool")
    ]
    doc_search = input["doc_search"]
    web_search = input["web_search"]
    input["doc_content"] = ""
    if doc_search:
        textNodes: List = (
            doc_search.get("data", {}).get("sourceNodes", {}).get("textNodes", [])
        )
        doc_content = "\n".join(
            [
                node.get("content") + ", fileName:" + node.get("fileName")
                for node in textNodes
            ]
        )
        input["doc_content"] = doc_content
    stage = ParamsExtractStage.AGENT_CHAT
    if doc_search or web_search:
        stage = ParamsExtractStage.AGENT_DOC_CHAT
    prompt = prompt_selector.gen_prompt(
        input={
            "question": config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.QUESTION],
            "history": history,
            "extra_info": extra_info,
            **config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.HINT],
            **input,
        },
        stage=stage,
        config=config,
    )
    return {"prompt": prompt, **input}


class ChatToolOnlyForDisplay(BaseTool):
    name: str = "chat"
    description: str = "全能工具。能力范围：除针对环境中数据查询和处理外的所有问题"
    args_schema: Type[BaseModel] = ChatInputOnlyForDisplay

    def _run(self):
        raise NotImplementedError()


def process_message(inputs: Dict[str, str], config: RunnableConfig):
    project_config = get_project_config(
        project_name=config[CHAIN_META][ChainMeta.PROJECT_NAME],
        model_name=config[CHAIN_META][ChainMeta.MODEL_NAME],
    )
    model_type = (
        "base_model_nothink"
        if inputs.get("think", "false") == "false"
        else "base_model"
    )
    llm = create_llm_model_by_project_config(model_type, project_config)
    chains = (
        RunnableLambda(lambda input: input.get("prompt", "")) | llm | StrOutputParser()
    )

    for chunk in chains.stream(inputs, config):
        yield chunk

    if inputs.get("doc_search", ""):
        yield "\n\n"
        yield "======sourceNodes=======:\n"
        yield json.dumps(inputs.get("doc_search", ""), ensure_ascii=False, indent=2)


class Input(BaseModel):
    think: str = Field(
        description="是否需要思考能力。如果需要深度推理或综合分析多个信息源，为true。如果只有单个信息源，不需要思考能力，那么为false"
    )


@register_tool(name="chat")
class ChatTool(BaseTool):
    name: str = "chat"
    description: str = "全能工具。能力范围：除针对环境中数据查询和处理外的所有问题。chat 工具与其他工具互斥，不可同时使用。"
    args_schema: Type[BaseModel] = Input
    breadcrumbs: Optional[List[str]] = None

    def _run(self, think, config: RunnableConfig):
        # run_time
        rt_enable_doc_search = config[CHAIN_META][ChainMeta.RUN_TIME].get(
            ChainMeta.ENABLE_DOC_SEARCH, True
        )
        rt_enable_internet_search = config[CHAIN_META][ChainMeta.RUN_TIME].get(
            ChainMeta.ENABLE_INTERNET_SEARCH, rt_enable_doc_search
        )
        # config
        enable_internet_search = config[CHAIN_META][ChainMeta.ENABLE_INTERNET_SEARCH]
        enable_doc_search = config[CHAIN_META][ChainMeta.ENABLE_DOC_SEARCH]
        query = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.QUESTION]

        action = {
            "web_search": WebSearcherTool()
            if enable_internet_search and rt_enable_internet_search
            else lambda _: "",
            "doc_search": RetrieverDocument()
            if enable_doc_search and rt_enable_doc_search
            else lambda _: "",
            "think": lambda _: think,
        }
        chain = (
            RunnableParallel(action)
            | RunnableLambda(chat_preprocess, name="chat_preprocess")
            | RunnableLambda(process_message, name="llm_chat_and_doc_resource_node")
            | StrOutputParser()
        )
        chain.name = "chat"
        return reporter_run_chain(
            breadcrumbs=self.breadcrumbs,
            chain=chain,
            input={"query": query, "think": think},
            config=config,
            name=self.name,
            # chat没有后续步骤，
            # 所以大模型输出结束后直接上报succeed
            code=0,
        )
