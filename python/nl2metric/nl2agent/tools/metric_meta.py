from common.manual_select.manual_select import ManualSelectTool
from common.types.base import (
    CHAIN_META,
    ChainMeta,
    ChainRuntime,
    JobType,
    add_model_to_chain_meta,
    del_model_from_chain_meta,
)
from common.types.exceptions import ParamsExtractEmptyResult, NeedManualSelect
from config.project_config import get_project_config
from langchain.tools import BaseTool as LangChainBaseTool
from langchain_core.runnables import RunnableConfig
from metastore.service import get_db_appstore
from nl2agent.tools.base_tool import register_tool
from langchain_core.pydantic_v1 import BaseModel, Field
from typing import Dict, Type
from common.trace import tracer
from nl2meta.nl2meta import MetaIntentParams


class MetricMetaInput(BaseModel):
    query: str = Field(description="用户输入的查询")


@register_tool(name="metric_meta")
class MetricMetaTool(LangChainBaseTool):
    name: str = "metric_meta"
    description: str = "元数据工具。能力范围：指标、维度详情；指标、维度列表；系统数据概述（例如查询系统前5条数据）"
    args_schema: Type[BaseModel] = MetricMetaInput
    manual_select_tool: ManualSelectTool = Field(default_factory=ManualSelectTool)

    def _run(self, query: str, config: RunnableConfig):
        from http_router.api.query_metric_meta import query_metric_meta, query_meta
        from http_router.api.project_query_metric_meta import project_query_metric_meta

        early_stop = config[CHAIN_META][ChainMeta.RUN_TIME].get(
            ChainRuntime.EARLY_STOP, False
        )
        is_project_metric_meta = not config[CHAIN_META][ChainMeta.MODEL_ID]
        project_name = config[CHAIN_META][ChainMeta.PROJECT_NAME]
        project_id = config[CHAIN_META][ChainMeta.PROJECT_ID]

        app_store = get_db_appstore()

        def _project_query_meta(
            model_id, meta_intent_params: MetaIntentParams, config: RunnableConfig
        ):
            semantic_model = app_store.get_model_by_id(model_id)
            add_model_to_chain_meta(config[CHAIN_META], semantic_model)
            result = query_meta(meta_intent_params, config)
            del_model_from_chain_meta(config[CHAIN_META])
            return result

        # 产品要求不做置信度选择
        # TODO: 注意不做选择的话，后面场景多了很可能会超长
        check_manual_select = False
        # check_manual_select = (
        #     config[CHAIN_META][ChainMeta.JOB_TYPE] == JobType.AGENT
        #     and (not early_stop)
        #     and get_project_config(
        #         project_name, config[CHAIN_META][ChainMeta.MODEL_NAME]
        #     ).enable_manual_select
        # )

        if is_project_metric_meta:
            if self.manual_select_tool.need_manual_select:
                all_manual_selects_result = config[CHAIN_META].get(
                    ChainMeta.MANUAL_SELECTS_RESULT, {}
                )
                manual_select_result = all_manual_selects_result.get(
                    self.manual_select_tool.manual_select_id, {}
                )["data"]
                if not manual_select_result:
                    raise NeedManualSelect(self.manual_select_tool.manual_selects)
                model_id, meta_intent_params = next(iter(manual_select_result.items()))
                return str(
                    _project_query_meta(
                        model_id=model_id,
                        meta_intent_params=meta_intent_params,
                        config=config,
                    )
                )
            else:
                project_result: Dict[str, MetaIntentParams] = project_query_metric_meta(
                    trace_id=tracer.create_trace_id(),
                    question=query,
                    project_name=config[CHAIN_META][ChainMeta.PROJECT_NAME],
                    project_id=config[CHAIN_META][ChainMeta.PROJECT_ID],
                    model_type=config[CHAIN_META][ChainMeta.LOOKUP_DATA_MODEL_TYPE],
                    user_id=config[CHAIN_META].get(ChainMeta.USER_ID, None),
                    additional_info=config[CHAIN_META].get(
                        ChainMeta.ADDITIONAL_INFO, None
                    ),
                )
                if not project_result:
                    raise ParamsExtractEmptyResult(
                        f"project {project_id} project_query_metric_meta got no model"
                    )
                elif len(project_result) > 1 and check_manual_select:
                    self.manual_select_tool.need_manual_select = True
                    self.manual_select_tool.manual_selects = {
                        self.manual_select_tool.manual_select_id: {
                            "type": "project_query_metric_meta",
                            "data": project_result,
                            "query": query,
                        }
                    }
                    raise NeedManualSelect(self.manual_select_tool.manual_selects)
                else:
                    if early_stop:
                        return {
                            model_id: meta_intent_params.model_dump()
                            for model_id, meta_intent_params in project_result.items()
                        }
                    else:
                        result = ""
                        for model_id, meta_intent_params in project_result.items():
                            model_label = app_store.get_model_by_id(model_id).label
                            meta_attr_result = str(
                                _project_query_meta(
                                    model_id=model_id,
                                    meta_intent_params=meta_intent_params,
                                    config=config,
                                )
                            )
                            result += f"{model_label}:\n{meta_attr_result}\n\n"
                        return result

        else:
            result: MetaIntentParams = query_metric_meta(
                trace_id=tracer.create_trace_id(),
                question=query,
                project_name=config[CHAIN_META][ChainMeta.PROJECT_NAME],
                project_id=config[CHAIN_META][ChainMeta.PROJECT_ID],
                model_name=config[CHAIN_META][ChainMeta.MODEL_NAME],
                model_label=config[CHAIN_META][ChainMeta.MODEL_LABEL],
                model_type=config[CHAIN_META].get(
                    ChainMeta.LOOKUP_DATA_MODEL_TYPE,
                    config[CHAIN_META][ChainMeta.MODEL_TYPE],
                ),
                model_id=config[CHAIN_META][ChainMeta.MODEL_ID],
                user_id=config[CHAIN_META].get(ChainMeta.USER_ID, None),
                additional_info=config[CHAIN_META].get(ChainMeta.ADDITIONAL_INFO, None),
            )

            if early_stop:
                model_id = config[CHAIN_META][ChainMeta.MODEL_ID]
                return {model_id: result.model_dump()}
            else:
                return str(query_meta(result, config=config))
