from nl2agent.tools.base_tool import BaseTool, register_tool
from nl2agent.dag.node import Node
from common.types.base import CHAIN_META, ChainMeta
from langchain_core.runnables import RunnableConfig
from nl2time_query.nl2time_query import call_nl2time_query
from pydantic import BaseModel, Field
from typing import Type


def get_time_range(
    target: Node,
    question: str,
    config: RunnableConfig,
):
    model_type = config[CHAIN_META][ChainMeta.MODEL_TYPE]
    prompt_selector = config[CHAIN_META][ChainMeta.PROMPT_SELECTOR]
    project_name = config[CHAIN_META][ChainMeta.PROJECT_NAME]
    model_name = config[CHAIN_META][ChainMeta.MODEL_NAME]
    chain = call_nl2time_query(
        model_type=model_type,
        prompt_selector=prompt_selector,
        project_name=project_name,
        model_name=model_name,
    )
    target.meta["time_range"] = chain.invoke(question, config=config)


class TimeRangeInput(BaseModel):
    time_range: str = Field(description="描述时间范围的一段话")


@register_tool(name="time_range")
class TimeRangeTool(BaseTool):
    name: str = "time_range"
    description: str = "获得给定问题涉及的时间范围"
    args_schema: Type[BaseModel] = TimeRangeInput

    def _run(self, time_range: str, config: RunnableConfig):
        get_time_range(self.target, time_range, config)
        return self.target

    def verify_degree(self, degree):
        return degree == 0

    def table_description(self, kwargs):
        return kwargs["time_range"]

    def verify_result(self, target: Node):
        if not target.meta["time_range"]:
            return False, "found no time_range"
        return True, None
