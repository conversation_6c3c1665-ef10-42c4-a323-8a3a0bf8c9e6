from metastore.base import safe_match_time_format
from nl2agent.tools.base_tool import BaseTool, register_tool
from nl2agent.dag.table_node_data import TableNodeData
from nl2agent.types import (
    ColumnType,
    CalculatedColumn,
    TableColumnMeta,
    TableColumn,
)
from prediction.time_series.time_series import Time_Series, convert_date_format
from typing import Type
from pydantic import BaseModel, Field


class PredictInput(BaseModel):
    data_node_name: str = Field(description="历史数据对应的表名")
    time_range_node_name: str = Field(description="待预测的未来时间对应的对象名")


@register_tool(name="time_series_service")
class PredictTool(BaseTool):
    name: str = "time_series_service"
    description: str = "预测数据未来走势"
    args_schema: Type[BaseModel] = PredictInput

    def _run(self, data_node_name: str, time_range_node_name: str):
        self.target.node_data = TableNodeData()
        data_node = self.dag_executor.get_node(data_node_name)
        time_range_node = self.dag_executor.get_node(time_range_node_name)

        # check data_node
        x = None
        dt = None
        for col in data_node.node_data.schema:
            if col.is_metric:
                x = col.column_name
            # overwrite unknown dt with is_time_dimension dt
            elif col.is_time_dimension:
                dt = col.column_name
            elif dt is None and col.is_unknown:
                dt = col.column_name
        if not x or not dt:
            raise RuntimeError(
                f"predict df invalid: {data_node.node_data.column_names}"
            )
        data_df = data_node.node_data.data[[dt, x]]

        try:
            timeGranularity = data_node.meta[
                "query_metric"
            ].timeQueryParams.timeGranularity
        except Exception as e:
            raise RuntimeError("cannot find timeGranularity in data_node of predict")

        # check time_range_node
        pred_range = time_range_node.meta["time_range"].dict()
        pred_range[
            "timeGranularity"
        ] = timeGranularity  # overwrite with lookup_data timeGranularity

        # do predict
        data_df[dt] = data_df[dt].apply(
            lambda x: convert_date_format(x, pred_range["timeGranularity"])
        )
        ts = Time_Series(data=data_df, dt=dt, x=x)
        ts.fit()
        result_df = ts.predict(pred_range).reset_index()
        result_df = result_df[["index", "mean"]].rename(
            columns={"index": dt, "mean": x}
        )
        result_df[dt] = result_df[dt].apply(
            lambda x: convert_date_format(x, pred_range["timeGranularity"])
        )
        self.target.node_data.data = result_df

        # init node_data.schema
        self.target.node_data.schema = []
        time_format = safe_match_time_format(self.target.node_data.data.loc[0, dt])
        for df_col_name in self.target.node_data.data.columns:
            schema_col = data_node.node_data.find_column(df_col_name, safe=True)
            if schema_col != None:
                target_col = schema_col.copy()
            else:
                target_col = TableColumn(
                    column_name=df_col_name,
                    column_detail=CalculatedColumn(
                        description="",
                        expression_type=ColumnType.PREDICT,
                    ),
                    meta={TableColumnMeta.ORIGINAL_PARAM_KEY: self.target.param_key},
                )
            if df_col_name == dt:
                target_col.meta[TableColumnMeta.TIME_DIMENSION_FORMAT] = time_format
            self.target.node_data.schema.append(target_col)
        return self.target

    def verify_degree(self, degree):
        return degree == 2
