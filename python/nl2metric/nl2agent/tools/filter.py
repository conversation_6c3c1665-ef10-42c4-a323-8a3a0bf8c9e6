import re

from langchain_core.runnables import RunnableConfig

from backend_stage_reporter.reporter import ProgressRunStatus
from common.logging.logger import get_logger
from common.types.base import CHAIN_META, ChainMeta
from common.utils.string_utils import trim_quotes
from enum import Enum
from metastore.base import format_time
from nl2agent.tools.base_tool import BaseTool, register_tool
from nl2agent.dag.node import Node
from nl2agent.dag.table_node_data import TableNodeData
from nl2agent.types import TableColumn, TableColumnMeta
from nl2metric.query_where import _split_operator_condition
from typing import Type, Any, List
from pydantic import BaseModel, Field

logger = get_logger(__name__)


class SubFilterCondition(BaseModel):
    name: str
    operator: str
    conditions: List[Any]

    def __init__(self, name: str, operator: str, conditions: Any, column: TableColumn):
        processed_conditions = []
        is_metric = column.is_metric
        is_calculated = column.is_calculated
        is_time_dimension = column.is_time_dimension
        time_dimension_format = column.meta.get(
            TableColumnMeta.TIME_DIMENSION_FORMAT, None
        )
        for raw_condition in conditions:
            condition = trim_quotes(raw_condition)
            if is_metric or is_calculated:
                processed_conditions.append(condition)
            elif is_time_dimension and (time_dimension_format != None):
                processed_conditions.append(
                    f'"{format_time(condition, time_dimension_format)}"'
                )
            else:
                processed_conditions.append(f'"{condition}"')
        super().__init__(name=name, operator=operator, conditions=processed_conditions)

    def as_dataframe_query(self):
        if not self.conditions:
            return None
        if self.operator.upper() == "IN":
            condition = ", ".join(self.conditions)
            return f"{self.name} in [{condition}]"
        elif self.operator.upper() == "NOT IN":
            condition = ", ".join(self.conditions)
            return f"{self.name} not in [{condition}]"
        else:
            assert (
                len(self.conditions) == 1
            ), f"name {self.name} operator {self.operator} conditions {self.conditions} not one"
            operator = self.operator
            if operator == "=":
                operator = "=="
            return f"{self.name} {operator} {self.conditions[0]}"


class FilterLink(Enum):
    AND = "AND"
    OR = "OR"


DEFAULT_FILTER_LINK = FilterLink.AND


class FilterCondition(BaseModel):
    filter_link: FilterLink = DEFAULT_FILTER_LINK
    sub_filters: List[SubFilterCondition] = []

    def as_dataframe_query(self):
        if self.filter_link == FilterLink.AND:
            link = " & "
        else:
            link = " | "

        conditions = []
        for s in self.sub_filters:
            tmp = s.as_dataframe_query()
            if tmp:
                conditions.append(tmp)
        if len(conditions) > 1:
            return link.join([f"({c})" for c in conditions])
        elif len(conditions) == 1:
            return conditions[0]
        else:
            return None


def split_filter_expr(expr: str, input: Node) -> FilterCondition:
    # this code is quite similar to split_where_sql
    pattern_and = re.compile(r"\s+AND\s+", re.IGNORECASE)
    found_and = bool(pattern_and.search(expr))
    pattern_or = re.compile(r"\s+OR\s+", re.IGNORECASE)
    found_or = bool(pattern_or.search(expr))
    if found_and and found_or:
        raise RuntimeError(f"found both 'and' and 'or' in [{expr}]")

    if found_and:
        filter_link = FilterLink.AND
    elif found_or:
        filter_link = FilterLink.OR
    else:
        filter_link = DEFAULT_FILTER_LINK
    result = FilterCondition(filter_link=filter_link)

    # spit expr by (and/or surrounded by blank), ignore case.
    pattern = re.compile(r"\s+(AND|OR)\s+", re.IGNORECASE)
    substrings = pattern.split(expr)

    for i in range(0, len(substrings), 2):
        if (i + 1 < len(substrings)) and (
            substrings[i + 1].strip().upper() not in {"AND", "OR"}
        ):
            raise RuntimeError(f"substrings {substrings}, and/or not found at {i+1}")
        sub_expr = substrings[i].strip()
        if not sub_expr:
            continue
        try:
            name, operator, condition = _split_operator_condition(sub_expr)
            column = input.node_data.find_column(name, safe=False)
            if operator.upper() == "IN" or operator.upper() == "NOT IN":
                if condition.startswith("(") and condition.endswith(")"):
                    condition = condition[1:-1]
                    conditions = condition.split(",")
                else:
                    logger.error(
                        f"split_filter_expr original expr [{expr}], i {i}, sub_expr [{sub_expr}] illegal IN or NOT IN"
                    )
                    continue
            else:
                conditions = [
                    condition,
                ]
            result.sub_filters.append(
                SubFilterCondition(name, operator, conditions, column)
            )
        except Exception as e:
            logger.error(
                f"split_filter_expr original expr [{expr}], i {i}, sub_expr [{sub_expr}] illegal, exception {e}"
            )
            continue
    return result


class FilterInput(BaseModel):
    expr: str = Field(description="筛选公式")


@register_tool(name="filter")
class FilterTool(BaseTool):
    name: str = "filter"
    description: str = "动态模拟工具子工具，对临时表进行筛选"
    args_schema: Type[BaseModel] = FilterInput

    def _run(self, expr: str, config: RunnableConfig):
        try:
            input = self.dag_executor.get_node(self.target.parent_node_names[0])
            self.target.node_data = TableNodeData()
            processed_expr = split_filter_expr(expr, input).as_dataframe_query()
            if not processed_expr:
                logger.warning(f"expr {expr}, processed_expr empty")
                self.target.node_data.data = input.node_data.data.copy(deep=True)
                self.target.node_data.schema = input.node_data.copy_schema()
                reporter = config[CHAIN_META][ChainMeta.AGENT_REPORTER]
                nl2agent_steps_list_tuple = (
                    self.target.param_key,
                    [ProgressRunStatus.SUCCEED, self.target],
                )
                reporter.report(
                    breadcrumbs=self.breadcrumbs,
                    config=config,
                    nl2agent_steps_list_tuple=nl2agent_steps_list_tuple,
                )
                return self.target
            else:
                self.target.node_data.data = input.node_data.data.query(
                    processed_expr
                ).reset_index(drop=True)
                self.target.node_data.schema = input.node_data.copy_schema()
                reporter = config[CHAIN_META][ChainMeta.AGENT_REPORTER]
                nl2agent_steps_list_tuple = (
                    self.target.param_key,
                    [ProgressRunStatus.SUCCEED, self.target],
                )
                reporter.report(
                    breadcrumbs=self.breadcrumbs,
                    config=config,
                    nl2agent_steps_list_tuple=nl2agent_steps_list_tuple,
                )
                return self.target
        except Exception as e:
            reporter = config[CHAIN_META][ChainMeta.AGENT_REPORTER]
            nl2agent_steps_list_tuple = (
                self.target.param_key,
                [ProgressRunStatus.FAILED, e],
            )
            reporter.report(
                breadcrumbs=self.breadcrumbs,
                config=config,
                nl2agent_steps_list_tuple=nl2agent_steps_list_tuple,
            )
            raise e

    def verify_degree(self, degree):
        return degree == 1
