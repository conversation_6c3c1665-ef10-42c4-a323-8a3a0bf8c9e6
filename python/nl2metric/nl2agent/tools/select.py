import pandas as pd
from langchain_core.runnables import RunnableConfig

from pydantic import BaseModel, Field

from backend_stage_reporter.reporter import ProgressRunStatus
from common.types.base import CHAIN_META, ChainMeta
from nl2agent.tools.base_tool import <PERSON>Tool, register_tool
from nl2agent.dag.node import Node
from nl2agent.dag.table_node_data import TableNodeData
from nl2agent.types import (
    SelectItem,
    ColumnType,
    CalculatedColumn,
    TableColumnMeta,
    TableColumn,
)
from typing import List, Optional, Type


def _create_column_schema(s: SelectItem, input: Node, param_key: str):
    if s.expression_type == ColumnType.COLUMN:
        src_col = input.node_data.find_column(s.expression)
        return src_col.copy(s.name)
    elif s.expression_type.can_select():
        return TableColumn(
            column_name=s.name,
            column_detail=CalculatedColumn(
                description="",
                expression_type=s.expression_type,
                expression=s.expression,
            ),
            meta={TableColumnMeta.ORIGINAL_PARAM_KEY: param_key},
        )
    else:
        raise RuntimeError(f"invalid select type {s.expression_type}")


def _groupby_agg(
    input: Node,
    target: Node,
    select_items: List[SelectItem],
    groupby: List[str],
):
    agg_param = {}
    for s in select_items:
        if s.expression_type == ColumnType.CALCULATE:
            raise RuntimeError(f"cannot do calculate in groupby")
        else:
            agg_param[s.name] = (s.expression, s.df_agg_type)

    target.node_data.data = (
        input.node_data.data.groupby(groupby).agg(**agg_param).reset_index(drop=True)
    )
    target.node_data.data = target.node_data.data.reindex(
        columns=[s.name for s in select_items]
    )  # just to be sure


def _agg(
    input: Node,
    target: Node,
    select_items: List[SelectItem],
):
    df = input.node_data.data
    max_min_cnt = 0
    agg_cnt = 0
    for s in select_items:
        if s.expression_type == ColumnType.CALCULATE:
            raise RuntimeError(f"cannot do calculate in groupby")
        if s.is_agg():
            agg_cnt += 1
        if s.expression_type == ColumnType.MAX or s.expression_type == ColumnType.MIN:
            max_min_cnt += 1
            max_min_select = s
    if max_min_cnt == 1 and agg_cnt == 1:
        # 只有单一的max/min，返回对应最大最小值的那一行
        if max_min_select.expression_type == ColumnType.MAX:
            max_min_id = df[max_min_select.expression].idxmax()
        else:
            max_min_id = df[max_min_select.expression].idxmin()

        select_column_names = []
        new_column_names = []
        for s in select_items:
            select_column_names.append(s.expression)
            new_column_names.append(s.name)
        select_column_values = df.loc[max_min_id, select_column_names]
        target.node_data.data = pd.DataFrame(
            [select_column_values.values], columns=new_column_names
        )
    else:
        # 有多个聚合函数，普通的select就取第一行
        new_column_names = []
        select_column_values = []
        for s in select_items:
            new_column_names.append(s.name)
            if s.expression_type == ColumnType.COLUMN:
                select_column_values.append(df.loc[0, s.expression])
            elif s.expression_type == ColumnType.COUNT:
                select_column_values.append(df[s.expression].count())
            elif s.expression_type == ColumnType.COUNT_DISTINCT:
                select_column_values.append(df[s.expression].nunique())
            elif s.expression_type == ColumnType.SUM:
                select_column_values.append(df[s.expression].sum())
            elif s.expression_type == ColumnType.AVG:
                select_column_values.append(df[s.expression].mean())
            elif s.expression_type == ColumnType.MAX:
                select_column_values.append(df[s.expression].max())
            elif s.expression_type == ColumnType.MIN:
                select_column_values.append(df[s.expression].min())
            else:
                raise RuntimeError(f"un-supported expression_type {s.expression_type}")
        target.node_data.data = pd.DataFrame(
            [select_column_values], columns=new_column_names
        )


def _calculate(
    input: Node,
    target: Node,
    select_items: List[SelectItem],
):
    df = input.node_data.data
    new_df = pd.DataFrame()
    for s in select_items:
        if s.expression_type == ColumnType.COLUMN:
            new_df[s.name] = df[s.expression]
        elif s.expression_type == ColumnType.CALCULATE:
            new_df[s.name] = df.eval(s.expression)
        else:
            raise RuntimeError(f"un-supported expression_type {s.expression_type}")
    target.node_data.data = new_df


def do_select(
    input: Node,
    target: Node,
    select_items: List[SelectItem],
    groupby: Optional[List[str]] = None,
):
    if input.empty():
        raise ValueError("Input data is empty or not provided")
    if not select_items:
        raise ValueError("select nothing")

    target.node_data = TableNodeData()
    for s in select_items:
        target.node_data.schema.append(
            _create_column_schema(s, input, target.param_key)
        )

    has_agg = any(s.is_agg() for s in select_items)
    if has_agg:
        if groupby:
            # groupby时，非agg的列取第一个值
            _groupby_agg(input, target, select_items, groupby)
        else:
            # 只有单一的max/min:取出对应的列
            # 其它情况下，非agg的列取第一行
            _agg(input, target, select_items)
    else:
        # 无agg的情况
        _calculate(input, target, select_items)


class SelectInput(BaseModel):
    select: List[SelectItem] = Field(
        description="选取的列项目, 其中SelectItem中分别需要有新生成的列名，操作类型（可选COLUMN、CALCULATE、COUNT、SUM、AVG、MAX、MIN），操作内容（原来的列名/表达式/计数列名）"
    )
    groupby: Optional[List[str]] = Field(default=None, description="分组")


@register_tool(name="select")
class SelectTool(BaseTool):
    name: str = "select"
    description: str = "动态模拟工具子工具，选取需要的列进行相关操作，并创建临时表"
    args_schema: Type[BaseModel] = SelectInput

    def _run(
        self,
        select: List[SelectItem],
        config: RunnableConfig,
        groupby: Optional[List[str]] = None,
    ):
        try:
            input_node = self.dag_executor.get_node(self.target.parent_node_names[0])
            self.target.node_data = TableNodeData()
            do_select(
                input=input_node,
                target=self.target,
                select_items=select,
                groupby=groupby,
            )
        except Exception as e:
            reporter = config[CHAIN_META][ChainMeta.AGENT_REPORTER]
            nl2agent_steps_list_tuple = (
                self.target.param_key,
                [ProgressRunStatus.FAILED, e],
            )
            reporter.report(
                breadcrumbs=self.breadcrumbs,
                config=config,
                nl2agent_steps_list_tuple=nl2agent_steps_list_tuple,
            )
            raise e
        reporter = config[CHAIN_META][ChainMeta.AGENT_REPORTER]
        nl2agent_steps_list_tuple = (
            self.target.param_key,
            [ProgressRunStatus.SUCCEED, self.target],
        )
        reporter.report(
            breadcrumbs=self.breadcrumbs,
            config=config,
            nl2agent_steps_list_tuple=nl2agent_steps_list_tuple,
        )
        return self.target

    def verify_degree(self, degree):
        return degree == 1
