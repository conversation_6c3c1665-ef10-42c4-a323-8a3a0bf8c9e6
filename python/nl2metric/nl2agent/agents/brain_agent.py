import copy
import json
from typing import Dict, List, Any, <PERSON><PERSON>, Union

from langchain_core.messages import ChatMessage, AIMessage
from langchain_core.output_parsers import Str<PERSON>utputParser
from langchain_core.runnables import (
    <PERSON><PERSON>bleLambda,
    RunnableConfig,
    RunnableBranch,
)
from langchain_core.utils.function_calling import convert_to_openai_function

from common.logging.logger import get_logger
from common.prompt_selector.prompts.gen_nl2agent_brain import BRAIN_SYSTEM_PROMPT
from common.types.base import (
    ParamsExtractStage,
    CHAIN_META,
    ChainMeta,
    ChainRuntime,
)
from common.types.exception_cacher import _exception_to_code
from common.types.exceptions import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Failed,
    <PERSON><PERSON>rainIllegalToolCall,
    AgentJudgeTool<PERSON>allFailed,
)
from common.utils.llm_utils import create_llm_model_by_project_config
from common.utils.reflect_util import Importer
from config.project_config import get_project_config, ProjectConfig
from metastore.utils import get_metrics_latest_time
from nl2agent.agents.base_agent import BaseAgent
from nl2agent.agents.bi_agent import BiAgent
from nl2agent.common.agent_reporter import reporter_run_chain
from nl2agent.dag.node import Node
from nl2agent.tools.base_tool import run_tools
from nl2agent.tools.chat import ChatTool
from nl2agent.tools.lookup_data import FastLookupTool
from pre_filter.retrive import _metric_dimension_retrieval
from tools.code_agent import PyCodeAgent
from tools.code_tool import PyCodeInterpreterTool
from vector_store.data_models import AgentType

logger = get_logger(__name__)


def _format_result(input):
    """格式化结果"""
    return {
        "name": input["name"],
        "args": input["args"],
        "code": input["code"],
        "result": input["result"],
    }


class BrainAgent(BaseAgent):
    """
    BrainAgent: 智能大脑Agent - 负责理解用户问题、规划执行步骤并生成最终回答

    ┌─────────────────────────────────────────────────────────────────────┐
    │                         BrainAgent 处理流程                          │
    └─────────────────────────────────────────────────────────────────────┘

    ┌───────────┐     ┌───────────┐     ┌───────────┐     ┌───────────┐
    │  用户问题  │────>│ 追问改写  │────>│ 检索信息  │────>│  预处理   │
    └───────────┘     └───────────┘     └───────────┘     └───────────┘
           │                                                    │
           │                                                    ▼
    ┌───────────┐                                         ┌───────────┐
    │   返回    │<────────────────────────────────────────│   规划    │
    │   结果    │                                         └───────────┘
    └───────────┘                                               │
           ▲                                                    │
           │                                                    ▼
    ┌───────────┐     ┌───────────┐                      ┌───────────┐
    │ 结果评估  │<────│ 执行规划  │<─────────────────────│工具调用规划│
    └───────────┘     └───────────┘                      └───────────┘
           ▲                │
           │                │
           │                ▼
           │          ┌───────────┐
           └──────────│ Early Stop│ (提前返回模式)
                      └───────────┘

    详细步骤说明:

    1. 追问改写 (condense_query)
       将用户问题与历史上下文结合，处理多轮对话中的追问，改写为完整查询
       输入: 原始用户问题
       输出: 改写后的完整查询文本

    2. 检索信息 (retrieve)
       从数据库检索相关指标、维度和业务术语
       输入: 改写后的查询
       输出: 关联指标、维度和业务提示信息

    3. 预处理 (preprocess)
       准备消息和提示，构造LLM输入
       输入: 检索结果和查询
       输出: 结构化的消息列表

    4. 规划 (planning)
       调用LLM决定执行步骤，生成工具调用计划
       输入: 预处理后的消息
       输出: 工具调用规划

    5. 执行 (execute)
       执行工具调用，获取数据或生成内容
       输入: 工具调用规划
       输出: 工具执行结果

    6. Early Stop检测
       判断是否需要提前返回结果（不进行结果评估）
       - 如果触发提前返回条件（例如使用chat工具）：直接返回执行结果
       - 如果不触发：继续进行结果评估

    7. 结果评估 (judge_results)
       分析执行结果，生成最终答案
       输入: 执行结果
       输出: 格式化的最终回答

    数据流转示意:

    用户问题 ──> 追问改写 ──> 检索数据 ──┐
                                       │
    最终结果 <── 结果评估 <── 执行结果 <──┼── 预处理 ──> 规划执行 ──> 工具调用
               ↑              │         │
               └──── Early Stop检测 ────┘
                    (提前返回)

    工具使用流程:

    ┌─────────────┐    ┌──────────────┐    ┌─────────────┐
    │ 数据获取工具 │    │  计算分析工具  │    │ 生成解释工具 │
    └─────────────┘    └──────────────┘    └─────────────┘
          │                   │                   │
          └───────────┬───────┴───────────┬───────┘
                      │                   │
                      ▼                   ▼
              ┌──────────────┐    ┌──────────────┐
              │ 工具结果整合  │───>│  结果评估    │
              └──────────────┘    └──────────────┘

    Early Stop模式说明:
    * 触发条件: 当规划阶段选择使用特定工具(chat、python_code_tool等)时触发
    * 处理逻辑: 执行工具后直接返回结果，跳过评估阶段
    * 使用场景: 适用于用户直接要求聊天或执行代码等明确任务的场景
    * 优势: 减少处理步骤，提高响应速度，特别适合简单明确的任务
    """

    def __init__(self):
        """初始化BrainAgent"""
        super().__init__("brain_agent")
        self.system_prompt = BRAIN_SYSTEM_PROMPT
        self.early_stop_tools = {
            "chat",
            "python_code_tool",
            "code_agent",
        }

        # 迭代相关
        self.max_iterations = 1  # 当前支持1轮,未来可扩展为多轮
        self.current_iteration = 0

        # 处理链
        self.main_chain = None
        self.after_manual_select_chain = None

    def _get_tools(self, agent_type: str, project_config: ProjectConfig) -> List[Dict]:
        """
        获取指定类型的工具

        Args:
            agent_type: 工具类型，'brain'或'judge'
            project_config: 项目配置

        Returns:
            工具函数列表
        """
        # 从配置中获取支持的工具列表
        supported_tools = []
        if agent_type == "brain":
            supported_tools = project_config.agent_brain_tools_whitelist
        elif agent_type == "judge":
            supported_tools = project_config.agent_judge_tools_whitelist

        # 动态加载工具
        tools = []
        for tool_path in supported_tools:
            try:
                tool_class, _ = Importer.import_module_content(tool_path)
                tool = tool_class()
                tools.append(convert_to_openai_function(tool))
            except Exception as e:
                logger.warning(f"Failed to load tool {tool_path}: {e}")
                continue

        return tools

    def _init_tool_handlers(
        self,
        breadcrumbs_index: int,
        model_tool_calls: List[Dict],
        config: RunnableConfig,
    ) -> Tuple[List, List]:
        plan = []
        tool_calls = []

        # 检查是否需要early_stop
        early_stop = False
        for tool_call in model_tool_calls:
            if (
                tool_call["name"] == "early_stop"
                or tool_call["name"] in self.early_stop_tools
            ):
                early_stop = True
                break
        config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.EARLY_STOP] = early_stop

        for i, model_tool_call in enumerate(model_tool_calls):
            # 复制工具调用并添加面包屑
            tool_call = copy.copy(model_tool_call)
            tool_call["breadcrumbs"] = (str(breadcrumbs_index), str(i))
            tool_name = tool_call["name"]

            # 添加基本计划项
            plan.append(
                {
                    "breadcrumbs": list(tool_call["breadcrumbs"]),
                    "name": tool_name,
                    "args": tool_call["args"],
                }
            )

            # 处理不同类型的工具
            if tool_name == "early_stop":
                continue
            elif tool_name == "fast_lookup":
                chain = FastLookupTool(
                    target=Node(param_key=str(i), tool_name=tool_name),
                    do_query=(not early_stop),
                ) | RunnableLambda(self._table_postprocess, name="table_postprocess")
                chain.name = tool_name
                tool_call["tool_chain"] = chain
                tool_calls.append(tool_call)
            elif tool_name == "bi":
                chain = BiAgent(
                    target=Node(param_key=str(i), tool_name=tool_name),
                    do_query=(not early_stop),
                    breadcrumbs=tool_call["breadcrumbs"],
                ) | RunnableLambda(self._table_postprocess, name="table_postprocess")
                chain.name = tool_name
                tool_call["tool_chain"] = chain
                tool_calls.append(tool_call)
            elif tool_name == "python_code_tool":
                chat_breadcrumbs = list(tool_call["breadcrumbs"])
                chat_breadcrumbs.append("0")
                plan.append(
                    {
                        "breadcrumbs": chat_breadcrumbs,
                        "name": "chat",
                        "args": {"think": "true"},
                    }
                )

                # 设置配置项
                if CHAIN_META in config and ChainMeta.RUN_TIME in config[CHAIN_META]:
                    config[CHAIN_META][ChainMeta.RUN_TIME][
                        ChainMeta.ENABLE_INTERNET_SEARCH
                    ] = False
                    config[CHAIN_META][ChainMeta.RUN_TIME][
                        ChainMeta.ENABLE_DOC_SEARCH
                    ] = False

                chain = (
                    RunnableLambda(self._run_code_tool, name="run_code_tool")
                    | RunnableLambda(
                        self._code_chat_preprocess, name="code_chat_preprocess"
                    )
                    | ChatTool(breadcrumbs=tuple(chat_breadcrumbs))
                )
                chain.name = tool_name
                tool_call["tool_chain"] = chain
                tool_calls.append(tool_call)
            elif tool_name == "chat":
                chain = ChatTool(breadcrumbs=tool_call["breadcrumbs"])
                chain.name = tool_name
                tool_call["tool_chain"] = chain
                tool_calls.append(tool_call)
            else:
                tool_calls.append(tool_call)

        return plan, tool_calls

    def condense_query(self, question: Union[str, dict], config: RunnableConfig) -> str:
        """
        处理追问，将多轮对话中的省略问题改写为完整问题

        Args:
            question: 用户问题
            config: 可运行配置

        Returns:
            改写后的完整问题
        """
        project_config = get_project_config(
            project_name=config[CHAIN_META][ChainMeta.PROJECT_NAME],
            model_name=config[CHAIN_META][ChainMeta.MODEL_NAME],
        )
        need_condense = project_config.condense_query_in_brain
        condense_with_history_response = project_config.condense_with_history_response
        history = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.HISTORY_MESSAGES]

        if not need_condense or not history:
            return question

        if isinstance(question, dict):
            question = question["question"]

        def _gen_condense_prompt(query: str):
            prompt_selector = config[CHAIN_META][ChainMeta.PROMPT_SELECTOR]
            stage = (
                ParamsExtractStage.AGENT_CONDENSE
                if condense_with_history_response
                else ParamsExtractStage.AGENT_CONDENSE_NO_HISTORY_RESPONSE
            )
            prompt = prompt_selector.gen_prompt(
                input={
                    "history": history,
                    "question": query,
                    **config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.HINT],
                },
                stage=stage,
                config=config,
            )
            return prompt

        # 创建模型
        model = create_llm_model_by_project_config("condense_model", project_config)
        condense_chain = (
            RunnableLambda(_gen_condense_prompt, name="_gen_condense_prompt")
            | model
            | StrOutputParser()
        )
        condense_chain.name = "agent_condense"
        condense_result = condense_chain.invoke(question)
        # reset history to empty after condense
        config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.HISTORY_MESSAGES] = []
        result = condense_result.rsplit("</think>")[-1].strip()
        config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.QUESTION] = result
        return result

    def retrieve_hint(
        self,
        question: Union[str, dict],
        condense_query_after: bool,
        config: RunnableConfig,
    ):
        # 是否跳过提示
        skip_hint = config[CHAIN_META][ChainMeta.ADDITIONAL_INFO].get(
            "skip_hint", False
        )
        if isinstance(question, dict):
            question = question["question"]

        # 从配置导入全局hint设置
        from config import app_config

        ENABLE_HINT_API = app_config.ENABLE_HINT_API

        if skip_hint or not ENABLE_HINT_API:
            config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.HINT] = {
                f"{agent_type_enum.value}_hint": "" for agent_type_enum in AgentType
            }
            return {"question": question}

        # 获取业务术语
        def get_business_terms(q: str):
            from hint.business_hint import retrieve_business_term

            return retrieve_business_term(q, condense_query_after, config)

        results = RunnableLambda(get_business_terms).invoke(question, config=config)
        config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.HINT] = results
        return {
            **results,
            "question": question,
        }

    def retrieve(self, question: str, config: RunnableConfig) -> Dict:
        """
        检索相关信息

        Args:
            question: 用户问题
            config: 可运行配置

        Returns:
            检索结果
        """
        # 处理可能是字典的输入
        if isinstance(question, dict):
            question = question["question"]

        # 如果不启用bi，则直接返回原问题
        if not config[CHAIN_META][ChainMeta.ENABLE_BI]:
            return {
                "metrics": [],
                "dimensions": [],
                "question": question,
            }

        # 定义指标和维度检索函数
        def get_metrics_dimensions(q: str):
            metrics, dimensions = _metric_dimension_retrieval(
                question=q,
                skip_metrics=False,
                config=config,
            )

            # 追问场景下对历史问题额外召回一次
            history_messages = config[CHAIN_META][ChainMeta.RUN_TIME][
                ChainRuntime.HISTORY_MESSAGES
            ]
            if history_messages:
                user_history_messages = [
                    m.content for m in history_messages if m.role == "user"
                ]
                user_history_messages = " ".join(user_history_messages)
                if user_history_messages:
                    history_metrics, history_dimensions = _metric_dimension_retrieval(
                        question=user_history_messages,
                        skip_metrics=False,
                        config=config,
                    )
                    metrics_names = {m.name for m in metrics}
                    for m in history_metrics:
                        if m.name not in metrics_names:
                            metrics.append(m)

                    dimension_names = {d.name for d in dimensions}
                    for d in history_dimensions:
                        if d.name not in dimension_names:
                            dimensions.append(d)

            return {"metrics": metrics, "dimensions": dimensions}

        # 并行执行检索
        runner = RunnableLambda(get_metrics_dimensions, name="get_metrics_dimensions")
        results = runner.invoke(question, config=config)

        # 组合结果
        return {
            **results,
            "question": question,
        }

    def preprocess(self, input: Dict, config: RunnableConfig) -> List[ChatMessage]:
        """
        预处理输入,生成消息列表

        Args:
            input: 输入数据
            config: 可运行配置

        Returns:
            处理后的消息列表
        """
        model_type = config[CHAIN_META][ChainMeta.MODEL_TYPE]
        prompt_selector = config[CHAIN_META][ChainMeta.PROMPT_SELECTOR]
        tools = self._get_tools_from_config(config, "brain")
        tools_description = "\n".join(
            json.dumps(item, ensure_ascii=False) for item in tools
        )

        # 根据模型类型选择系统提示
        if self._use_tool_call(model_type):
            messages = [ChatMessage(role="system", content=self.system_prompt)]
        else:
            sys_prompt = prompt_selector.gen_prompt(
                input={
                    "tools": tools_description,
                    **config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.HINT],
                },
                stage=ParamsExtractStage.BRAIN_TOOL,
                config=config,
            ).text
            messages = [ChatMessage(role="system", content=sys_prompt)]

        # 添加历史消息
        history_messages = config[CHAIN_META][ChainMeta.RUN_TIME][
            ChainRuntime.HISTORY_MESSAGES
        ]
        if history_messages:
            messages.extend(history_messages)
        input.update(config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.HINT])
        user_prompt = prompt_selector.gen_prompt(
            input=input, stage=ParamsExtractStage.BRAIN, config=config
        ).text

        messages.append(ChatMessage(role="user", content=user_prompt))
        config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.MESSAGES_RECORD] = messages
        input["metrics_latest_time"] = {}
        if get_project_config(
            config[CHAIN_META][ChainMeta.PROJECT_NAME],
            config[CHAIN_META][ChainMeta.MODEL_NAME],
        ).enable_metric_latest_time:
            input["metrics_latest_time"] = get_metrics_latest_time(
                input["metrics"], config[CHAIN_META][ChainMeta.PROJECT_ID]
            )
        config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.AGENT_BRAIN_INPUT] = input
        return messages

    def planning(
        self, messages: List[ChatMessage], config: RunnableConfig
    ) -> List[Dict]:
        """
        规划处理步骤

        Args:
            messages: 消息列表
            config: 运行配置

        Returns:
            (响应消息, 工具调用结果列表)
        """
        # 记录消息
        config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.MESSAGES_RECORD] = messages
        reporter = config[CHAIN_META][ChainMeta.AGENT_REPORTER]

        try:
            project_config = get_project_config(
                config[CHAIN_META][ChainMeta.PROJECT_NAME],
                config[CHAIN_META][ChainMeta.MODEL_NAME],
            )
            # 创建模型
            model = create_llm_model_by_project_config("agent_model", project_config)

            # # 内部的模型现在不支持绑定工具，会报错
            # '"auto" tool choice requires --enable-auto-tool-choice and --tool-call-parser to be set
            # if self._use_tool_call(model_type):
            #     brain_funcs = self._get_tools_from_config(config, "brain")
            #     model_with_tools = model.bind_tools(brain_funcs)
            #     # 调用模型
            #     message: AIMessage = reporter_run_chain(
            #         breadcrumbs=tuple("0"),
            #         chain=model_with_tools,
            #         input=messages,
            #         config=config,
            #         name="brain",
            #     )
            #     model_tool_calls = message.tool_calls
            # else:
            # 调用模型
            message: AIMessage = reporter_run_chain(
                breadcrumbs=tuple("0"),
                chain=model,
                input=messages,
                config=config,
                name="brain",
            )
            model_tool_calls = self._parse_tool_calls(message, config)

            # 过滤工具调用
            model_tool_calls = self._brain_remove_unknown_tool_calls(
                model_tool_calls, config
            )

            if not model_tool_calls:
                raise RuntimeError("brain found no tool_calls")

            # 执行自定义的plan后处理
            project_config = get_project_config(
                project_name=config[CHAIN_META][ChainMeta.PROJECT_NAME],
                model_name=config[CHAIN_META][ChainMeta.MODEL_NAME],
            )
            plan_postprocess_func = project_config.brain_plan_postprocess_func
            if plan_postprocess_func:
                from common.utils.reflect_util import Importer

                postprocess, _ = Importer.import_module_content(
                    plan_postprocess_func,
                    msg=f"brain_plan_postprocess_func {plan_postprocess_func}",
                )
                question = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.QUESTION]
                model_tool_calls = postprocess(question, model_tool_calls, config)

            # 使用_init_tool_handlers处理工具调用
            plan, tool_calls = self._init_tool_handlers("0", model_tool_calls, config)

            # 报告计划
            reporter.report(
                breadcrumbs=tuple("0"),
                plan=plan,
                config=config,
            )

            # 保存消息到运行时配置
            config[CHAIN_META][ChainMeta.RUN_TIME][
                ChainRuntime.AGENT_BRAIN_MSG
            ] = message

            # 标记执行成功
            reporter.report(
                breadcrumbs=tuple("0"),
                code=0,
                config=config,
            )

            return tool_calls

        except Exception as e:
            # 处理异常
            self._handle_error(e, reporter, tuple("0"), config)
            # 重新抛出异常
            raise e

    def execute(
        self, planning_result: List[Dict], config: RunnableConfig
    ) -> List[Dict]:
        """
        执行工具调用

        Args:
            planning_result: (响应消息, 工具调用列表)
            config: 运行配置

        Returns:
            (响应消息, 工具调用结果列表)
        """
        reporter = config[CHAIN_META][ChainMeta.AGENT_REPORTER]
        try:
            # 解析规划结果
            tool_calls = planning_result

            # 执行工具调用
            tools_result = run_tools(tool_calls, config=config)

            # 检查是否需要在工具调用失败时抛出异常
            project_config = get_project_config(
                project_name=config[CHAIN_META][ChainMeta.PROJECT_NAME],
                model_name=config[CHAIN_META][ChainMeta.MODEL_NAME],
            )

            if project_config.agent_brain_fail_if_no_succeed_toolcall and all(
                r["code"] != 0 for r in tools_result
            ):
                raise AgentBrainToolCallFailed(
                    f"brain tool call failed: {tools_result}"
                )

            # 报告执行成功 - 使用tuple("0")保持与原始代码一致
            reporter.report(
                breadcrumbs=tuple("0"),
                code=0,
                config=config,
            )

            return tools_result

        except Exception as e:
            # 处理异常 - 使用tuple("0")保持与原始代码一致
            self._handle_error(e, reporter, tuple("0"), config)
            # 重新抛出异常
            raise e

    def _judge_results(
        self, execution_result: List[Dict], config: RunnableConfig
    ) -> List[Dict]:
        """
        评估执行结果并生成最终回答

        Args:
            execution_result: 执行结果(工具结果列表)
            config: 可运行配置

        Returns:
            处理结果
        """
        reporter = config[CHAIN_META][ChainMeta.AGENT_REPORTER]

        try:
            # 1. 预处理
            prompt = self._judge_preprocess(execution_result, config)

            # 2. 调用模型获取评判结果
            model_type = config[CHAIN_META][ChainMeta.MODEL_TYPE]
            project_config = get_project_config(
                project_name=config[CHAIN_META][ChainMeta.PROJECT_NAME],
                model_name=config[CHAIN_META][ChainMeta.MODEL_NAME],
            )
            model = create_llm_model_by_project_config("judge_model", project_config)
            tools = self._get_tools_from_config(config, "judge")

            if self._use_tool_call(model_type):
                model_with_tools = model.bind_tools(tools)
                assistant_message: AIMessage = reporter_run_chain(
                    breadcrumbs=tuple("1"),
                    chain=model_with_tools,
                    input=prompt,
                    config=config,
                    name="judge",
                )
                model_tool_calls = assistant_message.tool_calls
            else:
                assistant_message: AIMessage = reporter_run_chain(
                    breadcrumbs=tuple("1"),
                    chain=model,
                    input=prompt,
                    config=config,
                    name="judge",
                )
                model_tool_calls = self._parse_tool_calls(assistant_message, config)

            # 添加消息到记录
            messages = config[CHAIN_META][ChainMeta.RUN_TIME][
                ChainRuntime.MESSAGES_RECORD
            ]
            messages.append(assistant_message)

            # 过滤工具调用
            valid_tools = {f["name"] for f in tools}
            model_tool_calls = self._filter_tools(model_tool_calls, valid_tools)

            # Judge只能有一个工具调用
            if len(model_tool_calls) > 1:
                model_tool_calls = model_tool_calls[:1]

            # 使用_init_tool_handlers处理工具调用
            plan, tool_calls = self._init_tool_handlers("1", model_tool_calls, config)

            # 报告计划
            reporter.report(
                breadcrumbs=tuple("1"),
                plan=plan,
                config=config,
            )

            # 处理不同情况
            if not tool_calls:
                # 没有工具调用,直接返回空列表
                reporter.report(
                    breadcrumbs=tuple("1"),
                    code=0,
                    config=config,
                )
                return []
            elif len(tool_calls) == 1:
                # 执行单个工具调用
                results = run_tools(tool_calls, config=config)

                # 报告整体成功
                reporter.report(
                    breadcrumbs=tuple("1"),
                    code=0,
                    config=config,
                )
                return [_format_result(results[0])]
            else:
                # 不应该有多个工具调用
                raise AgentJudgeToolCallFailed(
                    f"judge got multi tool call: {tool_calls}"
                )

        except Exception as e:
            # 使用统一的错误处理
            self._handle_error(e, reporter, tuple("1"), config)
            raise e

    def _judge_preprocess(self, input: List[Dict], config: RunnableConfig):
        """
        预处理Brain执行结果(用于Judge阶段)

        Args:
            input: Brain执行结果(工具结果列表)
            config: 可运行配置

        Returns:
            处理后的提示
        """
        messages = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.MESSAGES_RECORD]
        prompt_selector = config[CHAIN_META][ChainMeta.PROMPT_SELECTOR]

        # 解析输入
        results = input
        assistant_message = config[CHAIN_META][ChainMeta.RUN_TIME][
            ChainRuntime.AGENT_BRAIN_MSG
        ]

        # 解析结果函数
        def _parse_result(result):
            args = result["args"]
            question = args["query"] if "query" in args else args.get("question", args)
            tool_name = result["name"]
            if result["code"] != 0:
                return f'{tool_name}工具查询"{question}"失败'
            else:
                if tool_name == "chat":
                    return result["result"]
                if tool_name == "metric_attr":
                    agent_msg = next(iter(result["result"].values())).get("agent_msg")
                    if agent_msg:
                        return f'{tool_name}工具查询"{question}"成功，结果：\n{agent_msg}'
                return f"{tool_name}工具查询\"{question}\"成功，结果：{result['result']}"

        # 准备工具消息
        model_type = config[CHAIN_META][ChainMeta.MODEL_TYPE]
        tool_messages = [
            ChatMessage(role="tool", content=_parse_result(result))
            for result in results
        ]

        # 处理工具调用
        if self._use_tool_call(model_type):
            assistant_message.tool_calls = []
            for result in results:
                tool_call = copy.copy(result["original_tool_call"])
                tool_call["args"] = result["args"]
                assistant_message.tool_calls.append(tool_call)

        # 添加消息
        messages.append(assistant_message)
        messages.extend(tool_messages)

        # 处理数据工具结果
        # 如果数据工具（option）bi、fast_lookup、meta、metric_attr并行获取数据都失败了，走 doc+search
        data_tool_result = {}
        for result in results:
            if result["name"] in ["fast_lookup", "bi", "metric_meta", "metric_attr"]:
                data_tool_result[result["name"]] = result["code"]
        is_for_doc = self.is_metric_for_doc(config)
        if data_tool_result and any(
            [data_tool_result[tool] == 0 for tool in data_tool_result]
        ):
            logger.info(f"data_tool_result:{data_tool_result}")
            config[CHAIN_META][ChainMeta.RUN_TIME][
                ChainMeta.ENABLE_INTERNET_SEARCH
            ] = is_for_doc
            config[CHAIN_META][ChainMeta.RUN_TIME][
                ChainMeta.ENABLE_DOC_SEARCH
            ] = is_for_doc

        # 处理工具结果
        bi_result = []
        other_result = []
        for result in results:
            if result["name"] in ["fast_lookup", "bi", "metric_meta", "metric_attr"]:
                if not is_for_doc:
                    bi_result.append(_parse_result(result))
            else:
                other_result.append(_parse_result(result))

        # 生成Judge提示
        tools = self._get_tools_from_config(config, "judge")
        tools_description = "\n".join(
            json.dumps(item, ensure_ascii=False) for item in tools
        )

        judge_prompt = prompt_selector.gen_prompt(
            input={
                "query": config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.QUESTION],
                "tools": tools_description,
                "bi_result": "\n".join(bi_result),
                "other_result": "\n".join(other_result),
                **config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.HINT],
            },
            stage=ParamsExtractStage.JUDGE_TOOL,
            config=config,
        )

        # 记录消息
        if ChainRuntime.MESSAGES_IN_RESP not in config[CHAIN_META][ChainMeta.RUN_TIME]:
            config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.MESSAGES_IN_RESP] = []
        config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.MESSAGES_IN_RESP].append(
            ("judge", judge_prompt.text)
        )

        return judge_prompt

    def _table_postprocess(self, node: Node, config: RunnableConfig):
        """
        表格数据后处理逻辑

        在Early Stop模式下的特殊处理:
        - 对于fast_lookup工具: 从元数据提取信息而非执行查询
        - 处理project_query_metric和query_metric的逻辑

        Args:
            node: 节点数据
            config: 可运行配置

        Returns:
            处理后的数据

        Raises:
            RuntimeError: 当未查询到相关数据时
        """
        early_stop = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.EARLY_STOP]
        if (
            early_stop or self.is_metric_for_doc(config)
        ) and node.tool_name == "fast_lookup":
            if "query_metric_result" in node.meta:
                # 注意多场景且非early_stop的时候meta是有query_metric的
                # 所以判断project_query_metric要放在前面
                project_query_metric = node.meta["query_metric_result"]
                if not project_query_metric:
                    raise RuntimeError("未查询到相关数据")
                return {
                    model_id: query_metric.model_dump()
                    for model_id, query_metric in project_query_metric.items()
                }
        else:
            if (
                node.node_data is None
                or node.node_data.data is None
                or (
                    (
                        hasattr(node.node_data.data, "empty")
                        and node.node_data.data.empty
                    )  # 检查 Pandas 对象
                    or (
                        isinstance(node.node_data.data, list)
                        and not node.node_data.data
                    )  # 检查空列表
                )
            ):
                return "共查询到0行数据"

            csv_mgr = config[CHAIN_META][ChainMeta.CSV_MGR]
            if isinstance(node.node_data.data, list):
                results = []
                for single_node in node.node_data.data:
                    query = single_node.tool_kw_params.get("query", "")
                    df = single_node.node_data.data
                    results.append(csv_mgr.create_csv_content(query, df))
                return results
            else:
                query = node.tool_kw_params.get("query", "")
                df = node.node_data.data
                return csv_mgr.create_csv_content(query, df)

    def _run_code_tool(self, input):
        """运行代码工具"""
        try:
            return input, PyCodeInterpreterTool().invoke(input), True
        except Exception as e:
            return input, str(e), False

    def _run_code(self, input):
        """运行代码"""
        try:
            return input, PyCodeAgent().invoke(input), True
        except Exception as e:
            return input, str(e), False

    def _code_chat_preprocess(self, input, config: RunnableConfig):
        """代码聊天预处理"""
        messages = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.MESSAGES_RECORD]
        question, result, succeed = input
        question = (
            question["query"]
            if "query" in question
            else question.get("question", question)
        )
        if succeed:
            content = f'code工具查询"{question}"成功，结果：{result}'
        else:
            content = f'code工具查询"{question}"失败'
        messages.append(ChatMessage(role="tool", content=content))
        # 与原始nl2agent.py保持一致
        return {"input": content, "think": "true"}

    def _is_early_stop(self, input, config: RunnableConfig):
        """检查是否需要早期停止"""
        if self.is_metric_for_doc(config):
            return False
        return config[CHAIN_META][ChainMeta.RUN_TIME].get(
            ChainRuntime.EARLY_STOP, False
        )

    def _early_stop_postprocess(self, input, config: RunnableConfig):
        """处理早期停止的结果"""

        results = input

        if not results:
            raise AgentBrainIllegalToolCall("brain earlystop calls nothing")
        return [_format_result(r) for r in results]

    def create_chain(self):
        """
        创建整个处理链
        """

        def planning_and_execute(messages, config: RunnableConfig):
            # 确保langfuse上下文正确传递
            if "langfuse" not in config:
                config["langfuse"] = {}

            # 确保保留原始config，不要创建新的
            planning_result = self.planning(messages, config)
            return self.execute(planning_result, config)

        # 与原始代码保持一致的处理方式
        chain = (
            RunnableLambda(
                self.retrieve_hint,
                name="retrieve_hint",
            ).bind(condense_query_after=False)
            | RunnableLambda(
                self.condense_query,
                name="condense_query",
            )
            | RunnableLambda(
                self.retrieve_hint,
                name="retrieve_hint",
            ).bind(condense_query_after=True)
            | RunnableLambda(
                self.retrieve,
                name="retrieve",
            )
            | RunnableLambda(
                self.preprocess,
                name="preprocess",
            )
            | RunnableLambda(
                planning_and_execute,
                name="planning_and_execute",
            )
            | RunnableBranch(
                # 首先判断是否需要人工选择
                (
                    RunnableLambda(self.need_manual_select, name="need_manual_select"),
                    RunnableLambda(
                        self.manual_select_postprocess, name="manual_select_postprocess"
                    ),
                ),
                # 然后判断是否早期停止
                (
                    RunnableLambda(self._is_early_stop, name="is_early_stop"),
                    RunnableLambda(
                        self._early_stop_postprocess, name="early_stop_postprocess"
                    ),
                ),
                # 否则进行判断处理
                RunnableLambda(
                    self._judge_results,
                    name="judge_results",
                ),
            )
        )

        # 设置整个链的名称
        chain.name = "brain_agent"

        # 保存链供外部使用
        self.main_chain = chain
        return chain

    def create_chain_from_plan(self):
        """
        创建从已有plan继续执行的处理链
        这个链专门用于处理人工选择后的继续执行
        不需要重新进行plan，而是直接从tools_result继续执行

        Returns:
            处理链
        """
        # 创建从plan继续执行的链
        chain = RunnableLambda(
            self.run_tools_after_manual_select,
            name="run_tools_after_manual_select",
        ) | RunnableBranch(
            (
                RunnableLambda(self._is_early_stop, name="is_early_stop"),
                RunnableLambda(
                    self._early_stop_postprocess, name="early_stop_postprocess"
                ),
            ),
            RunnableLambda(self._judge_results, name="_judge_results"),
        )

        # 设置链的名称
        chain.name = "brain_agent_from_plan"
        return chain

    def run(self, input_data: Any, config: RunnableConfig) -> Any:
        """
        运行整个处理链

        Args:
            input_data: 输入数据
            config: 运行配置

        Returns:
            处理结果
        """
        question = input_data
        if isinstance(question, dict):
            question = question.get("question", question)

        # 确保chain_meta存在
        if CHAIN_META not in config:
            # 未设置配置信息，使用默认配置
            raise ValueError("Chain meta not found in config")

        # 获取报告器
        reporter = config[CHAIN_META][ChainMeta.AGENT_REPORTER]

        # 初始化运行时配置
        if ChainMeta.RUN_TIME not in config[CHAIN_META]:
            config[CHAIN_META][ChainMeta.RUN_TIME] = {}

        # 设置问题到运行时配置
        config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.QUESTION] = question

        # 初始化消息记录列表
        if ChainRuntime.MESSAGES_IN_RESP not in config[CHAIN_META][ChainMeta.RUN_TIME]:
            config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.MESSAGES_IN_RESP] = []

        # 初始化消息记录
        if ChainRuntime.MESSAGES_RECORD not in config[CHAIN_META][ChainMeta.RUN_TIME]:
            config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.MESSAGES_RECORD] = []

        # 确保langfuse上下文正确设置
        if "langfuse" not in config:
            config["langfuse"] = {}

        try:
            # 运行整个处理链
            agent_chain = self.create_chain()

            # 不对config进行深拷贝，直接使用原始config
            # 避免因不可序列化对象导致的错误
            response_data = agent_chain.invoke(
                question,
                config=config,  # 直接使用原始config
            )

            # 检查是否需要人工选择
            need_manual_select = any(
                r.get("name") == "manual_selects" for r in response_data
            )

            # 报告执行完成
            reporter.report(
                code=0,
                config=config,
            )
            return response_data
        except Exception as e:
            err = _exception_to_code(e)
            reporter.report(
                code=int(err["code"]),
                data=err["data"],
                config=config,
            )
            # 重新抛出异常
            raise e

    def _brain_remove_unknown_tool_calls(
        self, model_tool_calls, config: RunnableConfig
    ):
        """移除未知的工具调用"""
        # 获取有效的工具名称集合
        valid_tools = {f["name"] for f in self._get_tools_from_config(config, "brain")}

        # 过滤工具调用
        valid_tool_calls = []
        for tool_call in model_tool_calls:
            if tool_call["name"] in valid_tools:
                valid_tool_calls.append(tool_call)

        # 如果没有有效的工具调用，返回默认的chat工具
        if not valid_tool_calls:
            return [{"name": "chat", "args": {"think": "true"}}]

        return valid_tool_calls

    def is_metric_for_doc(self, config: RunnableConfig):
        """判断是否是仅限文档的指标"""
        from nl2agent.tools.lookup_data import is_all_doc_metrics
        from common.types import ParamsExtractData

        raw_metrics = config[CHAIN_META][ChainMeta.RUN_TIME].get(
            ChainRuntime.RAW_METRICS, []
        )
        params_extract_data: ParamsExtractData = config[CHAIN_META][
            ChainMeta.RUN_TIME
        ].get(ChainRuntime.PARAMS_EXTRACT_DATA, [])

        return is_all_doc_metrics(raw_metrics, params_extract_data)

    def need_manual_select(self, tools_result, config: RunnableConfig):
        """判断是否需要人工选择"""
        from common.types.exception_cacher import ResponseCode

        NEED_MANUAL_SELECT_CODE = int(ResponseCode.NEED_MANUAL_SELECT.value)

        results = tools_result

        return any(r["code"] == NEED_MANUAL_SELECT_CODE for r in results)

    def manual_select_postprocess(self, tools_result, config: RunnableConfig):
        """处理需要人工选择的结果"""
        from common.manual_select.manual_select import (
            BrainManualSelectItem,
            add_manual_select_trace,
        )
        from common.trace import tracer
        from common.types.exception_cacher import ResponseCode
        from common.types.exceptions import AgentBrainIllegalToolCall

        NEED_MANUAL_SELECT_CODE = int(ResponseCode.NEED_MANUAL_SELECT.value)

        results = tools_result

        if not results:
            raise AgentBrainIllegalToolCall("brain manual_select got no tools_result")

        manual_selects = {}
        for r in results:
            if r["code"] == NEED_MANUAL_SELECT_CODE:
                manual_selects.update(r["result"])

        add_manual_select_trace(
            tracer.get_trace_id(),
            BrainManualSelectItem(
                config_chain_meta=config[CHAIN_META],
                manual_selects=manual_selects,
                tools_result=results,
            ),
        )

        return [
            {
                "name": "manual_selects",
                "manual_selects": manual_selects,
            }
        ]

    def run_tools_after_manual_select(self, tools_result, config: RunnableConfig):
        """人工选择后执行工具调用"""
        from common.types.exception_cacher import ResponseCode

        NEED_MANUAL_SELECT_CODE = int(ResponseCode.NEED_MANUAL_SELECT.value)

        reporter = config[CHAIN_META][ChainMeta.AGENT_REPORTER]
        reporter.report(
            breadcrumbs=tuple("2"),
            tool_type="run_tools_after_manual_select",
            config=config,
        )

        # 如果输入是元组(message, results)，提取results部分
        if isinstance(tools_result, tuple) and len(tools_result) == 2:
            _, results = tools_result
        else:
            results = tools_result

        new_tools_result = []
        tool_calls = []
        for r in results:
            if r["code"] == NEED_MANUAL_SELECT_CODE:
                tool_calls.append(r["original_tool_call"])
            else:
                new_tools_result.append(r)

        try:
            result = run_tools(tool_calls, config=config)
            reporter.report(
                breadcrumbs=tuple("2"),
                code=0,
                config=config,
            )
            new_tools_result.extend(result)
            return new_tools_result
        except Exception as e:
            err = _exception_to_code(e)
            reporter.report(
                breadcrumbs=tuple("2"),
                code=int(err["code"]),
                data=err["data"],
                config=config,
            )
            raise e
