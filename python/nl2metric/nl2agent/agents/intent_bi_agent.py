import json

from common.logging.logger import get_logger
from common.logging.utils import chain_ok_log
from common.types.base import ParamsExtractStage, CHAIN_META, ChainMeta, ChainRuntime
from common.utils.json_utils import extract_json_from_string
from langchain_core.runnables import (
    RunnableConfig,
)
from pydantic import BaseModel, Field
from langchain_core.runnables import <PERSON>nableLambda
from nl2agent.agents.base_agent_with_dag import BaseAgentWithDAG
from nl2agent.dag.dag_executor import DagExecutor
from nl2agent.tools.base_tool import BaseTool
from nl2agent.tools.calculate import CalculateTool
from nl2agent.tools.filter import FilterTool
from nl2agent.tools.join import JoinTool, AutoJoinTool
from nl2agent.tools.lookup_data import LookupDataTool
from nl2agent.tools.order import OrderTool
from nl2agent.tools.predict import PredictTool
from nl2agent.tools.select import SelectTool
from nl2agent.tools.table_tools import TableTools
from nl2agent.tools.time_range import TimeRangeTool
from nl2intent.types import Intent
from typing import List, Type

logger = get_logger(__name__)


class IntentBiAgentInput(BaseModel):
    question: str = Field(description="需要处理的问题")


# 这个用于兼容老的基于意图识别的实现
class IntentBiAgent(BaseAgentWithDAG):
    name: str = "intent_bi_agent"
    description: str = "BI工具"
    args_schema: Type[BaseModel] = IntentBiAgentInput
    stage: ParamsExtractStage = ParamsExtractStage.INTENT_BI
    # 保留字段，提示该Agent使用哪些tool方便阅读理解，
    # 当前不能灵活增删，只能由算法决定，以后要更灵活
    tools: List[BaseTool] = [
        LookupDataTool,
        CalculateTool,
        FilterTool,
        JoinTool,
        AutoJoinTool,
        OrderTool,
        PredictTool,
        SelectTool,
        TimeRangeTool,
        TableTools,
    ]

    def _gen_prompt(self, input, config: RunnableConfig):
        """get prompt by intent"""
        prompt_selector = config[CHAIN_META][ChainMeta.PROMPT_SELECTOR]
        intent = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.INTENT].intent
        if intent == Intent.percentage:
            stage = ParamsExtractStage.PERCENTAGE
        elif intent == Intent.period_on_period:
            stage = ParamsExtractStage.PERIOD_ON_PERIOD
        elif intent == Intent.predict:
            stage = ParamsExtractStage.PREDICT
        else:
            raise RuntimeError(f"agent_gen_prompt invalid intent {intent}")
        return prompt_selector.gen_prompt(input, stage=stage, config=config)

    def _build(self, response, config: RunnableConfig):
        """build dag based on plan"""
        intent = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.INTENT].intent
        parts = response.split("答案整理阶段", 1)
        raw_json = None
        if parts:
            thought = parts[0].strip()
            if thought and thought[-1] == "#":
                thought = thought[:-1]
            if len(parts) > 1:
                raw_json = parts[1].strip()
        else:
            thought = response
        if not raw_json:
            raw_json = response
        data = extract_json_from_string(raw_json, "agent_build")
        chain_ok_log(logger, config, f"agent_build {raw_json}, json: {data}")
        dag_executor = DagExecutor.build(data)

        # TODO(bhx): rm this code
        # this is tmp code for old frontend report
        def _report_agent_build(input):
            return {"thought": thought, "steps": dag_executor.steps()}

        RunnableLambda(_report_agent_build, name="agent_build").invoke(
            None, config=config
        )
        return dag_executor

    def verify_degree(self, degree):
        return degree == 0

    def table_description(self, kwargs):
        return kwargs["question"]

    # 这里算法给的最新代码是query，老逻辑还是question
    def _run(self, question, config: RunnableConfig):
        chain = self._run_with_dag_plan()
        return chain.invoke(question, config=config)
