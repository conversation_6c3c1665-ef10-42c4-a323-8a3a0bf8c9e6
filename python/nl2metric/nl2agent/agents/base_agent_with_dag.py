from abc import ABC

from common.llm.general import create_chat_model_in_chain
from common.logging.logger import get_logger
from common.logging.utils import chain_ok_log
from common.types.base import ParamsExtractStage, CHAIN_META, ChainMeta, ChainRuntime
from common.utils.json_utils import extract_json_from_string
from langchain_core.runnables import (
    RunnableLambda,
    RunnableConfig,
)
from langchain_core.output_parsers import StrOutputParser

from config.project_config import get_project_config
from metastore.utils import get_metrics_latest_time
from nl2agent.common.agent_reporter import reporter_run_chain
from nl2agent.dag.dag_executor import DagExecutor
from nl2agent.dag.table_node_data import TableNodeData
from nl2agent.tools.base_tool import BaseTool
from pre_filter.retrive import _metric_dimension_retrieval
from typing import Optional, List, Union

logger = get_logger(__name__)


class BaseAgentWithDAG(BaseTool, ABC):
    # BaseAgentWithDAG::agent_dag_executor是BaseAgent自己局部的executor
    # BaseTool::dag_executor是BaseAgent作为一个tool归属于的父级
    agent_dag_executor: Optional[DagExecutor] = None
    stage: ParamsExtractStage
    breadcrumbs: Optional[List[str]] = None

    def chit_chat(self, query: str, config: RunnableConfig):
        return {"answer": query}

    def _retrieve(self, query: Union[str, dict], config: RunnableConfig):
        if isinstance(query, dict):
            query = query["query"]
        metrics, dimensions = _metric_dimension_retrieval(
            question=query,
            skip_metrics=False,
            config=config,
        )
        return {
            "metrics": metrics,
            "dimensions": dimensions,
            "question": query,
        }

    def _gen_prompt(self, input, config: RunnableConfig):
        prompt_selector = config[CHAIN_META][ChainMeta.PROMPT_SELECTOR]
        input["metrics_latest_time"] = {}
        input.update(config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.HINT])
        if get_project_config(
            config[CHAIN_META][ChainMeta.PROJECT_NAME],
            config[CHAIN_META][ChainMeta.MODEL_NAME],
        ).enable_metric_latest_time:
            input["metrics_latest_time"] = get_metrics_latest_time(
                input["metrics"], config[CHAIN_META][ChainMeta.PROJECT_ID]
            )
        return prompt_selector.gen_prompt(input, stage=self.stage, config=config)

    def _create_chat_model(self, prompt, config: RunnableConfig):
        model = create_chat_model_in_chain(
            prompt=prompt, stage=self.stage, config=config, do_invoke=False
        )
        return reporter_run_chain(
            breadcrumbs=self.breadcrumbs,
            chain=model,
            input=prompt,
            config=config,
            name=self.name,
        )

    def _build(self, response, config: RunnableConfig):
        parts = response.split("答案整理阶段", 1)
        # 尽量使用"答案整理阶段"提取json，因为很多“规划阶段”也会生成json
        raw_json = None
        if parts:
            thought = parts[0].strip()
            if thought and thought[-1] == "#":
                thought = thought[:-1]
            if len(parts) > 1:
                raw_json = parts[1].strip()
        else:
            thought = response
        if not raw_json:
            raw_json = response
        data = extract_json_from_string(raw_json, "agent_build")
        chain_ok_log(logger, config, f"agent_build {raw_json}, json: {data}")
        return DagExecutor.build(data)

    def _preprocess(self, dag_executor: DagExecutor, config: RunnableConfig):
        # currently not needed
        self.agent_dag_executor = dag_executor
        return dag_executor

    def _run_plan(self, dag_executor: DagExecutor, config: RunnableConfig):
        return dag_executor.run()

    def _postprocess(self, input, config: RunnableConfig):
        result_nodes = self.agent_dag_executor.get_all_leaf_nodes()
        if self.target is not None:
            # 只有一个叶子节点，走老逻辑
            if len(result_nodes) == 1:
                self.target.node_data = result_nodes[0].node_data
                self.target.meta.update(result_nodes[0].meta)
                return self.target

            # 如果有多个叶子节点，代表有多个结果，需要用list存到data里往下传递
            elif len(result_nodes) > 1:
                self.target.node_data = TableNodeData(data=result_nodes)
                return self.target
        return input

    def _run_with_dag_plan(self):
        chain = (
            RunnableLambda(self._retrieve, name=f"{self.name}_retrieve")
            # get prompt by intent
            | RunnableLambda(self._gen_prompt, name=f"{self.name}_gen_prompt")
            # create dag, complicated questions need second run(currently not implemented)
            | RunnableLambda(
                self._create_chat_model, name=f"{self.name}_create_chat_model"
            )
            | StrOutputParser()
            # build dag based on dag
            | RunnableLambda(self._build, name=f"{self.name}_build")
            # preprocess for agent
            | RunnableLambda(self._preprocess, name=f"{self.name}_preprocess")
            # execute agent dag
            | RunnableLambda(self._run_plan, name=f"{self.name}_run_plan")
            # postprocess for agent
            | RunnableLambda(self._postprocess, name=f"{self.name}_postprocess")
        )
        chain.name = f"{self.name}_agent_plan_tool"
        return chain

    def _run_with_dag_plan_after_manual_select(self):
        chain = (
            # execute agent dag
            RunnableLambda(self._run_plan, name=f"{self.name}_run_plan")
            # postprocess for agent
            | RunnableLambda(self._postprocess, name=f"{self.name}_postprocess")
        )
        chain.name = f"{self.name}_agent_plan_tool"
        return chain

    def _run(self, query, config: RunnableConfig):
        if self.agent_dag_executor == None:
            self.manual_fill_params({"query": query})
            chain = self._run_with_dag_plan()
            return chain.invoke(query, config=config)
        else:
            chain = self._run_with_dag_plan_after_manual_select()
            return chain.invoke(self.agent_dag_executor, config=config)
