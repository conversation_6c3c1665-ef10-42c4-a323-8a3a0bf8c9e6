import numpy as np
import pandas as pd
from nl2agent.tools.order import OrderTool, OrderByType
from nl2agent.dag.dag_executor import DagExecutor
from nl2agent.dag.table_node_data import TableNodeData
from nl2agent.dag.node import Node
from nl2agent.types import (
    CalculatedColumn,
    ColumnType,
    JoinType,
    ToolType,
    UnknownColumn,
    TableColumn,
)
from metastore.base import Metric, Dimension


def _create_data():
    data = {
        "category": [
            "A",
            "B",
            "C",
            "D",
        ],
        "value": [10, 20, 30, 40],
        "quantity": [7, 5, 6, 3],
    }
    input = Node.create(
        param_key="a", descp=[ToolType.lookup_data, ["lookup value and quantity"], None]
    )
    input.node_data = TableNodeData()
    input.node_data.data = pd.DataFrame(data)
    input.node_data.schema = [
        TableColumn(
            column_name="category",
            column_detail=Dimension(name="category", label="category_label"),
            meta={},
        ),
        TableColumn(
            column_name="value",
            column_detail=Metric(name="value", label="value_label", model_names=["a"]),
            meta={},
        ),
        TableColumn(
            column_name="quantity",
            column_detail=CalculatedColumn(
                description="quantity", expression_type=ColumnType.AVG, expression="b"
            ),
            meta={},
        ),
    ]
    print(input.node_data.data)
    return input


def _create_plan_mgr():
    a = _create_data()
    output = Node.create(
        param_key="output", descp=[ToolType.join, ["test"], ["a", "b"]]
    )
    plan_mgr = DagExecutor(
        {
            "a": a,
            "output": output,
        }
    )
    return output, plan_mgr


def test_order_1():
    output, plan_mgr = _create_plan_mgr()
    tool = OrderTool(dag_executor=plan_mgr, target=output)
    tool.run(
        {
            "column_name": "quantity",
            "type": OrderByType.ASC,
        }
    )
    print(output.node_data.data)
    assert plan_mgr.get_node("a").node_data.data.equals(
        pd.DataFrame(
            {
                "category": [
                    "A",
                    "B",
                    "C",
                    "D",
                ],
                "value": [10, 20, 30, 40],
                "quantity": [7, 5, 6, 3],
            }
        )
    )
    assert output.node_data.data.equals(
        pd.DataFrame(
            {
                "category": [
                    "D",
                    "B",
                    "C",
                    "A",
                ],
                "value": [40, 20, 30, 10],
                "quantity": [3, 5, 6, 7],
            }
        )
    )
    assert tool.verify_result(output)


def test_order_2():
    output, plan_mgr = _create_plan_mgr()
    tool = OrderTool(dag_executor=plan_mgr, target=output)
    tool.run(
        {
            "column_name": "value",
            "type": OrderByType.DESC,
        }
    )
    print(output.node_data.data)
    assert plan_mgr.get_node("a").node_data.data.equals(
        pd.DataFrame(
            {
                "category": [
                    "A",
                    "B",
                    "C",
                    "D",
                ],
                "value": [10, 20, 30, 40],
                "quantity": [7, 5, 6, 3],
            }
        )
    )
    assert output.node_data.data.equals(
        pd.DataFrame(
            {
                "category": [
                    "D",
                    "C",
                    "B",
                    "A",
                ],
                "value": [40, 30, 20, 10],
                "quantity": [3, 6, 5, 7],
            }
        )
    )
    assert tool.verify_result(output)


def test_order_3():
    output, plan_mgr = _create_plan_mgr()
    tool = OrderTool(dag_executor=plan_mgr, target=output)
    tool.run({"column_name": "category", "type": OrderByType.DESC, "limit": 3})
    print(output.node_data.data)
    assert plan_mgr.get_node("a").node_data.data.equals(
        pd.DataFrame(
            {
                "category": [
                    "A",
                    "B",
                    "C",
                    "D",
                ],
                "value": [10, 20, 30, 40],
                "quantity": [7, 5, 6, 3],
            }
        )
    )
    assert output.node_data.data.equals(
        pd.DataFrame(
            {
                "category": [
                    "D",
                    "C",
                    "B",
                ],
                "value": [40, 30, 20],
                "quantity": [3, 6, 5],
            }
        )
    )
    assert tool.verify_result(output)


if __name__ == "__main__":
    test_order_3()
