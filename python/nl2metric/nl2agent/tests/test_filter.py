import numpy as np
import pandas as pd
from nl2agent.tools.filter import FilterTool
from nl2agent.dag.dag_executor import DagExecutor
from nl2agent.dag.node import Node
from nl2agent.dag.table_node_data import TableNodeData
from nl2agent.types import (
    CalculatedColumn,
    ColumnType,
    ToolType,
    UnknownColumn,
    TableColumnMeta,
    TableColumn,
)
from metastore.base import Metric, Dimension


def _create_data():
    data = {
        "category": [
            "A",
            "B",
            "C",
            "D",
        ],
        "value": [10, 20, 30, 40],
        "quantity": [7, 5, 6, 3],
        "日期": ["202301", "202302", "202303", "202304"],
    }
    input = Node.create(
        param_key="a", descp=[ToolType.lookup_data, ["lookup value and quantity"], None]
    )
    input.node_data = TableNodeData()
    input.node_data.data = pd.DataFrame(data)
    input.node_data.schema = [
        TableColumn(
            column_name="category",
            column_detail=Dimension(name="category", label="category_label"),
            meta={},
        ),
        TableColumn(
            column_name="value",
            column_detail=Metric(name="value", label="value_label", model_names=["a"]),
            meta={},
        ),
        TableColumn(
            column_name="quantity",
            column_detail=CalculatedColumn(
                description="quantity", expression_type=ColumnType.AVG, expression="b"
            ),
            meta={},
        ),
        TableColumn(
            column_name="日期",
            column_detail=UnknownColumn(
                original_name="V_DATE_",
            ),
            meta={TableColumnMeta.TIME_DIMENSION_FORMAT: "yyyyMM"},
        ),
    ]
    print(input.node_data.data)
    return input


def _create_plan_mgr():
    a = _create_data()
    output = Node.create(
        param_key="output", descp=[ToolType.join, ["test"], ["a", "b"]]
    )
    plan_mgr = DagExecutor(
        {
            "a": a,
            "output": output,
        }
    )
    return output, plan_mgr


def _do_filter_test(condition, result):
    output, plan_mgr = _create_plan_mgr()
    tool = FilterTool(dag_executor=plan_mgr, target=output)
    tool.run(
        {
            "expr": condition,
        }
    )
    print(output.node_data.data)
    assert plan_mgr.get_node("a").node_data.data.equals(
        pd.DataFrame(
            {
                "category": [
                    "A",
                    "B",
                    "C",
                    "D",
                ],
                "value": [10, 20, 30, 40],
                "quantity": [7, 5, 6, 3],
                "日期": ["202301", "202302", "202303", "202304"],
            }
        )
    )
    assert output.node_data.data.equals(pd.DataFrame(result))
    assert tool.verify_result(output)


def test_filter_1():
    _do_filter_test(
        "value < 40",
        {
            "category": [
                "A",
                "B",
                "C",
            ],
            "value": [10, 20, 30],
            "quantity": [7, 5, 6],
            "日期": ["202301", "202302", "202303"],
        },
    )


def test_filter_2():
    _do_filter_test(
        "value < 40 AND quantity >= 6",
        {
            "category": [
                "A",
                "C",
            ],
            "value": [10, 30],
            "quantity": [7, 6],
            "日期": ["202301", "202303"],
        },
    )


def test_filter_3():
    _do_filter_test(
        'category = "B"',
        {
            "category": [
                "B",
            ],
            "value": [20],
            "quantity": [5],
            "日期": ["202302"],
        },
    )


def test_filter_4():
    _do_filter_test(
        '日期 = "2023年2月"',
        {
            "category": [
                "B",
            ],
            "value": [20],
            "quantity": [5],
            "日期": ["202302"],
        },
    )


def test_filter_5():
    _do_filter_test(
        '日期 < "2023-04" AND value > 10 AND quantity != 6 AND category > A',
        {
            "category": [
                "B",
            ],
            "value": [20],
            "quantity": [5],
            "日期": ["202302"],
        },
    )


def test_filter_6():
    _do_filter_test(
        '日期 IN ("2023年04月", 202302) OR value=10',
        {
            "category": [
                "A",
                "B",
                "D",
            ],
            "value": [10, 20, 40],
            "quantity": [7, 5, 3],
            "日期": ["202301", "202302", "202304"],
        },
    )


def test_filter_7():
    _do_filter_test(
        '日期 NOT IN ("2023年04月", "202302) OR value=20',
        {
            "category": [
                "A",
                "B",
                "C",
            ],
            "value": [10, 20, 30],
            "quantity": [7, 5, 6],
            "日期": ["202301", "202302", "202303"],
        },
    )


if __name__ == "__main__":
    test_filter_7()
