import asyncio
import json
from typing import Type, Dict, List

from langchain_core.runnables import RunnableConfig
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

from common.logging.logger import get_logger
from common.types import TimeQueryParams, ParamsExtractData
from common.types.base import CHAIN_META, ChainMeta, ChainRuntime
from common.utils.reflect_util import Importer
from config.app_config import doc_endpoint
from config.project_config import get_project_config
from metastore.base import Dimension, Metric
from nl2agent.tools.base_tool import register_tool

import aiohttp

log = get_logger(__name__)


def doc_retrieve_preprocess(query: str, config: RunnableConfig) -> Dict:
    file_ids = config[CHAIN_META][ChainMeta.DOC_FILE_IDS]
    dir_ids = config[CHAIN_META][ChainMeta.DOC_DIR_IDS]
    payload = {
        "query": query,
        "ids": file_ids or [],
        "dir_ids": dir_ids or [],
        "only_nodes": True,
        "company_id": [],
        "start_year": None,
        "month": None,
        "end_year": None,
        "end_month": None,
    }
    return payload


class RetrieverArgs(BaseModel):
    query: str = Field(description="用户输入的查询")


@register_tool(name="doc_retrieval")
class RetrieverDocument(BaseTool):
    name: str = "doc_retrieval"
    description: str = "文档查询工具，关于文档内容相关的问题"
    args_schema: Type[BaseModel] = RetrieverArgs

    async def _arun(self, query: str, config: RunnableConfig) -> Dict:
        url = f"{doc_endpoint}/api/doc_index/query_document"  # 替换为基础地址
        doc_retrieve_preprocess_func = get_project_config(
            project_name=config[CHAIN_META][ChainMeta.PROJECT_NAME],
            model_name=config[CHAIN_META][ChainMeta.MODEL_NAME],
        ).doc_retrieve_preprocess_func
        preprocess, _ = Importer.import_module_content(
            doc_retrieve_preprocess_func,
            msg=f"doc_retrieve_preprocess_func {doc_retrieve_preprocess_func}",
        )
        payload = preprocess(query, config)
        # 该权限无任何文档，直接返回空
        if not payload.get("dir_ids") and not payload.get("ids"):
            return {
                "code": 0,
                "msg": "",
                "data": {
                    "content": "",
                    "sourceNodes": {"imageNodes": None, "textNodes": []},
                },
            }
        log.info(f"payload:{json.dumps(payload, ensure_ascii=False)}")
        async with aiohttp.ClientSession() as session:
            async with session.post(url=url, json=payload) as res:
                res.raise_for_status()
                ret = await res.json()
                if ret.get("code") != 0:
                    # return empty response
                    return {
                        "code": ret.get("code"),
                        "msg": ret.get("msg"),
                        "data": {
                            "content": "",
                            "sourceNodes": {"imageNodes": None, "textNodes": []},
                        },
                    }
                return ret

    def _run(self, query: str, config: RunnableConfig) -> str:
        return asyncio.run(self._arun(query, config))


class QueryDocuments(BaseTool):
    name: str = "doc_query"
    description: str = "文档查询工具，关于文档内容相关的问题."
    args_schema: Type[BaseModel] = RetrieverArgs

    async def _arun(self, query: str, config: RunnableConfig) -> str:
        url = f"{doc_endpoint}/api/doc_index/query_document"  # 替换为基础地址
        file_ids = config[CHAIN_META][ChainMeta.DOC_FILE_IDS]
        payload = {"query": query, "ids": file_ids}
        async with aiohttp.ClientSession() as session:
            async with session.post(url=url, json=payload) as res:
                res.raise_for_status()
                ret = await res.json()
                if ret.get("code") != 0:
                    return "Error"
                return ret.get("data", {}).get("content", "")

    def _run(self, query: str, config: RunnableConfig) -> str:
        return asyncio.run(self._arun(query, config))


if __name__ == "__main__":
    # QueryDocuments().invoke(
    #     {"query": "中国宝武钢铁集团有限公司 2024 年 03 月的资产总计是多少？期末金额相比年初金额有何变化？"}
    # )
    RetrieverDocument().invoke(
        {
            "query": "中国宝武钢铁集团有限公司 2024 年 03 月的资产总计是多少？期末金额相比年初金额有何变化？",
        }
    )
