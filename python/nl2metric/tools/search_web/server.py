import asyncio
import time
from typing import List

from fastapi import FastAPI, Body

from tools.search_web.read_web import (
    WebContentExtractor,
    URLRequest,
    APIResponse,
    ContentExtrResponder,
)
from tools.search_web.search_web import SearchService, SearchRequest, SearchResultResp

app = FastAPI()

search_service = SearchService()

extractor = WebContentExtractor()


# 定义 API 端点
@app.post("/extract")
async def extract_content(request: URLRequest):
    # 创建异步任务列表
    tasks = [extractor.fetch_and_convert(url) for url in request.urls]
    results: List[ContentExtrResponder] = await asyncio.gather(*tasks)  # 并行执行任务

    return [result.model_dump() for result in results]


@app.post("/search")
async def search_api(request: SearchRequest = Body(...)):
    resp: SearchResultResp = await search_service.search(
        request.query, request.max_results
    )
    return resp.model_dump()


@app.on_event("shutdown")
async def shutdown_event():
    await search_service.shutdown()


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)
