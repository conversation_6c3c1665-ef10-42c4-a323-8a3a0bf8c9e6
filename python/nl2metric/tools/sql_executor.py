import threading
from typing import Dict  # 新增导入语句
import logging
from dbutils.pooled_db import PooledDB
import pymysql


class Client:
    def connect(self):
        pass

    def query(self, sql):
        pass


class Config:
    def __init__(self, host, port, user, passwd, database):
        self.host = host
        self.port = port
        self.user = user
        self.passwd = passwd
        self.database = database

    def __eq__(self, other):
        if isinstance(other, Config):
            return (
                self.host == other.host
                and self.port == other.port
                and self.user == other.user
                and self.passwd == other.passwd
                and self.database == other.database
            )
        return False

    def __hash__(self):
        return hash((self.host, self.port, self.user, self.passwd, self.database))


class MysqlClient(Client):
    _instances: Dict[Config, "MysqlClient"] = {}  # 保留原有类型注解
    _lock = threading.Lock()

    @staticmethod
    def get_instance(config) -> "MysqlClient":
        with MysqlClient._lock:
            if config not in MysqlClient._instances:
                MysqlClient._instances[config] = MysqlClient(config)
        return MysqlClient._instances[config]

    @staticmethod
    def get_instance_by_params(
        host: str, port: int, user: str, passwd: str, database: str
    ) -> "MysqlClient":
        config = Config(host, port, user, passwd, database)
        return MysqlClient.get_instance(config)

    def __init__(self, config):
        self.config = config
        self.pool = None
        self.connect()

    def name(self):
        return "mysql {}:{}".format(self.config.host, self.config.port)

    def connect(self):
        c = self.config
        self.pool = PooledDB(
            creator=pymysql,
            maxconnections=10,
            mincached=2,
            maxcached=5,
            maxshared=3,
            blocking=True,
            maxusage=None,
            setsession=[],
            ping=0,
            host=c.host,
            port=int(c.port),
            user=c.user,
            passwd=c.passwd,
            database=c.database,
            charset="utf8",
        )

        if not self.pool:
            raise Exception("mysql client failed: {}".format(self.config))

    def close(self):
        if self.pool:
            self.pool.close()  # 关闭连接池所有连接
            self.pool = None

    def query(self, sql, detail=False):
        with self.pool.connection() as conn:
            with conn.cursor() as cursor:
                affect_rows = cursor.execute(sql)
                if detail:
                    message = "  [{}] exec sql, result rows: {}\n  {}".format(
                        self.name(), affect_rows, sql
                    )
                    logging.info(message)
                    # 获取列名
                if cursor.description:
                    column_names = [col[0] for col in cursor.description]
                return column_names, cursor.fetchall()
