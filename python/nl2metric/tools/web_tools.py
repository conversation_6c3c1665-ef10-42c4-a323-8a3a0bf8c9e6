import asyncio
import json
import uuid

import requests
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field
from smolagents import VisitWebpageTool

from common.tools.zhipu_web_search import zhipu_web_search
from config import app_config
from nl2agent.tools.base_tool import register_tool
from typing import Type


class WebSearchArgs(BaseModel):
    query: str = Field(description="用户输入的查询")


@register_tool(name="web_search")
class WebSearcherTool(BaseTool):
    name: str = "web_search"
    description: str = "搜索工具，搜索网络公开信息"
    args_schema: Type[BaseModel] = WebSearchArgs

    def _run(self, query: str, config: RunnableConfig) -> str:
        parsed_results = []
        try:
            if app_config.WEB_SEARCH_TOOL == "aliyun":
                from common.tools.aliyun_web_search import aliyun_generic_search

                results = asyncio.run(aliyun_generic_search(query))
            elif app_config.WEB_SEARCH_TOOL == "bocha":
                from common.tools.bocha_web_search import bocha_web_search

                results = bocha_web_search(query)
            else:
                results = zhipu_web_search(query)
        except Exception as e:
            return f"搜索出错: {e}"
        for result in results:
            parsed_results.append("\n".join(result))
        return "\n".join(parsed_results)


class FetchArgs(BaseModel):
    url: str = Field(description="The url of the webpage to fetch")


class FetchWebpageTool(BaseTool):
    name: str = "visit_webpage"
    description: str = "Visits a webpage at the given url and reads its content as a markdown string. Use this to browse webpages."
    args_schema: Type[BaseModel] = FetchArgs

    def _run(self, url: str, config: RunnableConfig) -> str:
        return VisitWebpageTool()(url)


if __name__ == "__main__":
    tool = WebSearcherTool()
    print(tool.run("如何使用python爬取网页"))
    # fetched_webpage_tool = FetchWebpageTool()
    # print(fetched_webpage_tool.run("https://www.langchain.com/"))
