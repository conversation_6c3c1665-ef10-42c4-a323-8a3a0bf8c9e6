import json
import os
import time

from typing import Any
from pathlib import Path

from common.types.base import ParamsExtractStage, CHAIN_META, ChainMeta, ChainRuntime
from common.types.exceptions import IntentNotSupported
from common.llm.general import create_chat_model
from common.logging.logger import get_logger
from common.utils.json_utils import extract_json_from_string
from common.prompt_selector.prompt_selector_base import PromptSelectorBase
from config.project_config import get_project_config
from langchain_core.runnables import (
    Runnable,
    RunnableLambda,
    RunnableBranch,
    RunnableConfig,
)
from langchain_core.runnables.utils import Input, Output
from langchain_core.output_parsers import StrOutputParser

from nl2intent.nl2intent_by_tag_v2 import get_nl2intent_by_tags_chain_v2
from nl2metric.few_shots import CHINA_LIFE_NAME, JINGFEN_BI
from nl2intent.types import Intent, IntentParams, intent2type, type2intent
from nl2intent.nl2intent_by_tag import get_nl2intent_by_tags_chain

logger = get_logger(__name__)


def retrive_for_intent(question: str, config: RunnableConfig):
    if isinstance(question, dict):
        question = question["question"]
    dimension_list = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.RAW_DIMENSIONS]
    metric_list = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.RAW_METRICS]

    if (
        config[CHAIN_META][ChainMeta.PROJECT_NAME] == CHINA_LIFE_NAME
        or config[CHAIN_META][ChainMeta.PROJECT_NAME] == JINGFEN_BI
    ):
        metric_list = [
            metric.label + "/" + "/".join(metric.synonyms)
            if metric.synonyms
            else metric.label
            for metric in metric_list
        ]
        metrics = "[" + ", ".join(metric_list) + "]"
        dimension_list = [
            dimension.label + "/" + "/".join(dimension.synonyms)
            if dimension.synonyms
            else dimension.label
            for dimension in dimension_list
        ]
        dimensions = "[" + ", ".join(dimension_list) + "]"
    else:
        metrics = "[" + ", ".join([metric.label for metric in metric_list]) + "]"
        dimensions = "[" + ", ".join([dim.label for dim in dimension_list]) + "]"

    # save_test_dataset.append(
    #     {"question": question, "dimensions": dimensions, "metrics": metrics}
    # )
    return {"dimensions": dimensions, "metrics": metrics, "question": question}


def is_force_intent(question, config: RunnableConfig):
    force_intent = config[CHAIN_META].get(ChainMeta.FORCE_INTENT, None)
    return force_intent is not None


def force_intent(question, config: RunnableConfig):
    force_intent = config[CHAIN_META].get(ChainMeta.FORCE_INTENT)
    assert force_intent is not None
    return IntentParams(intent=force_intent)


def is_nl2intent_by_tag(question, config: RunnableConfig):
    project_name = config[CHAIN_META][ChainMeta.PROJECT_NAME]
    model_name = config[CHAIN_META][ChainMeta.MODEL_NAME]
    nl2intent_params = get_project_config(
        project_name, model_name
    ).special_subchain_params(ParamsExtractStage.NL2INTENT)
    if not nl2intent_params:
        return False
    return nl2intent_params["type"] == ParamsExtractStage.NL2INTENT_BY_TAG


def is_nl2intent_by_tag_v2(question, config: RunnableConfig):
    project_name = config[CHAIN_META][ChainMeta.PROJECT_NAME]
    model_name = config[CHAIN_META][ChainMeta.MODEL_NAME]
    nl2intent_params = get_project_config(
        project_name, model_name
    ).special_subchain_params(ParamsExtractStage.NL2INTENT)
    if not nl2intent_params:
        return False
    return nl2intent_params["type"] == ParamsExtractStage.NL2INTENT_BY_TAG_V2


# 调用大模型
# invoke with question
def call_nl2intent(
    model_type: str, prompt_selector: PromptSelectorBase
) -> Runnable[Input, Output]:
    general_chain = (
        RunnableLambda(retrive_for_intent, name="retrive_for_intent")
        | RunnableLambda(
            prompt_selector.gen_prompt,
            name="PromptSelectorBase.gen_prompt:" + ParamsExtractStage.NL2INTENT,
        ).bind(stage=ParamsExtractStage.NL2INTENT)
        | create_chat_model(model_type)
        | StrOutputParser()
        | RunnableLambda(parse_intent_response, name="parse_intent_response")
    )

    chain = RunnableBranch(
        (
            is_force_intent,
            force_intent,
        ),
        (
            lambda question: any(keyword in question for keyword in ["趋势", "同比", "环比"]),
            lambda x: IntentParams(intent=Intent.query_metric),
        ),
        (
            lambda question: any(
                keyword in question for keyword in ["原因", "为什么", "为何", "为啥", "原因是"]
            ),
            lambda x: IntentParams(intent=Intent.attribution_analysis),
        ),
        (
            is_nl2intent_by_tag,
            get_nl2intent_by_tags_chain(prompt_selector),
        ),
        (
            is_nl2intent_by_tag_v2,
            get_nl2intent_by_tags_chain_v2(prompt_selector),
        ),
        general_chain,
    )
    chain.name = ParamsExtractStage.NL2INTENT
    return chain


def _dump(path: str, obj: Any):
    fold_path: str = os.path.dirname(path)
    os.makedirs(fold_path, exist_ok=True)
    with open(path, mode="w") as fw:
        json.dump(obj, fw, indent=2, ensure_ascii=False)


# 解析响应
def _parse_intent_response(response: str, config: RunnableConfig) -> IntentParams:
    # # 用于debug
    # save_test_dataset[-1]["intent"] = response
    # _dump(
    #     os.path.dirname(Path(__file__).parent) + '/tests/test_zyj/predict/宝武.json',
    #     save_test_dataset,
    # )

    if "{" in response:
        obj = extract_json_from_string(response, "parse_intent_response")
        if obj is None:
            raise ValueError(f"parse_intent_response got illegal json str: {response}")
        intent_param = IntentParams(**obj)
    else:
        intent = next((intent for intent in Intent if intent.value in response), None)
        if intent is None:
            raise ValueError(f"parse_intent_response got illegal str: {response}")
        intent_param = IntentParams(intent=intent)

    # intent check
    project_name = config[CHAIN_META][ChainMeta.PROJECT_NAME]
    model_name = config[CHAIN_META][ChainMeta.MODEL_NAME]
    not_allowed_intent_list = get_project_config(
        project_name, model_name
    ).not_allowed_intent_list
    if intent_param.intent in not_allowed_intent_list:
        raise IntentNotSupported(
            f"project {project_name} model {model_name} intent not supported {intent_param.intent}"
        )

    config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.INTENT] = intent_param
    return intent_param


def parse_intent_response(response: str, config: RunnableConfig) -> IntentParams:
    try:
        return _parse_intent_response(response, config)
    except Exception as e:
        if ChainRuntime.INTENT not in config[CHAIN_META][ChainMeta.RUN_TIME]:
            # without this, wait_for_intent will keep waiting for a long time
            config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.INTENT] = None
        raise e


# ChainRuntime.INTENT_TAG must be before ChainRuntime.INTENT, because of wait_for_intent
# no need to mutex config, we have GIL
# It is highly unlikely that we donnot have intent when call wait_for_intent, so cond is unnecessary
def wait_for_intent(safe, config):
    intent = None
    run_time = config[CHAIN_META][ChainMeta.RUN_TIME]
    for i in range(100):
        if ChainRuntime.INTENT in run_time:
            intent = run_time[ChainRuntime.INTENT]
            break
        time.sleep(0.5)
        logger.warning(f"wait_for_intent: {i}")
    if intent is None:
        if safe:
            return None, None
        else:
            raise IntentNotSupported("wait_for_intent does not get intent")
    intent_tag = run_time.get(ChainRuntime.INTENT_TAG, None)
    return intent, intent_tag
