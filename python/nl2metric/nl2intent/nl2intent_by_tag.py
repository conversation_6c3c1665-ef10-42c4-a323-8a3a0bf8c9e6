from typing import List, Tuple, Optional

from langchain_core.runnables import (
    RunnableLambda,
    RunnableConfig,
)
from llama_index.core.vector_stores import (
    MetadataFilters,
    MetadataFilter,
    FilterOperator,
)
from pydantic import BaseModel

from common.llm.general import create_chat_model_in_chain
from common.logging.logger import get_logger
from common.parser.custom_pydantic import CustomPydanticOutputParser
from common.prompt_selector.prompt_selector_base import PromptSelectorBase
from common.types.base import (
    ParamsExtractStage,
    CHAIN_META,
    ChainMeta,
    ChainRuntime,
    JobType,
)
from common.types.exceptions import IntentNotSupported, RetryParamExtract
from config.project_config import get_project_config
from nl2document.common.base.const import (
    LLAMA_INDEX_FILE_ID,
    LLAMA_INDEX_FILE_NAME,
    LLAMA_INDEX_META_PART_NAME,
)
from nl2document.common.models.model import get_file_ids_by_scene_id
from nl2intent.types import Intent, IntentParams
from nl2meta.nl2meta import MetaIntentType
from nl2metric.few_shots import SCIENCE_CITY_NAME

logger = get_logger(__name__)

# use list because tags should be strictly in order
TAGS_NAME = [
    "追问",
    "连续问",
    "选场景",
    "与召回相关",
    "非召回相关",
    "要素模糊",
    "维度详情",
    "指标详情",
    "维度列表",
    "指标列表",
    "数据概述",
    "模型列表",
    "闲聊",
    "问指标",
    "问码值",
    "问知识",
    "需排序",
    "需对比",
    "需对比全部指标",
    "需占比",
    "需分布",
]
TAGS_DICT = {tag: index for index, tag in enumerate(TAGS_NAME)}
# '要素模糊' cause many err, so erase it from UNSUPPORTED_TAGS_NAME
UNSUPPORTED_TAGS_NAME = {
    "问知识",
    "需对比全部指标",
    "需占比",
}


class IntentTags:
    def __init__(self, tags: List[str]) -> None:
        self.__tags_bool = [False] * len(TAGS_NAME)
        for tag in tags:
            index = TAGS_DICT.get(tag, -1)
            if index >= 0:
                self.__tags_bool[index] = True
        self.__tags = []
        self.__unsupported = False
        for i in range(len(TAGS_NAME)):
            if self.__tags_bool[i]:
                if TAGS_NAME[i] in UNSUPPORTED_TAGS_NAME:
                    self.__unsupported = True
                self.__tags.append(TAGS_NAME[i])

        self._intent, self._meta_intent = self._intent_impl()
        if "非召回相关" in self.__tags and (not self._intent == Intent.other):
            self.__unsupported = True

    @property
    def tags(self):
        return self.__tags

    def has_tag(self, tag: str):
        return self.__tags_bool[TAGS_DICT[tag]]

    @property
    def is_follow_up(self):
        return self.has_tag("追问")

    @property
    def is_sequential(self):
        return self.has_tag("连续问")

    @property
    def intent(self) -> Tuple[Intent, Optional[MetaIntentType]]:
        if self.is_follow_up:
            raise RuntimeError(
                f"follow_up question's intent should be the same as history intent"
            )
        if self.is_sequential:
            raise RuntimeError(
                f"continuous question's intent should be determined by first question"
            )
        if self.__unsupported:
            raise IntentNotSupported(f"found unsupported tag, tag list: {self.tags}")
        if self._intent is None:
            raise IntentNotSupported(f"found unprocesssed tag, tag list: {self.tags}")
        return self._intent, self._meta_intent

    def _intent_impl(self):
        # get intent strictly in order of TAGS_NAME
        if self.has_tag("维度详情") or self.has_tag("指标详情"):
            return Intent.query_metric, None
        if self.has_tag("维度列表"):
            return Intent.other, MetaIntentType.DIMENSION_LIST
        if self.has_tag("指标列表"):
            return Intent.other, MetaIntentType.METRIC_LIST
        if self.has_tag("数据概述"):
            return Intent.other, MetaIntentType.ALL_DATA
        if self.has_tag("模型列表"):
            return Intent.other, MetaIntentType.TABLE_LIST
        if self.has_tag("闲聊"):
            return Intent.other, MetaIntentType.CHITCHAT
        if self.has_tag("问指标"):
            return Intent.query_metric, None
        if self.has_tag("问码值"):
            return Intent.query_metric, None
        if self.has_tag("需排序"):
            return Intent.query_metric, None
        if self.has_tag("需对比"):
            return Intent.attribution_analysis, None
        if self.has_tag("需分布"):
            return Intent.query_metric, None
        return None, None


class IntentByTagResult(BaseModel):
    intent_list: List[str]
    first_question: Optional[str] = None
    dimension_param: Optional[List[str]] = None
    metric_param: Optional[List[str]] = None
    askbi: Optional[bool] = False
    askdoc: Optional[bool] = False


def retrive_for_intent_by_tags(question: str, config: RunnableConfig):
    if isinstance(question, dict):
        question = question["question"]
    dimensions = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.RAW_DIMENSIONS]
    metric_list = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.RAW_METRICS]
    metric_list = [
        metric.label + "/" + "/".join(metric.synonyms)
        if metric.synonyms
        else metric.label
        for metric in metric_list
    ]
    metrics = "[" + ", ".join(metric_list) + "]"

    messages = config[CHAIN_META].get(ChainMeta.MESSAGES, None)
    if messages:
        messages = [f'"{m.content}"' for m in messages if m.role == "user"]
    if messages:
        messages.pop()  # del current question
    if messages:
        history_questions = f"[{','.join(messages)}]"
    else:
        history_questions = "[]"

    # 召回文档参与意图识别，科学城项目
    doc_recalls = []
    if config[CHAIN_META][ChainMeta.PROJECT_NAME] == SCIENCE_CITY_NAME:
        scene_id = config[CHAIN_META][ChainMeta.MODEL_ID]
        file_ids = get_file_ids_by_scene_id(scene_id)
        file_ids = [str(file_id) for file_id in file_ids]
        doc_filters = MetadataFilters(
            filters=[
                MetadataFilter(
                    key=LLAMA_INDEX_FILE_ID, value=file_ids, operator=FilterOperator.IN
                )
            ]
        )
        from nl2document.index.chains.query_document import retrieve_default

        nodes_with_scores = retrieve_default(query=question, filters=doc_filters)
        doc_recalls = []
        # 只取前2个结果
        if len(nodes_with_scores) > 2:
            nodes_with_scores = nodes_with_scores[:2]
        for node in nodes_with_scores:
            doc_recall = {
                "fileName": node.metadata.get(LLAMA_INDEX_FILE_NAME, ""),
                "partName": node.metadata.get(LLAMA_INDEX_META_PART_NAME, ""),
                "content": node.get_content()[:800]
                if len(node.get_content()) > 800
                else node.get_content(),
            }
            doc_recalls.append(doc_recall)

    return {
        "dimensions": dimensions,
        "metrics": metrics,
        "question": question,
        "history_questions": history_questions,
        "doc_recall": doc_recalls,
    }


def _parse_intent_by_tag_response(result: IntentByTagResult, config: RunnableConfig):
    if not result.intent_list:
        raise IntentNotSupported("empty intent tag list")
    tags = IntentTags(result.intent_list)
    # ChainRuntime.INTENT_TAG must be before ChainRuntime.INTENT, because of wait_for_intent
    config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.INTENT_TAG] = tags
    question = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.QUESTION]
    project_name = config[CHAIN_META][ChainMeta.PROJECT_NAME]
    model_name = config[CHAIN_META][ChainMeta.MODEL_NAME]
    if tags.is_sequential:
        if result.first_question:
            # 这里直接抛出重试异常，代码要通用很多。
            # 否则的话，要重新做retrive.py的召回，代码架构就乱套了。
            # 也有不好的地方：这里抛出异常，提参查数会无效地多跑一遍。
            raise RetryParamExtract(
                message=f"question {question} is sequential, first_question is {result.first_question}",
                question=result.first_question,
            )
        else:
            raise RuntimeError(
                f"question {question} is sequential but no first_question found"
            )
    if tags.is_follow_up:
        history_params_extract_data = config[CHAIN_META].get(
            ChainMeta.HISTORY_PARAMS_EXTRACT_DATA, None
        )
        if not history_params_extract_data:
            raise RuntimeError(
                f"question {question} is follow_up but no history_params_extract_data found"
            )
        job_type = config[CHAIN_META][ChainMeta.JOB_TYPE]
        if job_type == JobType.PROJECT_PARAMS_EXTRACT:
            # TODO(bhx): what if history_params_extract_data have a query_metric and a agent
            intent, meta_intent = next(
                iter(history_params_extract_data.values())
            ).intent
        else:
            intent, meta_intent = history_params_extract_data.intent
    else:
        intent, meta_intent = tags.intent

    not_allowed_intent_list = get_project_config(
        project_name, model_name
    ).not_allowed_intent_list

    # intent check
    if intent in not_allowed_intent_list:
        raise IntentNotSupported(
            f"project {project_name} model {model_name} question {question} intent not supported {intent}"
        )
    intent_param = IntentParams(intent=intent)
    config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.INTENT] = intent_param
    if meta_intent:
        not_allowed_meta_intent_list = get_project_config(
            project_name, model_name
        ).not_allowed_meta_intent_list
        if meta_intent in not_allowed_meta_intent_list:
            raise IntentNotSupported(
                f"project {project_name} model {model_name} question {question} meta intent not supported {meta_intent}"
            )
        config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.META_INTENT] = meta_intent
    return intent_param


def parse_intent_by_tag_response(result: IntentByTagResult, config: RunnableConfig):
    try:
        return _parse_intent_by_tag_response(result, config)
    except Exception as e:
        if ChainRuntime.INTENT not in config[CHAIN_META][ChainMeta.RUN_TIME]:
            # without this, wait_for_intent will keep waiting for a long time
            config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.INTENT] = None
        raise e


def get_nl2intent_by_tags_chain(prompt_selector: PromptSelectorBase):
    return (
        RunnableLambda(retrive_for_intent_by_tags, name="retrive_for_intent_by_tags")
        | RunnableLambda(
            prompt_selector.gen_prompt,
            name="PromptSelectorBase.gen_prompt:" + ParamsExtractStage.NL2INTENT_BY_TAG,
        ).bind(stage=ParamsExtractStage.NL2INTENT_BY_TAG)
        | RunnableLambda(create_chat_model_in_chain).bind(
            stage=ParamsExtractStage.NL2INTENT
        )
        | CustomPydanticOutputParser(pydantic_object=IntentByTagResult)
        | RunnableLambda(
            parse_intent_by_tag_response, name="parse_intent_by_tag_response"
        )
    )
