import asyncio
import traceback

from colorama import Fore
from fastapi import Request
from fastapi.responses import StreamingResponse
from langchain_core.runnables import RunnableLambda, RunnableConfig
from llama_index.core import ServiceContext
from llama_index.core.text_splitter import Token<PERSON>extSplitter

from backend_stage_reporter.cb_mgr import process_cb_task
from backend_stage_reporter.reporter import get_reporter_cb
from biz.cmcc_meeting_biz.cmcc_common.data_model import (
    QueryMeetingRequest,
    QueryMeetingResponse,
)
from common.llm.llama_llm import get_llm
from common.logging.logger import get_logger
from common.prompt_selector.json_prompt_selector import MemPromptSelector
from common.trace import tracer
from common.types.base import (
    gen_nl2meeting_chain_meta,
    CHAIN_META,
    ChainMeta,
    ChainRuntime,
    JobType,
)
from common.types.callback_handler import LogCallbackHandler
from common.utils.base import check_condition
from common.utils.string_utils import class_to_dict
from config import app_config
from config.doc_config import (
    QUERY_MEETING_LLM_CONTEXT_WINDOW,
)
from nl2document.common.models.base_model import (
    get_data_source,
)

from biz.cmcc_meeting_biz.service.chain.nl2meeting import call_nl2meeting
from nl2document.index.chains.query_condense import call_condense_query
from nl2document.index.service.service import DocumentIndexService

logger = get_logger(__name__)


def query_meeting_postprocess(response, config: RunnableConfig):
    return QueryMeetingResponse(
        content=response,
    )


class CMCCMeetingService(DocumentIndexService):
    def __init__(self):
        super().__init__()
        self.router.add_api_route(
            "/api/doc_index/query_meeting",
            self.query_meeting,
            methods=["POST"],
        )

    async def query_meeting(self, req: QueryMeetingRequest, request: Request):
        headers = request.headers
        trace_id = headers.get("Traceid", tracer.get_trace_id())
        check_condition(
            req.user_id, f"req.user_id should not be empty, but: `{req.user_id}` "
        )
        logger.info(f"receive query meeting request info: {req}")

        service_context = ServiceContext.from_defaults(
            llm=get_llm(req.model_type),
            embed_model=self._embed_model,
            text_splitter=TokenTextSplitter(),
            # 由于智谱大模型最大token支持8192，这里设置为8000
            context_window=QUERY_MEETING_LLM_CONTEXT_WINDOW,
        )
        if len(req.file_id) > 0:
            req.file_ids.append(req.file_id)
        chain_metadata = gen_nl2meeting_chain_meta(
            job_type=JobType.QUERY_MEETING,
            document_index_service=self,
            llamaindex_service_ctx=service_context,
            user_id=req.user_id,
            file_ids=req.file_ids,
            model_type=req.model_type,
            condense_info=req.condense_info,
        )
        cbs = []
        # 获取当前协程对象
        if app_config.ENABLE_LANGFUSE:
            backend_cb = get_reporter_cb(
                trace_id,
                None,
                f"query_meeting:{req.model_type}:{req.condense_info.cur_question}",
                None,
            )
            cbs = [
                LogCallbackHandler(id=trace_id, host=app_config.CLUSTER_ID),
            ]
            if backend_cb is not None:
                cbs.append(backend_cb)

            event = asyncio.Event()
            asyncio.create_task(process_cb_task(event, backend_cb))
            asyncio.current_task().add_done_callback(lambda _: event.set())
        prompt_selector = MemPromptSelector(model_type=req.model_type)
        chain_condense = call_condense_query(req.model_type)
        chain_meeting = call_nl2meeting(req.model_type, prompt_selector)

        try:
            # 先执行 condense query
            condensed_query = await chain_condense.ainvoke(
                class_to_dict(req.condense_info),
                config={
                    CHAIN_META: chain_metadata,
                    "callbacks": cbs,
                    "max_concurrency": app_config.MAX_CONCURRENCY,
                },
            )

            if req.stream:
                async_generator = chain_meeting.astream(
                    condensed_query,
                    config={
                        CHAIN_META: chain_metadata,
                        "callbacks": cbs,
                        "max_concurrency": app_config.MAX_CONCURRENCY,
                    },
                )
                return StreamingResponse(
                    async_generator, media_type="text/event-stream"
                )
            else:
                chain_meeting = chain_meeting | RunnableLambda(
                    query_meeting_postprocess, name="query_meeting_postprocess"
                )
                response = await chain_meeting.ainvoke(
                    condensed_query,
                    config={
                        CHAIN_META: chain_metadata,
                        "callbacks": cbs,
                        "max_concurrency": app_config.MAX_CONCURRENCY,
                    },
                )
                logger.info(
                    Fore.CYAN + "QueryMeeting: %s" + Fore.RESET, response.content
                )
                return class_to_dict(response)
        except Exception as e:
            logger.error(f"query meeting error: {e}. trace: {traceback.format_exc()}")
            return {"code": 500, "msg": "internal error"}
        finally:
            # todo save condense query to db
            new_query = chain_metadata[ChainMeta.RUN_TIME][ChainRuntime.QUESTION]
            await get_data_source().aupdate_chat_record(trace_id, new_query, "", [])
