from typing import Optional

from langchain_core.pydantic_v1 import BaseModel
from langchain_core.runnables import (
    Runnable,
    RunnableLambda,
    RunnableConfig,
)
from langchain_core.runnables.utils import Input, Output

from common.llm.general import create_chat_model
from common.logging.logger import get_logger
from common.parser.custom_pydantic import CustomPydanticOutputParser
from common.prompt_selector.prompt_selector_base import PromptSelectorBase
from common.types.base import ParamsExtractStage
from metastore.meeting.db_meeting_metastore import get_db_meeting_metastore

logger = get_logger(__name__)


class QueryMeetingParamResult(BaseModel):
    meeting: Optional[str] = ""
    speaker: Optional[str] = ""


def query_meeting_param_postprocess(
    input: QueryMeetingParamResult, config: RunnableConfig
):
    metastore = get_db_meeting_metastore()

    meeting = None
    if input.meeting:
        meeting = metastore.safe_get_meeting_by_id(input.meeting)

    speaker = None
    if input.speaker:
        speaker = metastore.safe_get_personnel_by_name(input.speaker)

    return {
        "meetings": meeting,
        "speakers": speaker,
    }


def query_meeting_param(
    model_type: str, prompt_selector: PromptSelectorBase
) -> Runnable[Input, Output]:
    chain = (
        RunnableLambda(
            prompt_selector.gen_prompt,
            name="PromptSelectorBase.gen_prompt:"
            + ParamsExtractStage.NL2MEETING_PARAMS,
        ).bind(stage=ParamsExtractStage.NL2MEETING_PARAMS)
        | create_chat_model(model_type)
        | CustomPydanticOutputParser(pydantic_object=QueryMeetingParamResult)
        | RunnableLambda(
            query_meeting_param_postprocess, name="query_meeting_param_postprocess"
        )
    )
    chain.name = ParamsExtractStage.NL2MEETING_PARAMS
    return chain
