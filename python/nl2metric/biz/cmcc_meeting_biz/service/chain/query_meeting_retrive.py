from typing import List
from common.logging.logger import get_logger
from common.types.base import CHAIN_META, ChainMeta, ChainRuntime
from langchain_core.runnables import (
    Run<PERSON>ble,
    RunnableLambda,
    RunnableConfig,
)
from langchain_core.runnables.utils import Input, Output

from pre_filter.meeting_index.service import get_meeting_pre_filter_service

logger = get_logger(__name__)


async def meeting_retrieval(question: str, config: RunnableConfig):
    if isinstance(question, dict):
        question = question["question"]
    user_id = config[CHAIN_META][ChainMeta.NL2MEETING_USER]
    service = get_meeting_pre_filter_service()

    config[CHAIN_META][ChainMeta.RUN_TIME][
        ChainRuntime.MEETING
    ] = await service.retrieve_meetings(query=question, user_id=user_id)
    config[CHAIN_META][ChainMeta.RUN_TIME][
        ChainRuntime.MEETING_PERSONNEL
    ] = await service.retrieve_personnels(query=question, user_id=user_id)
    return {"question": question}


def call_meeting_retrive() -> Runnable[Input, Output]:
    return RunnableLambda(meeting_retrieval, name="meeting_retrieval")
