from graphlib import TopologicalSorter
from pathlib import Path
from typing import List
from dotenv import load_dotenv
from llama_index.core.schema import BaseNode

# specify path to easily change env in docker
env_file = Path(__file__).parent.parent.parent.parent / ".env"
print(f"env_file: {env_file}")
load_dotenv(dotenv_path=env_file, override=True)
# both app_config and doc_config needs load_doten
from llama_index.core.vector_stores import (
    MetadataFilters,
    MetadataFilter,
    FilterOperator,
)
from biz.cmcc_meeting_biz.builder.cmcc_index_builder import save_document_summary
from nl2document.common.vector.vector_store import get_vector_store

from nl2document.common.models.base_model import get_doc_ids_with_summary_null
from nl2document.common.models.model import CommonDocumentModel


def sort_nodes(nodes: List[BaseNode]) -> List[BaseNode]:
    # 第一步：创建节点字典
    node_dict = {n.node_id: n for n in nodes}

    # 第二步：初始化 TopologicalSorter
    sorter = TopologicalSorter()

    # 第三步：添加依赖关系
    for node in nodes:
        if node.prev_node and node.prev_node.node_id in node_dict:
            sorter.add(node.node_id, node.prev_node.node_id)  # 当前节点依赖于前置节点
        else:
            sorter.add(node.node_id)  # 无依赖的节点

    # 第四步：执行排序
    try:
        sorted_ids = list(sorter.static_order())  # 获取排序后的节点 ID
        return [node_dict[node_id] for node_id in sorted_ids]  # 转换为节点对象列表
    except ValueError:
        raise ValueError("图中存在循环，无法完成排序。")


def main():
    doc_list: List[CommonDocumentModel] = get_doc_ids_with_summary_null()
    vector_store = get_vector_store()
    for doc in doc_list:
        filters = MetadataFilters(
            filters=[
                MetadataFilter(key="file_id", value=doc.id, operator=FilterOperator.EQ),
                # MetadataFilter(key="parent_node", value="", operator=FilterOperator.EQ),
            ]
        )

        nodes = vector_store.get_nodes(filters=filters)
        nodes = list(filter(lambda x: x.metadata.get("parent_node") is None, nodes))
        nodes = sort_nodes(nodes)
        if len(nodes) == 0:
            print(f"file {doc.file_name} has no nodes")
            continue
        print(
            [{n.node_id: n.prev_node.node_id if n.prev_node else None} for n in nodes]
        )
        save_document_summary(doc, nodes)
        print(f"completed file {doc.file_name} save_document_summary")


if __name__ == "__main__":
    main()
