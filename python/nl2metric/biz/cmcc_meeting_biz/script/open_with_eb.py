import webbrowser

import requests


def get_access_token():
    """获取移动会议的access token"""
    url = f"https://meeting-hw.dashipin.cn:18443/middle/auth/login"
    headers = {"From": "APP_ANDROID", "Content-Type": "application/json"}
    data = {"account": "***********", "password": "Ydzq2024"}

    try:
        response = requests.post(url, headers=headers, json=data)
        print(response.text)
        if response.status_code == 200:
            result = response.json()
            return result.get("accessToken"), result.get("xlocation")
        else:
            print(f"请求失败: {response.status_code}")
    except Exception as e:
        print(f"请求异常: {str(e)}")
    return None, None


def open_meeting_url():
    """打开移动会议助手页面"""
    base_url = "https://agent.dashipin.cn:1443/ai-assistant"
    user_id = "83ONUswWyc3M8t8m7mrmDA%3D%3D"

    # 获取最新token
    access_token, xlocation = get_access_token()
    if not access_token or not xlocation:
        print("获取认证信息失败")
        return

    # 构建完整URL
    url = (
        f"{base_url}?userId={user_id}&oem=MobileMeeting"
        f"&authorization={access_token}&xlocation={xlocation}"
    )

    try:
        webbrowser.open(url)
        print("成功打开移动会议助手页面")
    except Exception as e:
        print(f"打开URL失败: {str(e)}")


if __name__ == "__main__":
    open_meeting_url()
