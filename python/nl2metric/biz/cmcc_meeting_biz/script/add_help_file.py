import json
import os
from dotenv import load_dotenv

load_dotenv()
import uuid
from config.doc_config import HELP_USER_ID, milvus_uri
from nl2document.common.models.model import CommonDocumentModel, UploadType
from nl2document.common.base.const import (
    LLAMA_INDEX_FILE_ID,
    LLAMA_INDEX_META_USER_ID,
    LLAMA_UPLOAD_TYPE,
)
from config.doc_config import milvus_collection_name
from pymilvus import MilvusClient
from biz.cmcc_meeting_biz.builder.cmcc_index_builder import CMCCIndexBuilder

client = MilvusClient(milvus_uri)
save_path = "./milvus_nodes"

index_builder = CMCCIndexBuilder()

no_sentence_node = True


def add_help_file(file_path: str):
    document_model = CommonDocumentModel(
        file_name=os.path.basename(file_path),
        id=str(uuid.uuid4()) + "-DIPEAK-SYSTEM",
    )
    document_model.meta_info = {
        LLAMA_INDEX_FILE_ID: document_model.id,
        LLAMA_INDEX_META_USER_ID: HELP_USER_ID,
        LLAMA_UPLOAD_TYPE: UploadType.meeting,
    }
    document_model.upload_type = UploadType.temp
    index_builder._create_index(file_path, document_model)


def get_milvus_nodes():
    res = client.query(
        collection_name=milvus_collection_name, filter=f" user_id=='{HELP_USER_ID}'"
    )

    target_file = f"help_doc_milvus_nodes.json"
    result_dict = []

    for item in res:
        node_content = json.loads(item["_node_content"])
        content = node_content["text"]
        node_id = node_content["id_"]
        temp = {
            "file_id": item["file_id"],
            "part_name": item["part_name"],
            "node_id": node_id,
            "content": content,
            "parent_node": item.get("parent_node", ""),
        }
        if no_sentence_node:
            if temp["parent_node"] != "":
                continue
        result_dict.append(temp)

    with open(target_file, "w") as f:
        json.dump(result_dict, f, indent=4, ensure_ascii=False)


def delete_old_milvus_nodes():
    res = client.query(
        collection_name=milvus_collection_name, filter=f" user_id=='{HELP_USER_ID}'"
    )
    for item in res:
        client.delete(
            collection_name=milvus_collection_name,
            filter=f" file_id=='{item['file_id']}'",
        )


if __name__ == "__main__":
    file_dir = "./help_file"
    delete_old_milvus_nodes()
    for file_path in os.listdir(file_dir):
        add_help_file(os.path.join(file_dir, file_path))
    get_milvus_nodes()
