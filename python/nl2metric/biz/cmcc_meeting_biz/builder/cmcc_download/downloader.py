from abc import ABC, abstractmethod
from typing import Dict, Optional

from common.logging.logger import get_logger

logger = get_logger(__name__)


class FileDownloader(ABC):
    @abstractmethod
    def generate_auth_header(self) -> Dict[str, str]:
        """生成认证头"""
        pass

    @abstractmethod
    def get_file_info(self, file_id: str, x_location: str = "") -> Optional[Dict]:
        """获取文件信息"""
        pass

    def download_file(self, file_id: str, save_path: str, x_location: str = "") -> None:
        pass
