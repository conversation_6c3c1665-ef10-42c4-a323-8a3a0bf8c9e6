import time
import hmac
import hashlib
import base64

import requests

from common.logging import logger
from config.doc_config import eb_app_id, eb_app_key, eb_get_file_url


def generate_signature(app_id, app_key, timestamp):
    """
    生成 HMAC-SHA256 签名
    """
    # 构建待签名字符串
    message = f"{app_id}:{timestamp}"

    # 生成 HMAC-SHA256 签名
    signature = (
        hmac.new(
            key=app_key.encode("utf-8"),
            msg=message.encode("utf-8"),
            digestmod=hashlib.sha256,
        )
        .hexdigest()
        .lower()
    )

    return signature


def build_headers(app_id, app_key, timestamp):
    """
    构建请求头
    """
    signature = generate_signature(app_id, app_key, timestamp)

    headers = {
        "authType": "APP_ID_AUTH",
        "Authorization": f"HMAC-SHA256 signature={signature}",
        "timestamp": str(timestamp),
        "appId": str(app_id),
    }

    return headers


def get_file_info(file_id):
    url = f"{eb_get_file_url}/middle/sys/zhipu/file/get"
    params = {"FileID": file_id}
    headers = build_headers(eb_app_id, eb_app_key, timestamp)
    response = requests.get(url, params=params, headers=headers)
    logger.info(f"response:, {response.text}")
    if response.status_code == 200:
        data = response.json()
        if data["code"] == 0:
            return data["data"]
        else:
            logger.error(f"Error:{data['message']}")
    else:
        logger.error(f"HTTP Error: {response.status_code}")

    return None


def download_file(file_id, save_path):
    file_info = get_file_info(file_id)
    if file_info is None:
        raise Exception("get cmcc eb file info not found")
    download_url = file_info["downloadURL"]
    headers = build_headers(eb_app_id, eb_app_key, timestamp)
    response = requests.get(download_url, stream=True, headers=headers)

    if response.status_code == 200:
        with open(save_path, "wb") as file:
            for chunk in response.iter_content(chunk_size=8192):
                file.write(chunk)
        logger.info(f"File downloaded successfully: {save_path}")
    else:
        logger.error(f"Download failed:, {response.status_code}")
        raise Exception("download file failed")


if __name__ == "__main__":
    timestamp = int(time.time())  # 当前时间戳，单位为秒

    # 构建请求头
    headers = build_headers(eb_app_id, eb_app_key, timestamp)

    # 输出请求头
    print("Generated Headers:")
    for key, value in headers.items():
        print(f"{key}: {value}")

    file_id = "1856139265876082688"
    get_file_info(file_id)
    download_file(file_id, "test.pdf")
