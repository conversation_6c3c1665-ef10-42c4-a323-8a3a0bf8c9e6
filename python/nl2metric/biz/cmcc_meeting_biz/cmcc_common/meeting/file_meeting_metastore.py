import yaml
from pathlib import Path

from biz.cmcc_meeting_biz.cmcc_common.meeting.base import Meeting, BaseMeetingMetaStore
from common.logging.logger import get_logger
from common.utils.concurrent_utils import do_once

logger = get_logger(__name__)


class FileMeetingMetaStore(BaseMeetingMetaStore):
    def __init__(self):
        logger.debug("FileMeetingMetaStore creating...")
        self.__file_path = Path(__file__).parent.parent.parent / "meeting_meta.yml"

        super().__init__()
        logger.info("FileMeetingMetaStore created")

    def _fetch_all_meetings(self):
        try:
            with open(self.__file_path, "r", encoding="utf8") as file:
                data = yaml.safe_load(file)
            meetings = {}
            for raw_meeting in data["meetings"]:
                m = Meeting.model_validate(raw_meeting)
                meetings[m.id] = m
            return meetings
        except FileNotFoundError as e:
            return {}


# index per user can leads to massive amount of indexs
@do_once
def get_file_meeting_metastore():
    return FileMeetingMetaStore()
