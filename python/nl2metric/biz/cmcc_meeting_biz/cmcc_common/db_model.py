from datetime import datetime
import random
from typing import List, Sequence, Union

from pymysql import InternalError
from sqlalchemy import (
    DateTime,
    Integer,
    Text,
    func,
    select,
    update,
    Column,
    JSON,
    or_,
)
from sqlalchemy import String
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import Session
from sqlalchemy.orm import mapped_column

from common.utils.retry import retry_async
from nl2document.common.base.const import (
    LLAMA_INDEX_FILE_ID,
    LLAMA_UPLOAD_TYPE,
    LLAMA_INDEX_META_USER_ID,
    LLAMA_INDEX_MEETING_ID,
)
from nl2document.common.models.base_model import DocumentDataSource, UploadStatus
from nl2document.common.models.model import (
    Base,
    engine,
    async_session,
    CommonDocumentModel,
)
from nl2document.common.msg.doc import FileStatus


class CMCCFileModel(Base):
    __tablename__ = "cmcc_files"
    id: Mapped[int] = mapped_column(primary_key=True)
    file_id: Mapped[str] = mapped_column(String(256))
    file_name: Mapped[str] = mapped_column(String(256))
    user_id: Mapped[str] = mapped_column(String(256))
    meeting_id: Mapped[str] = mapped_column(String(256))
    file_type: Mapped[str] = mapped_column(String(64))
    upload_status: Mapped[str] = mapped_column(String(64))
    upload_type: Mapped[str] = mapped_column(String(64))
    platform: Mapped[str] = mapped_column(String(64))
    parse_result: Mapped[str] = mapped_column(String(128))
    parse_error_msg: Mapped[str] = mapped_column(String(2048))
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=func.now(), nullable=False
    )
    app_id: Mapped[str] = mapped_column(String(64))
    summary: Mapped[str] = mapped_column(Text, nullable=True)
    x_location: Mapped[str] = mapped_column(String(256), nullable=True)


class CMCCChatRecordModel(Base):
    __tablename__ = "cmcc_chat_record"

    id: Mapped[int] = mapped_column(
        Integer, primary_key=True, autoincrement=True, comment="主键id"
    )
    request_id: Mapped[str] = mapped_column(
        String(255), nullable=False, default="", comment="请求ID"
    )
    session_id: Mapped[str] = mapped_column(
        String(128), nullable=False, default="", comment="会话ID"
    )
    user_id: Mapped[str] = mapped_column(
        String(128), nullable=False, default="", comment="用户ID"
    )
    meeting_id: Mapped[str] = mapped_column(
        String(128), nullable=False, default="", comment="会议ID"
    )
    file_id: Mapped[str] = mapped_column(
        String(128), nullable=False, default="", comment="文件ID"
    )
    question: Mapped[str] = mapped_column(Text, nullable=False, comment="问题")
    content: Mapped[str] = mapped_column(Text, nullable=False, comment="回答")
    new_query: Mapped[str] = mapped_column(Text, nullable=True, comment="新问题")
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=func.now(), nullable=True, comment="创建对话时间"
    )


class MeetingInfo(Base):
    __tablename__ = "meeting_info"
    id = Column(String(255), primary_key=True, comment="会议ID")
    user_ids = Column(JSON, comment="可观测列表，JSON 格式")
    organizer = Column(String(255), nullable=False, comment="会议发起人")
    participants = Column(JSON, comment="会议参与人，JSON 格式")
    speaker = Column(JSON, comment="发言人，JSON 格式")
    topic = Column(JSON, comment="会议主题，JSON 格式")
    meeting_time = Column(DateTime, comment="会议时间")

    def to_string(self):
        return f"会议ID: {self.id}\n会议发起人: {self.organizer}\n会议参与人: {self.participants}\n发言人: {self.speaker}\n会议主题: {self.topic}\n会议时间: {self.meeting_time}"


def insert_meeting_info(meeting_info: MeetingInfo):
    with Session(engine) as session:
        session.merge(meeting_info)
        session.commit()


def get_meeting_infos():
    with Session(engine) as session:
        stmt = select(MeetingInfo)
        return list(session.scalars(stmt))


async def aget_meeting_infos():
    async with async_session() as session:
        stmt = select(MeetingInfo)
        result = await session.scalars(stmt)
        return list(result)


def get_meeting_info_by_id(meeting_id: str):
    with Session(engine) as session:
        stmt = select(MeetingInfo).where(MeetingInfo.id == meeting_id)
        return session.scalar(stmt)


async def aget_meeting_info_by_id(meeting_id: str) -> MeetingInfo:
    async with async_session() as session:
        stmt = select(MeetingInfo).where(MeetingInfo.id == meeting_id)
        result = await session.scalar(stmt)
        return result


async def aget_meeting_info_by_ids(meeting_ids: List[str]) -> List[MeetingInfo]:
    async with async_session() as session:
        stmt = select(MeetingInfo).where(MeetingInfo.id.in_(meeting_ids))
        result = await session.execute(stmt)
        meeting_list: List[MeetingInfo] = result.scalars().all()
        return meeting_list


class CMCCDocDataSource(DocumentDataSource):
    def get_and_update_first_document_status(
        self, status: Sequence
    ) -> List[CommonDocumentModel]:
        with Session(engine) as session:
            stmt = (
                select(CMCCFileModel)
                .where(CMCCFileModel.upload_status.in_(status))
                .limit(10)
                .with_for_update()
            )

            cmcc_files = list(session.scalars(stmt))
            if len(cmcc_files) == 0:
                return []
            # 随机选一个
            cmcc_file = random.choice(cmcc_files)
            if cmcc_file:
                cmcc_file.upload_status = UploadStatus.INDEX_BUILDING
                session.commit()

            return (
                [
                    CommonDocumentModel(
                        id=cmcc_file.file_id,
                        file_type=cmcc_file.file_type,
                        file_name=cmcc_file.file_name,
                        platform=cmcc_file.platform,
                        upload_type=cmcc_file.upload_type,
                        source_url=cmcc_file.file_id,
                        file_status=FileStatus.Ready,
                        app_id=cmcc_file.app_id,
                        x_location=cmcc_file.x_location,
                        meta_info={
                            LLAMA_INDEX_MEETING_ID: cmcc_file.meeting_id,
                            LLAMA_INDEX_META_USER_ID: cmcc_file.user_id,
                            LLAMA_INDEX_FILE_ID: cmcc_file.file_id,
                            LLAMA_UPLOAD_TYPE: cmcc_file.upload_type,
                        },
                    )
                ]
                if cmcc_file
                else []
            )

    def UpdateDocumentStatus(self, document_id: Union[int, str], **values):
        values.pop("file_status")
        # 实现更新数据库中文档状态的逻辑
        with Session(engine) as session:
            stmt = (
                update(CMCCFileModel)
                .where(CMCCFileModel.file_id == document_id)
                .values(**values)
            )
            session.execute(stmt)
            session.commit()

    def get_document_by_id(self, id: Union[int, str]) -> CommonDocumentModel | None:
        with Session(engine) as session:
            stmt = select(CMCCFileModel).where(CMCCFileModel.file_id == id)
            cmcc_file: CMCCFileModel = session.scalar(stmt)
            if cmcc_file is None:
                return None
            return CommonDocumentModel(
                id=cmcc_file.file_id,
                file_type=cmcc_file.file_type,
                file_name=cmcc_file.file_name,
                platform=cmcc_file.platform,
                upload_type=cmcc_file.upload_type,
                source_url=cmcc_file.file_id,
                upload_status=cmcc_file.upload_status,
                app_id=cmcc_file.app_id,
                x_location=cmcc_file.x_location,
                meta_info={
                    LLAMA_INDEX_MEETING_ID: cmcc_file.meeting_id,
                    LLAMA_INDEX_META_USER_ID: cmcc_file.user_id,
                    LLAMA_INDEX_FILE_ID: cmcc_file.file_id,
                    LLAMA_UPLOAD_TYPE: cmcc_file.upload_type,
                },
            )

    @retry_async(exceptions=(InternalError,))
    async def aget_document_by_id(
        self, id: Union[int, str]
    ) -> CommonDocumentModel | None:
        async with async_session() as session:
            stmt = select(CMCCFileModel).where(CMCCFileModel.file_id == id)
            cmcc_file = await session.scalar(stmt)
            if cmcc_file is None:
                return None
            return CommonDocumentModel(
                id=cmcc_file.file_id,
                file_type=cmcc_file.file_type,
                file_name=cmcc_file.file_name,
                platform=cmcc_file.platform,
                upload_type=cmcc_file.upload_type,
                source_url=cmcc_file.file_id,
                upload_status=cmcc_file.upload_status,
                app_id=cmcc_file.app_id,
                x_location=cmcc_file.x_location,
                meta_info={
                    LLAMA_INDEX_MEETING_ID: cmcc_file.meeting_id,
                    LLAMA_INDEX_META_USER_ID: cmcc_file.user_id,
                    LLAMA_INDEX_FILE_ID: cmcc_file.file_id,
                    LLAMA_UPLOAD_TYPE: cmcc_file.upload_type,
                },
            )

    @retry_async(exceptions=(InternalError,))
    async def aget_document_by_ids(
        self, ids: List[Union[int, str]]
    ) -> List[CommonDocumentModel]:
        async with async_session() as session:
            # 如果 ids 是单个整数或字符串，转换为列表
            if not isinstance(ids, list):
                ids = [ids]
            stmt = select(CMCCFileModel).where(CMCCFileModel.file_id.in_(ids))
            result = await session.execute(stmt)
            cmcc_file_list = result.scalars().all()
            return [
                CommonDocumentModel(
                    id=cmcc_file.file_id,
                    file_type=cmcc_file.file_type,
                    file_name=cmcc_file.file_name,
                    platform=cmcc_file.platform,
                    upload_type=cmcc_file.upload_type,
                    source_url=cmcc_file.file_id,
                    upload_status=cmcc_file.upload_status,
                    app_id=cmcc_file.app_id,
                    x_location=cmcc_file.x_location,
                    meta_info={
                        LLAMA_INDEX_MEETING_ID: cmcc_file.meeting_id,
                        LLAMA_INDEX_META_USER_ID: cmcc_file.user_id,
                        LLAMA_INDEX_FILE_ID: cmcc_file.file_id,
                        LLAMA_UPLOAD_TYPE: cmcc_file.upload_type,
                    },
                )
                for cmcc_file in cmcc_file_list
            ]

    def get_document_by_ids(
        self, ids: List[Union[int, str]]
    ) -> List[CommonDocumentModel]:
        with Session(engine) as session:
            stmt = select(CMCCFileModel).where(CMCCFileModel.file_id.in_(ids))
            cmcc_file_list = list(session.scalars(stmt))
            return [
                CommonDocumentModel(
                    id=cmcc_file.file_id,
                    file_type=cmcc_file.file_type,
                    file_name=cmcc_file.file_name,
                    platform=cmcc_file.platform,
                    upload_type=cmcc_file.upload_type,
                    source_url=cmcc_file.file_id,
                    upload_status=cmcc_file.upload_status,
                    app_id=cmcc_file.app_id,
                    x_location=cmcc_file.x_location,
                    meta_info={
                        LLAMA_INDEX_MEETING_ID: cmcc_file.meeting_id,
                        LLAMA_INDEX_META_USER_ID: cmcc_file.user_id,
                        LLAMA_INDEX_FILE_ID: cmcc_file.file_id,
                        LLAMA_UPLOAD_TYPE: cmcc_file.upload_type,
                    },
                )
                for cmcc_file in cmcc_file_list
            ]

    def update_document_summary(
        self, document_model: CommonDocumentModel, summary: str
    ):
        with Session(engine) as session:
            stmt = (
                update(CMCCFileModel)
                .where(CMCCFileModel.file_id == document_model.id)
                .values(summary=summary)
            )
            session.execute(stmt)
            session.commit()

    def get_doc_ids_with_summary_null(self) -> List[CommonDocumentModel]:
        with Session(engine) as session:
            # summary is null or summary = ''
            stmt = select(CMCCFileModel).where(
                or_(CMCCFileModel.summary.is_(None), CMCCFileModel.summary == "")
            )
            cmcc_file_list = list(session.scalars(stmt))
            return [
                CommonDocumentModel(
                    id=cmcc_file.file_id,
                    file_type=cmcc_file.file_type,
                    file_name=cmcc_file.file_name,
                    platform=cmcc_file.platform,
                    upload_type=cmcc_file.upload_type,
                    source_url=cmcc_file.file_id,
                    upload_status=cmcc_file.upload_status,
                    app_id=cmcc_file.app_id,
                    x_location=cmcc_file.x_location,
                    meta_info={
                        LLAMA_INDEX_MEETING_ID: cmcc_file.meeting_id,
                        LLAMA_INDEX_META_USER_ID: cmcc_file.user_id,
                        LLAMA_INDEX_FILE_ID: cmcc_file.file_id,
                        LLAMA_UPLOAD_TYPE: cmcc_file.upload_type,
                    },
                )
                for cmcc_file in cmcc_file_list
            ]

    async def aupdate_chat_record(
        self,
        request_id: str,
        new_query: str,
        content: str,
        file_metas: list[dict],
        has_exception: bool = False,
    ):
        async with async_session() as session:
            stmt = (
                update(CMCCChatRecordModel)
                .where(CMCCChatRecordModel.request_id == request_id)
                .values(
                    new_query=new_query,
                    content=content,
                    file_id="",
                )
            )
            await session.execute(stmt)
            await session.commit()


tables = Base.metadata.tables
for k, v in tables.items():
    # <class 'str'> < class 'sqlalchemy.sql.schema.Table' >
    print(__file__, " : ", k, type(v))
Base.metadata.create_all(engine, list(tables.values()))
