from pathlib import Path

from dotenv import load_dotenv

# specify path to easily change env in docker
env_file = Path(__file__).parent / ".env"
print(f"load dotenv {env_file}")
load_dotenv(dotenv_path=env_file, override=True)
# both app_config and doc_config needs load_dotenv

from contextlib import asynccontextmanager

from backend_stage_reporter.reporter import (
    backend_stage_reporter_monitor_start,
    reporter_process,
)
from common.http.fastapi_hook import (
    TracerMiddleware,
    ProfilerMiddleware,
    AccessLogMiddleware,
)
from common.logging.logger import get_logger
from config import app_config
from refesher.prompt_refresher import start_prompt_studio_updater
from common.trace import tracer
from fastapi import FastAPI, Request
from biz.cmcc_meeting_biz.register import register_resources

register_resources()


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup logic
    if app_config.ENABLE_LANGFUSE:
        backend_stage_reporter_monitor_start()
    yield  # This will run the application
    if reporter_process:
        reporter_process.terminate()
    log.info("shutdown")


app = FastAPI(lifespan=lifespan)

from fastapi.responses import JSONResponse

start_prompt_studio_updater()

tracer.init_trace_provider(app_config.APP_NAME)

# 注册中间件
app.add_middleware(TracerMiddleware)
app.add_middleware(ProfilerMiddleware)
app.add_middleware(AccessLogMiddleware)
log = get_logger(__name__)


@app.exception_handler(Exception)
async def http_exception_handler(request: Request, exc: Exception):
    log.exception("Request failed: %s", exc)
    return JSONResponse(status_code=200, content={"error": str(exc)})


from biz.cmcc_meeting_biz.service import cmcc_meeting_service

app.include_router(cmcc_meeting_service.router)

if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=9099, workers=1)  # for debug
