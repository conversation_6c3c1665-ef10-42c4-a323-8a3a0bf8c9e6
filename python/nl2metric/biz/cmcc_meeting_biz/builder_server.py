from pathlib import Path

from dotenv import load_dotenv

# specify path to easily change env in docker
env_file = Path(__file__).parent / ".env"
print(f"env_file: {env_file}")
load_dotenv(dotenv_path=env_file, override=True)
# both app_config and doc_config needs load_doter

from nl2document.builder.task_manager import TaskManager
from biz.cmcc_meeting_biz.builder.cmcc_index_builder import CMCCIndexBuilder

from biz.cmcc_meeting_biz.register import register_resources

register_resources()


if __name__ == "__main__":
    TaskManager(index_builder=CMCCIndexBuilder()).run()
