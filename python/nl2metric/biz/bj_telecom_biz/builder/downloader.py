from urllib.parse import quote

import requests

from common.logging.logger import get_logger
from config.doc_config import (
    DeployMode,
    BJ_TELECOM_INTERNAL_MOCK,
    BJ_TELECOM_FILE_DOWNLOAD_PREFIX,
)
from nl2document.builder.downloader.base_downloader import BaseDownloader
from nl2document.builder.downloader.registry import register_downloader
import os

from nl2document.common.models.model import CommonDocumentModel
from biz.bj_telecom_biz.bj_common.data_model import BJ_TELECOM_EXTRA_INFO_VALUE

logger = get_logger(__name__)


def save_html_file(document: CommonDocumentModel, save_path: str):
    local_source_path = os.path.join(
        save_path,
        document.file_name + "_" + document.product_extra_info_key + ".html",
    )
    if not os.path.exists(os.path.dirname(local_source_path)):
        os.makedirs(os.path.dirname(local_source_path), exist_ok=True)
    with open(local_source_path, "w") as f:
        f.write(document.meta_info.get(BJ_TELECOM_EXTRA_INFO_VALUE, ""))
    return local_source_path


class BJTelecomDownloader(BaseDownloader):
    def download(
        self, document: CommonDocumentModel, document_url: str, save_path: str
    ) -> str:
        if document.product_extra_info_key and document.product_extra_info_key != "":
            if document.file_type == "html":
                return save_html_file(document, save_path)
            else:
                return ""
        dir_path = os.path.join(
            os.path.dirname(save_path), os.path.basename(document_url)
        )
        os.makedirs(dir_path, exist_ok=True)
        final_path = os.path.join(dir_path, document.file_name)
        self.bj_telecom_download_file(document, final_path)
        return final_path

    def bj_telecom_download_file(self, document: CommonDocumentModel, save_path):
        if BJ_TELECOM_INTERNAL_MOCK:
            encoded_file_name = quote(document.file_name)
            download_url = f"{BJ_TELECOM_FILE_DOWNLOAD_PREFIX}?FileID={document.id}&FileName={encoded_file_name}"
        else:
            # 确保URL正确拼接
            base_url = BJ_TELECOM_FILE_DOWNLOAD_PREFIX.rstrip("/")
            source_url = document.source_url.lstrip("/")
            download_url = f"{base_url}/{source_url}"

        logger.info(f"start Download url: {download_url}")
        # 临时使用
        headers = {"satokentemp": "1#admin"}
        response = requests.get(download_url, stream=True, headers=headers)

        if response.status_code == 200:
            with open(save_path, "wb") as file:
                for chunk in response.iter_content(chunk_size=8192):
                    file.write(chunk)
            logger.info(f"File downloaded successfully: {save_path}")
        else:
            logger.error(f"Download failed:, {response.status_code}")
            raise Exception("download file failed")
