import random
from datetime import datetime
from typing import List, Union, Sequence
from uuid import uuid4

from sqlalchemy import (
    DateTime,
    Integer,
    UniqueConstraint,
    delete,
    func,
    select,
    update,
    JSO<PERSON>,
    <PERSON><PERSON>an,
    Enum,
)
from sqlalchemy import String
from sqlalchemy import desc, and_
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import Session
from sqlalchemy.orm import mapped_column

from biz.bj_telecom_biz.bj_common.data_model import (
    BJ_TELECOM_EXTRA_INFO_KEY,
    BJ_TELECOM_EXTRA_INFO_TYPE,
    BJ_TELECOM_EXTRA_INFO_VALUE,
    BJ_TELECOM_RESOURCE_ID,
    BJ_TELECOM_RESOURCE_NAME,
    BJ_TELECOM_RESOURCE_TYPE,
    BJTelecomDeleteProductExtraInfo,
    BJTelecomFileItem,
    BJTelecomAppendProductExtraInfo,
)
from config.doc_config import deployMode, DeployMode
from nl2document.common.models.base_model import DocumentDataSource
from nl2document.common.models.model import (
    CommonDocumentModel,
    engine,
    async_session,
    ChatRecordModel,
    Base,
)
from nl2document.common.msg.doc import FileStatus, get_file_status_name


class BJTelecomDocumentData(DocumentDataSource):
    def get_document_by_status(self, status: List[str]):
        with Session(engine) as session:
            stmt = select(BjTelecomFile).where(BjTelecomFile.upload_status.in_(status))
            file_list = list(session.scalars(stmt))
            return [
                CommonDocumentModel(
                    id=document.file_id,
                    file_name=document.file_name,
                    file_type=document.file_type,
                    source_url=document.file_id,
                    upload_status=document.upload_status,
                    meta_info=document.meta_info,
                    product_extra_info_key=document.product_extra_info_key,
                )
                for document in file_list
            ]

    def get_and_update_first_document_status(self, status: Sequence):
        with Session(engine) as session:
            stmt = (
                select(BjTelecomFile)
                .where(BjTelecomFile.upload_status.in_(status))
                .limit(10)
                .with_for_update()
            )
            docs = list(session.scalars(stmt))
            if len(docs) == 0:
                return []
            document = random.choice(docs)
            if document:
                document.upload_status = get_file_status_name(FileStatus.INDEX_BUILDING)
                session.commit()

            updated_meta_info = document.meta_info or {}  # 如果为空，初始化为一个空字典
            updated_meta_info.update(
                {
                    "file_id": document.file_id,
                    "file_name": document.file_name,
                }
            )
            return (
                [
                    CommonDocumentModel(
                        id=document.file_id,
                        file_name=document.file_name,
                        file_type=document.file_type,
                        source_url=updated_meta_info.get("url", document.file_id)
                        if updated_meta_info
                        else document.file_id,
                        file_status=FileStatus.Ready,
                        meta_info=updated_meta_info,
                        product_extra_info_key=document.product_extra_info_key,
                    )
                ]
                if document
                else []
            )

    def UpdateDocumentStatus(self, document_id: Union[int, str], **values):
        values.pop("file_status")
        # 实现更新数据库中文档状态的逻辑
        with Session(engine) as session:
            stmt = (
                update(BjTelecomFile)
                .where(BjTelecomFile.file_id == document_id)
                .values(**values)
            )
            session.execute(stmt)
            session.commit()

    def get_document_by_ids(
        self, ids: List[Union[int, str]]
    ) -> List[CommonDocumentModel]:
        with Session(engine) as session:
            stmt = select(BjTelecomFile).where(BjTelecomFile.file_id.in_(ids))
            file_list = list(session.scalars(stmt))
            return [
                CommonDocumentModel(
                    id=document.file_id,
                    file_name=document.file_name,
                    file_type=document.file_type,
                    source_url=document.meta_info.get("url", document.file_id)
                    if document.meta_info
                    else document.file_id,
                    upload_status=document.upload_status,
                    meta_info=document.meta_info,
                    product_extra_info_key=document.product_extra_info_key,
                )
                for document in file_list
            ]

    def get_document_by_id(self, id: Union[int, str]) -> CommonDocumentModel | None:
        with Session(engine) as session:
            stmt = select(BjTelecomFile).where(BjTelecomFile.file_id == id)
            document = session.scalar(stmt)
            updated_meta_info = document.meta_info or {}  # 如果为空，初始化为一个空字典
            updated_meta_info.update(
                {
                    "file_id": document.file_id,
                    "file_name": document.file_name,
                }
            )
            if document:
                return CommonDocumentModel(
                    id=document.file_id,
                    file_name=document.file_name,
                    file_type=document.file_type,
                    source_url=document.meta_info.get("url", document.file_id)
                    if document.meta_info
                    else document.file_id,
                    upload_status=document.upload_status,
                    meta_info=updated_meta_info,
                    product_extra_info_key=document.product_extra_info_key,
                )
            return None

    async def aget_document_by_ids(
        self, ids: List[Union[int, str]]
    ) -> List[CommonDocumentModel]:
        async with async_session() as session:
            stmt = select(BjTelecomFile).where(BjTelecomFile.file_id.in_(ids))
            result = await session.execute(stmt)
            file_list: List[BjTelecomFile] = result.scalars().all()
            return [
                CommonDocumentModel(
                    id=document.file_id,
                    file_name=document.file_name,
                    file_type=document.file_type,
                    source_url=document.meta_info.get("url", document.file_id)
                    if document.meta_info
                    else document.file_id,
                    upload_status=document.upload_status,
                    meta_info=document.meta_info,
                    product_extra_info_key=document.product_extra_info_key,
                )
                for document in file_list
            ]

    async def aget_document_by_id(
        self, id: Union[int, str]
    ) -> CommonDocumentModel | None:
        async with async_session() as session:
            stmt = select(BjTelecomFile).where(BjTelecomFile.file_id == id)
            document = await session.scalar(stmt)
            if document is None:
                return None
            if document:
                return CommonDocumentModel(
                    id=document.file_id,
                    file_name=document.file_name,
                    file_type=document.file_type,
                    source_url=document.meta_info.get("url", document.file_id)
                    if document.meta_info
                    else document.file_id,
                    upload_status=document.upload_status,
                    meta_info=document.meta_info,
                    product_extra_info_key=document.product_extra_info_key,
                )
            return None

    async def aget_latest_chat_record(self, session_id: str) -> ChatRecordModel:
        async with async_session() as session:
            stmt = (
                select(ChatRecordModel)
                .where(
                    and_(
                        ChatRecordModel.session_id == session_id,
                        ChatRecordModel.has_exception == False,
                    )
                )
                .order_by(desc(ChatRecordModel.created_at))
                .limit(1)
            )
            result = await session.execute(stmt)
            return result.scalar()

    async def aupdate_chat_record(
        self,
        request_id: str,
        new_query: str,
        content: str,
        file_metas: list[dict],
        has_exception: bool = False,
    ):
        async with async_session() as session:
            stmt = (
                update(ChatRecordModel)
                .where(ChatRecordModel.request_id == request_id)
                .values(
                    new_query=new_query,
                    content=content,
                    file_metas=file_metas,
                    has_exception=has_exception,
                )
            )
            await session.execute(stmt)
            await session.commit()

    async def acreate_chat_record(self, record: ChatRecordModel):
        async with async_session() as session:
            session.add(record)
            await session.commit()

    async def aupdate_chat_record_feedback(
        self, request_id: str, useful: bool, expectedAnswer
    ):
        async with async_session() as session:
            stmt = (
                update(ChatRecordModel)
                .where(ChatRecordModel.request_id == request_id)
                .values(useful=useful, expected_answer=expectedAnswer)
            )
            await session.execute(stmt)
            await session.commit()


class BjTelecomFile(Base):
    __tablename__ = "bj_telecom_files"

    file_id: Mapped[str] = mapped_column(String(64), primary_key=True)
    resource_id: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    resource_type: Mapped[str] = mapped_column(String(50), nullable=False, default="")
    file_name: Mapped[str] = mapped_column(String(255), nullable=False)
    file_type: Mapped[str] = mapped_column(String(64), nullable=False, comment="文件类型")
    upload_status: Mapped[str] = mapped_column(
        Enum(
            "UPLOAD_COMPLETED",
            "INDEX_BUILDING",
            "INDEX_BUILD_FAILED",
            "INDEX_BUILD_SUCCESS",
            "INDEX_BUILD_RETRY",
        ),
        nullable=False,
        default="UPLOAD_COMPLETED",
        comment="文件状态（上传完毕，索引构建中，索引构建失败，索引构建成功, 等待重新构建）",
    )
    removed: Mapped[bool] = mapped_column(
        Boolean, nullable=False, default=False, comment="是否已下架"
    )
    meta_info: Mapped[dict] = mapped_column(
        JSON, nullable=True, comment="文件的元信息，存储为JSON格式"
    )
    parse_result: Mapped[str] = mapped_column(String(128))
    parse_error_msg: Mapped[str] = mapped_column(String(2048))
    product_extra_info_key: Mapped[str] = mapped_column(
        String(128), nullable=True, comment="产品额外信息键"
    )

    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=func.now(), nullable=False, comment="记录创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="记录最后修改时间",
    )

    __table_args__ = (
        UniqueConstraint(
            "resource_id", "resource_type", "file_name", name="uq_resource_file"
        ),
        UniqueConstraint(
            "resource_id",
            "resource_type",
            "product_extra_info_key",
            name="unique_resource_key",
        ),
    )


async def a_insert_or_update_bj_telecom_files(doc: BJTelecomFileItem) -> str:
    """
    插入或更新文件信息
    返回待删除的file_id
    """
    resource_id = 0
    resource_type = ""
    file_id_to_delete = ""
    if doc.metaInfo:
        resource_id = doc.metaInfo.get("resourceId", 0)
        resource_type = doc.metaInfo.get("resourceType", "")
    with Session(engine) as session:
        # Check if a file with the same file_id already exists
        existing_file = (
            session.query(BjTelecomFile)
            .filter(BjTelecomFile.file_id == doc.fileId)
            .first()
        )

        if existing_file:
            # Update fields for the existing file entry by file_id
            existing_file.removed = False
            existing_file.meta_info = doc.metaInfo
            existing_file.resource_id = resource_id
            existing_file.resource_type = resource_type
            existing_file.updated_at = func.now()
            existing_file.upload_status = "UPLOAD_COMPLETED"
        else:
            # Check if a file with the same resourceId, resourceType, and fileName combination exists
            duplicate_file = (
                session.query(BjTelecomFile)
                .filter(
                    BjTelecomFile.resource_id == resource_id,
                    BjTelecomFile.resource_type == resource_type,
                    BjTelecomFile.file_name == doc.fileName,
                )
                .first()
            )

            if duplicate_file:
                file_id_to_delete = duplicate_file.file_id
                # Update if the combination already exists
                duplicate_file.file_id = doc.fileId
                duplicate_file.meta_info = doc.metaInfo
                duplicate_file.removed = False
                duplicate_file.updated_at = func.now()
                duplicate_file.upload_status = "UPLOAD_COMPLETED"
            else:
                # Create a new file record
                new_file = BjTelecomFile(
                    file_id=doc.fileId,
                    resource_id=resource_id,
                    resource_type=resource_type,
                    file_name=doc.fileName,
                    file_type=doc.fileName.split(".")[-1],
                    removed=False,
                    meta_info=doc.metaInfo,
                )
                session.add(new_file)

        session.commit()
        return file_id_to_delete


async def a_removed_bj_telecom_files(file_ids: List[str]):
    with Session(engine) as session:
        session.query(BjTelecomFile).filter(BjTelecomFile.file_id.in_(file_ids)).update(
            {BjTelecomFile.removed: True}
        )
        session.commit()


async def a_delete_product_extra_info(
    extra_info: BJTelecomDeleteProductExtraInfo,
) -> str:
    async with async_session() as session:
        # 先查询获取file_id
        query = select(BjTelecomFile.file_id).filter(
            BjTelecomFile.product_extra_info_key == extra_info.extra_info_key,
            BjTelecomFile.resource_id == extra_info.resource_id,
            BjTelecomFile.resource_type == extra_info.resource_type,
        )
        result = await session.execute(query)
        file_id = result.scalar()

        # 删除记录
        await session.execute(
            delete(BjTelecomFile).filter(
                BjTelecomFile.product_extra_info_key == extra_info.extra_info_key,
                BjTelecomFile.resource_id == extra_info.resource_id,
                BjTelecomFile.resource_type == extra_info.resource_type,
            )
        )
        await session.commit()

        return file_id


async def a_append_bj_telecom_product_extra_infos(
    extra_info: BJTelecomAppendProductExtraInfo,
):
    async with async_session() as session:
        for info in extra_info.extra_infos:
            info_key = info.key
            meta_info = {
                BJ_TELECOM_RESOURCE_ID: extra_info.resource_id,
                BJ_TELECOM_RESOURCE_TYPE: extra_info.resource_type,
                BJ_TELECOM_RESOURCE_NAME: extra_info.resource_name,
                BJ_TELECOM_EXTRA_INFO_KEY: info.key,
                BJ_TELECOM_EXTRA_INFO_VALUE: info.value,
                BJ_TELECOM_EXTRA_INFO_TYPE: info.type,
            }

            try:
                # 尝试创建新记录
                new_info = BjTelecomFile(
                    file_id=str(uuid4().hex),
                    resource_id=extra_info.resource_id,
                    resource_type=extra_info.resource_type,
                    product_extra_info_key=info_key,
                    file_name=extra_info.resource_name,
                    file_type=info.type,
                    upload_status="UPLOAD_COMPLETED",
                    meta_info=meta_info,
                )
                session.add(new_info)
                await session.flush()
            except Exception as e:
                # 如果发生唯一约束冲突,则更新已存在的记录
                await session.rollback()

                existing_info = await session.execute(
                    select(BjTelecomFile)
                    .filter(BjTelecomFile.resource_id == extra_info.resource_id)
                    .filter(BjTelecomFile.resource_type == extra_info.resource_type)
                    .filter(BjTelecomFile.product_extra_info_key == info_key)
                )
                existing_info = existing_info.scalar_one()

                existing_info.meta_info = meta_info
                existing_info.updated_at = func.now()
                existing_info.upload_status = "UPLOAD_COMPLETED"

        await session.commit()


tables = Base.metadata.tables
for k, v in tables.items():
    # <class 'str'> < class 'sqlalchemy.sql.schema.Table' >
    print(__file__, " : ", k, type(v))
Base.metadata.create_all(engine, list(tables.values()))
