import pandas as pd
from typing import List
import json

from biz.yishuitong.report_generator import Section


def mock_report():
    section_4_1 = {
        "title": "增值税税负变动率检查",
        "data": [
            {"所属期": "2022", "税负率": "0.86%", "税负变动率": "--"},
            {"所属期": "2023", "税负率": "1.62%", "税负变动率": "88.37%"},
            {"所属期": "2021", "税负率": "--", "税负变动率": "--"},
        ],
        "metric_description": "税负率=(本期应纳税额合计)÷(本期增值税应税销售额+免、抵、退办法出口货物销售额+免税销售额)×100%；税负变动率=(本期税负-上期税负)÷上期税负*100%。纳税人自身税负变化过大，可能存在账外经营、已实现纳税义务而未结转收入、取得进项税额不符合规定、享受税收优惠政策期间购进货物不取得可抵扣进项税额发票或虚开发票等问题。",
        "risk_description": "贵企业2023 税负变化过大，可能存在账外经营、已实现纳税义务而未结转收入、取得进项税额不符合规定、享受税收优惠政策期间购进货物不取得可抵扣进项税额发票或虚开发票等问题检查纳税人的销售业务，从原始凭证到记帐凭证、销售、应收帐款、货币资金、存货等将本期与其他各时期进行比较分析，对异常变动情况进一步查明原因，以核实是否存在漏记、隐瞒或虚记收入的行为。检查企业固定资产抵扣是否合理、有未获取相应数据将外购的存货用于职工福利、个人消费、对外投资、捐赠等情况。检查重点：检查纳税人的销售业务，从原始凭证到记帐凭证、销售、应收帐款、货币资金、存货等将本期与其他各时期进行比较分析，对异常变动情况进一步查明原因，以核实是否存在漏记、隐瞒或虚记收入的行为。检查企业固定资产抵扣是否合理、有未获取相应数据将外购的存货用于职工福利、个人消费、对外投资、捐赠等情况",
    }
    section_4_2 = {
        "title": "企业所得税贡献率检查",
        "data": [
            {"所属期": "2021", "实际应纳所得税额（元）": 0.00, "营业收入（元）": 0.00, "企业所得税贡献率": "0.00%"},
            {
                "所属期": "2022",
                "实际应纳所得税额（元）": 2994.57,
                "营业收入（元）": 236963.68,
                "企业所得税贡献率": "1.26%",
            },
            {
                "所属期": "2023",
                "实际应纳所得税额（元）": 10162.59,
                "营业收入（元）": 810656.53,
                "企业所得税贡献率": "1.25%",
            },
        ],
        "metric_description": "企业所得税贡献率=（实际应纳所得税额/营业收入）×100%；企业所得税贡献率反映的是企业所得税缴纳税额与收入的比例关系，在对所得税进行纳税评估时最为关注的指标。",
        "risk_description": "贵企业 2021 企业税负严重偏低,确认是否存在多计成本费用，少计收入的行为。",
    }
    section_4_3 = {
        "title": "应税所得率检查",
        "data": [
            {"所属期": "2021", "纳税调整后所得（元）": 0.00, "营业收入（元）": 0.00, "应税所得率": "0.00%"},
            {
                "所属期": "2022",
                "纳税调整后所得（元）": 119783.00,
                "营业收入（元）": 236963.68,
                "应税所得率": "60.55%",
            },
            {
                "所属期": "2023",
                "纳税调整后所得（元）": 203251.83,
                "营业收入（元）": 810656.53,
                "应税所得率": "25.07%",
            },
        ],
        "metric_description": "应税所得率=纳税调整后所得/营业收入×100%；应税所得率就是应纳税所得额与销售收入的比率，行业不同，应税所得率也有所不同，总体区间大约为【5%--25%】。",
        "risk_description": "贵企业 2021 应税所得率异常，需关注纳税调整项目明细信息，极易引起税务稽查风险！",
    }
    section_4_4 = {
        "title": "增值税连续3个月零申报检测",
        "data": [{"税款所属期起": None, "税款所属期止": None, "期间连续零申报次数": None}],
        "metric_description": "增值税一般纳税人需关注“本期应补(退)税额”连续三个月是否等于零。",
        "risk_description": "该指标项未检测到风险",
    }
    section_4_5 = {
        "title": "小规模纳税人销售额超标检测",
        "data": [{"所属年度": "2024", "销售额（元）": 203940.59}],
        "metric_description": "小规模纳税人短期内销售超过500万或连续多月零申报，收入额快速超过500万",
        "risk_description": "该指标项未检测到风险",
    }
    section_4_6 = {
        "title": "期初留抵税额与上期期末留抵税额核对分析",
        "data": [
            {"税款所属期起": None, "税款所属期止": None, "期初留抵税额（元）": None, "期末留抵税额（元）": None}
        ],
        "metric_description": "增值税一般纳税人需关注近12个月申报表中是否存在期初留抵税额与上期期末留抵税额不相等的情况。",
        "risk_description": "该指标项未检测到风险",
    }
    section_list = [
        section_4_1,
        section_4_2,
        section_4_3,
        section_4_4,
        section_4_5,
        section_4_6,
    ]

    report = Section(
        level=0,
        order=0,
        title="report",
        subsections=[],
        data=[],
        metric_description="",
        risk_description="",
    )
    chapter4 = Section(
        level=4,
        order=0,
        title="税负风险检查",
        subsections=[],
        data=[],
        metric_description="",
        risk_description="",
    )
    for order, section in enumerate(section_list):
        chapter4.add_section(
            Section(
                level=4,
                order=order + 1,
                title=section["title"],
                subsections=[],
                data=section["data"],
                metric_description=section["metric_description"],
                risk_description=section["risk_description"],
            )
        )
    report.add_section(chapter4)
    return report


def load_test_data():
    with open(
        "/Users/<USER>/ask-bi/python/nl2metric/biz/yishuitong/test/fapiao"
    ) as file:
        fapiao_data = json.load(file)

    with open(
        "/Users/<USER>/ask-bi/python/nl2metric/biz/yishuitong/test/tax"
    ) as file:
        tax_data = json.load(file)

    return fapiao_data, tax_data
