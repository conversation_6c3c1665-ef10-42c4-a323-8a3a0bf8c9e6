import asyncio
import json

from biz.yishuitong.model import CreateTaxReportRequest
from biz.yishuitong.report_generator import generate_report, generate_html_report
from biz.yishuitong.service import fetch_data

req = CreateTaxReportRequest(
    orderId="123",
    taxpayerId="91350583MADDX89793",
    taxNo="TAX_NO_20250514101506396ZRl",
    email="<EMAIL>",
)

if __name__ == "__main__":
    # fapiao_data, tax_data = asyncio.run(fetch_data(req))
    # with open("./fapiao_data.json", "w") as f:
    #     f.write(json.dumps(fapiao_data, indent=2, ensure_ascii=False))
    # with open("./tax_data.json", "w") as f:
    #     f.write(json.dumps(tax_data, indent=2, ensure_ascii=False))
    with open("./fapiao_data.json", "r") as f:
        fapiao_data = json.load(f)
    with open("./tax_data.json", "r") as f:
        tax_data = json.load(f)
    temp_dir = "./"
    sections, summary_text, total_risk_num = asyncio.run(
        generate_report(req, fapiao_data, tax_data, temp_dir)
    )
    results = asyncio.run(generate_html_report(sections, summary_text, total_risk_num))
    print(results)
