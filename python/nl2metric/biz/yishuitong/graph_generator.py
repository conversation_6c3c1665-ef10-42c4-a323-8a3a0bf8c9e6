import os
import uuid
from typing import List

import matplotlib.patches as mpatches
from matplotlib.offsetbox import OffsetImage, AnnotationBbox
import platform
import matplotlib.pyplot as plt
from matplotlib.gridspec import GridSpec
from pathlib import Path

system_name = platform.system()

if system_name == "Windows":
    plt.rcParams["font.family"] = ["SimHei"]
elif system_name == "Linux":
    plt.rcParams["font.family"] = ["WenQuanYi Zen Hei"]
elif system_name == "Darwin":
    plt.rcParams["font.family"] = ["Arial Unicode MS"]
else:
    print("未知操作系统，无法准确配置字体，可能出现中文显示问题")
    plt.rcParams["font.family"] = ["sans-serif"]
plt.rcParams["axes.unicode_minus"] = False


def radder_graph_generator(path: str, data: List):
    import numpy as np

    # 数据
    labels = [
        "发票风险检查",
        "税负风险检查",
        "申报真实性风险检查",
        "隐匿收入风险检查",
        "虚增成本风险检查",
        "盈利风险分析",
        "运营风险分析",
        "偿债风险分析",
    ]
    num_vars = len(labels)

    # values = [0.8, 0.16, 0.75, 0.625, 0.75, 1, 0.5, 0.75]  # 示例数据
    values = [
        round(risk_num / total_num, 2)
        for (risk_num, total_num) in data
        if total_num != 0
    ]

    # 计算角度
    angles = np.linspace(0, 2 * np.pi, num_vars, endpoint=False).tolist()

    # 使雷达图闭合
    values += values[:1]
    angles += angles[:1]

    # 创建极坐标画布
    fig, ax = plt.subplots(figsize=(5, 5), subplot_kw={"polar": True}, dpi=100)
    ax.fill(angles, values, color="blue", alpha=0.25)  # 填充数据区域

    # 关键修改1：彻底关闭所有默认网格
    ax.grid(False)  # 关闭全部网格（包括径向和角度）
    ax.set_frame_on(False)  # 移除坐标系外框

    # 关键修改2：计算动态最大半径
    max_r = max(values[:-1]) * 1.1  # 留出10%空白
    ax.set_ylim(0, max_r)

    # 绘制八边形边框
    polygon_vertices = [(angle, max_r) for angle in angles[:-1]]
    polygon = mpatches.Polygon(
        polygon_vertices,
        closed=True,
        transform=ax.transData,  # 确保坐标系转换正确
        edgecolor="black",
        fill=False,
        linewidth=1,
    )
    ax.add_patch(polygon)

    # # 绘制径向线（从中心到顶点）
    # for angle in angles[:-1]:
    #     ax.plot([angle, angle], [0, max_r], color='gray', linestyle='--', linewidth=0.5)

    # 绘制内部八角形辅助线（浅灰色虚线）
    # for i in range(num_vars):
    #     start_angle = angles[i]
    #     end_angle = angles[(i+1) % num_vars]
    #     ax.plot(
    #         [start_angle, end_angle],
    #         [max_r, max_r],
    #         color='lightgray',
    #         linestyle='--',
    #         linewidth=0.8
    #     )
    # 关键修改4：绘制内部八角形辅助线（正确方式）
    inner_radii = [max_r * 0.25, max_r * 0.5, max_r * 0.75]  # 定义三个辅助层
    for r in inner_radii:
        inner_vertices = [(angle, r) for angle in angles[:-1]]
        inner_polygon = mpatches.Polygon(
            inner_vertices,
            closed=True,
            transform=ax.transData,
            edgecolor="lightgray",
            linestyle="--",
            fill=False,
            linewidth=0.8,
        )
        ax.add_patch(inner_polygon)

    # 关键修改5：添加径向辅助线（从中心到顶点）
    for angle in angles[:-1]:
        ax.plot(
            [angle, angle], [0, max_r], color="lightgray", linestyle=":", linewidth=0.5
        )
    # 添加标签
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(labels)
    ax.set_yticklabels([])  # 隐藏径向刻度

    # 添加标签
    ax.set_yticklabels([])
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(labels)

    plt.tight_layout()
    # plt.show()
    temp_filename = str(uuid.uuid4()) + ".png"
    file_path = os.path.join(path, temp_filename)
    plt.savefig(file_path, dpi=300)
    plt.clf()
    plt.close()
    return file_path


def summary_graph_generator(path: str):
    import matplotlib.image as mpimg

    # 图标路径
    icon_paths = {
        "invoice": "./icon/发票风险检查.png",
        "tax": "./icon/税负风险检查.png",
        "declaration": "./icon/申报真实性风险检查.png",
        "income": "./icon/隐匿收入风险检查.png",
        "cost": "./icon/虚增成本风险检查.png",
        "profit": "./icon/盈利风险分析.png",
        "operation": "./icon/运营风险分析.png",
        "debt": "./icon/偿债风险分析.png",
    }

    # 数据
    data = {
        "发票风险检查": {"value": 18, "icon": "invoice"},
        "税负风险检查": {"value": 6, "icon": "tax"},
        "申报真实性风险检查": {"value": 4, "icon": "declaration"},
        "隐匿收入风险检查": {"value": 8, "icon": "income"},
        "虚增成本风险检查": {"value": 12, "icon": "cost"},
        "盈利风险分析": {"value": 3, "icon": "profit"},
        "运营风险分析": {"value": 4, "icon": "operation"},
        "偿债风险分析": {"value": 8, "icon": "debt"},
    }

    # 创建图形和GridSpec
    fig = plt.figure(figsize=(10, 5))
    gs = GridSpec(2, 4, figure=fig)

    for i, (label, info) in enumerate(data.items()):
        row = i // 4
        col = i % 4
        ax = fig.add_subplot(gs[row, col])
        ax.axis("off")
        ax.set_facecolor("white")

        # 加载图标并缩放
        icon = mpimg.imread(f"{Path(__file__).parent}/{icon_paths[info['icon']]}")
        zoom = 0.5  # 调整缩放比例

        imagebox = OffsetImage(icon, zoom=zoom)
        ab = AnnotationBbox(imagebox, (0.1, 0.5), frameon=False, box_alignment=(0, 0.5))
        ax.add_artist(ab)
        # 添加文本（右侧居中）
        ax.text(0.8, 0.5, f'{info["value"]}项\n{label}', fontsize=15, va="center")

    # 显示图形
    plt.tight_layout()
    # plt.show()
    temp_filename = str(uuid.uuid4()) + ".png"
    file_path = os.path.join(path, temp_filename)
    plt.savefig(file_path, dpi=300)
    plt.clf()
    plt.close()
    return file_path


if __name__ == "__main__":
    radder_graph_generator("1")
