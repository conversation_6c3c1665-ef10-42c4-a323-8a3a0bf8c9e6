from collections import defaultdict
import random
from datetime import datetime
from dateutil.relativedelta import relativedelta


class ReportChapter4:
    # 4.1
    def calculate_tax_burden_modulus(self, dt, data, threshold=0.2):
        """
        4.1 增值税税负变动率检查
        增值税税负变动率检查，增加过去3年的过滤条件

        参数:
            current_date (str): 当前日期，格式为 'YYYYMM'
            data (dict): 包含企业财税数据的字典

        返回:
            list: 包含每年税负率和税负变动率的列表，格式如下：
                [
                    {"所属期": "2021", "税负率": "1.62%", "税负变动率": "88.37"},
                    ...
                ]
            str: 风险描述，格式为“风险描述: 近三年内，{年份}税负率为 {税负率}%，较上期变动 {税负变动率}%，大幅波动可能存在申报错误或经营异常等问题。”
        """

        # 税号确认
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            default_result = ["所属期", "税负率", "税负变动率"]
            default_risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, default_risk_desc

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")

        value_added = data["data"]["valueAdded"]  # valueAdded 表示增值税
        # annual_data = defaultdict(lambda: {'tax_amount': 0.00, 'sell_amount': 0.00})
        detail_data = defaultdict(lambda: [])

        # 小规模->一般纳税人的时间节点...
        small_end_date = max(
            v["endDate"]
            for v in value_added
            if v.get("levyProjectName") == "《增值税及附加税费申报表（小规模纳税人适用）》"
        )
        small_end = datetime.strptime(small_end_date, "%Y-%m-%d %H:%M:%S").strftime(
            "%Y%m%d"
        )

        for value in value_added:
            if value["invalidMark"] == "Y":
                continue
            dt_start = datetime.strptime(
                value["beginDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y%m%d")
            dt_end = datetime.strptime(value["endDate"], "%Y-%m-%d %H:%M:%S").strftime(
                "%Y%m%d"
            )
            dt_end_year = datetime.strptime(
                value["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue

            if value["levyProjectName"] == "《增值税及附加税费申报表（一般纳税人适用）》":
                if dt_end != dt_end_year + "1231":
                    continue
                # 即征即退项目-本年累计
                immed_amount = (
                    float(value.get("immediateRetreatYearAccumulativeAmount", 0.0))
                    if value.get("immediateRetreatYearAccumulativeAmount", 0.0) != None
                    else 0.0
                )
                # 一般项目-本年累计
                general_amount = (
                    float(value.get("generalYearAccumulativeAmount", 0.0))
                    if value.get("generalYearAccumulativeAmount", 0.0) != None
                    else 0.0
                )

                if value["projectName"] in [
                    "（一）按适用税率计税销售额",
                    "（二）按简易办法计税销售额",
                    "（三）免、抵、退办法出口销售额",
                    "（四）免税销售额",
                ] or value["projectName"] in ["应纳税额合计"]:
                    detail_data[(dt_start, dt_end)].append(
                        {
                            "source_type": "general",
                            "amount": immed_amount + general_amount,
                            "project_name": value["projectName"],
                            "change_type": value["changeType"],
                        }
                    )
                else:
                    continue

            elif value["levyProjectName"] == "《增值税及附加税费申报表（小规模纳税人适用）》":
                if dt_end != small_end:
                    continue
                # 本年累计-货物及劳务
                immed_amount = (
                    float(value.get("currentYearAccumulativeGoods", 0.0))
                    if value.get("currentYearAccumulativeGoods", 0.0) != None
                    else 0.0
                )
                # 本年累计-服务不动产和无形资产
                general_amount = (
                    float(value.get("currentYearAccumulativeService", 0.0))
                    if value.get("currentYearAccumulativeService", 0.0) != None
                    else 0.0
                )

                if value["projectName"] in [
                    "（一）应征增值税不含税销售额（3%征收率）",
                    "（二）应征增值税不含税销售额（5%征收率）",
                    "（三）销售使用过的固定资产不含税销售额",
                    "（四）免税销售额",
                    "（五）出口免税销售额",
                ] or value["projectName"] in ["应纳税额合计"]:
                    detail_data[(dt_start, dt_end)].append(
                        {
                            "source_type": "small",
                            "amount": immed_amount + general_amount,
                            "project_name": value["projectName"],
                            "change_type": value["changeType"],
                        }
                    )

                else:
                    continue

        def filter_detail_data(detail_data):
            # 存储 (dt_start, dt_end, source_type, project_name) -> 最佳记录
            best_records = {}

            # 遍历所有日期范围和记录
            for date_range, records in detail_data.items():
                dt_start, dt_end = date_range
                for record in records:
                    key = (
                        dt_start,
                        dt_end,
                        record["source_type"],
                        record["project_name"],
                    )
                    current_change_type = record["change_type"]

                    # 如果当前记录是 "更正后全量模式"，直接覆盖
                    if current_change_type == "更正后新产生的申报表（全量模式）":
                        best_records[key] = record
                    # 否则，仅当 key 不存在时才保留
                    elif key not in best_records:
                        best_records[key] = record

            # 重新构建 detail_data（按原时间分组）
            new_detail_data = {}
            for key, record in best_records.items():
                dt_start, dt_end, _, _ = key
                date_range = (dt_start, dt_end)
                if date_range not in new_detail_data:
                    new_detail_data[date_range] = []
                new_detail_data[date_range].append(record)

            return new_detail_data

        new_detail_data = filter_detail_data(detail_data)
        annual_data = defaultdict(lambda: {"tax_amount": 0.00, "sell_amount": 0.00})
        for (dt_start, dt_end), value in new_detail_data.items():
            for item in value:
                if item["project_name"] == "应纳税额合计":
                    annual_data[dt_end[:4]]["tax_amount"] += item["amount"]
                else:
                    annual_data[dt_end[:4]]["sell_amount"] += item["amount"]
                # print(dt_start, dt_end, item['project_name'], item['amount'])

        sorted_data_desc = dict(
            sorted(annual_data.items(), key=lambda item: item[0], reverse=False)
        )

        # # 计算税负率和税负变动率
        result = []
        risk_years = []
        previous_tax_ratio = None
        all_zero = True  # 标记是否所有税负率和税负变动率都为0

        for year in sorted_data_desc.keys():
            sell_amount = annual_data[year]["sell_amount"]
            tax_ratio = (
                annual_data[year]["tax_amount"] / sell_amount if sell_amount > 0 else 0
            )
            formatted_tax_ratio = (
                "{:,.2f}".format(tax_ratio * 100, 2) + "%"
            )  # 转换为百分比并保留两位小数

            # 计算税负变动率
            if previous_tax_ratio:
                tax_ratio_change = (
                    (tax_ratio - previous_tax_ratio) / previous_tax_ratio
                    if previous_tax_ratio != 0
                    else 0
                )
            else:
                tax_ratio_change = 0.00
            formatted_tax_ratio_change = (
                "{:,.2f}".format(tax_ratio_change * 100, 2) + "%"
            )

            # 检查是否有非零的税负率或税负变动率
            if tax_ratio != 0 or tax_ratio_change != 0:
                all_zero = False

            result.append(
                {
                    "所属期": year + "年",
                    "税负率": formatted_tax_ratio if tax_ratio is not None else "--",
                    "税负变动率": formatted_tax_ratio_change if tax_ratio_change else "--",
                }
            )
            # 风险识别：连续三年内税负变动率超过±20%
            if tax_ratio_change and abs(tax_ratio_change) > threshold:
                risk_years.append(
                    {
                        "年份": year + "年",
                        "税负率": formatted_tax_ratio,
                        "税负变动率": formatted_tax_ratio_change,
                    }
                )

            previous_tax_ratio = tax_ratio
        # 如果所有税负率和税负变动率都为0，则只输出标题
        if all_zero:
            result = ["所属期", "税负率", "税负变动率"]
        formatted_threshold = "{:,.2f}".format(threshold * 100, 2) + "%"
        # 生成风险描述
        if risk_years:
            risk_desc = "风险描述: 近三年内，"
            for risk_year in risk_years:
                risk_desc += f"{risk_year['年份']}税负率为{risk_year['税负率']}，较上期变动{risk_year['税负变动率']}，超过{formatted_threshold}"
            risk_desc += f"，大幅波动可能存在申报错误或经营异常等问题。"
        else:
            risk_desc = "风险描述：该指标项未检测到风险"
        return result, risk_desc

    # 4.2
    def calculate_income_tax_contribution_rate(self, dt, data, threshold=0.5):
        """
        4.2 企业所得税贡献率检查
        计算每年的企业所得税贡献率（企业所得税贡献率 = 应纳所得税额 / 营业收入 * 100）。
        参数:
            data (dict): 包含企业财税数据的字典
        返回:
            list: 包含每年营业收入、应纳所得税额和所得税贡献率的列表，格式如下：
                [
                    {"所属期": "2021", "实际应纳所得税额（元）": 2944.5, "营业收入（元）": 236963.68, "企业所得税贡献率": "1.26%"},
                    ...
                ]
            str: 风险描述，格式为“风险描述: 近三年内，{年份} 企业所得税贡献率为 {贡献率}%，大幅下降可能存在利润操纵或税收筹划不当等问题。”
        """
        # 提取corporateIncome数据
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            # 税号没对齐
            default_result = ["所属期", "实际应纳所得税额(元)", "营业收入(元)", "企业所得税贡献率"]
            risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        # annual_data = defaultdict(lambda: {'revenue_amount': 0.00, 'tax_amount': 0.00})
        revenue_data = defaultdict(lambda: [])
        tax_data = defaultdict(lambda: [])
        detail_data = defaultdict(lambda: [])
        # 遍历corporateIncome数据
        income_records = data["data"]["corporateIncome"]  # # corporateIncome 表示所得税
        for record in income_records:
            dt_end_year = datetime.strptime(
                record["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if record["invalidMark"] == "Y":
                continue
            # if dt_end != dt_year + '1231': continue
            # 检查是否为年度纳税申报表
            if record["levyProjectName"] == "《中华人民共和国企业所得税年度纳税申报表（A类）》（A100000）":
                amount = float(record["accumulativeAmount"] or 0)
                # 提取营业收入
                if record["projectName"] == "一、营业收入（填写A101010/101020/103000）":
                    detail_data[dt_end_year].append(
                        {
                            "amount": amount,
                            "project_name": "营业收入",
                            "change_type": record["changeType"],
                        }
                    )
                # 提取应纳所得税额
                if record["projectName"].__contains__("八、实际应纳所得税额"):
                    # annual_data[dt_end_year]['tax_amount'] = tax
                    detail_data[dt_end_year].append(
                        {
                            "amount": amount,
                            "project_name": "实际应纳所得税",
                            "change_type": record["changeType"],
                        }
                    )

        def filter_detail_data(detail_data):
            # 存储 (dt_start, dt_end, source_type, project_name) -> 最佳记录
            best_records = {}

            # 遍历所有日期范围和记录
            for date_range, records in detail_data.items():
                for record in records:
                    key = (date_range, record["project_name"])
                    current_change_type = record["change_type"]
                    # 如果当前记录是 "更正后全量模式"，直接覆盖
                    if current_change_type == "更正后新产生的申报表（全量模式）":
                        best_records[key] = record
                    # 否则，仅当 key 不存在时才保留
                    elif key not in best_records:
                        best_records[key] = record

            # 重新构建 detail_data（按原时间分组）
            new_detail_data = {}
            for key, record in best_records.items():
                date_range, _ = key
                if date_range not in new_detail_data:
                    new_detail_data[date_range] = []
                new_detail_data[date_range].append(record)
            return new_detail_data

        new_detail_data = filter_detail_data(detail_data)
        # 计算企业所得税贡献率并格式化输出
        all_zero = True  # 标记是否所有应纳税所得额和营业收入都是00
        risk_years = []  # 存储风险年份及相关信息
        result = []
        annual_data = defaultdict(lambda: {"tax_amount": 0.00, "income_amount": 0.00})
        # 数据进行聚合
        for dt_year, value in new_detail_data.items():
            for item in value:
                if item["project_name"] == "实际应纳所得税":
                    annual_data[dt_year]["tax_amount"] += item["amount"]
                elif item["project_name"] == "营业收入":
                    annual_data[dt_year]["income_amount"] += item["amount"]

        sorted_data_desc = dict(
            sorted(annual_data.items(), key=lambda item: item[0], reverse=False)
        )

        for year in sorted(sorted_data_desc.keys()):
            tax = annual_data[year]["tax_amount"]
            revenue = annual_data[year]["income_amount"]
            if revenue or tax:
                all_zero = False
            income_tax_contribution_ratio = (tax / revenue) if revenue > 0 else 0.00
            formatted_income_tax_contribution_ratio = (
                "{:,.2f}".format(income_tax_contribution_ratio * 100, 2) + "%"
                if income_tax_contribution_ratio is not None
                else "--"
            )  # 保留两位小数并添加百分号

            result.append(
                {
                    "所属期": year + "年",
                    "实际应纳所得税额(元)": "{:,.2f}".format(tax, 2),
                    "营业收入(元)": "{:,.2f}".format(revenue, 2),
                    "企业所得税贡献率": formatted_income_tax_contribution_ratio,
                }
            )

            # 风险识别：近三年内企业所得税贡献率小于 0.5%
            if income_tax_contribution_ratio < threshold:
                risk_years.append(
                    {
                        "年份": year + "年",
                        "企业所得税贡献率": formatted_income_tax_contribution_ratio,
                    }
                )

        # 生成风险描述
        if all_zero:  # 如果所有数据为空，那么只展示标题即可。
            result = ["所属期", "实际应纳所得税额(元)", "营业收入(元)", "企业所得税贡献率"]

        # threshold
        formatted_threshold = "{:,.2f}".format(threshold * 100, 2) + "%"
        if risk_years:
            risk_desc = f"风险描述: 近三年内，{company_name}在"
            # for risk_year in risk_years:
            #     risk_desc += f"{risk_year['年份']}企业所得税贡献率为{risk_year['企业所得税贡献率']}"
            risk_desc += ",".join(
                [
                    f"{risk_year['年份']}企业所得税贡献率为{risk_year['企业所得税贡献率']}"
                    for risk_year in risk_years
                ]
            )
            risk_desc += f"，低于{formatted_threshold}, 可能存在利润操纵或税收筹划不当等问题。"
        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return result, risk_desc

    # 4.3
    def calculate_taxable_income_rate(self, dt, data, threshold=0.5):
        """
        4.3 应税所得率检查
        计算每年的应税所得率（应税所得率 = 纳税调整后所得 / 营业收入 * 100），增加过去三年的过滤条件。

        参数:
            current_date (str): 当前日期，格式为 'YYYYMM'
            data (dict): 包含企业财税数据的字典

        返回:
            list: 包含每年营业收入、纳税调整后所得和应税所得率的列表，格式如下：
                [
                    {"所属期": "2021", "纳税调整后所得（元）": 2944.5, "营业收入（元）": 236963.68, "应税所得率为": "1.26%"},
                    ...
                ]
            str: 风险描述，格式为“风险描述: 近三年内，{年份} 应税所得率为 {所得率}%，大幅下降可能存在成本费用异常或利润操纵等问题。”
        """
        # 提取corporateIncome数据
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            # 税号没对齐
            default_result = ["所属期", "纳税调整后所得(元)", "营业收入(元)", "应税所得率"]
            default_desc = "风险描述：该指标项未检测到风险"
            return default_result, default_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        annual_data = defaultdict(
            lambda: {"revenue_amount": 0.00, "tax_amount": 0.00, "change_type": ""}
        )
        revenue_data = defaultdict(lambda: [])
        tax_data = defaultdict(lambda: [])

        detail_data = defaultdict(lambda: [])
        # 遍历corporateIncome数据
        income_records = data["data"]["corporateIncome"]  # # corporateIncome 表示所得税
        for record in income_records:
            dt_end_year = datetime.strptime(
                record["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if record["invalidMark"] == "Y":
                continue
            # if dt_end != dt_year + '1231': continue
            # 检查是否为年度纳税申报表
            if record["levyProjectName"] == "《中华人民共和国企业所得税年度纳税申报表（A类）》（A100000）":
                amount = float(record["accumulativeAmount"] or 0)
                if record["projectName"] == "一、营业收入（填写A101010/101020/103000）":
                    # 提取营业收入
                    detail_data[dt_end_year].append(
                        {
                            "project_name": "营业收入",
                            "amount": amount,
                            "change_type": record["changeType"],
                        }
                    )
                # 提取纳税调整后所得
                if record["projectName"].__contains__("四、纳税调整后所得"):
                    detail_data[dt_end_year].append(
                        {
                            "project_name": "纳税调整后所得",
                            "amount": amount,
                            "change_type": record["changeType"],
                        }
                    )

        def filter_detail_data(detail_data):
            # 存储 (dt_start, dt_end, source_type, project_name) -> 最佳记录
            best_records = {}

            # 遍历所有日期范围和记录
            for date_range, records in detail_data.items():
                for record in records:
                    key = (date_range, record["project_name"])
                    current_change_type = record["change_type"]
                    # 如果当前记录是 "更正后全量模式"，直接覆盖
                    if current_change_type == "更正后新产生的申报表（全量模式）":
                        best_records[key] = record
                    # 否则，仅当 key 不存在时才保留
                    elif key not in best_records:
                        best_records[key] = record

            # 重新构建 detail_data（按原时间分组）
            new_detail_data = {}
            for key, record in best_records.items():
                date_range, _ = key
                if date_range not in new_detail_data:
                    new_detail_data[date_range] = []
                new_detail_data[date_range].append(record)
            return new_detail_data

        # 取最新数据
        new_detail_data = filter_detail_data(detail_data)
        annual_data = defaultdict(lambda: {"tax_amount": 0.00, "income_amount": 0.00})
        for dt_year, value in new_detail_data.items():
            for item in value:
                if item["project_name"] == "纳税调整后所得":
                    annual_data[dt_year]["tax_amount"] += item["amount"]
                elif item["project_name"] == "营业收入":
                    annual_data[dt_year]["income_amount"] += item["amount"]

        sorted_data_desc = dict(
            sorted(annual_data.items(), key=lambda item: item[0], reverse=False)
        )

        # 计算应税所得率并格式化输出
        all_zero = True  # 标记是否应税所得率和营业收入都是00
        risk_years = []  # 存储风险年份及相关信息
        result = []
        for year in sorted(sorted_data_desc.keys()):
            revenue = annual_data[year]["income_amount"]
            tax = annual_data[year]["tax_amount"]
            if revenue or tax:
                all_zero = False
            income_tax_contribution_ratio = (tax / revenue) if revenue > 0 else 0.00
            formatted_income_tax_contribution_ratio = (
                "{:,.2f}".format(income_tax_contribution_ratio * 100, 2) + "%"
                if income_tax_contribution_ratio is not None
                else "--"
            )  # 保留两位小数并添加百分号

            result.append(
                {
                    "所属期": year + "年",
                    "纳税调整后所得(元)": "{:,.2f}".format(tax, 2),
                    "营业收入(元)": "{:,.2f}".format(revenue, 2),
                    "应税所得率": formatted_income_tax_contribution_ratio,
                }
            )

            # 风险识别：近三年内应税所得率小于
            # threshold
            formatted_threshold = "{:,.2f}".format(threshold * 100, 2) + "%"
            if income_tax_contribution_ratio < threshold:
                risk_years.append(
                    {
                        "年份": year + "年",
                        "应税所得率": formatted_income_tax_contribution_ratio,
                        "描述": "是负值"
                        if income_tax_contribution_ratio < 0
                        else f"低于{formatted_threshold}",
                    }
                )

        # 生成风险描述
        if all_zero:  # 如果所有数据为空，那么只展示标题即可。
            result = ["所属期", "纳税调整后所得(元)", "营业收入(元)", "应税所得率"]

        if risk_years:
            risk_desc = f"风险描述: 近三年内，{company_name}在"
            # for risk_year in risk_years:
            #     risk_desc += f"{risk_year['年份']}企业所得税贡献率为{risk_year['企业所得税贡献率']}"
            risk_desc += ",".join(
                [f"{risk_year['年份']}应税所得率{risk_year['描述']}" for risk_year in risk_years]
            )
            risk_desc += f", 可能存在成本费用异常或利润操纵等问题"
        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return result, risk_desc

    # 4.4
    def zero_declaration_detection(self, dt, data, threshold=0.5):
        """
        4.4 增值税连续3个月零申报检测
        增值税连续3个月零申报检测，增值税一般纳税人需关注“本期应补(退)税额”连续三个月是否等于零。
        参数:
            current_date (str): 当前日期，格式为 'YYYYMM'
            data (dict): 包含企业财税数据的字典
        返回:
            list: 包含税款所属期起、税款所属期止和期间连续零申报次数的列表
            str: 风险描述
        """
        # 税号确认
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        default_result = ["税款所属期起", "税款所属期止", "期间零申报次数"]
        default_risk_desc = "风险描述：该指标项未检测到风险"
        if taxid != self.company_taxid:
            return default_result, default_risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]
        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        value_added = data["data"]["valueAdded"]  # valueAdded 表示增值税
        montyly_data = defaultdict(lambda: [])
        detail_data = defaultdict(lambda: [])

        # 遍历valueAdded数据
        years = []
        for value in value_added:
            dt_start = datetime.strptime(
                value["beginDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y%m%d")
            dt_end = datetime.strptime(value["endDate"], "%Y-%m-%d %H:%M:%S").strftime(
                "%Y%m%d"
            )
            dt_end_year = datetime.strptime(
                value["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if value["invalidMark"] == "Y":
                continue
            if value["levyProjectName"] == "《增值税及附加税费申报表（一般纳税人适用）》":
                # 提取申报信息
                if value["projectName"] == "本期应补（退）税额":
                    years.append(dt_end_year)
                    # 即征即退项目-本月数
                    immed_amount = (
                        float(value.get("immediateRetreatMonthAmount", 0.0))
                        if value.get("immediateRetreatMonthAmount", 0.0) != None
                        else 0.0
                    )
                    # 一般项目-本月数
                    general_amount = (
                        float(value.get("generalMonthAmount", 0.0))
                        if value.get("generalMonthAmount", 0.0) != None
                        else 0.0
                    )
                    detail_data[(dt_start, dt_end)].append(
                        {
                            "project_name": "本期应补（退）税额",
                            "amount": immed_amount + general_amount,
                            "change_type": value["changeType"],
                        }
                    )
        if not years:
            return default_result, default_risk_desc

        def filter_detail_data(detail_data):
            # 存储 (dt_start, dt_end, source_type, project_name) -> 最佳记录
            best_records = {}

            # 遍历所有日期范围和记录
            for date_range, records in detail_data.items():
                dt_start, dt_end = date_range
                for record in records:
                    key = (dt_start, dt_end, record["project_name"])
                    current_change_type = record["change_type"]

                    # 如果当前记录是 "更正后全量模式"，直接覆盖
                    if current_change_type == "更正后新产生的申报表（全量模式）":
                        best_records[key] = record
                    # 否则，仅当 key 不存在时才保留
                    elif key not in best_records:
                        best_records[key] = record

            # 重新构建 detail_data（按原时间分组）
            new_detail_data = {}
            for key, record in best_records.items():
                dt_start, dt_end, _ = key
                date_range = (dt_start, dt_end)
                if date_range not in new_detail_data:
                    new_detail_data[date_range] = []
                new_detail_data[date_range].append(record)

            return new_detail_data

        new_detail_data = filter_detail_data(detail_data)
        # annual_data = defaultdict(lambda: {'tax_amount': 0.00, 'sell_amount': 0.00})
        annual_data = defaultdict(
            lambda: {
                "tax_amount": 0.0,
                "zero_cnt": 0,
                "total_cnt": 0,
                "start_list": [],
                "end_list": [],
            }
        )
        # 取最新状态数据：
        for (dt_start, dt_end), value in new_detail_data.items():
            # print(dt_start, dt_end)
            month_amt = 0
            for item in value:
                if item["project_name"] == "本期应补（退）税额":
                    annual_data[dt_start[:4]]["tax_amount"] += item["amount"]
            month_amt += item["amount"]
            annual_data[dt_start[:4]]["start_list"].append(dt_start)
            annual_data[dt_start[:4]]["end_list"].append(dt_end)
            annual_data[dt_start[:4]]["total_cnt"] += 1
            # print(annual_data[dt_start[:4]]['tax_amount'])
            if month_amt == 0:
                annual_data[dt_start[:4]]["zero_cnt"] += 1
        # 初始化结果列表和风险期间集合
        result = []
        risk_years = []

        for year in sorted(annual_data.keys()):
            value = annual_data[year]
            year_start, year_end = min(value["start_list"]), max(value["end_list"])
            result.append(
                {
                    "税款所属期起": f"{year_start[:4]}年{year_start[4:6]}月{year_start[6:]}日",
                    "税款所属期止": f"{year_end[:4]}年{year_end[4:6]}月{year_end[6:]}日",
                    "期间零申报次数": f"{str(value['zero_cnt'])}次（共申报{str(value['total_cnt'])}次）",
                }
            )
            # # 风险识别：退税额是0的次数占期间超过threshold
            if value["zero_cnt"] / value["total_cnt"] > threshold:
                risk_years.append(
                    {
                        "期间": f"{year_start[:4]}年{year_start[4:6]}月{year_start[6:]}日~{year_end[:4]}年{year_end[4:6]}月{year_start[6:]}日",
                        "期间零申报次数": value["zero_cnt"],
                    }
                )

        if risk_years:
            risk_desc = f"风险描述: 近三年内，{company_name}作为一般纳税人的增值税申报中，其中"
            risk_desc += "，".join([f"{x['期间']}({x['期间零申报次数']}次)" for x in risk_years])
            formatted_threshold = "{:,.2f}".format(threshold * 100, 2) + "%"
            risk_desc += f"期间内存在多次零申报，超过期间内申报总月份的{formatted_threshold}，需要重点关注和实地核查。"
        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return result, risk_desc

    # 4.5
    def sales_volume_exceeded_detection(self, dt, data, threshold=5000000):
        """
        4.5 小规模纳税人销售额超标检测
        小规模纳税人销售额超标检测，小规模纳税人短期内销售超过500万或连续多月零申报，收入额快速超过500万。
        参数:
            data (dict): 包含企业财税数据的字典
        返回:
            list: 包含每年销售额的列表，格式如下：
                [
                    {"所属期": "2021", "销售额（元）": 2944.5},
                    ...
                ]
        """
        # 税号确认
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        default_result = ["所属年度", "销售额"]
        default_risk_desc = "风险描述：该指标项未检测到风险"
        if taxid != self.company_taxid:
            return default_result, default_risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        value_added = data["data"]["valueAdded"]  # valueAdded 表示增值税

        # 小规模->一般纳税人的时间节点...
        small_end_date = max(
            v["endDate"]
            for v in value_added
            if v.get("levyProjectName") == "《增值税及附加税费申报表（小规模纳税人适用）》"
        )
        small_end = datetime.strptime(small_end_date, "%Y-%m-%d %H:%M:%S").strftime(
            "%Y%m%d"
        )

        # 数据结果保存
        detail_data = defaultdict(lambda: [])
        value_added = data["data"]["valueAdded"]
        for value in value_added:
            # 0:一般纳税人 1:小规模纳税人 3:其他
            # 小规模识别方式 也可以用 levyProjectName来识别。
            # print(value['taxpayerType'], value['levyProjectName'])
            if (
                value["taxpayerType"] != "1"
                or value["levyProjectName"] != "《增值税及附加税费申报表（小规模纳税人适用）》"
            ):
                continue
            if value["invalidMark"] == "Y":
                continue
            dt_start = datetime.strptime(
                value["beginDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y%m%d")
            dt_end = datetime.strptime(value["endDate"], "%Y-%m-%d %H:%M:%S").strftime(
                "%Y%m%d"
            )
            dt_end_year = datetime.strptime(
                value["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year:
                continue  # 考虑今年以前的即可。

            # 销售额
            if dt_end != small_end:
                continue
            # 本年累计-货物及劳务
            immed_amount = (
                float(value.get("currentYearAccumulativeGoods", 0.0))
                if value.get("currentYearAccumulativeGoods", 0.0) != None
                else 0.0
            )
            # 本年累计-服务不动产和无形资产
            general_amount = (
                float(value.get("currentYearAccumulativeService", 0.0))
                if value.get("currentYearAccumulativeService", 0.0) != None
                else 0.0
            )

            if value["projectName"] in [
                "（一）应征增值税不含税销售额（3%征收率）",
                "（二）应征增值税不含税销售额（5%征收率）",
                "（三）销售使用过的固定资产不含税销售额",
                "（四）免税销售额",
                "（五）出口免税销售额",
            ]:
                detail_data[(dt_start, dt_end)].append(
                    {
                        "amount": immed_amount + general_amount,
                        "project_name": value["projectName"],
                        "change_type": value["changeType"],
                    }
                )
                # print({
                #     'amount': immed_amount + general_amount,
                #     'project_name': value['projectName'],
                #     'change_type': value['changeType']
                # })

        def filter_detail_data(detail_data):
            # 存储 (dt_start, dt_end, source_type, project_name) -> 最佳记录
            best_records = {}

            # 遍历所有日期范围和记录
            for date_range, records in detail_data.items():
                dt_start, dt_end = date_range
                for record in records:
                    key = (dt_start, dt_end, record["project_name"])
                    current_change_type = record["change_type"]

                    # 如果当前记录是 "更正后全量模式"，直接覆盖
                    if current_change_type == "更正后新产生的申报表（全量模式）":
                        best_records[key] = record
                    # 否则，仅当 key 不存在时才保留
                    elif key not in best_records:
                        best_records[key] = record

            # 重新构建 detail_data（按原时间分组）
            new_detail_data = {}
            for key, record in best_records.items():
                dt_start, dt_end, _ = key
                date_range = (dt_start, dt_end)
                if date_range not in new_detail_data:
                    new_detail_data[date_range] = []
                new_detail_data[date_range].append(record)

            return new_detail_data

        new_detail_data = filter_detail_data(detail_data)
        annual_data = defaultdict(lambda: {"sell_amount": 0.00})
        for (dt_start, dt_end), value in new_detail_data.items():
            for item in value:
                annual_data[dt_end[:4]]["sell_amount"] += item["amount"]
                # print(item)

        # 将结果转换为指定格式
        result = [
            {
                "所属年度": year + "年",
                "销售额（元）": "{:,.2f}".format(annual_data[year]["sell_amount"], 2),
            }
            for year in sorted(annual_data.keys())
        ]
        #
        risk_years = []
        # 风险识别：销售额≤500万
        for year in sorted(annual_data.keys()):
            if annual_data[year]["sell_amount"] > threshold:
                risk_years.append(
                    {
                        "年份": year + "年",
                        "销售额（元）": "{:,.2f}".format(annual_data[year]["sell_amount"], 2),
                    }
                )

        # 生成风险描述
        if risk_years:
            risk_desc = "风险描述: 近三年内，"
            for risk_year in risk_years:
                risk_desc += f"{risk_year['年份']}年，累计销售额已达到{risk_year['销售额']}元，"
                risk_desc += f"超过小规模纳税人标准。"
        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return result, risk_desc

    # 4.6
    def begin_end_tax(self, dt, data):
        """
        4.6 期初留抵税额与上期期末留抵税额核对分析
        参数:
            current_date (str): 当前日期，格式为 'YYYYMM'
            data (dict): 包含企业财税数据的字典
        返回:
            list: 包含税款所属期起、税款所属期止、期初留抵税额和期末留抵税额的列表，格式如下：
                [
                    {
                        '税款所属期起': "2024-01-01 00:00:00",
                        '税款所属期止': "2024-03-31 00:00:00",
                        '期初留抵税额（元）': 1234.5,
                        '期末留抵税额（元）': 1000.0
                    },
                    ...
                ]
            str: 风险描述
        """
        # 税号确认
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        default_result = [
            {"税款所属期起": "", "税款所属期止": "", "期初留抵税额（元）": "", "期末留抵税额（元）": ""}
        ]
        default_risk_desc = "风险描述：该指标项未检测到风险"
        if taxid != self.company_taxid:
            return default_result, default_risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]
        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        # 初始化近 12 个月的月份列表
        months = [
            (dt_format - relativedelta(months=i)).strftime("%Y%m") for i in range(1, 13)
        ][::-1]

        # 取数据
        value_added = data["data"]["valueAdded"]  # valueAdded 表示增值税
        detail_data = defaultdict(lambda: [])

        # # 遍历valueAdded数据
        # years = []

        for value in value_added:
            dt_start = datetime.strptime(
                value["beginDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y%m%d")
            dt_end = datetime.strptime(value["endDate"], "%Y-%m-%d %H:%M:%S").strftime(
                "%Y%m%d"
            )
            dt_end_year = datetime.strptime(
                value["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            dt_end_month = datetime.strptime(
                value["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y%m")
            if dt_end_month not in months:
                continue
            if value["invalidMark"] == "Y":
                continue
            if value["levyProjectName"] == "《增值税及附加税费申报表（一般纳税人适用）》":
                # 即征即退项目-本月数
                immed_amount = (
                    float(value.get("immediateRetreatMonthAmount", 0.0))
                    if value.get("immediateRetreatMonthAmount", 0.0) != None
                    else 0.0
                )
                # 一般项目-本月数
                general_amount = (
                    float(value.get("generalMonthAmount", 0.0))
                    if value.get("generalMonthAmount", 0.0) != None
                    else 0.0
                )
                if value["projectName"] == "上期留抵税额":
                    detail_data[(dt_start, dt_end)].append(
                        {
                            "project_name": "上期留抵税额",
                            "amount": immed_amount + general_amount,
                            "change_type": value["changeType"],
                        }
                    )

                elif value["projectName"] == "期末留抵税额":
                    detail_data[(dt_start, dt_end)].append(
                        {
                            "project_name": "期末留抵税额",
                            "amount": immed_amount + general_amount,
                            "change_type": value["changeType"],
                        }
                    )

        def filter_detail_data(detail_data):
            # 存储 (dt_start, dt_end, source_type, project_name) -> 最佳记录
            best_records = {}

            # 遍历所有日期范围和记录
            for date_range, records in detail_data.items():
                dt_start, dt_end = date_range
                for record in records:
                    key = (dt_start, dt_end, record["project_name"])
                    current_change_type = record["change_type"]

                    # 如果当前记录是 "更正后全量模式"，直接覆盖
                    if current_change_type == "更正后新产生的申报表（全量模式）":
                        best_records[key] = record
                    # 否则，仅当 key 不存在时才保留
                    elif key not in best_records:
                        best_records[key] = record

            # 重新构建 detail_data（按原时间分组）
            new_detail_data = {}
            for key, record in best_records.items():
                dt_start, dt_end, _ = key
                date_range = (dt_start, dt_end)
                if date_range not in new_detail_data:
                    new_detail_data[date_range] = []
                new_detail_data[date_range].append(record)

            return new_detail_data

        new_detail_data = filter_detail_data(detail_data)
        # 留抵税额data
        period_data = defaultdict(
            lambda: {"start_tax_amount": 0.00, "end_tax_amount": 0.00}
        )
        for (dt_start, dt_end), value in new_detail_data.items():
            for item in value:
                if item["project_name"] == "上期留抵税额":
                    period_data[(dt_start, dt_end)]["start_tax_amount"] += item[
                        "amount"
                    ]
                elif item["project_name"] == "期末留抵税额":
                    period_data[(dt_start, dt_end)]["end_tax_amount"] += item["amount"]
        # 使用字典存储每个时间段的累加结果
        tax_periods = {}
        all_zero = True
        risk_period = []
        result = []
        cnt = 0
        last_end_amount = 0.0
        for dt_start, dt_end in sorted(
            period_data.keys(), key=lambda x: x[0], reverse=False
        ):
            start_amount = period_data[(dt_start, dt_end)]["start_tax_amount"]
            end_amount = period_data[(dt_start, dt_end)]["end_tax_amount"]
            if start_amount and end_amount:
                result.append(
                    {
                        "税款所属期起": f"{dt_start[:4]}年{dt_start[4:6]}月{dt_start[6:]}日",
                        "税款所属期止": f"{dt_end[:4]}年{dt_end[4:6]}月{dt_end[6:]}日",
                        "期初留抵税额（元）": start_amount,
                        "期末留抵税额（元）": end_amount,
                    }
                )
                all_zero = False
            # print(dt_start, dt_end, start_amount, end_amount, last_end_amount)
            if cnt and last_end_amount != start_amount:
                risk_period.append(
                    {
                        "税款所属期": f"{dt_start[:4]}年{dt_start[4:6]}月",
                        "期初留抵税额（元）": start_amount,
                        "期末留抵税额（元）": end_amount,
                    }
                )
            last_end_amount = end_amount
            cnt += 1

        if all_zero:
            result = [{"税款所属期起": "", "税款所属期止": "", "期初留抵税额（元）": "", "期末留抵税额（元）": ""}]
        if risk_period:
            risk_desc = f"⻛险描述：{company_name}在过去12个月内，{','.join([item['税款所属期'] for item in risk_period])}存在期初留抵税额与上月期末留抵税额不一致。"
            # risk_desc += ','.join([f"{item['税款所属期起']}" for item in risk_period])
        else:
            risk_desc = f"⻛险描述：该指标项未检测到⻛险"

        return result, risk_desc
