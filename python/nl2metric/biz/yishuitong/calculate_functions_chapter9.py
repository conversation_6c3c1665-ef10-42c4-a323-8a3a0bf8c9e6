import json
from collections import defaultdict
from decimal import Decimal
import math
from datetime import datetime

from dateutil.relativedelta import relativedelta


class ReportChapter9:
    def calculate_receivables_turnover(self, dt, data, threshold1=5, threshold2=72):
        # 9.1 应收账款周转率
        # 税号确认
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            default_result = ["所属期", "营业收入（元）", "平均应收账款（元）", "应收账款周率", "应收账款周天数"]
            default_risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, default_risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        annual_data = defaultdict(
            lambda: {"income_amount": 0.00, "receivable_amount": 0.00}
        )

        """step1: 利润表：营业收入"""
        # 提取financeProfit数据：取年报表（一定存在年粒度财报，从3个年表里面会填写一个）
        financeProfit = data["data"]["financeProfit"]
        for profit in financeProfit:
            dt_end_year = datetime.strptime(
                profit["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if profit["period"] == "Year" and profit["projectName"] == "一、营业收入":
                amount = float(profit["currentYearAccumulativeAmount"] or 0)
                annual_data[dt_end_year]["income_amount"] = amount

        """step2: 资产负债表：（应收帐款）期初+期末/2"""
        # 提取financeBalance数据：取年报表
        financeBalance = data["data"]["financeBalance"]
        for balance in financeBalance:
            dt_end_year = datetime.strptime(
                balance["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if balance["period"] == "Year" and balance["projectName"] == "应收账款":
                ending_balance = float(balance["endingBalance"] or 0)
                init_balance = float(balance["initialBalance"] or 0)
                avg_balance = (ending_balance + init_balance) / 2
                annual_data[dt_end_year]["receivable_amount"] = avg_balance
                # print(f"{dt_end_year}\n期末:{ending_balance},期初:{init_balance}, 均值:{avg_balance}")

        """数据分析"""
        result = []
        risk_years = []
        all_zero = True
        sorted_years = sorted(annual_data.keys(), reverse=False)
        formatted_threshold1 = "{:,.0f}".format(threshold1)
        formatted_threshold2 = "{:,.0f}".format(threshold2)
        for year in sorted_years:
            income_amount = annual_data[year]["income_amount"]
            receivable_amount = annual_data[year]["receivable_amount"]

            receivable_income_rate = (
                income_amount / receivable_amount if receivable_amount else None
            )
            receivable_income_days = (
                int(365 / receivable_income_rate) if receivable_income_rate else None
            )
            if income_amount or receivable_amount:
                all_zero = False
            # 格式化
            formatted_income_amount = "{:,.2f}".format(income_amount, 2)
            formatted_receivable_amount = "{:,.2f}".format(receivable_amount, 2)
            formatted_receivable_income_rate = (
                "{:,.2f}".format(receivable_income_rate, 2)
                if receivable_income_rate
                else "--"
            )
            formatted_receivable_income_days = (
                "{:,.0f}".format(receivable_income_days) + "天"
                if receivable_income_days
                else "--"
            )

            result.append(
                {
                    "所属期": year + "年",
                    "营业收入（元）": formatted_income_amount,
                    "平均应收账款（元）": formatted_receivable_amount,
                    "应收账款周转率": formatted_receivable_income_rate,
                    "应收账款周转天数": formatted_receivable_income_days,
                }
            )
            if (
                receivable_income_rate
                and receivable_income_days
                and (
                    receivable_income_rate < threshold1
                    or receivable_income_days > threshold2
                )
            ):
                risk_years.append(
                    {
                        "所属期": year + "年",
                        "营业收入（元）": formatted_income_amount,
                        "平均应收账款（元）": formatted_receivable_amount,
                        "应收账款周转率": formatted_receivable_income_rate,
                        "应收账款周转天数": formatted_receivable_income_days,
                        "描述": f"应收账款周转率({formatted_receivable_income_rate})低于{formatted_threshold1}"
                        if receivable_income_rate < threshold1
                        else f"应收账款周天数({formatted_receivable_income_days})高于{formatted_threshold2}天",
                    }
                )

        if all_zero:
            result = ["所属期", "营业收入（元）", "平均应收账款（元）", "应收账款周率", "应收账款周天数"]

        if risk_years:
            risk_desc = f"风险描述：{company_name}在过去3年中，"
            risk_desc += ",".join([f"{item['所属期']}{item['描述']}" for item in risk_years])
            risk_desc += "。应收账款周转率偏低或周转天数过长可能表明企业回款能力较差，存在信用政策宽松或客户质量不佳的问题，这可能导致坏账风险，需关注应收账款管理及账龄情况。"

        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return result, risk_desc

    def calculate_inventory_turnover(self, dt, data, threshold1=3, threshold2=120):
        # 9.2 存货周转率
        # 税号确认
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            default_result = [
                {"所属期": "", "营业收入（元）": "", "平均存货（元）": "", "存活周转率": "", "存货周转率天数": ""}
            ]
            default_risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, default_risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        annual_data = defaultdict(
            lambda: {"income_amount": 0.00, "inventory_amount": 0.00}
        )

        """step1: 利润表：营业收入"""
        # 提取financeProfit数据：取年报表（一定存在年粒度财报，从3个年表里面会填写一个）
        financeProfit = data["data"]["financeProfit"]
        for profit in financeProfit:
            dt_end_year = datetime.strptime(
                profit["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if profit["period"] == "Year" and profit["projectName"] == "一、营业收入":
                amount = float(profit["currentYearAccumulativeAmount"] or 0)
                annual_data[dt_end_year]["income_amount"] = amount

        """step2: 资产负债表：（存货）期初+期末/2"""
        # 提取financeBalance数据：取年报表
        financeBalance = data["data"]["financeBalance"]
        for balance in financeBalance:
            dt_end_year = datetime.strptime(
                balance["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if balance["period"] == "Year" and balance["projectName"] == "存货":
                ending_balance = float(balance["endingBalance"] or 0)
                init_balance = float(balance["initialBalance"] or 0)
                avg_balance = (ending_balance + init_balance) / 2
                annual_data[dt_end_year]["inventory_amount"] = avg_balance
                # print(f"{dt_end_year}\n期末:{ending_balance},期初:{init_balance}, 均值:{avg_balance}")

        """数据分析"""
        result = []
        risk_years = []
        all_zero = True
        sorted_years = sorted(annual_data.keys(), reverse=False)
        formatted_threshold1 = "{:,.0f}".format(threshold1)
        formatted_threshold2 = "{:,.0f}".format(threshold2)
        for year in sorted_years:
            income_amount = annual_data[year]["income_amount"]
            inventory_amount = annual_data[year]["inventory_amount"]
            inventory_income_rate = (
                income_amount / inventory_amount if inventory_amount else None
            )
            inventory_income_days = (
                int(365 / inventory_income_rate) if inventory_income_rate else None
            )
            if income_amount or inventory_amount:
                all_zero = False

            # 格式化
            formatted_income_amount = "{:,.2f}".format(income_amount, 2)
            formatted_inventory_amount = "{:,.2f}".format(inventory_amount, 2)
            formatted_inventory_income_rate = (
                "{:,.2f}".format(inventory_income_rate, 2)
                if inventory_income_rate
                else "--"
            )
            formatted_inventory_income_days = (
                "{:,.0f}".format(inventory_income_days) + "天"
                if inventory_income_days
                else "--"
            )

            result.append(
                {
                    "所属期": year + "年",
                    "营业收入（元）": formatted_income_amount,
                    "平均存货（元）": formatted_inventory_amount,
                    "存货周转率": formatted_inventory_income_rate,
                    "存货周转率天数": formatted_inventory_income_days,
                }
            )
            if (
                inventory_income_rate
                and inventory_income_days
                and (
                    inventory_income_rate < threshold1
                    or inventory_income_days > threshold2
                )
            ):
                risk_years.append(
                    {
                        "所属期": year + "年",
                        "营业收入（元）": formatted_income_amount,
                        "平均存货（元）": formatted_inventory_amount,
                        "存货周转率": formatted_inventory_income_rate,
                        "存货周转率天数": formatted_inventory_income_days,
                        "描述": f"存货周转率({formatted_inventory_income_rate})低于{formatted_threshold1}"
                        if inventory_income_rate < threshold1
                        else f"存货周转率天数({formatted_inventory_income_days})高于{formatted_threshold2}",
                    }
                )
        if all_zero:
            result = [
                {"所属期": "", "营业收入（元）": "", "平均存货（元）": "", "存活周转率": "", "存货周转率天数": ""}
            ]
        if risk_years:
            risk_desc = "风险描述："
            risk_desc += ",".join([f"{item['所属期']}{item['描述']}" for item in risk_years])
            risk_desc += (
                "。存货周转率偏低或周转天数过长可能反映企业存货积压、产品滞销或库存管理不善，这可能导致资金占用过大或存货减值风险，需关注存货结构和管理效率。"
            )

        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return result, risk_desc

    def calculate_current_assets_turnover(
        self, dt, data, threshold1=1.5, threshold2=2.4
    ):
        # 9.3 流动资产周转率
        # 税号确认
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            default_result = ["所属期", "营业收入（元）", "平均流动资产（元）", "流动资产周转率", "流动资产周转天数"]
            default_risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, default_risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        annual_data = defaultdict(
            lambda: {"income_amount": 0.00, "current_asset_amount": 0.00}
        )

        """step1: 利润表：营业收入"""
        # 提取financeProfit数据：取年报表（一定存在年粒度财报，从3个年表里面会填写一个）
        financeProfit = data["data"]["financeProfit"]
        for profit in financeProfit:
            dt_end_year = datetime.strptime(
                profit["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if profit["period"] == "Year" and profit["projectName"] == "一、营业收入":
                amount = float(profit["currentYearAccumulativeAmount"] or 0)
                annual_data[dt_end_year]["income_amount"] = amount

        """step2: 资产负债表：（流动资产）期初+期末/2"""
        # 提取financeBalance数据：取年报表
        financeBalance = data["data"]["financeBalance"]
        for balance in financeBalance:
            dt_end_year = datetime.strptime(
                balance["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if balance["period"] == "Year" and balance["projectName"] == "流动资产合计":
                ending_balance = float(balance["endingBalance"] or 0)
                init_balance = float(balance["initialBalance"] or 0)
                avg_balance = (ending_balance + init_balance) / 2
                annual_data[dt_end_year]["current_asset_amount"] = avg_balance
                # print(f"{dt_end_year}\n期末:{ending_balance},期初:{init_balance}, 均值:{avg_balance}")

        """数据分析"""
        result = []
        risk_years = []
        all_zero = True
        sorted_years = sorted(annual_data.keys(), reverse=False)
        formatted_threshold1 = "{:,.0f}".format(threshold1)
        formatted_threshold2 = "{:,.0f}".format(threshold2)
        for year in sorted_years:
            income_amount = annual_data[year]["income_amount"]
            current_asset_amount = annual_data[year]["current_asset_amount"]

            current_asset_income_rate = (
                income_amount / current_asset_amount if current_asset_amount else None
            )
            current_asset_income_days = (
                int(365 / current_asset_income_rate)
                if current_asset_income_rate
                else None
            )
            if income_amount or current_asset_amount:
                all_zero = False
            # 格式化
            formatted_income_amount = "{:,.2f}".format(income_amount, 2)
            formatted_current_asset_amount = "{:,.2f}".format(current_asset_amount, 2)
            formatted_current_asset_income_rate = (
                "{:,.2f}".format(current_asset_income_rate, 2)
                if current_asset_income_rate
                else "--"
            )
            formatted_current_asset_income_days = (
                "{:,.0f}".format(current_asset_income_days) + "天"
                if current_asset_income_days
                else "--"
            )

            result.append(
                {
                    "所属期": year + "年",
                    "营业收入（元）": formatted_income_amount,
                    "平均流动资产（元）": formatted_current_asset_amount,
                    "流动资产周转率": formatted_current_asset_income_rate,
                    "流动资产周转天数": formatted_current_asset_income_days,
                }
            )
            if (
                current_asset_income_rate
                and current_asset_income_days
                and (
                    current_asset_income_rate < threshold1
                    or current_asset_income_days > threshold2
                )
            ):
                risk_years.append(
                    {
                        "所属期": year + "年",
                        "营业收入（元）": formatted_income_amount,
                        "平均流动资产（元）": formatted_current_asset_amount,
                        "流动资产周转率": formatted_current_asset_income_rate,
                        "流动资产周转天数": current_asset_income_days,
                        "描述": f"流动资产周转率({formatted_current_asset_income_rate})低于{formatted_threshold1}"
                        if current_asset_income_rate < threshold1
                        else f"流动资产周转天数({formatted_current_asset_income_days})高于{formatted_threshold2}天",
                    }
                )

        if all_zero:
            result = ["所属期", "营业收入（元）", "平均流动资产（元）", "流动资产周转率", "流动资产周转天数"]

        if risk_years:
            risk_desc = f"风险描述：{company_name}在过去3年中，"
            risk_desc += ",".join([f"{item['所属期']}{item['描述']}" for item in risk_years])
            risk_desc += (
                "。流动资产周转率偏低或周转天数过长可能表明企业流动资产利用效率低下，或存在流动资产虚增、占用过多等问题，需重点关注流动资产的构成及变现能力。"
            )

        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return result, risk_desc

    def calculate_total_assets_turnover(self, dt, data, threshold1=0.6, threshold2=600):
        # 9.4 总资产周转率
        # 税号确认
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            default_result = ["所属期", "营业收入（元）", "平均资产（元）", "总资产周转率", "总资产周转天数"]
            default_risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, default_risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        annual_data = defaultdict(
            lambda: {"income_amount": 0.00, "balance_amount": 0.00}
        )

        """step1: 利润表：营业收入"""
        # 提取financeProfit数据：取年报表（一定存在年粒度财报，从3个年表里面会填写一个）
        financeProfit = data["data"]["financeProfit"]
        for profit in financeProfit:
            dt_end_year = datetime.strptime(
                profit["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if profit["period"] == "Year" and profit["projectName"] == "一、营业收入":
                amount = float(profit["currentYearAccumulativeAmount"] or 0)
                annual_data[dt_end_year]["income_amount"] = amount

        """step2: 资产负债表：（总资产-资产合计）期初+期末/2"""
        # 提取financeBalance数据：取年报表
        financeBalance = data["data"]["financeBalance"]
        for balance in financeBalance:
            dt_end_year = datetime.strptime(
                balance["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if balance["period"] == "Year" and balance["projectName"] == "资产合计":
                ending_balance = float(balance["endingBalance"] or 0)
                init_balance = float(balance["initialBalance"] or 0)
                avg_balance = (ending_balance + init_balance) / 2
                annual_data[dt_end_year]["balance_amount"] = avg_balance
                # print(f"{dt_end_year}\n期末:{ending_balance},期初:{init_balance}, 均值:{avg_balance}")

        """数据分析"""
        result = []
        risk_years = []
        all_zero = True
        sorted_years = sorted(annual_data.keys(), reverse=False)
        formatted_threshold1 = "{:,.0f}".format(threshold1)
        formatted_threshold2 = "{:,.0f}".format(threshold2)
        for year in sorted_years:
            income_amount = annual_data[year]["income_amount"]
            balance_amount = annual_data[year]["balance_amount"]

            balance_income_rate = (
                income_amount / balance_amount if balance_amount else None
            )
            balance_income_days = (
                int(365 / balance_income_rate) if balance_income_rate else None
            )
            if income_amount or balance_amount:
                all_zero = False
            # 格式化
            formatted_income_amount = "{:,.2f}".format(income_amount, 2)
            formatted_balance_amount = "{:,.2f}".format(balance_amount, 2)
            formatted_balance_income_rate = (
                "{:,.2f}".format(balance_income_rate, 2)
                if balance_income_rate
                else "--"
            )
            formatted_balance_income_days = (
                "{:,.0f}".format(balance_income_days) + "天"
                if balance_income_days
                else "--"
            )

            result.append(
                {
                    "所属期": year + "年",
                    "营业收入（元）": formatted_income_amount,
                    "平均总资产（元）": formatted_balance_amount,
                    "总资产周转率": formatted_balance_income_rate,
                    "总资产周转天数": formatted_balance_income_days,
                }
            )
            if (
                balance_income_rate
                and balance_income_days
                and (
                    balance_income_rate < threshold1 or balance_income_days > threshold2
                )
            ):
                risk_years.append(
                    {
                        "所属期": year + "年",
                        "营业收入（元）": formatted_income_amount,
                        "平均总资产（元）": formatted_balance_amount,
                        "总资产周转率": formatted_balance_income_rate,
                        "总资产周转天数": formatted_balance_income_days,
                        "描述": f"总资产周转率({formatted_balance_income_rate})低于{formatted_threshold1}"
                        if balance_income_rate < threshold1
                        else f"总资产周转天数({formatted_balance_income_days})高于{formatted_threshold2}天",
                    }
                )

        if all_zero:
            result = ["所属期", "营业收入（元）", "平均总资产（元）", "总资产周转率", "总资产周转天数"]

        if risk_years:
            risk_desc = f"风险描述：{company_name}在过去3年中，"
            risk_desc += ",".join([f"{item['所属期']}{item['描述']}" for item in risk_years])
            risk_desc += (
                "。总资产周转率偏低可能反映企业资产利用效率不足，或存在资产闲置、低效配置等问题，这可能进一步影响企业的盈利能力，需关注资产管理和运营效率。"
            )

        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return result, risk_desc
