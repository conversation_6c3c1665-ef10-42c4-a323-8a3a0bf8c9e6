from collections import defaultdict
from datetime import datetime
import json
from dateutil.relativedelta import relativedelta


class ReportChapter7:
    # 7.1
    def calculate_period_expense_tax_elastic_modulus(self, dt, data, threshold=0.3):
        # 7.1 期间费用与企业所得税贡献变动检查

        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            # 税号没对齐
            default_result = [
                "所属期",
                "期间费用（元）",
                "营业收入(元)",
                "实际应纳所得税额（元）",
                "期间费用变动率",
                "所得税贡献变动率",
                "期间费用与企业所得税贡献变动系数",
            ]
            risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        detail_data = defaultdict(lambda: [])
        # 遍历corporateIncome数据
        income_records = data["data"]["corporateIncome"]  # # corporateIncome 表示所得税
        period_name = [
            "减：财务费用（填写A104000）",
            "减：销售费用（填写A104000）",
            "减：管理费用（填写A104000）",
            "减：研发费用（填写A104000）",
        ]
        for record in income_records:
            dt_end_year = datetime.strptime(
                record["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if record["invalidMark"] == "Y":
                continue
            # if dt_end != dt_year + '1231': continue年度申报表，不需要做年份时间的过滤了。
            # 检查是否为年度纳税申报表
            if record["levyProjectName"] == "《中华人民共和国企业所得税年度纳税申报表（A类）》（A100000）":
                amount = float(record["accumulativeAmount"] or 0)
                # 提取营业收入
                if record["projectName"] == "一、营业收入（填写A101010/101020/103000）":
                    detail_data[dt_end_year].append(
                        {
                            "amount": amount,
                            "project_name": "营业收入",
                            "change_type": record["changeType"],
                        }
                    )
                # 提取应纳所得税额
                if record["projectName"].__contains__("八、实际应纳所得税额"):
                    detail_data[dt_end_year].append(
                        {
                            "amount": amount,
                            "project_name": "实际应纳所得税额",
                            "change_type": record["changeType"],
                        }
                    )
                # 提取期间费用

                if record["projectName"] in period_name:
                    detail_data[dt_end_year].append(
                        {
                            "amount": amount,
                            "project_name": record["projectName"],
                            "change_type": record["changeType"],
                        }
                    )

        # 添加风险阈值常量
        def filter_detail_data(detail_data):
            # 存储 (dt_start, dt_end, source_type, project_name) -> 最佳记录
            best_records = {}
            # 遍历所有日期范围和记录
            for date_range, records in detail_data.items():
                for record in records:
                    key = (date_range, record["project_name"])
                    current_change_type = record["change_type"]
                    # 如果当前记录是 "更正后全量模式"，直接覆盖
                    if current_change_type == "更正后新产生的申报表（全量模式）":
                        best_records[key] = record
                    # 否则，仅当 key 不存在时才保留
                    elif key not in best_records:
                        best_records[key] = record

            # 重新构建 detail_data（按原时间分组）
            new_detail_data = {}
            for key, record in best_records.items():
                date_range, _ = key
                if date_range not in new_detail_data:
                    new_detail_data[date_range] = []
                new_detail_data[date_range].append(record)
            return new_detail_data

        new_detail_data = filter_detail_data(detail_data)
        annual_data = defaultdict(
            lambda: {"period_amount": 0.00, "tax_amount": 0.00, "income_amount": 0.00}
        )
        # 企业所得税年报汇总：
        for dt_year, value in new_detail_data.items():
            for item in value:
                if item["project_name"] in period_name:
                    # 计算期间费用（管理费用、销售费用、财务费用）
                    annual_data[dt_year]["period_amount"] += item["amount"]
                elif item["project_name"] == "实际应纳所得税额":
                    annual_data[dt_year]["tax_amount"] += item["amount"]
                elif item["project_name"] == "营业收入":
                    annual_data[dt_year]["income_amount"] += item["amount"]

        # 嵌套函数：生成风险描述

        sorted_years = sorted(annual_data.keys(), reverse=False)

        # 计算变动率
        risk_years = []
        all_zero = True
        result = []
        for i, year in enumerate(sorted_years):
            if i > 0:  # 从第二年开始计算变动率
                prev_year = sorted_years[i - 1]
                # 期间费用变动率
                if annual_data[prev_year]["period_amount"] != 0:
                    annual_data[year]["period_amount_rate"] = (
                        annual_data[year]["period_amount"]
                        - annual_data[prev_year]["period_amount"]
                    ) / annual_data[prev_year]["period_amount"]
                else:
                    annual_data[year]["period_amount_rate"] = None  # 上一年营业收入为 0，无法计算变动率

                # 应纳税额变动率
                if annual_data[prev_year]["tax_amount"] != 0:
                    annual_data[year]["tax_amount_rate"] = (
                        annual_data[year]["tax_amount"]
                        - annual_data[prev_year]["tax_amount"]
                    ) / annual_data[prev_year]["tax_amount"]
                else:
                    annual_data[year]["tax_amount"] = None  # 上一年应纳税额为 0，无法计算变动率
            else:
                # 第一年没有上一年数据，变动率为 null
                annual_data[year]["period_amount_rate"] = None
                annual_data[year]["tax_amount_rate"] = None

            # 计算弹性系数
            sales_growth_rate = annual_data[year]["period_amount_rate"]
            tax_growth_rate = annual_data[year]["tax_amount_rate"]
            if (
                sales_growth_rate is not None
                and tax_growth_rate is not None
                and tax_growth_rate != 0
            ):
                annual_data[year]["elastic_modulus"] = (
                    sales_growth_rate / tax_growth_rate
                )
            else:
                annual_data[year]["elastic_modulus"] = None  # 无法计算弹性系数

            # 格式化
            if (
                annual_data[year]["period_amount"]
                or annual_data[year]["income_amount"]
                or annual_data[year]["tax_amount"]
            ):
                all_zero = False
            formatted_period_amount = (
                "{:,.2f}".format(annual_data[year]["period_amount"], 2)
                if annual_data[year]["period_amount"]
                else "0.00"
            )
            formatted_income_amount = (
                "{:,.2f}".format(annual_data[year]["income_amount"], 2)
                if annual_data[year]["income_amount"]
                else "0.00"
            )
            formatted_tax_amount = (
                "{:,.2f}".format(annual_data[year]["tax_amount"], 2)
                if annual_data[year]["tax_amount"]
                else "0.00"
            )
            formatted_period_amount_rate = (
                "{:,.2f}".format(annual_data[year]["period_amount_rate"] * 100, 2) + "%"
                if annual_data[year]["period_amount_rate"]
                else "--"
            )
            formatted_tax_amount_rate = (
                "{:,.2f}".format(annual_data[year]["tax_amount_rate"] * 100, 2) + "%"
                if annual_data[year]["tax_amount_rate"]
                else "--"
            )
            formatted_elastic_modulus = (
                "{:,.2f}".format(annual_data[year]["elastic_modulus"] * 100, 2) + "%"
                if annual_data[year]["elastic_modulus"]
                else "--"
            )
            result.append(
                {
                    "所属期": year + "年",
                    "期间费用（元）": formatted_period_amount,
                    "营业收入（元）": formatted_income_amount,
                    "实际应纳所得税额（元）": formatted_tax_amount,
                    "期间费用变动率": formatted_period_amount_rate,
                    "所得税贡献变动率": formatted_tax_amount_rate,
                    "期间费用与企业所得税贡献变动系数": formatted_elastic_modulus,
                }
            )

            # 检查是否存在风险
            if (
                annual_data[year]["elastic_modulus"] is not None
                and abs(annual_data[year]["elastic_modulus"]) > threshold
            ):
                risk_years.append(
                    {
                        "所属期": year + "年",
                        "期间费用（元）": formatted_period_amount,
                        "营业收入（元）": formatted_income_amount,
                        "实际应纳所得税额（元）": formatted_tax_amount,
                        "期间费用变动率": formatted_period_amount_rate,
                        "所得税贡献变动率": formatted_tax_amount_rate,
                        "期间费用与企业所得税贡献变动系数": formatted_elastic_modulus,
                    }
                )

        if all_zero:
            result = [
                "所属期",
                "期间费用（元）",
                "营业收入(元)",
                "实际应纳所得税额（元）",
                "期间费用变动率",
                "所得税贡献变动率",
                "期间费用与企业所得税贡献变动系数",
            ]

        if risk_years:
            # 风险条件
            formatted_threshold = "{:,.2f}".format(threshold * 100, 2) + "%"
            risk_desc = "风险描述："
            risk_desc += ",".join(
                [
                    f"{item['所属期']}的期间费用变动率与所得税贡献变动率为{item['期间费用与企业所得税贡献变动系数']}"
                    for item in risk_years
                ]
            )
            risk_desc += f"超过±{formatted_threshold}。期间费用与所得税贡献变动不匹配可能表明费用核算不合理、税务筹划不当或利润操纵行为，需核实期间费用的真实性及税务申报的合规性。"
        else:
            risk_desc = "风险描述：该指标项未检测到风险"
        return result, risk_desc

    # 7.2
    def expense_admin_analyzer(self, dt, data, threshold=0.15):
        # 7.2 管理费用同比检查
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            # 税号没对齐
            default_result = ["所属期", "管理费用", "营业收入(元)", "管理费用与营业收入比值变动值"]
            risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        detail_data = defaultdict(lambda: [])
        # 遍历corporateIncome数据
        income_records = data["data"]["corporateIncome"]  # # corporateIncome 表示所得税

        for record in income_records:
            dt_end_year = datetime.strptime(
                record["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if record["invalidMark"] == "Y":
                continue
            # if dt_end != dt_year + '1231': continue年度申报表，不需要做年份时间的过滤了。
            # 检查是否为年度纳税申报表
            if record["levyProjectName"] == "《中华人民共和国企业所得税年度纳税申报表（A类）》（A100000）":
                amount = float(record["accumulativeAmount"] or 0)
                # 提取营业收入
                if record["projectName"] == "一、营业收入（填写A101010/101020/103000）":
                    detail_data[dt_end_year].append(
                        {
                            "amount": amount,
                            "project_name": "营业收入",
                            "change_type": record["changeType"],
                        }
                    )
                # 提取管理费用
                if record["projectName"] == "减：管理费用（填写A104000）":
                    detail_data[dt_end_year].append(
                        {
                            "amount": amount,
                            "project_name": "管理费用",
                            "change_type": record["changeType"],
                        }
                    )

        # 添加风险阈值常量
        def filter_detail_data(detail_data):
            # 存储 (dt_start, dt_end, source_type, project_name) -> 最佳记录
            best_records = {}
            # 遍历所有日期范围和记录
            for date_range, records in detail_data.items():
                for record in records:
                    key = (date_range, record["project_name"])
                    current_change_type = record["change_type"]
                    # 如果当前记录是 "更正后全量模式"，直接覆盖
                    if current_change_type == "更正后新产生的申报表（全量模式）":
                        best_records[key] = record
                    # 否则，仅当 key 不存在时才保留
                    elif key not in best_records:
                        best_records[key] = record

            # 重新构建 detail_data（按原时间分组）
            new_detail_data = {}
            for key, record in best_records.items():
                date_range, _ = key
                if date_range not in new_detail_data:
                    new_detail_data[date_range] = []
                new_detail_data[date_range].append(record)
            return new_detail_data

        new_detail_data = filter_detail_data(detail_data)
        annual_data = defaultdict(
            lambda: {"manage_amount": 0.00, "income_amount": 0.00}
        )
        # 企业所得税年报汇总：
        for dt_year, value in new_detail_data.items():
            for item in value:
                if item["project_name"] == "管理费用":
                    # 管理费用
                    annual_data[dt_year]["manage_amount"] += item["amount"]
                elif item["project_name"] == "营业收入":
                    annual_data[dt_year]["income_amount"] += item["amount"]

        sorted_years = sorted(annual_data.keys(), reverse=False)

        # 添加风险年份记录
        risk_years = {}
        all_zero = True
        result = []

        # 计算管理费用与营业收入比值变动值
        for i, year in enumerate(sorted_years):
            if i > 0:  # 从第二年开始计算变动率
                prev_year = sorted_years[i - 1]
                # 管理费用与营业收入比值变动值
                if annual_data[prev_year]["manage_amount"] != 0:
                    annual_data[year]["change_rate"] = (
                        annual_data[year]["manage_amount"]
                        / annual_data[year]["income_amount"]
                        - annual_data[prev_year]["manage_amount"]
                        / annual_data[prev_year]["income_amount"]
                    )
                else:
                    annual_data[year]["change_rate"] = None  # 上一年营业收入为 0，无法计算变动率
            else:
                # 第一年没有上一年数据，变动率为 null
                annual_data[year]["change_rate"] = None
                # 格式化
            if annual_data[year]["manage_amount"] or annual_data[year]["income_amount"]:
                all_zero = False
            formatted_manage_amount = (
                "{:,.2f}".format(annual_data[year]["manage_amount"], 2)
                if annual_data[year]["manage_amount"]
                else "0.00"
            )
            formatted_income_amount = (
                "{:,.2f}".format(annual_data[year]["income_amount"], 2)
                if annual_data[year]["income_amount"]
                else "0.00"
            )
            formatted_change_rate = (
                "{:,.2f}".format(annual_data[year]["change_rate"] * 100, 2) + "%"
                if annual_data[year]["change_rate"]
                else "--"
            )

            result.append(
                {
                    "所属期": year + "年",
                    "管理费用（元）": formatted_manage_amount,
                    "营业收入（元）": formatted_income_amount,
                    "管理费用与营业收入比值变动值": formatted_change_rate,
                }
            )

            # 检查是否存在风险
            if (
                annual_data[year]["change_rate"] is not None
                and abs(annual_data[year]["change_rate"]) > threshold
            ):
                risk_years[year].append(
                    {
                        "所属期": year + "年",
                        "管理费用（元）": formatted_manage_amount,
                        "营业收入（元）": formatted_income_amount,
                        "管理费用与营业收入比值变动值（元）": formatted_change_rate,
                    }
                )
        if all_zero:
            result = ["所属期", "管理费用（元）", "营业收入(元)", "管理费用与营业收入比值变动值（元）"]
        formatted_threshold = "{:,.2f}".format(threshold * 100, 2) + "%"
        if risk_years:
            risk_desc = "风险描述："
            risk_desc += ",".join(
                [
                    f"{year}的管理费用与营业收入比值变动值为{round(rate, 2)}"
                    for year, rate in risk_years.items()
                ]
            )
            risk_desc += f"超过±{formatted_threshold}, 管理费用变动过大或占比异常可能反映费用分摊不合理、企业管理效率下降或费用虚增，需进一步核实管理费用的真实性和必要性。"
            return risk_desc
        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return result, risk_desc

    # 7.3
    def expense_sell_analyzer(self, dt, data, threshold=0.2):
        # 7.3 销售费用同比检查
        ## 税号确认
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            default_result = ["所属期", "销售费用（元）", "营业收入（元）", "销售费用与营业收入比值变动值"]
            default_risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, default_risk_desc

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")

        """step1: 增值税"""
        value_added = data["data"]["valueAdded"]  # valueAdded 表示增值税
        detail_data_add = defaultdict(lambda: [])

        # 小规模->一般纳税人的时间节点...
        small_end_date = max(
            v["endDate"]
            for v in value_added
            if v.get("levyProjectName") == "《增值税及附加税费申报表（小规模纳税人适用）》"
        )
        small_end = datetime.strptime(small_end_date, "%Y-%m-%d %H:%M:%S").strftime(
            "%Y%m%d"
        )
        for value in value_added:
            if value["invalidMark"] == "Y":
                continue
            dt_start = datetime.strptime(
                value["beginDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y%m%d")
            dt_end = datetime.strptime(value["endDate"], "%Y-%m-%d %H:%M:%S").strftime(
                "%Y%m%d"
            )
            dt_end_year = datetime.strptime(
                value["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if value["levyProjectName"] == "《增值税及附加税费申报表（一般纳税人适用）》":
                if dt_end != dt_end_year + "1231":
                    continue
                # 即征即退项目-本年累计
                immed_amount = (
                    float(value.get("immediateRetreatYearAccumulativeAmount", 0.0))
                    if value.get("immediateRetreatYearAccumulativeAmount", 0.0) != None
                    else 0.0
                )
                # 一般项目-本年累计
                general_amount = (
                    float(value.get("generalYearAccumulativeAmount", 0.0))
                    if value.get("generalYearAccumulativeAmount", 0.0) != None
                    else 0.0
                )

                if value["projectName"] in [
                    "（一）按适用税率计税销售额",
                    "（二）按简易办法计税销售额",
                    "（三）免、抵、退办法出口销售额",
                    "（四）免税销售额",
                ]:
                    detail_data_add[dt_end_year].append(
                        {
                            "source_type": "general",
                            "amount": immed_amount + general_amount,
                            "project_name": value["projectName"],
                            "change_type": value["changeType"],
                        }
                    )
                else:
                    continue
            elif value["levyProjectName"] == "《增值税及附加税费申报表（小规模纳税人适用）》":
                if dt_end != small_end:
                    continue
                # 本年累计-货物及劳务
                immed_amount = (
                    float(value.get("currentYearAccumulativeGoods", 0.0))
                    if value.get("currentYearAccumulativeGoods", 0.0) != None
                    else 0.0
                )
                # 本年累计-服务不动产和无形资产
                general_amount = (
                    float(value.get("currentYearAccumulativeService", 0.0))
                    if value.get("currentYearAccumulativeService", 0.0) != None
                    else 0.0
                )

                if value["projectName"] in [
                    "（一）应征增值税不含税销售额（3%征收率）",
                    "（二）应征增值税不含税销售额（5%征收率）",
                    "（三）销售使用过的固定资产不含税销售额",
                    "（四）免税销售额",
                    "（五）出口免税销售额",
                ]:
                    detail_data_add[dt_end_year].append(
                        {
                            "source_type": "small",
                            "amount": immed_amount + general_amount,
                            "project_name": value["projectName"],
                            "change_type": value["changeType"],
                        }
                    )

                else:
                    continue

        """step2: 企业所得税"""
        income_records = data["data"]["corporateIncome"]  # # corporateIncome 表示所得税
        detail_data_income = defaultdict(lambda: [])
        for record in income_records:
            dt_end_year = datetime.strptime(
                record["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if record["invalidMark"] == "Y":
                continue
            # if dt_end != dt_year + '1231': continue
            # 检查是否为年度纳税申报表
            if record["levyProjectName"] == "《中华人民共和国企业所得税年度纳税申报表（A类）》（A100000）":
                amount = float(record["accumulativeAmount"] or 0)
                # 提取营业收入
                if record["projectName"] == "一、营业收入（填写A101010/101020/103000）":
                    detail_data_income[dt_end_year].append(
                        {
                            "amount": amount,
                            "project_name": "营业收入",
                            "change_type": record["changeType"],
                        }
                    )

        def filter_detail_data(detail_data):
            # 存储 (dt_start, dt_end, source_type, project_name) -> 最佳记录
            best_records = {}
            # 遍历所有日期范围和记录
            for date_range, records in detail_data.items():
                for record in records:
                    key = (date_range, record["project_name"])
                    current_change_type = record["change_type"]
                    # 如果当前记录是 "更正后全量模式"，直接覆盖
                    if current_change_type == "更正后新产生的申报表（全量模式）":
                        best_records[key] = record
                    # 否则，仅当 key 不存在时才保留
                    elif key not in best_records:
                        best_records[key] = record

            # 重新构建 detail_data（按原时间分组）
            new_detail_data = {}
            for key, record in best_records.items():
                date_range, _ = key
                if date_range not in new_detail_data:
                    new_detail_data[date_range] = []
                new_detail_data[date_range].append(record)
            return new_detail_data

        new_detail_data_add = filter_detail_data(detail_data_add)
        new_detail_data_income = filter_detail_data(detail_data_income)

        annual_data = defaultdict(lambda: {"sell_amount": 0.00, "income_amount": 0.00})
        # 增值税聚合：
        for dt_year, value in new_detail_data_add.items():
            for item in value:
                annual_data[dt_year]["sell_amount"] += item["amount"]
        # 企业所得税聚合：
        for dt_year, value in new_detail_data_income.items():
            for item in value:
                annual_data[dt_year]["income_amount"] += item["amount"]

        all_zero = True  # 标记是否所有应纳税所得额和营业收入都是00
        # 计算差额并格式化输出
        result = []
        risks = []

        #
        #
        # def generate_risk_description(risk_years):
        #     """生成风险描述"""
        #     if not risk_years:
        #         return "风险描述：该指标项未检测到风险"
        #
        #     risk_desc = "风险描述："
        #     for year, rate in risk_years.items():
        #         risk_desc += f"{year}年的销售费用与营业收入比值变动值为{round(rate, 2)}，超过±20%，"
        #     risk_desc += "销售费用与营业收入变动不匹配可能表明费用虚增或销售策略调整无效，需关注费用的合理性及其对收入增长的作用。"
        #     return risk_desc
        risk_years = []
        sorted_years = sorted(annual_data.keys(), reverse=False)
        # 计算销售费用与营业收入比值变动值
        for i, year in enumerate(sorted_years):
            if i > 0:  # 从第二年开始计算变动率
                prev_year = sorted_years[i - 1]
                # 销售费用与营业收入比值变动值
                if annual_data[prev_year]["income_amount"] != 0:
                    annual_data[year]["change_rate"] = (
                        annual_data[year]["sell_amount"]
                        / annual_data[year]["income_amount"]
                        - annual_data[prev_year]["sell_amount"]
                        / annual_data[prev_year]["income_amount"]
                    )
                else:
                    annual_data[year]["change_rate"] = None  # 上一年营业收入为 0，无法计算变动率
            else:
                # 第一年没有上一年数据，变动率为 null
                annual_data[year]["change_rate"] = None

            if annual_data[year]["income_amount"] or annual_data[year]["sell_amount"]:
                all_zero = False
            formatted_sell_amount = (
                "{:,.2f}".format(annual_data[year]["sell_amount"], 2)
                if annual_data[year]["sell_amount"]
                else "0.00"
            )
            formatted_income_amount = (
                "{:,.2f}".format(annual_data[year]["income_amount"], 2)
                if annual_data[year]["income_amount"]
                else "0.00"
            )
            formatted_change_rate = (
                "{:,.2f}".format(annual_data[year]["change_rate"] * 100, 2) + "%"
                if annual_data[year]["change_rate"]
                else "--"
            )

            result.append(
                {
                    "所属期": year + "年",
                    "销售费用（元）": formatted_sell_amount,
                    "营业收入（元）": formatted_income_amount,
                    "销售费用与营业收入比值变动值": formatted_change_rate,
                }
            )
            if (
                annual_data[year]["change_rate"] is not None
                and abs(annual_data[year]["change_rate"]) > threshold
            ):
                risk_years[year].append(
                    {
                        "所属期": year + "年",
                        "销售费用（元）": formatted_sell_amount,
                        "营业收入（元）": formatted_income_amount,
                        "销售费用与营业收入比值变动值": formatted_change_rate,
                    }
                )

        formatted_threshold = "{:,.2f}".format(threshold * 100, 2) + "%"
        if all_zero:
            result = ["所属期", "销售费用（元）", "营业收入（元）", "销售费用与营业收入比值变动值"]
        if risk_years:
            risk_desc = "风险描述："

            risk_desc += ",".join(
                [
                    f"{year}的销售费用与营业收入比值变动值为{round(rate, 2)}"
                    for year, rate in risk_years.items()
                ]
            )
            risk_desc += f"超过±{formatted_threshold}, 销售费用与营业收入变动不匹配可能表明费用虚增或销售策略调整无效，需关注费用的合理性及其对收入增长的作用。"
            return risk_desc
        else:
            risk_desc = "风险描述：该指标项未检测到风险"
        return result, risk_desc

    # 7.4
    def expense_fin_analyzer(self, dt, data, threshold=0.25):
        # 7.4 财务费用同比检查
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            # 税号没对齐
            default_result = ["所属期", "财务费用", "营业收入(元)", "财务费用与营业收入比值变动值"]
            risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        detail_data = defaultdict(lambda: [])
        # 遍历corporateIncome数据
        income_records = data["data"]["corporateIncome"]  # # corporateIncome 表示所得税

        for record in income_records:
            dt_end_year = datetime.strptime(
                record["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if record["invalidMark"] == "Y":
                continue
            # if dt_end != dt_year + '1231': continue年度申报表，不需要做年份时间的过滤了。
            # 检查是否为年度纳税申报表
            if record["levyProjectName"] == "《中华人民共和国企业所得税年度纳税申报表（A类）》（A100000）":
                amount = float(record["accumulativeAmount"] or 0)
                # 提取营业收入
                if record["projectName"] == "一、营业收入（填写A101010/101020/103000）":
                    detail_data[dt_end_year].append(
                        {
                            "amount": amount,
                            "project_name": "营业收入",
                            "change_type": record["changeType"],
                        }
                    )
                # 提取管理费用
                if record["projectName"] == "减：财务费用（填写A104000）":
                    detail_data[dt_end_year].append(
                        {
                            "amount": amount,
                            "project_name": "财务费用",
                            "change_type": record["changeType"],
                        }
                    )

        # 添加风险阈值常量
        def filter_detail_data(detail_data):
            # 存储 (dt_start, dt_end, source_type, project_name) -> 最佳记录
            best_records = {}
            # 遍历所有日期范围和记录
            for date_range, records in detail_data.items():
                for record in records:
                    key = (date_range, record["project_name"])
                    current_change_type = record["change_type"]
                    # 如果当前记录是 "更正后全量模式"，直接覆盖
                    if current_change_type == "更正后新产生的申报表（全量模式）":
                        best_records[key] = record
                    # 否则，仅当 key 不存在时才保留
                    elif key not in best_records:
                        best_records[key] = record

            # 重新构建 detail_data（按原时间分组）
            new_detail_data = {}
            for key, record in best_records.items():
                date_range, _ = key
                if date_range not in new_detail_data:
                    new_detail_data[date_range] = []
                new_detail_data[date_range].append(record)
            return new_detail_data

        new_detail_data = filter_detail_data(detail_data)
        annual_data = defaultdict(
            lambda: {"finance_amount": 0.00, "income_amount": 0.00}
        )
        # 企业所得税年报汇总：
        for dt_year, value in new_detail_data.items():
            for item in value:
                if item["project_name"] == "财务费用":
                    # 管理费用
                    annual_data[dt_year]["finance_amount"] += item["amount"]
                elif item["project_name"] == "营业收入":
                    annual_data[dt_year]["income_amount"] += item["amount"]

        sorted_years = sorted(annual_data.keys(), reverse=False)

        # 添加风险年份记录
        risk_years = {}
        all_zero = True
        result = []

        # 计算管理费用与营业收入比值变动值
        for i, year in enumerate(sorted_years):
            if i > 0:  # 从第二年开始计算变动率
                prev_year = sorted_years[i - 1]
                # 管理费用与营业收入比值变动值
                if annual_data[prev_year]["finance_amount"] != 0:
                    annual_data[year]["change_rate"] = (
                        annual_data[year]["finance_amount"]
                        / annual_data[year]["income_amount"]
                        - annual_data[prev_year]["finance_amount"]
                        / annual_data[prev_year]["income_amount"]
                    )
                else:
                    annual_data[year]["change_rate"] = None  # 上一年营业收入为 0，无法计算变动率
            else:
                # 第一年没有上一年数据，变动率为 null
                annual_data[year]["change_rate"] = None
                # 格式化
            if (
                annual_data[year]["finance_amount"]
                or annual_data[year]["income_amount"]
            ):
                all_zero = False
            formatted_finance_amount = (
                "{:,.2f}".format(annual_data[year]["finance_amount"], 2)
                if annual_data[year]["finance_amount"]
                else "0.00"
            )
            formatted_income_amount = (
                "{:,.2f}".format(annual_data[year]["income_amount"], 2)
                if annual_data[year]["income_amount"]
                else "0.00"
            )
            formatted_change_rate = (
                "{:,.2f}".format(annual_data[year]["change_rate"] * 100, 2) + "%"
                if annual_data[year]["change_rate"]
                else "--"
            )

            result.append(
                {
                    "所属期": year + "年",
                    "财务费用（元）": formatted_finance_amount,
                    "营业收入（元）": formatted_income_amount,
                    "财务费用与营业收入比值变动值": formatted_change_rate,
                }
            )

            # 检查是否存在风险
            if (
                annual_data[year]["change_rate"] is not None
                and abs(annual_data[year]["change_rate"]) > threshold
            ):
                risk_years[year].append(
                    {
                        "所属期": year + "年",
                        "财务费用（元）": formatted_finance_amount,
                        "营业收入（元）": formatted_income_amount,
                        "管理费用与营业收入比值变动值（元）": formatted_change_rate,
                    }
                )
        if all_zero:
            result = ["所属期", "财务费用（元）", "营业收入(元)", "财务费用与营业收入比值变动值（元）"]
        formatted_threshold = "{:,.2f}".format(threshold * 100, 2) + "%"
        if risk_years:
            risk_desc = "风险描述："
            risk_desc += ",".join(
                [
                    f"{year}的财务费用与营业收入比值变动值为{round(rate, 2)}"
                    for year, rate in risk_years.items()
                ]
            )
            risk_desc += f"超过±{formatted_threshold}, 财务费用变动过大可能反映企业融资成本异常、借贷规模不合理或财务费用核算不准确，需重点关注企业的资金使用及财务管理状况。"
            return risk_desc
        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return result, risk_desc

    # 7.5
    def union_fund_analyzer(self, data):
        # 提取financeProfit数据
        profits = data["data"]["financeProfit"]
        income_records = data["data"]["financeBalance"]

        # 初始化一个字典来存储每年的营业收入和应纳所得税额
        annual_data = {}

        # 添加风险年份记录
        risk_years = {}

        def generate_risk_description(risk_years):
            """生成风险描述"""
            if not risk_years:
                return "风险描述：该指标项未检测到风险"

            desc = "风险描述："
            for year, ratio in risk_years.items():
                desc += f"{year}年的工会经费支出占工资薪金支出的{ratio}%，超过2%，"
            desc += "工会经费超支可能导致无法足额税前扣除，增加企业税负，也可能反映工会经费管理不规范，需关注支出是否合规。"
            return desc

        for profit in profits:
            if profit["period"] == "Year":
                year = profit["endDate"][:4]

                # 提取财务费用
                if profit["projectName"] == "管理费用":
                    expense_admin = float(profit["currentYearAccumulativeAmount"] or 0)
                    if year not in annual_data:
                        annual_data[year] = {
                            "revenue": 0,
                            "expense_admin": expense_admin,
                        }
                    else:
                        annual_data[year]["expense_admin"] = expense_admin

        # 遍历financeProfit数据
        for record in income_records:
            # 检查是否为年度财报表
            if record["period"] == "Year":
                # 提取年份
                year = record["endDate"][:4]  # 从endDate中提取年份

                # 提取营业收入
                if record["projectName"] == "应付职工薪酬":
                    revenue = float(record["endingBalance"] or 0)
                    if year not in annual_data:
                        annual_data[year] = {"revenue": revenue, "expense_admin": 0}
                    else:
                        annual_data[year]["revenue"] = revenue

                # 计算超支比例
                salary = annual_data[year]["expense_admin"]
                if salary > 0:
                    ratio = (annual_data[year]["revenue"] / salary) * 100
                    if ratio > 2:
                        risk_years[year] = round(ratio, 2)

        sorted_years = sorted(annual_data.keys(), reverse=False)

        # 格式化输出
        result = []
        for year in sorted_years:
            result.append(
                {
                    "所属期": year,
                    "工会经费支出（元）": round(annual_data[year]["expense_admin"], 2),
                    "工资薪金支出税收金额（元）": round(annual_data[year]["revenue"], 2),
                }
            )

        risk_description = generate_risk_description(risk_years)

        return result, risk_description

    # 7.6
    def employee_welfare_fund_analyzer(self, data):
        # 提取financeProfit数据
        profits = data["data"]["financeProfit"]
        income_records = data["data"]["financeBalance"]

        # 初始化一个字典来存储每年的营业收入和应纳所得税额
        annual_data = {}

        # 添加风险年份记录
        risk_years = {}

        def generate_risk_description(risk_years):
            """生成风险描述"""
            if not risk_years:
                return "风险描述：该指标项未检测到风险"

            desc = "风险描述："
            for year, ratio in risk_years.items():
                desc += f"{year}年的职工福利费支出占工资薪金支出的{ratio}%，超过14%，"
            desc += "职工福利费超支可能导致税前扣除受限，同时反映企业可能存在隐性福利或虚列费用，需核实福利费支出的真实性与合规性。"
            return desc

        for profit in profits:
            if profit["period"] == "Year":
                year = profit["endDate"][:4]

                # 提取财务费用
                if profit["projectName"] == "管理费用":
                    expense_admin = float(profit["currentYearAccumulativeAmount"] or 0)
                    if year not in annual_data:
                        annual_data[year] = {
                            "revenue": 0,
                            "expense_admin": expense_admin,
                        }
                    else:
                        annual_data[year]["expense_admin"] = expense_admin

        # 遍历financeProfit数据
        for record in income_records:
            # 检查是否为年度财报表
            if record["period"] == "Year":
                # 提取年份
                year = record["endDate"][:4]  # 从endDate中提取年份

                # 提取营业收入
                if record["projectName"] == "应付职工薪酬":
                    revenue = float(record["endingBalance"] or 0)
                    if year not in annual_data:
                        annual_data[year] = {"revenue": revenue, "expense_admin": 0}
                    else:
                        annual_data[year]["revenue"] = revenue

                # 计算超支比例
                salary = annual_data[year]["expense_admin"]
                if salary > 0:
                    ratio = (annual_data[year]["revenue"] / salary) * 100
                    if ratio > 14:
                        risk_years[year] = round(ratio, 2)

        sorted_years = sorted(annual_data.keys(), reverse=False)

        # 格式化输出
        result = []
        for year in sorted_years:
            ratio = (
                annual_data[year]["revenue"] / annual_data[year]["expense_admin"]
                if annual_data[year]["expense_admin"]
                else 0
            )

            result.append(
                {
                    "所属期": year,
                    "职工福利费支出（元）": round(annual_data[year]["expense_admin"], 2),
                    "工资薪金支出税收金额（元）": round(annual_data[year]["revenue"], 2),
                    "职工福利费占工资薪金支出税收占比": f"{round(ratio, 2) * 100}%",
                }
            )

        risk_description = generate_risk_description(risk_years)

        return result, risk_description

    # 7.7
    def advertising_expenses_analyzer(self, data):
        # 提取financeProfit数据
        profits = data["data"]["financeProfit"]
        income_records = data["data"]["financeBalance"]

        # 初始化一个字典来存储每年的营业收入和应纳所得税额
        annual_data = {}

        # 添加风险年份记录
        risk_years = {}

        def generate_risk_description(risk_years):
            """生成风险描述"""
            if not risk_years:
                return "风险描述：该指标项未检测到风险"

            desc = "风险描述："
            for year, ratio in risk_years.items():
                desc += f"{year}的广宣费支出占营业收入的{ratio}%，超过15%，"
            desc += "广宣费超支可能导致部分支出无法税前扣除，同时可能存在广宣费用虚列或广告投入转化率较低的问题，需核查广宣费支出的合理性和真实性。"
            return desc

        for profit in profits:
            if profit["period"] == "Year":
                year = profit["endDate"][:4]

                # 提取财务费用
                if profit["projectName"] == "销售费用":
                    expense_admin = float(profit["currentYearAccumulativeAmount"] or 0)
                    if year not in annual_data:
                        annual_data[year] = {
                            "revenue": 0,
                            "expense_admin": expense_admin,
                        }
                    else:
                        annual_data[year]["expense_admin"] = expense_admin

                # 提取营业收入
                if profit["projectName"] == "一、营业收入":
                    revenue = float(profit["currentYearAccumulativeAmount"] or 0)
                    if year not in annual_data:
                        annual_data[year] = {"revenue": revenue, "expense_admin": 0}
                    else:
                        annual_data[year]["revenue"] = revenue

                # 计算超支比例
                salary = (
                    annual_data[year]["expense_admin"] if year in annual_data else 0
                )
                if salary > 0:
                    ratio = (annual_data[year]["revenue"] / salary) * 100
                    if ratio > 15:
                        risk_years[year] = round(ratio, 2)

        sorted_years = sorted(annual_data.keys(), reverse=False)

        # 格式化输出
        result = []
        for year in sorted_years:
            result.append(
                {
                    "所属期": year,
                    "广宣费支出（元）": round(annual_data[year]["expense_admin"], 2),
                    "营业收入（元）": round(annual_data[year]["revenue"], 2),
                }
            )

        risk_description = generate_risk_description(risk_years)

        return result, risk_description

    # 7.8
    def expense_revenue_analyzer(self, dt, data, threshold=0.1):
        # 7.8 营业成本占营业收入比例变动检查
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            # 税号没对齐
            default_result = [
                "所属期",
                "营业收入(元)",
                "营业成本（元）",
                "营业成本占营业收入比例",
                "财务费用与营业收入比值变动值",
            ]
            risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        detail_data = defaultdict(lambda: [])
        # 遍历corporateIncome数据
        income_records = data["data"]["corporateIncome"]  # # corporateIncome 表示所得税

        for record in income_records:
            dt_end_year = datetime.strptime(
                record["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if record["invalidMark"] == "Y":
                continue
            # if dt_end != dt_year + '1231': continue年度申报表，不需要做年份时间的过滤了。
            # 检查是否为年度纳税申报表
            if record["levyProjectName"] == "《中华人民共和国企业所得税年度纳税申报表（A类）》（A100000）":
                amount = float(record["accumulativeAmount"] or 0)
                # 提取营业收入
                if record["projectName"] == "一、营业收入（填写A101010/101020/103000）":
                    detail_data[dt_end_year].append(
                        {
                            "amount": amount,
                            "project_name": "营业收入",
                            "change_type": record["changeType"],
                        }
                    )
                # 提取管理费用
                if record["projectName"] == "减：营业成本（填写A102010/102020/103000）":
                    detail_data[dt_end_year].append(
                        {
                            "amount": amount,
                            "project_name": "营业成本",
                            "change_type": record["changeType"],
                        }
                    )

        # 添加风险阈值常量
        def filter_detail_data(detail_data):
            # 存储 (dt_start, dt_end, source_type, project_name) -> 最佳记录
            best_records = {}
            # 遍历所有日期范围和记录
            for date_range, records in detail_data.items():
                for record in records:
                    key = (date_range, record["project_name"])
                    current_change_type = record["change_type"]
                    # 如果当前记录是 "更正后全量模式"，直接覆盖
                    if current_change_type == "更正后新产生的申报表（全量模式）":
                        best_records[key] = record
                    # 否则，仅当 key 不存在时才保留
                    elif key not in best_records:
                        best_records[key] = record

            # 重新构建 detail_data（按原时间分组）
            new_detail_data = {}
            for key, record in best_records.items():
                date_range, _ = key
                if date_range not in new_detail_data:
                    new_detail_data[date_range] = []
                new_detail_data[date_range].append(record)
            return new_detail_data

        new_detail_data = filter_detail_data(detail_data)
        annual_data = defaultdict(lambda: {"cost_amount": 0.00, "income_amount": 0.00})
        # 企业所得税年报汇总：
        for dt_year, value in new_detail_data.items():
            for item in value:
                if item["project_name"] == "营业成本":
                    # 营业成本费用
                    annual_data[dt_year]["cost_amount"] += item["amount"]
                elif item["project_name"] == "营业收入":
                    annual_data[dt_year]["income_amount"] += item["amount"]

        sorted_years = sorted(annual_data.keys(), reverse=False)

        # 添加风险年份记录
        risk_years = {}
        all_zero = True
        result = []

        # 计算营业成本占营业收入比例 vs 计算营业成本与营业收入比值变动值
        for i, year in enumerate(sorted_years):
            if annual_data[year]["income_amount"] != 0:
                annual_data[year]["cost_income_rate"] = (
                    annual_data[year]["cost_amount"]
                    / annual_data[year]["income_amount"]
                )
            else:
                annual_data[year]["cost_income_rate"] = None

            if i > 0:  # 从第二年开始计算变动值
                prev_year = sorted_years[i - 1]
                # 营业成本与营业收入比值变动值
                if (
                    annual_data[year]["cost_income_rate"]
                    and annual_data[prev_year]["cost_income_rate"]
                ):
                    annual_data[year]["change_rate"] = (
                        annual_data[year]["cost_income_rate"]
                        - annual_data[prev_year]["cost_income_rate"]
                    )
                else:
                    annual_data[year]["change_rate"] = None  # 上一年营业收入为 0，无法计算变动值
            else:
                # 第一年没有上一年数据，变动率为 null
                annual_data[year]["change_rate"] = None

            # 格式化
            if annual_data[year]["income_amount"] or annual_data[year]["cost_amount"]:
                all_zero = False
            formatted_cost_amount = (
                "{:,.2f}".format(annual_data[year]["cost_amount"], 2)
                if annual_data[year]["cost_amount"]
                else "0.00"
            )
            formatted_income_amount = (
                "{:,.2f}".format(annual_data[year]["income_amount"], 2)
                if annual_data[year]["income_amount"]
                else "0.00"
            )
            formatted_cost_income_rate = (
                "{:,.2f}".format(annual_data[year]["cost_income_rate"] * 100, 2) + "%"
                if annual_data[year]["cost_income_rate"]
                else "--"
            )
            formatted_change_rate = (
                "{:,.2f}".format(annual_data[year]["change_rate"] * 100, 2) + "%"
                if annual_data[year]["change_rate"]
                else "--"
            )

            result.append(
                {
                    "所属期": year + "年",
                    "营业收入（元）": formatted_income_amount,
                    "营业成本（元）": formatted_cost_amount,
                    "营业成本占营业收入比例": formatted_cost_income_rate,
                    "营业成本占营业收入比例变动值": formatted_change_rate,
                }
            )

            # 检查是否存在风险
            if (
                annual_data[year]["change_rate"] is not None
                and abs(annual_data[year]["change_rate"]) > threshold
            ):
                risk_years[year].append(
                    {
                        "所属期": year + "年",
                        "营业收入（元）": formatted_income_amount,
                        "营业成本（元）": formatted_cost_amount,
                        "营业成本占营业收入比例": formatted_cost_income_rate,
                        "营业成本占营业收入比例变动值": formatted_change_rate,
                    }
                )
        if all_zero:
            result = ["所属期", "营业收入(元)", "营业成本（元）", "营业成本占营业收入比例", "财务费用与营业收入比值变动值"]

        formatted_threshold = "{:,.2f}".format(threshold * 100, 2) + "%"
        if risk_years:
            risk_desc = "风险描述："
            risk_desc += ",".join(
                [
                    f"{year}年的营业成本占营业收入比例变动值为{round(rate, 2)}"
                    for year, rate in risk_years.items()
                ]
            )
            risk_desc += f"超过±{formatted_threshold}。营业成本占收入比例波动较大可能反映成本核算或收入确认异常，需关注企业成本管理是否有效，以及收入与成本数据的匹配性。"

        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return result, risk_desc

    # 7.9
    def calculate_expense_revenue_elastic_modulus(
        self, dt, data, threshold1=0.8, threshold2=1.2
    ):
        # 7.9 营业成本与营业收入弹性系数检查
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            # 税号没对齐
            default_result = [
                "所属期",
                "营业收入(元)",
                "营业成本（元）",
                "营业收入变动率",
                "营业成本变动率",
                "营业成本与营业收入变动系数",
            ]
            risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        detail_data = defaultdict(lambda: [])
        # 遍历corporateIncome数据
        income_records = data["data"]["corporateIncome"]  # # corporateIncome 表示所得税

        for record in income_records:
            dt_end_year = datetime.strptime(
                record["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if record["invalidMark"] == "Y":
                continue
            # if dt_end != dt_year + '1231': continue年度申报表，不需要做年份时间的过滤了。
            # 检查是否为年度纳税申报表
            if record["levyProjectName"] == "《中华人民共和国企业所得税年度纳税申报表（A类）》（A100000）":
                amount = float(record["accumulativeAmount"] or 0)
                # 提取营业收入
                if record["projectName"] == "一、营业收入（填写A101010/101020/103000）":
                    detail_data[dt_end_year].append(
                        {
                            "amount": amount,
                            "project_name": "营业收入",
                            "change_type": record["changeType"],
                        }
                    )
                # 提取管理费用
                if record["projectName"] == "减：营业成本（填写A102010/102020/103000）":
                    detail_data[dt_end_year].append(
                        {
                            "amount": amount,
                            "project_name": "营业成本",
                            "change_type": record["changeType"],
                        }
                    )

        # 添加风险阈值常量
        def filter_detail_data(detail_data):
            # 存储 (dt_start, dt_end, source_type, project_name) -> 最佳记录
            best_records = {}
            # 遍历所有日期范围和记录
            for date_range, records in detail_data.items():
                for record in records:
                    key = (date_range, record["project_name"])
                    current_change_type = record["change_type"]
                    # 如果当前记录是 "更正后全量模式"，直接覆盖
                    if current_change_type == "更正后新产生的申报表（全量模式）":
                        best_records[key] = record
                    # 否则，仅当 key 不存在时才保留
                    elif key not in best_records:
                        best_records[key] = record

            # 重新构建 detail_data（按原时间分组）
            new_detail_data = {}
            for key, record in best_records.items():
                date_range, _ = key
                if date_range not in new_detail_data:
                    new_detail_data[date_range] = []
                new_detail_data[date_range].append(record)
            return new_detail_data

        new_detail_data = filter_detail_data(detail_data)
        annual_data = defaultdict(lambda: {"cost_amount": 0.00, "income_amount": 0.00})
        # 企业所得税年报汇总：
        for dt_year, value in new_detail_data.items():
            for item in value:
                if item["project_name"] == "营业成本":
                    # 营业成本费用
                    annual_data[dt_year]["cost_amount"] += item["amount"]
                elif item["project_name"] == "营业收入":
                    annual_data[dt_year]["income_amount"] += item["amount"]

        sorted_years = sorted(annual_data.keys(), reverse=False)

        # 添加风险年份记录
        risk_years = {}
        all_zero = True
        result = []

        # 计算变动率
        for i, year in enumerate(sorted_years):
            if i > 0:  # 从第二年开始计算变动率
                prev_year = sorted_years[i - 1]
                # 营业收入变动率
                if annual_data[prev_year]["income_amount"] != 0:
                    annual_data[year]["income_growth_rate"] = (
                        annual_data[year]["income_amount"]
                        - annual_data[prev_year]["income_amount"]
                    ) / annual_data[prev_year]["income_amount"]
                else:
                    annual_data[year]["income_growth_rate"] = None  # 上一年营业收入为 0，无法计算变动率

                # 营业成本变动率
                if annual_data[prev_year]["cost_amount"] != 0:
                    annual_data[year]["cost_growth_rate"] = (
                        annual_data[year]["cost_amount"]
                        - annual_data[prev_year]["cost_amount"]
                    ) / annual_data[prev_year]["cost_amount"]
                else:
                    annual_data[year]["cost_growth_rate"] = None  # 上一年营业成本为 0，无法计算变动率
            else:
                # 第一年没有上一年数据，变动率为 null
                annual_data[year]["income_growth_rate"] = None
                annual_data[year]["cost_growth_rate"] = None

            # 计算弹性系数
            income_growth_rate = annual_data[year]["income_growth_rate"]
            cost_growth_rate = annual_data[year]["cost_growth_rate"]
            if (
                income_growth_rate is not None
                and cost_growth_rate is not None
                and cost_growth_rate != 0
            ):
                annual_data[year]["elastic_modulus"] = (
                    income_growth_rate / cost_growth_rate
                )
            else:
                annual_data[year]["elastic_modulus"] = None  # 无法计算弹性系数

            # 格式化
            if annual_data[year]["income_amount"] or annual_data[year]["cost_amount"]:
                all_zero = False
            formatted_cost_amount = (
                "{:,.2f}".format(annual_data[year]["cost_amount"], 2)
                if annual_data[year]["cost_amount"]
                else "0.00"
            )
            formatted_income_amount = (
                "{:,.2f}".format(annual_data[year]["income_amount"], 2)
                if annual_data[year]["income_amount"]
                else "0.00"
            )
            formatted_income_growth_rate = (
                "{:,.2f}".format(annual_data[year]["income_growth_rate"] * 100, 2) + "%"
                if annual_data[year]["income_growth_rate"]
                else "--"
            )
            formatted_cost_growth_rate = (
                "{:,.2f}".format(annual_data[year]["cost_growth_rate"] * 100, 2) + "%"
                if annual_data[year]["cost_growth_rate"]
                else "--"
            )
            formatted_elastic_modulus = (
                "{:,.2f}".format(annual_data[year]["elastic_modulus"], 2)
                if annual_data[year]["elastic_modulus"]
                else "--"
            )

            result.append(
                {
                    "所属期": year + "年",
                    "营业收入（元）": formatted_income_amount,
                    "营业成本（元）": formatted_cost_amount,
                    "营业收入变动率": formatted_income_growth_rate,
                    "营业成本变动率": formatted_cost_growth_rate,
                    "营业成本与营业收入变动系数": formatted_elastic_modulus,
                }
            )

            # 检查是否存在风险
            if annual_data[year]["elastic_modulus"] is not None and (
                annual_data[year]["elastic_modulus"] < threshold1
                or annual_data[year]["elastic_modulus"] > threshold2
            ):
                risk_years[year].append(
                    {
                        "所属期": year + "年",
                        "营业收入（元）": formatted_income_amount,
                        "营业成本（元）": formatted_cost_amount,
                        "营业收入变动率": formatted_income_growth_rate,
                        "营业成本变动率": formatted_cost_growth_rate,
                        "营业成本与营业收入变动系数": formatted_elastic_modulus,
                    }
                )
        if all_zero:
            result = [
                "所属期",
                "营业收入(元)",
                "营业成本（元）",
                "营业收入变动率",
                "营业成本变动率",
                "营业成本与营业收入变动系数",
            ]

        formatted_threshold1 = "{:,.2f}".format(threshold1, 2)
        formatted_threshold2 = "{:,.2f}".format(threshold2, 2)
        if risk_years:
            risk_desc = "风险描述："
            risk_desc += ",".join(
                [
                    f"{year}年的营业成本与营业收入变动系数为{item['营业成本与营业收入变动系数']}"
                    for year, item in risk_years.items()
                ]
            )
            risk_desc += f"不在{formatted_threshold1}-{formatted_threshold2}之间。当营业成本变动与营业收入变动不匹配时，可能反映成本核算不合理或收入真实性存疑，需关注收入确认及成本归集的规范性。"
        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return result, risk_desc

    # 7.10
    def calculate_revenue_period_expense_elastic_modulus(
        self, dt, data, threshold1=0.8, threshold2=1.2
    ):
        # 7.10 营业收入与期间费用弹性系数检查

        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            # 税号没对齐
            default_result = [
                "所属期",
                "营业收入(元)",
                "期间费用（元）",
                "营业收入变动率",
                "期间费用变动率",
                "营业收入与期间费用变动系数",
            ]
            risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        detail_data = defaultdict(lambda: [])
        # 遍历corporateIncome数据
        income_records = data["data"]["corporateIncome"]  # # corporateIncome 表示所得税
        period_name = [
            "减：财务费用（填写A104000）",
            "减：销售费用（填写A104000）",
            "减：管理费用（填写A104000）",
            "减：研发费用（填写A104000）",
        ]
        for record in income_records:
            dt_end_year = datetime.strptime(
                record["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if record["invalidMark"] == "Y":
                continue
            # if dt_end != dt_year + '1231': continue年度申报表，不需要做年份时间的过滤了。
            # 检查是否为年度纳税申报表
            if record["levyProjectName"] == "《中华人民共和国企业所得税年度纳税申报表（A类）》（A100000）":
                amount = float(record["accumulativeAmount"] or 0)
                # 提取营业收入
                if record["projectName"] == "一、营业收入（填写A101010/101020/103000）":
                    detail_data[dt_end_year].append(
                        {
                            "amount": amount,
                            "project_name": "营业收入",
                            "change_type": record["changeType"],
                        }
                    )

                # 提取期间费用

                if record["projectName"] in period_name:
                    detail_data[dt_end_year].append(
                        {
                            "amount": amount,
                            "project_name": record["projectName"],
                            "change_type": record["changeType"],
                        }
                    )

        # 添加风险阈值常量
        def filter_detail_data(detail_data):
            # 存储 (dt_start, dt_end, source_type, project_name) -> 最佳记录
            best_records = {}
            # 遍历所有日期范围和记录
            for date_range, records in detail_data.items():
                for record in records:
                    key = (date_range, record["project_name"])
                    current_change_type = record["change_type"]
                    # 如果当前记录是 "更正后全量模式"，直接覆盖
                    if current_change_type == "更正后新产生的申报表（全量模式）":
                        best_records[key] = record
                    # 否则，仅当 key 不存在时才保留
                    elif key not in best_records:
                        best_records[key] = record

            # 重新构建 detail_data（按原时间分组）
            new_detail_data = {}
            for key, record in best_records.items():
                date_range, _ = key
                if date_range not in new_detail_data:
                    new_detail_data[date_range] = []
                new_detail_data[date_range].append(record)
            return new_detail_data

        new_detail_data = filter_detail_data(detail_data)
        annual_data = defaultdict(
            lambda: {"period_amount": 0.00, "income_amount": 0.00}
        )
        # 企业所得税年报汇总：
        for dt_year, value in new_detail_data.items():
            for item in value:
                if item["project_name"] in period_name:
                    # 计算期间费用（管理费用、销售费用、财务费用）
                    annual_data[dt_year]["period_amount"] += item["amount"]
                elif item["project_name"] == "营业收入":
                    annual_data[dt_year]["income_amount"] += item["amount"]

        sorted_years = sorted(annual_data.keys(), reverse=False)

        result = []
        risk_years = []
        # 嵌套函数：生成风险描述
        for i, year in enumerate(sorted_years):
            if i > 0:  # 从第二年开始计算变动率
                prev_year = sorted_years[i - 1]
                # 期间费用变动率
                if annual_data[prev_year]["period_amount"] != 0:
                    annual_data[year]["period_growth_rate"] = (
                        annual_data[year]["period_amount"]
                        - annual_data[prev_year]["period_amount"]
                    ) / annual_data[prev_year]["period_amount"]
                else:
                    annual_data[year]["period_growth_rate"] = None  # 上一年营业收入为 0，无法计算变动率

                # 营业收入变动率
                if annual_data[prev_year]["income_amount"] != 0:
                    annual_data[year]["income_growth_rate"] = (
                        annual_data[year]["income_amount"]
                        - annual_data[prev_year]["income_amount"]
                    ) / annual_data[prev_year]["income_amount"]
                else:
                    annual_data[year]["income_growth_rate"] = None  # 上一年应纳税额为 0，无法计算变动率
            else:
                # 第一年没有上一年数据，变动率为 null
                annual_data[year]["period_growth_rate"] = None
                annual_data[year]["income_growth_rate"] = None

            # 计算弹性系数
            period_growth_rate = annual_data[year]["period_growth_rate"]
            income_growth_rate = annual_data[year]["income_growth_rate"]
            if income_growth_rate is not None and period_growth_rate != 0:
                annual_data[year]["elastic_modulus"] = (
                    income_growth_rate / period_growth_rate
                )
            else:
                annual_data[year]["elastic_modulus"] = None  # 无法计算弹性系数

            # 格式化
            if annual_data[year]["period_amount"] or annual_data[year]["income_amount"]:
                all_zero = False
            formatted_income_amount = (
                "{:,.2f}".format(annual_data[year]["income_amount"], 2)
                if annual_data[year]["income_amount"]
                else "0.00"
            )
            formatted_period_amount = (
                "{:,.2f}".format(annual_data[year]["period_amount"], 2)
                if annual_data[year]["period_amount"]
                else "0.00"
            )
            formatted_income_growth_rate = (
                "{:,.2f}".format(annual_data[year]["income_growth_rate"] * 100, 2) + "%"
                if annual_data[year]["income_growth_rate"]
                else "--"
            )
            formatted_period_growth_rate = (
                "{:,.2f}".format(annual_data[year]["period_growth_rate"] * 100, 2) + "%"
                if annual_data[year]["period_growth_rate"]
                else "--"
            )
            formatted_elastic_modulus = (
                "{:,.2f}".format(annual_data[year]["elastic_modulus"], 2)
                if annual_data[year]["elastic_modulus"]
                else "--"
            )
            result.append(
                {
                    "所属期": year + "年",
                    "期间费用（元）": formatted_period_amount,
                    "营业收入（元）": formatted_income_amount,
                    "营业收入变动率": formatted_income_growth_rate,
                    "期间费用变动率": formatted_period_growth_rate,
                    "营业收入与期间费用变动系数": formatted_elastic_modulus,
                }
            )

            # 检查是否存在风险
            if annual_data[year]["elastic_modulus"] is not None and (
                annual_data[year]["elastic_modulus"] < threshold1
                or annual_data[year]["elastic_modulus"] > threshold2
            ):
                risk_years.append(
                    {
                        "所属期": year + "年",
                        "期间费用（元）": formatted_period_amount,
                        "营业收入（元）": formatted_income_amount,
                        "营业收入变动率": formatted_income_growth_rate,
                        "期间费用变动率": formatted_period_growth_rate,
                        "营业收入与期间费用变动系数": formatted_elastic_modulus,
                    }
                )
        if all_zero:
            result = [
                "所属期",
                "期间费用（元）",
                "营业收入(元)",
                "实际应纳所得税额（元）",
                "期间费用变动率",
                "所得税贡献变动率",
                "期间费用与企业所得税贡献变动系数",
            ]
        formatted_threshold1 = "{:,.2f}".format(threshold1, 2)
        formatted_threshold2 = "{:,.2f}".format(threshold2, 2)
        if risk_years:
            # 风险条件
            risk_desc = f"风险描述：{company_name}在最近3年内，"
            risk_desc += ",".join(
                [
                    f"{item['所属期']}年的营业收入与期间费用变动系数为{item['营业收入与期间费用变动系数']}"
                    for item in risk_years
                ]
            )
            risk_desc += f"不在{formatted_threshold1}-{formatted_threshold2}范围内。期间费用与营业收入变动不匹配可能表明费用核算不准确或业务扩张与费用控制不当，需核查费用的合理性及其与收入的关联性。"
        else:
            risk_desc = "风险描述：该指标项未检测到风险"
        return result, risk_desc

    # 7.11
    def calculate_tax_income_elastic_modulus(
        self, dt, data, threshold1=0.8, threshold2=1.2
    ):
        # 7.11 纳税调整与营业收入弹性系数

        # 提取corporateIncome数据
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            # 税号没对齐
            default_result = [
                "所属期",
                "营业收入(元)",
                "纳税调整后所得(元)",
                "纳税调整后所得变动率",
                "营业收入变动率",
                "纳税调整与营业收入弹性系数",
            ]
            default_desc = "风险描述：该指标项未检测到风险"
            return default_result, default_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        annual_data = defaultdict(
            lambda: {"revenue_amount": 0.00, "tax_amount": 0.00, "change_type": ""}
        )
        revenue_data = defaultdict(lambda: [])
        tax_data = defaultdict(lambda: [])

        detail_data = defaultdict(lambda: [])
        # 遍历corporateIncome数据
        income_records = data["data"]["corporateIncome"]  # # corporateIncome 表示所得税
        for record in income_records:
            dt_end_year = datetime.strptime(
                record["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if record["invalidMark"] == "Y":
                continue
            # if dt_end != dt_year + '1231': continue
            # 检查是否为年度纳税申报表
            if record["levyProjectName"] == "《中华人民共和国企业所得税年度纳税申报表（A类）》（A100000）":
                amount = float(record["accumulativeAmount"] or 0)
                if record["projectName"] == "一、营业收入（填写A101010/101020/103000）":
                    # 提取营业收入
                    detail_data[dt_end_year].append(
                        {
                            "project_name": "营业收入",
                            "amount": amount,
                            "change_type": record["changeType"],
                        }
                    )
                # 提取纳税调整后所得
                if record["projectName"].__contains__("四、纳税调整后所得"):
                    detail_data[dt_end_year].append(
                        {
                            "project_name": "纳税调整后所得",
                            "amount": amount,
                            "change_type": record["changeType"],
                        }
                    )

        def filter_detail_data(detail_data):
            # 存储 (dt_start, dt_end, source_type, project_name) -> 最佳记录
            best_records = {}

            # 遍历所有日期范围和记录
            for date_range, records in detail_data.items():
                for record in records:
                    key = (date_range, record["project_name"])
                    current_change_type = record["change_type"]
                    # 如果当前记录是 "更正后全量模式"，直接覆盖
                    if current_change_type == "更正后新产生的申报表（全量模式）":
                        best_records[key] = record
                    # 否则，仅当 key 不存在时才保留
                    elif key not in best_records:
                        best_records[key] = record

            # 重新构建 detail_data（按原时间分组）
            new_detail_data = {}
            for key, record in best_records.items():
                date_range, _ = key
                if date_range not in new_detail_data:
                    new_detail_data[date_range] = []
                new_detail_data[date_range].append(record)
            return new_detail_data

        # 取最新数据
        new_detail_data = filter_detail_data(detail_data)
        annual_data = defaultdict(lambda: {"tax_amount": 0.00, "income_amount": 0.00})
        for dt_year, value in new_detail_data.items():
            for item in value:
                if item["project_name"] == "纳税调整后所得":
                    annual_data[dt_year]["tax_amount"] += item["amount"]
                elif item["project_name"] == "营业收入":
                    annual_data[dt_year]["income_amount"] += item["amount"]

        # sorted_data_desc = dict(sorted(annual_data.items(), key=lambda item: item[0], reverse=False))
        sorted_years = sorted(annual_data.keys(), reverse=False)
        all_zero = True
        risk_years = []  # 存储风险年份及相关信息
        result = []  # 记录最终结果
        """计算变动率"""
        for i, year in enumerate(sorted_years):
            if i > 0:  # 从第二年开始计算变动率
                prev_year = sorted_years[i - 1]
                # 营业收入变动率
                if annual_data[prev_year]["income_amount"] != 0:
                    annual_data[year]["income_growth_rate"] = (
                        annual_data[year]["income_amount"]
                        - annual_data[prev_year]["income_amount"]
                    ) / annual_data[prev_year]["income_amount"]
                else:
                    annual_data[year]["income_growth_rate"] = None  # 上一年营业收入为 0，无法计算变动率

                # 纳税调整后所得变动率
                if annual_data[prev_year]["tax_amount"] != 0:
                    annual_data[year]["tax_growth_rate"] = (
                        annual_data[year]["tax_amount"]
                        - annual_data[prev_year]["tax_amount"]
                    ) / annual_data[prev_year]["tax_amount"]
                else:
                    annual_data[year]["tax_growth_rate"] = None  # 上一年纳税调整后所得为 0，无法计算变动率
            else:
                # 第一年没有上一年数据，变动率为 null
                annual_data[year]["income_growth_rate"] = None
                annual_data[year]["tax_growth_rate"] = None

            # 计算弹性系数=纳税调整后所得变动率/营业收入变动率
            income_growth_rate = annual_data[year]["income_growth_rate"]
            tax_growth_rate = annual_data[year]["tax_growth_rate"]
            if (
                income_growth_rate is not None
                and tax_growth_rate is not None
                and tax_growth_rate != 0
            ):
                annual_data[year]["elastic_modulus"] = (
                    income_growth_rate / tax_growth_rate
                )
            else:
                annual_data[year]["elastic_modulus"] = None  # 无法计算弹性系数

            # 格式化：
            if annual_data[year]["tax_amount"] or annual_data[year]["income_amount"]:
                all_zero = False
            formatted_income_amount = (
                "{:,.2f}".format(annual_data[year]["income_amount"], 2)
                if annual_data[year]["income_amount"]
                else "0.00"
            )
            formatted_tax_amount = (
                "{:,.2f}".format(annual_data[year]["tax_amount"], 2)
                if annual_data[year]["tax_amount"]
                else "0.00"
            )
            formatted_income_growth_rate = (
                "{:,.2f}".format(annual_data[year]["income_growth_rate"] * 100, 2) + "%"
                if annual_data[year]["income_growth_rate"]
                else "--"
            )
            formatted_tax_growth_rate = (
                "{:,.2f}".format(annual_data[year]["tax_growth_rate"] * 100, 2) + "%"
                if annual_data[year]["tax_growth_rate"]
                else "--"
            )
            formatted_elastic_modulus = (
                "{:,.2f}".format(annual_data[year]["elastic_modulus"], 2)
                if annual_data[year]["elastic_modulus"]
                else "--"
            )
            result.append(
                {
                    "所属期": year + "年",
                    "营业收入（元）": formatted_income_amount,
                    "纳税调整后所得（元）": formatted_tax_amount,
                    "纳税调整后所得变动率": formatted_tax_growth_rate,
                    "营业收入变动率": formatted_income_growth_rate,
                    "营业收入与期间费用变动系数": formatted_elastic_modulus,
                }
            )
            if annual_data[year]["elastic_modulus"] is not None and (
                annual_data[year]["elastic_modulus"] < threshold1
                or annual_data[year]["elastic_modulus"] > threshold2
            ):
                risk_years[year].append(
                    {
                        "所属期": year + "年",
                        "营业收入（元）": formatted_income_amount,
                        "纳税调整后所得（元）": formatted_tax_amount,
                        "纳税调整后所得变动率": formatted_tax_growth_rate,
                        "营业收入变动率": formatted_income_growth_rate,
                        "营业收入与期间费用变动系数": formatted_elastic_modulus,
                    }
                )

        if all_zero:
            result = [
                "所属期",
                "营业收入(元)",
                "纳税调整后所得(元)",
                "纳税调整后所得变动率",
                "营业收入变动率",
                "纳税调整与营业收入弹性系数",
            ]
        # 风险描述
        formatted_threshold1 = "{:,.2f}".format(threshold1, 2)
        formatted_threshold2 = "{:,.2f}".format(threshold2, 2)
        if risk_years:
            risk_desc = "风险描述："
            risk_desc += ",".join(
                [
                    f"{year}年的纳税调整与营业收入弹性系数为{item['纳税调整与营业收入弹性系数']}"
                    for year, item in risk_years.items()
                ]
            )
            risk_desc += f"，不在{formatted_threshold1} - {formatted_threshold2}之间，可能预示着企业存在内部控制不严、数据记录不准确或税务筹划异常等问题，需进行进一步的风险预警和检查。"
        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return result, risk_desc

    # 7.12
    def calculate_tax_cost_elastic_modulus(
        self, dt, data, threshold1=0.8, threshold2=1.2
    ):
        # 7.12 纳税调整与营业成本弹性系数

        # 提取corporateIncome数据
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            # 税号没对齐
            default_result = [
                "所属期",
                "营业成本(元)",
                "纳税调整后所得(元)",
                "纳税调整后所得变动率",
                "营业成本变动率",
                "纳税调整与营业成本弹性系数",
            ]
            default_desc = "风险描述：该指标项未检测到风险"
            return default_result, default_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        annual_data = defaultdict(
            lambda: {"revenue_amount": 0.00, "tax_amount": 0.00, "change_type": ""}
        )
        revenue_data = defaultdict(lambda: [])
        tax_data = defaultdict(lambda: [])

        detail_data = defaultdict(lambda: [])
        # 遍历corporateIncome数据
        income_records = data["data"]["corporateIncome"]  # # corporateIncome 表示所得税
        for record in income_records:
            dt_end_year = datetime.strptime(
                record["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if record["invalidMark"] == "Y":
                continue
            # if dt_end != dt_year + '1231': continue
            # 检查是否为年度纳税申报表
            if record["levyProjectName"] == "《中华人民共和国企业所得税年度纳税申报表（A类）》（A100000）":
                amount = float(record["accumulativeAmount"] or 0)
                if record["projectName"] == "减：营业成本（填写A102010/102020/103000）":
                    # 提取营业收入
                    detail_data[dt_end_year].append(
                        {
                            "project_name": "营业成本",
                            "amount": amount,
                            "change_type": record["changeType"],
                        }
                    )
                # 提取纳税调整后所得
                if record["projectName"].__contains__("四、纳税调整后所得"):
                    detail_data[dt_end_year].append(
                        {
                            "project_name": "纳税调整后所得",
                            "amount": amount,
                            "change_type": record["changeType"],
                        }
                    )

        def filter_detail_data(detail_data):
            # 存储 (dt_start, dt_end, source_type, project_name) -> 最佳记录
            best_records = {}

            # 遍历所有日期范围和记录
            for date_range, records in detail_data.items():
                for record in records:
                    key = (date_range, record["project_name"])
                    current_change_type = record["change_type"]
                    # 如果当前记录是 "更正后全量模式"，直接覆盖
                    if current_change_type == "更正后新产生的申报表（全量模式）":
                        best_records[key] = record
                    # 否则，仅当 key 不存在时才保留
                    elif key not in best_records:
                        best_records[key] = record

            # 重新构建 detail_data（按原时间分组）
            new_detail_data = {}
            for key, record in best_records.items():
                date_range, _ = key
                if date_range not in new_detail_data:
                    new_detail_data[date_range] = []
                new_detail_data[date_range].append(record)
            return new_detail_data

        # 取最新数据
        new_detail_data = filter_detail_data(detail_data)
        annual_data = defaultdict(lambda: {"tax_amount": 0.00, "cost_amount": 0.00})
        for dt_year, value in new_detail_data.items():
            for item in value:
                if item["project_name"] == "纳税调整后所得":
                    annual_data[dt_year]["tax_amount"] += item["amount"]
                elif item["project_name"] == "营业成本":
                    annual_data[dt_year]["cost_amount"] += item["amount"]

        # sorted_data_desc = dict(sorted(annual_data.items(), key=lambda item: item[0], reverse=False))
        sorted_years = sorted(annual_data.keys(), reverse=False)
        all_zero = True
        risk_years = []  # 存储风险年份及相关信息
        result = []  # 记录最终结果
        """计算变动率"""
        for i, year in enumerate(sorted_years):
            if i > 0:  # 从第二年开始计算变动率
                prev_year = sorted_years[i - 1]
                # 营业收入变动率
                if annual_data[prev_year]["cost_amount"] != 0:
                    annual_data[year]["cost_growth_rate"] = (
                        annual_data[year]["cost_amount"]
                        - annual_data[prev_year]["cost_amount"]
                    ) / annual_data[prev_year]["cost_amount"]
                else:
                    annual_data[year]["cost_growth_rate"] = None  # 上一年营业收入为 0，无法计算变动率

                # 纳税调整后所得变动率
                if annual_data[prev_year]["tax_amount"] != 0:
                    annual_data[year]["tax_growth_rate"] = (
                        annual_data[year]["tax_amount"]
                        - annual_data[prev_year]["tax_amount"]
                    ) / annual_data[prev_year]["tax_amount"]
                else:
                    annual_data[year]["tax_growth_rate"] = None  # 上一年纳税调整后所得为 0，无法计算变动率
            else:
                # 第一年没有上一年数据，变动率为 null
                annual_data[year]["cost_growth_rate"] = None
                annual_data[year]["tax_growth_rate"] = None

            # 计算弹性系数=纳税调整后所得变动率/营业收入变动率
            cost_growth_rate = annual_data[year]["cost_growth_rate"]
            tax_growth_rate = annual_data[year]["tax_growth_rate"]
            if (
                cost_growth_rate is not None
                and tax_growth_rate is not None
                and tax_growth_rate != 0
            ):
                annual_data[year]["elastic_modulus"] = (
                    cost_growth_rate / tax_growth_rate
                )
            else:
                annual_data[year]["elastic_modulus"] = None  # 无法计算弹性系数

            # 格式化：
            if annual_data[year]["tax_amount"] or annual_data[year]["cost_amount"]:
                all_zero = False
            formatted_cost_amount = (
                "{:,.2f}".format(annual_data[year]["cost_amount"], 2)
                if annual_data[year]["cost_amount"]
                else "0.00"
            )
            formatted_tax_amount = (
                "{:,.2f}".format(annual_data[year]["tax_amount"], 2)
                if annual_data[year]["tax_amount"]
                else "0.00"
            )
            formatted_cost_growth_rate = (
                "{:,.2f}".format(annual_data[year]["cost_growth_rate"] * 100, 2) + "%"
                if annual_data[year]["cost_growth_rate"]
                else "--"
            )
            formatted_tax_growth_rate = (
                "{:,.2f}".format(annual_data[year]["tax_growth_rate"] * 100, 2) + "%"
                if annual_data[year]["tax_growth_rate"]
                else "--"
            )
            formatted_elastic_modulus = (
                "{:,.2f}".format(annual_data[year]["elastic_modulus"], 2)
                if annual_data[year]["elastic_modulus"]
                else "--"
            )
            result.append(
                {
                    "所属期": year + "年",
                    "营业成本（元）": formatted_cost_amount,
                    "纳税调整后所得（元）": formatted_tax_amount,
                    "纳税调整后所得变动率": formatted_tax_growth_rate,
                    "营业成本变动率": formatted_cost_growth_rate,
                    "营业成本与期间费用变动系数": formatted_elastic_modulus,
                }
            )
            if annual_data[year]["elastic_modulus"] is not None and (
                annual_data[year]["elastic_modulus"] < threshold1
                or annual_data[year]["elastic_modulus"] > threshold2
            ):
                risk_years.append(
                    {
                        "所属期": year + "年",
                        "营业成本（元）": formatted_cost_amount,
                        "纳税调整后所得（元）": formatted_tax_amount,
                        "纳税调整后所得变动率": formatted_tax_growth_rate,
                        "营业成本变动率": formatted_cost_growth_rate,
                        "营业成本与期间费用变动系数": formatted_elastic_modulus,
                    }
                )

        if all_zero:
            result = [
                "所属期",
                "营业成本(元)",
                "纳税调整后所得(元)",
                "纳税调整后所得变动率",
                "营业成本变动率",
                "纳税调整与营业成本弹性系数",
            ]
        # 风险描述
        formatted_threshold1 = "{:,.2f}".format(threshold1, 2)
        formatted_threshold2 = "{:,.2f}".format(threshold2, 2)
        if risk_years:
            risk_desc = "风险描述："
            risk_desc += ",".join(
                [
                    f"{item['所属期']}的纳税调整与营业收入弹性系数为{item['纳税调整与营业收入弹性系数']}"
                    for item in risk_years
                ]
            )
            risk_desc += f"，不在{formatted_threshold1} - {formatted_threshold2}之间，可能表明企业存在内部数据记录不准确、内部控制缺陷或故意调整成本以达到税务筹划目的。"
        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return result, risk_desc
