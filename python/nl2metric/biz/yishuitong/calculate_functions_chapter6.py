from datetime import datetime
from dateutil.relativedelta import relativedelta
from collections import defaultdict


class ReportChapter6:
    # 6.1
    def calculate_income_tax_elastic_modulus(
        self, dt, data, threshold1=0.8, threshold2=1.2
    ):
        """
        6.1 营业收入与企业所得税贡献变动检查
        参数:
            company_taxid:企业税号
            current_date (str): 当前日期，格式为 'YYYYMM'
            data (dict): 包含企业财税数据的字典
        返回:
            list: 包含每年营业收入、应纳所得税额、营业收入变动率、所得税贡献变动率、营业收入与企业所得税贡献变动系数的列表，格式如下：
                [
                    {
                        "所属期": "2021",
                        "营业收入（元）": 0.00,
                        "实际应纳所得税额（元）": 0.00,
                        "营业收入变动率": "--",
                        "所得税贡献变动率": "--",
                        "营业收入与企业所得税贡献变动系数": "--"
                    },
                    ...
                ]
        """
        # 税号确认
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            default_result = [
                "所属期",
                "营业收入（元）",
                "实际应纳所得税额（元）",
                "营业收入变动率",
                "所得税贡献变动率",
                "营业收入与企业所得税贡献变动系数",
            ]
            default_risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, default_risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=4)).strftime("%Y")

        # 初始化一个字典来存储每年的营业收入和应纳所得税额
        detail_data = defaultdict(lambda: [])

        # 提取corporateIncome数据
        income_records = data["data"]["corporateIncome"]
        # 遍历corporateIncome数据
        for record in income_records:
            dt_end_year = datetime.strptime(
                record["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if record["invalidMark"] == "Y":
                continue
            if record["levyProjectName"] == "《中华人民共和国企业所得税年度纳税申报表（A类）》（A100000）":
                # 提取营业收入
                if record["projectName"] == "一、营业收入（填写A101010/101020/103000）":
                    income = float(record["accumulativeAmount"] or 0)
                    detail_data[dt_end_year].append(
                        {
                            "amount": income,
                            "project_name": "营业收入",
                            "change_type": record["changeType"],
                        }
                    )
                # 提取应纳所得税额
                if record["projectName"].__contains__("八、实际应纳所得税额"):
                    tax = float(record["accumulativeAmount"] or 0)

                    detail_data[dt_end_year].append(
                        {
                            "amount": tax,
                            "project_name": "实际应纳所得税",
                            "change_type": record["changeType"],
                        }
                    )

        def filter_detail_data(detail_data):
            # 存储 (dt_start, dt_end, source_type, project_name) -> 最佳记录
            best_records = {}

            # 遍历所有日期范围和记录
            for date_range, records in detail_data.items():
                for record in records:
                    key = (date_range, record["project_name"])
                    current_change_type = record["change_type"]
                    # 如果当前记录是 "更正后全量模式"，直接覆盖
                    if current_change_type == "更正后新产生的申报表（全量模式）":
                        best_records[key] = record
                    # 否则，仅当 key 不存在时才保留
                    elif key not in best_records:
                        best_records[key] = record

            # 重新构建 detail_data（按原时间分组）
            new_detail_data = {}
            for key, record in best_records.items():
                date_range, _ = key
                if date_range not in new_detail_data:
                    new_detail_data[date_range] = []
                new_detail_data[date_range].append(record)
            return new_detail_data

        new_detail_data = filter_detail_data(detail_data)

        # 计算变动率并格式化输出
        all_zero = True  # 标记是否所有应纳税所得额和营业收入都是00
        risk_years = []  # 存储风险年份及相关信息
        result = []
        annual_data = defaultdict(lambda: {"tax_amount": 0.00, "income_amount": 0.00})
        # 数据进行聚合
        for dt_year, value in new_detail_data.items():
            for item in value:
                if item["project_name"] == "实际应纳所得税":
                    annual_data[dt_year]["tax_amount"] += item["amount"]
                elif item["project_name"] == "营业收入":
                    annual_data[dt_year]["income_amount"] += item["amount"]
                # print(annual_data[dt_year]['income_amount'], annual_data[dt_year]['tax_amount'])
        sorted_years = sorted(annual_data.keys(), reverse=False)

        # 计算变动率
        result = []
        for i, year in enumerate(sorted_years):
            if i > 0:  # 从第二年开始计算变动率
                prev_year = sorted_years[i - 1]

                # 营业收入变动率
                annual_data[year]["income_growth_rate"] = (
                    (
                        (
                            annual_data[year]["income_amount"]
                            - annual_data[prev_year]["income_amount"]
                        )
                        / annual_data[prev_year]["income_amount"]
                    )
                    if annual_data[prev_year]["income_amount"]
                    else None
                )
                # 上一年销售额为 0，无法计算变动率

                # 应纳税额变动率
                annual_data[year]["tax_growth_rate"] = (
                    (
                        (
                            annual_data[year]["tax_amount"]
                            - annual_data[prev_year]["tax_amount"]
                        )
                        / annual_data[prev_year]["tax_amount"]
                    )
                    if annual_data[prev_year]["tax_amount"]
                    else None
                )
                # 上一年应纳税额为 0，无法计算变动率
            else:
                # 第一年没有上一年数据，变动率为 null
                annual_data[year]["income_growth_rate"] = None
                annual_data[year]["tax_growth_rate"] = None

            income_amount = annual_data[year]["income_amount"]
            tax_amount = annual_data[year]["tax_amount"]
            if income_amount or tax_amount:
                all_zero = False

            # 计算弹性系数
            income_growth_rate = annual_data[year]["income_growth_rate"]
            tax_growth_rate = annual_data[year]["tax_growth_rate"]
            elastic_modulus = (
                (income_growth_rate / tax_growth_rate)
                if income_growth_rate and tax_growth_rate
                else None
            )

            formatted_income_amount = "{:,.2f}".format(income_amount, 2)
            formatted_tax_amount = "{:,.2f}".format(tax_amount, 2)
            formatted_income_growth_rate = (
                "{:,.2f}".format(income_growth_rate * 100, 2) + "%"
                if income_growth_rate
                else "--"
            )
            formatted_tax_growth_rate = (
                "{:,.2f}".format(tax_growth_rate * 100, 2) + "%"
                if tax_growth_rate
                else "--"
            )
            formatted_elastic_modulus = (
                "{:,.2f}".format(elastic_modulus, 2) if elastic_modulus else "--"
            )

            result.append(
                {
                    "所属期": year + "年",
                    "营业收入（元）": formatted_income_amount,
                    "实际应纳所得税额（元）": formatted_tax_amount,
                    "营业收入变动率": formatted_income_growth_rate,
                    "所得税贡献变动率": formatted_tax_growth_rate,
                    "营业收入与企业所得税贡献变动系数": formatted_elastic_modulus,
                }
            )

            if elastic_modulus and (
                elastic_modulus < threshold1 or elastic_modulus > threshold2
            ):
                risk_years.append(
                    {
                        "所属期": year + "年",
                        "营业收入（元）": formatted_income_amount,
                        "实际应纳所得税额（元）": formatted_tax_amount,
                        "营业收入变动率": formatted_income_growth_rate,
                        "所得税贡献变动率": formatted_tax_growth_rate,
                        "营业收入与企业所得税贡献变动系数": formatted_elastic_modulus,
                    }
                )
        formatted_threshold1 = "{:,.2f}".format(threshold1, 2)
        formatted_threshold2 = "{:,.2f}".format(threshold2, 2)
        if all_zero:
            result = [
                "所属期",
                "营业收入（元）",
                "实际应纳所得税额（元）",
                "营业收入变动率",
                "所得税贡献变动率",
                "营业收入与企业所得税贡献变动系数",
            ]

        if risk_years:
            risk_desc = f"风险描述：{company_name}在最近3年内，"
            risk_desc += "、".join(
                [f"{item['所属期']}（{item['营业收入与企业所得税贡献变动系数']}）" for item in risk_years]
            )
            risk_desc += f"的营业收入与企业所得税贡献变动系数不属于合理范围（{formatted_threshold1}-{formatted_threshold2}）。弹性系数异常指示企业营业收入变动与所得税贡献变动之间存在不匹配的情况，可能是企业利润操纵、税收筹划异常或经营状况发生重大变化的信号，需要进一步调查核实。"
        else:
            risk_desc = f"风险描述：该指标项未检测到风险"

        return result, risk_desc

    # 6.2
    def calculate_income_profit_elastic_modulus(
        self, dt, data, threshold1=0.8, threshold2=1.2
    ):
        """
        6.2 营业收入与营业利润变动检查
        参数:
            company_taxid:企业税号
            dt: 当前日期，格式为 'YYYYMM'
            data (dict): 包含企业财税数据的字典
        返回:
            list: 包含每年营业收入、应纳所得税额、营业收入变动率、营业利润变动率、营业收入与营业利润变动系数的列表，格式如下：
                [
                    {
                        "所属期": "2021",
                        "营业收入（元）": 0.00,
                        "营业利润（元）": 0.00,
                        "营业收入变动率": "--",
                        "营业利润变动率": "--",
                        "营业收入与营业利润变动系数": "--"
                    },
                    ...
                ]
        """
        # 税号确认
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            default_result = ["所属期", "营业收入（元）", "营业利润（元）", "营业收入变动率", "营业收入与营业利润变动系数"]
            default_risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, default_risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        annual_data = defaultdict(
            lambda: {"profit_amount": 0.00, "profit_income_amount": 0.00}
        )

        # 提取利润表的营业收入和营业利润
        financeProfit = data["data"]["financeProfit"]
        for profit in financeProfit:
            dt_end_year = datetime.strptime(
                profit["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if profit["period"] == "Year" and profit["projectName"] == "一、营业收入":
                amount = float(profit["currentYearAccumulativeAmount"] or 0)
                annual_data[dt_end_year]["profit_income_amount"] = amount
            if profit["period"] == "Year" and profit["projectName"].__contains__(
                "营业利润"
            ):
                profit_amount = float(profit["currentYearAccumulativeAmount"] or 0)
                annual_data[dt_end_year]["profit_amount"] = profit_amount
        sorted_years = sorted(annual_data.keys(), reverse=False)
        #
        # 计算变动率
        for i, year in enumerate(sorted_years):
            if i > 0:  # 从第二年开始计算变动率
                prev_year = sorted_years[i - 1]
                # 营业收入变动率
                if annual_data[prev_year]["profit_income_amount"] != 0:
                    annual_data[year]["income_growth_rate"] = (
                        annual_data[year]["profit_income_amount"]
                        - annual_data[prev_year]["profit_income_amount"]
                    ) / annual_data[prev_year]["profit_income_amount"]
                else:
                    annual_data[year]["income_growth_rate"] = None  # 上一年销售额为 0，无法计算变动率

                # 营业利润变动率
                if annual_data[prev_year]["profit_amount"] != 0:
                    annual_data[year]["profit_growth_rate"] = (
                        annual_data[year]["profit_amount"]
                        - annual_data[prev_year]["profit_amount"]
                    ) / annual_data[prev_year]["profit_amount"]
                else:
                    annual_data[year]["profit_growth_rate"] = None  # 上一年应纳税额为 0，无法计算变动率
            else:
                # 第一年没有上一年数据，变动率为 null
                annual_data[year]["income_growth_rate"] = None
                annual_data[year]["profit_growth_rate"] = None

            # 计算弹性系数
            income_growth_rate = annual_data[year]["income_growth_rate"]
            profit_growth_rate = annual_data[year]["profit_growth_rate"]
            if (
                income_growth_rate is not None
                and profit_growth_rate is not None
                and profit_growth_rate != 0
            ):
                annual_data[year]["elastic_modulus"] = (
                    income_growth_rate / profit_growth_rate
                ) * 100
            else:
                annual_data[year]["elastic_modulus"] = None  # 无法计算弹性系数
        # 格式化输出
        result = []
        risks = []
        for year in sorted_years:
            income = annual_data[year]["profit_income_amount"]
            profit = annual_data[year]["profit_amount"]
            income_growth_rate = annual_data[year]["income_growth_rate"]
            profit_growth_rate = annual_data[year]["profit_growth_rate"]
            elastic_modulus = annual_data[year]["elastic_modulus"]

            # 格式化变动率和弹性系数
            formatted_income = "{:,.2f}".format(income, 2)
            formatted_profit = "{:,.2f}".format(profit, 2)

            formatted_income_growth_rate = (
                "{:,.2f}".format(income_growth_rate * 100, 2) + "%"
                if income_growth_rate is not None
                else "--"
            )
            formatted_profit_growth_rate = (
                "{:,.2f}".format(profit_growth_rate * 100, 2) + "%"
                if profit_growth_rate is not None
                else "--"
            )
            formatted_elastic_modulus = (
                "{:,.2f}".format(elastic_modulus * 100, 2) + "%"
                if elastic_modulus is not None
                else "--"
            )
            result.append(
                {
                    "所属期": year + "年",
                    "营业收入（元）": formatted_income,
                    "营业利润（元）": formatted_profit,
                    "营业收入变动率": formatted_income_growth_rate,
                    "营业利润变动率": formatted_profit_growth_rate,
                    "营业收入与营业利润变动系数": formatted_elastic_modulus,
                }
            )

            if elastic_modulus is not None and (
                elastic_modulus < threshold1 or elastic_modulus > threshold2
            ):
                risks.append(
                    {
                        "所属期": year + "年",
                        "营业收入（元）": formatted_income,
                        "营业利润（元）": formatted_profit,
                        "营业收入变动率": formatted_income_growth_rate,
                        "营业利润变动率": formatted_profit_growth_rate,
                        "营业收入与营业利润变动系数": formatted_elastic_modulus,
                    }
                )

        risk_desc = f"风险描述: "
        if risks:
            for i in risks:
                risk_desc += f"{i['所属期']}年，弹性系数为{i['营业收入与营业利润变动系数']},"
            risk_desc += f"弹性系数异常指示企业营业收入与营业利润变动之间存在不匹配的情况，可能是企业利润操纵、税收筹划异常或经营状况发生重大变化的信号，需要进一步调查核实。\n"
        else:
            risk_desc += f"该指标项未检测到风险"

        return result, risk_desc

    # 6.3
    def calculate_gross_margin_detection(self, dt, data, threshold=0.1):
        """
        6.3 毛利率变动检查
        参数:
            company_taxid:企业税号
            dt: 当前日期，格式为 'YYYYMM'
            data (dict): 包含企业财税数据的字典
        返回:
            list: 包含每年营业收入、营业成本、毛利率、毛利率变动率的列表，格式如下：
                [
                    {
                        "所属期": "2023",
                        "营业收入（元）": 1183235.59,
                        "营业成本（元）": 969318.33,
                        "毛利率": "18.08%",
                        "毛利率变动率": "-102.24%"
                    },
                    ...
                ]
        """
        # 税号确认
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            default_result = ["所属期", "营业收入（元）", "营业成本（元）", "毛利率", "毛利率变动率"]
            default_risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, default_risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        annual_data = defaultdict(lambda: {"cost_amount": 0.00, "income_amount": 0.00})

        # 提取利润表的营业收入和营业成本
        financeProfit = data["data"]["financeProfit"]
        for profit in financeProfit:
            dt_end_year = datetime.strptime(
                profit["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if profit["period"] == "Year" and profit["projectName"] == "一、营业收入":
                amount = float(profit["currentYearAccumulativeAmount"] or 0)
                annual_data[dt_end_year]["income_amount"] = amount
            if profit["period"] == "Year" and profit["projectName"] == "减：营业成本":
                cost_amount = float(profit["currentYearAccumulativeAmount"] or 0)
                annual_data[dt_end_year]["cost_amount"] = cost_amount
        # 计算毛利率
        for year in annual_data:
            income = annual_data[year]["income_amount"]
            cost = annual_data[year]["cost_amount"]
            annual_data[year]["gross_margin_rate"] = (
                ((income - cost) / income) if income > 0 else None
            )

        sorted_years = sorted(annual_data.keys(), reverse=False)
        # 计算毛利率变动率
        for i, year in enumerate(sorted_years):
            if i > 0:  # 从第二年开始计算变动率
                prev_year = sorted_years[i - 1]
                if (
                    annual_data[prev_year]["gross_margin_rate"] is not None
                    and annual_data[prev_year]["gross_margin_rate"] != 0
                ):
                    annual_data[year]["gross_margin_growth_rate"] = (
                        annual_data[year]["gross_margin_rate"]
                        - annual_data[prev_year]["gross_margin_rate"]
                    ) / annual_data[prev_year]["gross_margin_rate"]
                else:
                    annual_data[year][
                        "gross_margin_growth_rate"
                    ] = None  # 上一年销售额为 0，无法计算变动率
            else:
                # 第一年没有上一年数据，变动率为 null
                annual_data[year]["gross_margin_growth_rate"] = None

            # 格式化输出
        result = []
        risk_years = []
        all_zero = True
        for year in sorted_years:
            income_amount = annual_data[year]["income_amount"]
            cost_amount = annual_data[year]["cost_amount"]
            gross_margin_rate = annual_data[year]["gross_margin_rate"]
            gross_margin_growth_rate = annual_data[year]["gross_margin_growth_rate"]
            if income_amount or cost_amount:
                all_zero = False
            # 格式化变动率和弹性系数
            formatted_income_amount = "{:,.2f}".format(income_amount, 2)
            formatted_cost_amount = "{:,.2f}".format(cost_amount, 2)
            formatted_gross_margin_rate = (
                "{:,.2f}".format(gross_margin_rate * 100, 2) + "%"
                if gross_margin_rate is not None
                else "--"
            )
            formatted_gross_margin_growth_rate = (
                "{:,.2f}".format(gross_margin_growth_rate * 100, 2) + "%"
                if gross_margin_growth_rate is not None
                else "--"
            )
            result.append(
                {
                    "所属期": year + "年",
                    "营业收入（元）": formatted_income_amount,
                    "营业成本（元）": formatted_cost_amount,
                    "毛利率": formatted_gross_margin_rate,
                    "毛利率变动率": formatted_gross_margin_growth_rate,
                }
            )

            if gross_margin_growth_rate is not None and (
                abs(gross_margin_growth_rate) > threshold
            ):
                risk_years.append(
                    {
                        "所属期": year + "年",
                        "营业收入（元）": formatted_income_amount,
                        "营业成本（元）": formatted_cost_amount,
                        "毛利率": formatted_gross_margin_rate,
                        "毛利率变动率": formatted_gross_margin_growth_rate,
                    }
                )
        risk_desc = f"风险描述: "
        if all_zero:
            result = ["所属期", "营业收入（元）", "营业成本（元）", "毛利率", "毛利率变动率"]
        if risk_years:
            risk_desc += "，".join(
                [f"{item['所属期']}年毛利率波动过大，变动幅度为{item['毛利率变动率']}，" for item in risk_years]
            )
            risk_desc += (
                f"毛利率波动可能是成本核算异常或收入结构变化引起，也可能反映企业存在经营异常或财务操纵的风险，需重点核实收入和成本的真实性。"
            )
        else:
            risk_desc += f"该指标项未检测到风险"

        return result, risk_desc

    # 6.4
    def calculate_gross_margin(self, dt, data, threshold=0.15):
        """
        6.4 毛利率变动检查
        参数:
            company_taxid:企业税号
            dt: 当前日期，格式为 'YYYYMM'
            data (dict): 包含企业财税数据的字典
        返回:
            list: 包含每年营业收入、营业成本、毛利率、毛利率变动率的列表，格式如下：
                [
                    {
                        "所属期": "2023",
                        "营业收入（元）": 1183235.59,
                        "营业成本（元）": 969318.33,
                        "毛利率": "18.08%",
                        "毛利率变动率": "-102.24%"
                    },
                    ...
                ]
        """
        # 税号确认
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            default_result = ["所属期", "营业收入（元）", "营业成本（元）", "营业毛利", "营业收入毛利率"]
            default_risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, default_risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        annual_data = defaultdict(lambda: {"cost_amount": 0.00, "income_amount": 0.00})

        # 提取利润表的营业收入和营业成本
        financeProfit = data["data"]["financeProfit"]
        for profit in financeProfit:
            dt_end_year = datetime.strptime(
                profit["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if profit["period"] == "Year" and profit["projectName"] == "一、营业收入":
                amount = float(profit["currentYearAccumulativeAmount"] or 0)
                annual_data[dt_end_year]["income_amount"] = amount
            if profit["period"] == "Year" and profit["projectName"] == "减：营业成本":
                cost_amount = float(profit["currentYearAccumulativeAmount"] or 0)
                annual_data[dt_end_year]["cost_amount"] = cost_amount

        # 计算毛利和毛利率
        for year in annual_data:
            income_amount = annual_data[year]["income_amount"]
            cost_amount = annual_data[year]["cost_amount"]
            annual_data[year]["gross_margin"] = income_amount - cost_amount
            annual_data[year]["gross_margin_rate"] = (
                ((income_amount - cost_amount) / income_amount)
                if income_amount
                else None
            )

        sorted_years = sorted(annual_data.keys(), reverse=False)
        # 格式化输出
        result = []
        all_zero = True
        consecutive_low_years = []  # 存储连续毛利率过低的年份
        risk_years = []
        previous_year = None
        formatted_threshold = "{:,.2f}".format(threshold * 100, 2) + "%"
        for year in sorted_years:
            income_amount = annual_data[year]["income_amount"]
            cost_amount = annual_data[year]["cost_amount"]
            gross_margin = annual_data[year]["gross_margin"]
            gross_margin_rate = annual_data[year]["gross_margin_rate"]
            if income_amount or cost_amount:
                all_zero = False

            # 格式化
            formatted_income_amount = "{:,.2f}".format(income_amount, 2)
            formatted_cost_amount = "{:,.2f}".format(cost_amount, 2)
            formatted_gross_margin = "{:,.2f}".format(gross_margin, 2)
            formatted_gross_margin_rate = (
                "{:,.2f}".format(gross_margin_rate * 100, 2) + "%"
            )

            result.append(
                {
                    "所属期": year + "年",
                    "营业收入（元）": formatted_income_amount,
                    "营业成本（元）": formatted_cost_amount,
                    "营业毛利（元）": formatted_gross_margin,
                    "营业收入毛利率": formatted_gross_margin_rate,
                }
            )
            # 检查是否为连续亏损
            if gross_margin_rate and (gross_margin_rate < threshold):
                risk_years.append(
                    {
                        "所属期": year + "年",
                        "营业收入（元）": formatted_income_amount,
                        "营业成本（元）": formatted_cost_amount,
                        "营业毛利（元）": formatted_gross_margin,
                        "营业收入毛利率": formatted_gross_margin_rate,
                        "描述": f"营业毛利率为负值（{formatted_gross_margin_rate}）"
                        if gross_margin_rate < 0
                        else f"低于{formatted_threshold}",
                    }
                )
                if previous_year and int(year) == int(previous_year) + 1:
                    consecutive_low_years.append(year)
                else:
                    consecutive_low_years = [year]  # 重置连续亏损年份
                previous_year = year
            else:
                consecutive_low_years = []  # 重置连续亏损年份
                previous_year = None
        if all_zero:
            result = ["所属期", "营业收入（元）", "营业成本（元）", "营业毛利", "营业收入毛利率"]

        if risk_years:
            risk_desc = f"风险描述：近三年内，"
            for i in risk_years:
                risk_desc += (
                    f"{i['所属期']}年营业收入为{i['营业收入（元）']}元，营业成本为{i['营业成本（元）']}元，营业毛利为{i['营业毛利（元）']}元，"
                    f"{i['描述']}。"
                )
            if len(consecutive_low_years) >= 3:
                risk_desc += f"此外，可以观察到营业收入毛利率处于持续{len(consecutive_low_years)}年处于走低状态。"
            risk_desc += "可能指示企业产品竞争力或成本控制存在问题。"
        else:
            risk_desc = f"风险描述：该指标项未检测到风险"

        return result, risk_desc

    # 6.5
    def calculate_net_profit(self, dt, data, threshold=0):
        """
        6.5 连续亏损检查
        参数:
            company_taxid:企业税号
            dt: 当前日期，格式为 'YYYYMM'
            data (dict): 包含企业财税数据的字典
        返回:
            list: 包含每年利润总额、所得税费用、净利润的列表，格式如下：
                [
                    {
                    "所属期": "2021",
                        "利润总额（元）": -3457633.41,
                        "所得税费用（元）": 0.0,
                        "净利润（元）": -3457633.41
                    },
                    ...
                ]
            str: 风险描述，格式为“连续亏损可能意味着企业持续经营能力不足，存在财务数据异常或潜在税务避税风险，需关注是否有隐瞒收入、虚列成本或费用的行为。”
        """
        # 税号确认
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            default_result = ["所属期", "营业收入（元）", "所得税费用（元）", "净利润（元）"]
            default_risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, default_risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        annual_data = defaultdict(
            lambda: {"tax_amount": 0.00, "profit_income_amount": 0.00}
        )

        # 提取利润表的营业收入和营业利润
        financeProfit = data["data"]["financeProfit"]
        for profit in financeProfit:
            dt_end_year = datetime.strptime(
                profit["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if profit["period"] == "Year" and profit["projectName"].__contains__(
                "三、利润总额（亏损总额以“-”号填列）"
            ):
                amount = float(profit["currentYearAccumulativeAmount"] or 0)
                annual_data[dt_end_year]["profit_amount"] = amount
            if profit["period"] == "Year" and profit["projectName"].__contains__(
                "减：所得税费用"
            ):
                amount = float(profit["currentYearAccumulativeAmount"] or 0)
                annual_data[dt_end_year]["tax_amount"] = amount
        sorted_years = sorted(annual_data.keys(), reverse=False)
        # 计算净利润
        for year in sorted_years:
            annual_data[year]["net_profit"] = (
                annual_data[year]["profit_amount"] - annual_data[year]["tax_amount"]
            )
        # 格式化输出
        result = []
        all_zero = True
        consecutive_loss_years = []  # 存储连续亏损的年份
        previous_year = None
        risk_years = []
        for year in sorted_years:
            profit_amount = annual_data[year]["profit_amount"]
            tax_amount = annual_data[year]["tax_amount"]
            net_profit = annual_data[year]["net_profit"]
            if profit_amount or tax_amount:
                all_zero = False

            # 格式化
            formatted_profit_amount = "{:,.2f}".format(profit_amount, 2)
            formatted_tax_amount = "{:,.2f}".format(tax_amount, 2)
            formatted_net_profit = "{:,.2f}".format(net_profit, 2)

            result.append(
                {
                    "所属期": year + "年",
                    "利润总额（元）": formatted_profit_amount,
                    "所得税费用（元）": formatted_tax_amount,
                    "净利润（元）": formatted_net_profit,
                }
            )
            # 检查是否为连续亏损
            if net_profit < threshold:
                risk_years.append(
                    {
                        "所属期": year + "年",
                        "利润总额（元）": formatted_profit_amount,
                        "所得税费用（元）": formatted_tax_amount,
                        "净利润（元）": formatted_net_profit,
                    }
                )

                if previous_year and int(year) == int(previous_year) + 1:
                    consecutive_loss_years.append(
                        {
                            "所属期": year + "年",
                            "利润总额（元）": formatted_profit_amount,
                            "所得税费用（元）": formatted_tax_amount,
                            "净利润（元）": formatted_net_profit,
                        }
                    )
                else:
                    consecutive_loss_years = [
                        {
                            "所属期": year + "年",
                            "利润总额（元）": formatted_profit_amount,
                            "所得税费用（元）": formatted_tax_amount,
                            "净利润（元）": formatted_net_profit,
                        }
                    ]  # 重置连续亏损年份
                previous_year = year
            else:
                consecutive_loss_years = []  # 重置连续亏损年份
                previous_year = None

        if all_zero:
            result = ["所属期", "营业收入（元）", "所得税费用（元）", "净利润（元）"]

        if len(consecutive_loss_years) >= 3:
            risk_desc = f"风险描述: {company_name}在近三年连续亏损（"
            risk_desc += "、".join(
                [f"{item['所属期']}({item['净利润（元）']})" for item in consecutive_loss_years]
            )
            risk_desc += (
                "）。连续亏损可能意味着企业持续经营能力不足，存在财务数据异常或潜在税务避税风险，需关注是否有隐瞒收入、虚列成本或费用的行为。"
            )
        else:
            risk_desc = "风险描述：该指标项未检测到连续亏损风险"

        return result, risk_desc

    # 6.6
    def calculate_profit_detection(self, dt, data, threshold=0.05):
        # 6.6 盈利情况检查
        # 提取corporateIncome数据
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            # 税号没对齐
            default_result = [
                "所属期",
                "营业收入（元）",
                "营业成本(元)",
                "期间费用（元）",
                "期间费用率",
                "营业毛利率",
                "期间费用与营业毛利率差值",
            ]
            risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        detail_data = defaultdict(lambda: [])
        # 遍历corporateIncome数据
        income_records = data["data"]["corporateIncome"]  # # corporateIncome 表示所得税
        period_name = [
            "减：财务费用（填写A104000）",
            "减：销售费用（填写A104000）",
            "减：管理费用（填写A104000）",
            "减：研发费用（填写A104000）",
        ]
        for record in income_records:
            dt_end_year = datetime.strptime(
                record["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if record["invalidMark"] == "Y":
                continue
            # if dt_end != dt_year + '1231': continue年度申报表，不需要做年份时间的过滤了。
            # 检查是否为年度纳税申报表
            if record["levyProjectName"] == "《中华人民共和国企业所得税年度纳税申报表（A类）》（A100000）":
                amount = float(record["accumulativeAmount"] or 0)
                # 提取营业收入
                if record["projectName"] == "一、营业收入（填写A101010/101020/103000）":
                    detail_data[dt_end_year].append(
                        {
                            "amount": amount,
                            "project_name": "营业收入",
                            "change_type": record["changeType"],
                        }
                    )
                # 提取营业成本
                if record["projectName"].__contains__("减：营业成本"):
                    detail_data[dt_end_year].append(
                        {
                            "amount": amount,
                            "project_name": "营业成本",
                            "change_type": record["changeType"],
                        }
                    )
                # 提取期间费用
                if record["projectName"] in period_name:
                    detail_data[dt_end_year].append(
                        {
                            "amount": amount,
                            "project_name": record["projectName"],
                            "change_type": record["changeType"],
                        }
                    )

        def filter_detail_data(detail_data):
            # 存储 (dt_start, dt_end, source_type, project_name) -> 最佳记录
            best_records = {}
            # 遍历所有日期范围和记录
            for date_range, records in detail_data.items():
                for record in records:
                    key = (date_range, record["project_name"])
                    current_change_type = record["change_type"]
                    # 如果当前记录是 "更正后全量模式"，直接覆盖
                    if current_change_type == "更正后新产生的申报表（全量模式）":
                        best_records[key] = record
                    # 否则，仅当 key 不存在时才保留
                    elif key not in best_records:
                        best_records[key] = record

            # 重新构建 detail_data（按原时间分组）
            new_detail_data = {}
            for key, record in best_records.items():
                date_range, _ = key
                if date_range not in new_detail_data:
                    new_detail_data[date_range] = []
                new_detail_data[date_range].append(record)
            return new_detail_data

        new_detail_data = filter_detail_data(detail_data)
        annual_data = defaultdict(
            lambda: {"period_fee": 0.00, "cost_amount": 0.00, "income_amount": 0.00}
        )
        # 企业所得税年报汇总：
        for dt_year, value in new_detail_data.items():
            for item in value:
                if item["project_name"] in period_name:
                    # 计算期间费用（管理费用、销售费用、财务费用）
                    annual_data[dt_year]["period_fee"] += item["amount"]
                elif item["project_name"] == "营业成本":
                    annual_data[dt_year]["cost_amount"] += item["amount"]
                elif item["project_name"] == "营业收入":
                    annual_data[dt_year]["income_amount"] += item["amount"]
        sorted_years = sorted(annual_data.keys(), reverse=False)
        # 格式化输出
        risks = []
        all_zero = True
        result = []
        # 计算期间费用率、营业毛利率
        for year in sorted_years:
            income = annual_data[year]["income_amount"]
            cost = annual_data[year]["cost_amount"]
            period_fee = annual_data[year]["period_fee"]

            # 计算营业毛利率
            annual_data[year]["gross_margin"] = (
                ((income - cost) / income * 100) if income > 0 else 0
            )

            # 计算期间费用率
            annual_data[year]["period_fee_ratio"] = (
                (period_fee / income * 100) if income > 0 else 0
            )

            # 计算期间费用率与营业毛利率差值
            annual_data[year]["diff"] = (
                annual_data[year]["period_fee_ratio"]
                - annual_data[year]["gross_margin"]
            )

            # 格式化
            if (
                annual_data[year]["period_fee"]
                or annual_data[year]["income_amount"]
                or annual_data[year]["cost_amount"]
            ):
                all_zero = False
            formatted_period_fee = (
                "{:,.2f}".format(annual_data[year]["period_fee"], 2)
                if annual_data[year]["period_fee"]
                else "0.00"
            )
            formatted_income_amount = (
                "{:,.2f}".format(annual_data[year]["income_amount"], 2)
                if annual_data[year]["income_amount"]
                else "0.00"
            )
            formatted_cost_amount = (
                "{:,.2f}".format(annual_data[year]["cost_amount"], 2)
                if annual_data[year]["cost_amount"]
                else "0.00"
            )
            formatted_gross_margin = (
                "{:,.2f}".format(annual_data[year]["gross_margin"], 2) + "%"
                if annual_data[year]["gross_margin"]
                else "--"
            )
            formatted_period_fee_ratio = (
                "{:,.2f}".format(annual_data[year]["period_fee_ratio"], 2) + "%"
                if annual_data[year]["period_fee_ratio"]
                else "--"
            )
            formatted_diff = (
                "{:,.2f}".format(annual_data[year]["diff"], 2) + "%"
                if annual_data[year]["diff"]
                else "--"
            )
            result.append(
                {
                    "所属期": year + "年",
                    "营业收入（元）": formatted_income_amount,
                    "营业成本（元）": formatted_cost_amount,
                    "期间费用（元）": formatted_period_fee,
                    "期间费用率": formatted_period_fee_ratio,
                    "营业毛利率": formatted_gross_margin,
                    "期间费用与营业毛利率差值": formatted_diff,
                }
            )

            # 检查是否存在风险
            if (
                annual_data[year]["diff"] is not None
                and annual_data[year]["period_fee_ratio"] != 0
                and annual_data[year]["diff"] < threshold
            ):
                risks.append(year)
        if all_zero:
            result = [
                "所属期",
                "营业收入（元）",
                "营业成本(元)",
                "期间费用（元）",
                "期间费用率",
                "营业毛利率",
                "期间费用与营业毛利率差值",
            ]

        if risks:
            risk_desc = "风险描述："
            risk_desc += "、".join(risks)
            risk_desc += "年，期间费用率与营业毛利率差值大于5%，当期间费用率过高侵蚀毛利时，可能表明企业盈利能力不足或费用核算不准确，需关注期间费用是否存在虚增或成本费用分摊不合理的问题。"
        else:
            risk_desc = "风险描述：该指标项未检测到⻛险 "
        return result, risk_desc

    # 6.7
    def calculate_profit_comparison(self, dt, data, threshold1=0.3, threshold2=0.5):
        # 6.7 利润比对检查
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            # 税号没对齐
            default_result = ["所属期", "营业收入(元)", "营业成本（元）", "营业利润（元）", "成本利润率", "销售利润率"]
            risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")

        annual_data = defaultdict(
            lambda: {"cost_amount": 0.00, "income_amount": 0.00, "profit_amount": 0.00}
        )

        """利润表"""
        financeProfit = data["data"]["financeProfit"]
        for profit in financeProfit:
            dt_end_year = datetime.strptime(
                profit["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if profit["period"] == "Year" and profit["projectName"] == "一、营业收入":
                amount = float(profit["currentYearAccumulativeAmount"] or 0)
                annual_data[dt_end_year]["income_amount"] = amount
            if profit["period"] == "Year" and profit["projectName"] == "减：营业成本":
                amount = float(profit["currentYearAccumulativeAmount"] or 0)
                annual_data[dt_end_year]["cost_amount"] = amount
            if (
                profit["period"] == "Year"
                and profit["projectName"] == "二、营业利润（亏损以“-”号填列）"
            ):
                amount = float(profit["currentYearAccumulativeAmount"] or 0)
                annual_data[dt_end_year]["profit_amount"] = amount

        sorted_years = sorted(annual_data.keys(), reverse=False)

        # 添加风险年份记录
        risk_years = []
        all_zero = True
        result = []
        for year in sorted_years:
            income_amount = annual_data[dt_end_year]["income_amount"]
            profit_amount = annual_data[dt_end_year]["profit_amount"]
            cost_amount = annual_data[dt_end_year]["cost_amount"]
            cost_profit_rate = profit_amount / cost_amount if cost_amount else None
            income_profit_rate = (
                profit_amount / income_amount if income_amount else None
            )

            if income_amount or profit_amount or cost_amount:
                all_zero = False
            # 格式化：
            formatted_income_amount = "{:,.2f}".format(income_amount, 2)
            formatted_profit_amount = "{:,.2f}".format(profit_amount, 2)
            formatted_cost_amount = "{:,.2f}".format(cost_amount, 2)
            formatted_cost_profit_rate = (
                "{:,.2f}".format(cost_profit_rate * 100, 2) + "%"
            )
            formatted_income_profit_rate = (
                "{:,.2f}".format(income_profit_rate * 100, 2) + "%"
            )

            result.append(
                {
                    "所属期": year + "年",
                    "营业收入（元）": formatted_income_amount,
                    "营业成本（元）": formatted_cost_amount,
                    "营业利润（元）": formatted_profit_amount,
                    "成本利润率": formatted_cost_profit_rate,
                    "销售利润率": formatted_income_profit_rate,
                }
            )

            # 检查是否存在风险
            formatted_threshold1 = "{:,.2f}".format(threshold1 * 100, 2) + "%"
            formatted_threshold2 = "{:,.2f}".format(threshold2 * 100, 2) + "%"
            if (income_profit_rate and income_profit_rate < threshold1) or (
                cost_profit_rate and cost_profit_rate < threshold2
            ):
                desc = []
                if income_profit_rate and income_profit_rate < threshold1:
                    desc.append(
                        f"销售利润率({formatted_income_profit_rate})低于{formatted_threshold1}"
                    )
                if cost_profit_rate and cost_profit_rate < threshold2:
                    desc.append(
                        f"成本利润率({formatted_cost_profit_rate})低于{formatted_threshold2}"
                    )
                risk_years.append(
                    {
                        "所属期": year + "年",
                        "营业收入（元）": formatted_income_amount,
                        "营业成本（元）": formatted_cost_amount,
                        "营业利润（元）": formatted_profit_amount,
                        "成本利润率": formatted_cost_profit_rate,
                        "销售利润率": formatted_income_profit_rate,
                        "描述": ",".join(desc),
                    }
                )
        if all_zero:
            result = ["所属期", "营业收入(元)", "营业成本（元）", "营业利润（元）", "成本利润率", "销售利润率"]

        if risk_years:
            risk_desc = f"风险描述：{company_name}在最近3年内，"
            risk_desc += ",".join([f"{item['所属期']}{item['描述']}" for item in risk_years])
            risk_desc += f"利润率异常偏低可能反映企业成本核算或收入确认存在问题，需关注是否存在利润操纵、虚增成本或收入真实性风险。"
        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return result, risk_desc

    # 6.8
    def calculate_invoice_statistics_by_year(
        self, dt, data, threshold1=0.3, threshold2=0.5
    ):
        """
        6.8 商贸企业有进未获取相应数据销/有进低销风险
        按年份统计接收发票和销售发票的份数及金额，并检测是否存在“有进无销”或“有进低销”的风险。

        Args:
            data (dict): 包含发票数据的字典

        Returns:
            list: 按年份统计的接收发票和销售发票的份数及金额的列表，格式为:
                [
                    {
                        "所属年度": "2021",
                        "接收发票份数": 10,
                        "接收发票金额（元）": 1234.5,
                        "销售发票份数": 5,
                        "销售发票金额（元）": 5678.9
                    },
                    ...
                ]
            str: 风险描述
        """
        # 初始化结果字典
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        all_zero = True
        # 初始化统计容器
        yearly_result = defaultdict(
            lambda: {
                "receive_count": 0,
                "receive_amount": 0.0,
                "sales_count": 0,
                "sales_amount": 0.0,
            }
        )
        final_result = []
        risk_years = []

        # 数据处理逻辑（保持不变）
        for item in data.get("data", {}).get("items", []):
            belong_month = item.get("belongMonth", "")
            belong_year = belong_month[:4]
            if not belong_month or belong_year < start_year or belong_year > end_year:
                continue
            sign = item.get("sign", "").strip()
            if sign not in ["销项", "进项"]:
                continue
            amount = float(item.get("amount", 0.0))
            if sign == "销项" and item["sellerTaxIde"] == self.company_taxid:
                yearly_result[belong_year]["sales_amount"] += amount
                yearly_result[belong_year]["sales_count"] += 1
            elif sign == "进项" and item["buyerTaxIde"] == self.company_taxid:
                yearly_result[belong_year]["receive_amount"] += amount
                yearly_result[belong_year]["receive_count"] += 1

        if not len(yearly_result.keys()):
            default_result = ["所属年度", "接受发票份数", "接受发票金额（元）", "销售发票份数", "销售发票金额（元）"]
            default_risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, default_risk_desc
        formatted_threshold1 = "{:,.2f}".format(threshold1 * 100, 2) + "%"
        formatted_threshold2 = "{:,.2f}".format(threshold2 * 100, 2) + "%"

        for year in sorted(yearly_result.keys()):
            receive_count = yearly_result[year]["receive_count"]
            receive_amount = yearly_result[year]["receive_amount"]
            sales_count = yearly_result[year]["sales_count"]
            sales_amount = yearly_result[year]["sales_amount"]
            amount_rate = receive_amount / sales_amount if sales_amount else None
            count_rate = receive_count / sales_count if sales_count else None
            if receive_count or receive_amount or sales_count or sales_amount:
                all_zero = False

            formatted_sales_amount = "{:,.2f}".format(sales_amount, 2)
            # formatted_amount_rate = "{:,.2f}".format(amount_rate*100, 2)+"%" if amount_rate else None
            # formatted_count_rate = "{:,.2f}".format(count_rate*100, 2)+"%" if count_rate else None
            formatted_receive_amount = "{:,.2f}".format(receive_amount, 2)

            final_result.append(
                {
                    "所属年度": year + "年",
                    "接收发票份数": receive_count,
                    "接收发票金额(含税,元)": formatted_receive_amount,
                    "销售发票份数": sales_count,
                    "销售发票金额(含税,元)": formatted_sales_amount,
                }
            )

            # 风险识别：接收发票金额比销售发票金额多 30% 以上，或者接收发票份数比销售发票份数多 50% 以上
            if (amount_rate and amount_rate > 1 + threshold1) or (
                count_rate and count_rate > 1 + threshold2
            ):
                desc = []
                if sales_amount > 0 and (amount_rate > 1 + threshold1):
                    desc.append(
                        f"接收发票金额({formatted_receive_amount}元)较销售发票金额({formatted_sales_amount}元)多{formatted_threshold1}以上"
                    )
                if sales_count > 0 and count_rate > 1 + threshold2:
                    desc.append(
                        f"接收发票份数({receive_count}份)大于销售发票份数({sales_count}份)的{formatted_threshold2}以上"
                    )
                risk_years.append(
                    {
                        "所属年度": year + "年",
                        "接收发票金额(含税,元)": "{:,.2f}".format(receive_amount, 2),
                        "销售发票金额(含税,元)": "{:,.2f}".format(sales_amount, 2),
                        "描述": ",".join(desc),
                    }
                )

        if all_zero:
            final_result = ["所属年度", "接受发票份数", "接受发票金额（元）", "销售发票份数", "销售发票金额（元）"]
        # 生成风险描述
        risk_desc = "风险描述："
        if risk_years:
            risk_desc += f"企业存在“有进无销”或“有进低销”的情况，具体如下：\n"
            risk_desc += ";".join(
                [f"{item['所属年度']}{item['描述']}" for item in risk_years]
            )
            risk_desc += "。综上所述，该企业可能存在虚开发票、虚构成本或存货积压问题。需警惕潜在的偷税漏税行为及交易真实性问题。"
        else:
            risk_desc += "该指标项未检测到风险。"

        return final_result, risk_desc
