from biz.yishuitong.model import CreateTaxReportRequest

from biz.yishuitong.calculate_functions_chapter3 import Report<PERSON>hapter3
from biz.yishuitong.calculate_functions_chapter4 import ReportChapter4
from biz.yishuitong.calculate_functions_chapter5 import ReportChapter5
from biz.yishuitong.calculate_functions_chapter6 import <PERSON><PERSON>hapter6
from biz.yishuitong.calculate_functions_chapter7 import ReportChapter7
from biz.yishuitong.calculate_functions_chapter8 import Report<PERSON>hapter8
from biz.yishuitong.calculate_functions_chapter9 import ReportChapter9
from biz.yishuitong.calculate_function_chapter10 import ReportChapter10

from common.logging.logger import get_logger

logger = get_logger(__name__)


def check_result_data_type(data):
    if not data:
        return
    if not isinstance(data, list):
        logger.error(f"data must be a list of dict, data: {data}")
        raise TypeError("data must be a list")
    if not isinstance(data[0], dict):
        logger.error(f"data must be a list of dict, data: {data}")
        raise TypeError("data must be a list of dict")


def wrap_method(method):
    def wrapper(*args, **kwargs):
        result = method(*args, **kwargs)
        # 如果返回值是元组，检查第一个元素
        if isinstance(result, tuple) and len(result) > 0:
            check_result_data_type(result[0])
        # 如果返回值不是元组，直接检查
        else:
            check_result_data_type(result)
        return result

    return wrapper


class ReportBaseMeta(type):
    def __new__(cls, name, bases, attrs):
        # 遍历所有属性
        for attr_name, attr_value in attrs.items():
            # 只处理方法，排除特殊方法和私有方法
            if callable(attr_value) and not attr_name.startswith("_"):
                attrs[attr_name] = wrap_method(attr_value)

        return super().__new__(cls, name, bases, attrs)


class ReportBase(
    ReportChapter3,
    ReportChapter4,
    ReportChapter5,
    ReportChapter6,
    ReportChapter7,
    ReportChapter8,
    ReportChapter9,
    ReportChapter10,
    metaclass=ReportBaseMeta,
):
    def __init__(self, req: CreateTaxReportRequest) -> None:
        self.company_taxid = req.taxpayerId

    def __getattribute__(self, name):
        # 获取属性
        attr = super().__getattribute__(name)
        # 如果是方法且不是特殊方法或私有方法
        if callable(attr) and not name.startswith("_"):
            # 如果还没有被包装，则包装它
            if not hasattr(attr, "_wrapped"):
                wrapped = wrap_method(attr)
                wrapped._wrapped = True
                return wrapped
        return attr
