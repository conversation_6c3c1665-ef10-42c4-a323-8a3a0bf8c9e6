import json
from collections import defaultdict
from decimal import Decimal
import math
from dateutil.relativedelta import relativedelta
from datetime import datetime


class ReportChapter10:
    # 10.1
    def calculate_operating_assets(self, dt, data, threshold1=0, threshold2=0.2):
        # 10.1  运营资产
        # 税号确认
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            default_result = ["所属期", "流动资产（元）", "流动负债（元）", "运营资产（元）"]
            default_risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, default_risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        annual_data = defaultdict(
            lambda: {"current_asset_amount": 0.00, "current_balance_amount": 0.00}
        )

        """step2: 资产负债表：（总资产-资产合计）期初+期末/2"""
        # 提取financeBalance数据：取年报表
        financeBalance = data["data"]["financeBalance"]
        for balance in financeBalance:
            dt_end_year = datetime.strptime(
                balance["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if balance["period"] != "Year":
                continue
            ending_balance = float(balance["endingBalance"] or 0)
            init_balance = float(balance["initialBalance"] or 0)
            avg_balance = (ending_balance + init_balance) / 2

            if balance["projectName"] == "流动资产合计":
                annual_data[dt_end_year]["current_asset_amount"] = avg_balance
            if balance["projectName"] == "流动负债合计":
                annual_data[dt_end_year]["current_balance_amount"] = avg_balance

        """数据分析"""
        result = []
        risk_years = []
        all_zero = True
        sorted_years = sorted(annual_data.keys(), reverse=False)
        formatted_threshold1 = "{:,.2f}".format(threshold1, 2)
        formatted_threshold2 = "{:,.2f}".format(threshold2 * 100, 2) + "%"
        for year in sorted_years:
            current_asset_amount = annual_data[year]["current_asset_amount"]
            current_balance_amount = annual_data[year]["current_balance_amount"]
            operate_asset_amount = current_asset_amount - current_balance_amount
            operate_asset_rate = (
                operate_asset_amount / current_asset_amount
                if current_asset_amount
                else None
            )
            if current_asset_amount or current_balance_amount:
                all_zero = False

            # 格式化
            formatted_current_asset_amount = "{:,.2f}".format(current_asset_amount, 2)
            formatted_current_balance_amount = "{:,.2f}".format(
                current_balance_amount, 2
            )
            formatted_operate_asset_amount = (
                "{:,.2f}".format(operate_asset_amount, 2)
                if operate_asset_amount
                else "--"
            )
            formatted_operate_asset_rate = (
                "{:,.2f}".format(operate_asset_rate * 100, 2) + "%"
                if operate_asset_rate
                else "--"
            )

            result.append(
                {
                    "所属期": year + "年",
                    "流动资产（元）": formatted_current_asset_amount,
                    "流动负债（元）": formatted_current_balance_amount,
                    "运营资产（元）": formatted_operate_asset_amount,
                }
            )
            if (operate_asset_amount and operate_asset_amount < threshold1) or (
                operate_asset_rate and operate_asset_rate < threshold2
            ):
                risk_years.append(
                    {
                        "所属期": year + "年",
                        "流动资产（元）": formatted_current_asset_amount,
                        "流动负债（元）": formatted_current_balance_amount,
                        "运营资产（元）": formatted_operate_asset_amount,
                        "描述": f"运营资产({formatted_operate_asset_rate})低于{formatted_threshold1}"
                        if operate_asset_amount and operate_asset_amount < threshold1
                        else f"运营资产占流动资产的比例({formatted_operate_asset_rate})低于{formatted_threshold2}",
                    }
                )

        if all_zero:
            result = ["所属期", "流动资产（元）", "流动负债（元）", "运营资产（元）"]

        if risk_years:
            risk_desc = f"风险描述：{company_name}在过去3年中，"
            risk_desc += "、".join([f"{item['所属期']}{item['描述']}" for item in risk_years])
            risk_desc += f"。运营资产为负值或者占流动资产比例低于一定阈值时，表明企业流动负债超过流动资产。短期偿债能力不足，需关注流动资产的质量和流动负债的规模，避免因资金链紧张而引发财务危机。"

        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return result, risk_desc

    # 10.2
    def calculate_current_ratio(self, dt, data, threshold=1.5):
        # 10.2 流动比率
        # 税号确认
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            default_result = ["所属期", "流动资产（元）", "流动负债（元）", "流动比率"]
            default_risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, default_risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        annual_data = defaultdict(
            lambda: {"current_asset_amount": 0.00, "current_balance_amount": 0.00}
        )

        """step2: 资产负债表：（总资产-资产合计）期初+期末/2"""
        # 提取financeBalance数据：取年报表
        financeBalance = data["data"]["financeBalance"]
        for balance in financeBalance:
            dt_end_year = datetime.strptime(
                balance["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if balance["period"] != "Year":
                continue
            ending_balance = float(balance["endingBalance"] or 0)
            init_balance = float(balance["initialBalance"] or 0)
            avg_balance = (ending_balance + init_balance) / 2

            if balance["projectName"] == "流动资产合计":
                annual_data[dt_end_year]["current_asset_amount"] = avg_balance
            if balance["projectName"] == "流动负债合计":
                annual_data[dt_end_year]["current_balance_amount"] = avg_balance

        """数据分析"""
        result = []
        risk_years = []
        all_zero = True
        sorted_years = sorted(annual_data.keys(), reverse=False)
        for year in sorted_years:
            current_asset_amount = annual_data[year]["current_asset_amount"]
            current_balance_amount = annual_data[year]["current_balance_amount"]
            current_rate = (
                current_asset_amount / current_balance_amount
                if current_balance_amount
                else None
            )
            if current_asset_amount or current_balance_amount:
                all_zero = False

            # 格式化
            formatted_current_asset_amount = "{:,.2f}".format(current_asset_amount, 2)
            formatted_current_balance_amount = "{:,.2f}".format(
                current_balance_amount, 2
            )
            formatted_current_rate = (
                "{:,.2f}".format(current_rate, 2) if current_rate else "--"
            )

            result.append(
                {
                    "所属期": year + "年",
                    "流动资产（元）": formatted_current_asset_amount,
                    "流动负债（元）": formatted_current_balance_amount,
                    "流动比率": formatted_current_rate,
                }
            )
            if current_rate < threshold:
                risk_years.append(
                    {
                        "所属期": year + "年",
                        "流动资产（元）": formatted_current_asset_amount,
                        "流动负债（元）": formatted_current_balance_amount,
                        "流动比率": formatted_current_rate,
                    }
                )

        if all_zero:
            result = ["所属期", "流动资产（元）", "流动负债（元）", "流动比率"]

        if risk_years:
            risk_desc = f"风险描述：{company_name}在过去3年中，"
            risk_desc += "、".join(
                [f"{item['所属期']}({item['流动比率']})" for item in risk_years]
            )
            risk_desc += f"的流速比率低于{threshold}。流动比率偏低可能表明企业短期偿债能力不足，存在流动资产不足或流动负债过高的问题，需关注企业的流动性和资金周转情况。"

        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return result, risk_desc

    # 10.3
    def calculate_quick_ratio(self, dt, data, threshold=1):
        # 10.3 速动比率
        # 税号确认
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            default_result = ["所属期", "速动资产（元）", "流动负债（元）", "速动比率"]
            default_risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, default_risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        annual_data = defaultdict(
            lambda: {"quick_asset_amount": 0.00, "current_balance_amount": 0.00}
        )

        """step2: 资产负债表：（总资产-资产合计）期初+期末/2"""
        # 提取financeBalance数据：取年报表
        financeBalance = data["data"]["financeBalance"]
        for balance in financeBalance:
            dt_end_year = datetime.strptime(
                balance["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if balance["period"] != "Year":
                continue
            ending_balance = float(balance["endingBalance"] or 0)
            init_balance = float(balance["initialBalance"] or 0)
            avg_balance = (ending_balance + init_balance) / 2

            if balance["projectName"] in ["货币资金", "应收票据", "应收账款"]:
                annual_data[dt_end_year]["quick_asset_amount"] += avg_balance
            if balance["projectName"] == "流动负债合计":
                annual_data[dt_end_year]["current_balance_amount"] = avg_balance
        """数据分析"""
        result = []
        risk_years = []
        all_zero = True
        sorted_years = sorted(annual_data.keys(), reverse=False)
        for year in sorted_years:
            quick_asset_amount = annual_data[year]["quick_asset_amount"]
            current_balance_amount = annual_data[year]["current_balance_amount"]
            quick_rate = (
                quick_asset_amount / current_balance_amount
                if current_balance_amount
                else None
            )
            if quick_asset_amount or current_balance_amount:
                all_zero = False

            # 格式化
            formatted_quick_asset_amount = "{:,.2f}".format(quick_asset_amount, 2)
            formatted_current_balance_amount = "{:,.2f}".format(
                current_balance_amount, 2
            )
            formatted_quick_rate = (
                "{:,.2f}".format(quick_rate, 2) if quick_rate else "--"
            )

            result.append(
                {
                    "所属期": year + "年",
                    "速动资产（元）": formatted_quick_asset_amount,
                    "流动负债（元）": formatted_current_balance_amount,
                    "速冻比率": formatted_quick_rate,
                }
            )
            if quick_rate < threshold:
                risk_years.append(
                    {
                        "所属期": year + "年",
                        "速动资产（元）": formatted_quick_asset_amount,
                        "流动负债（元）": formatted_current_balance_amount,
                        "速动比率": formatted_quick_rate,
                    }
                )

        if all_zero:
            result = ["所属期", "速动资产（元）", "流动负债（元）", "速动比率"]

        if risk_years:
            risk_desc = f"风险描述：{company_name}在过去3年中，"
            risk_desc += "、".join(
                [f"{item['所属期']}({item['速动比率']})" for item in risk_years]
            )
            risk_desc += f"速动比率均低于{threshold}。速动比率偏低反映企业短期偿债能力较弱，即使剔除存货等流动性较差的资产后，企业可能难以偿还短期债务，需关注速动资产的质量及流动负债的管理。"

        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return result, risk_desc

    # 10.4
    def calculate_cash_ratio(self, dt, data, threshold=0.2):
        # 10.4 现金比率
        # 税号确认
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            default_result = ["所属期", "货币资金（元）", "流动负债（元）", "现金比率"]
            default_risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, default_risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        annual_data = defaultdict(
            lambda: {"cash_asset_amount": 0.00, "current_balance_amount": 0.00}
        )

        """step2: 资产负债表：（总资产-资产合计）期初+期末/2"""
        # 提取financeBalance数据：取年报表
        financeBalance = data["data"]["financeBalance"]
        for balance in financeBalance:
            dt_end_year = datetime.strptime(
                balance["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if balance["period"] != "Year":
                continue
            ending_balance = float(balance["endingBalance"] or 0)
            init_balance = float(balance["initialBalance"] or 0)
            avg_balance = (ending_balance + init_balance) / 2

            if balance["projectName"] in ["货币资金"]:
                annual_data[dt_end_year]["cash_asset_amount"] = avg_balance
            if balance["projectName"] == "流动负债合计":
                annual_data[dt_end_year]["current_balance_amount"] = avg_balance
        """数据分析"""
        result = []
        risk_years = []
        all_zero = True
        sorted_years = sorted(annual_data.keys(), reverse=False)
        for year in sorted_years:
            cash_asset_amount = annual_data[year]["cash_asset_amount"]
            current_balance_amount = annual_data[year]["current_balance_amount"]
            cash_rate = (
                cash_asset_amount / current_balance_amount
                if current_balance_amount
                else None
            )
            if cash_asset_amount or current_balance_amount:
                all_zero = False

            # 格式化
            formatted_cash_asset_amount = "{:,.2f}".format(cash_asset_amount, 2)
            formatted_current_balance_amount = "{:,.2f}".format(
                current_balance_amount, 2
            )
            formatted_cash_rate = "{:,.2f}".format(cash_rate, 2) if cash_rate else "--"

            result.append(
                {
                    "所属期": year + "年",
                    "货币资金（元）": formatted_cash_asset_amount,
                    "流动负债（元）": formatted_current_balance_amount,
                    "现金比率": formatted_cash_rate,
                }
            )
            if cash_rate < threshold:
                risk_years.append(
                    {
                        "所属期": year + "年",
                        "货币资金（元）": formatted_cash_asset_amount,
                        "流动负债（元）": formatted_current_balance_amount,
                        "现金比率": formatted_cash_rate,
                    }
                )

        if all_zero:
            result = ["所属期", "货币资金（元）", "流动负债（元）", "现金比率"]

        if risk_years:
            risk_desc = f"风险描述：{company_name}在过去3年中，"
            risk_desc += "、".join(
                [f"{item['所属期']}({item['现金比率']})" for item in risk_years]
            )
            risk_desc += (
                f"速动比率均低于{threshold}。现金比率过低表明企业现金储备不足，可能导致短期偿债压力较大，需关注货币资金的管理及企业的流动性风险。"
            )

        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return result, risk_desc

    # 10.5
    def calculate_balance_to_asset_ratio(self, dt, data, threshold=0.7):
        # 10.5 资产负债率
        # 税号确认
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            default_result = ["所属期", "总负债（元）", "总资产（元）", "资产负债率"]
            default_risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, default_risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        annual_data = defaultdict(
            lambda: {"asset_amount": 0.00, "balance_amount": 0.00}
        )

        """资产负债表：（资产合计+负债合计）期初+期末/2"""
        # 提取financeBalance数据：取年报表
        financeBalance = data["data"]["financeBalance"]
        for balance in financeBalance:
            dt_end_year = datetime.strptime(
                balance["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if balance["period"] != "Year":
                continue
            ending_balance = float(balance["endingBalance"] or 0)
            init_balance = float(balance["initialBalance"] or 0)
            avg_balance = (ending_balance + init_balance) / 2

            if balance["projectName"] in ["资产合计"]:
                annual_data[dt_end_year]["asset_amount"] = avg_balance
            if balance["projectName"] == "负债合计":
                annual_data[dt_end_year]["balance_amount"] = avg_balance
        """数据分析"""
        result = []
        risk_years = []
        all_zero = True
        sorted_years = sorted(annual_data.keys(), reverse=False)
        for year in sorted_years:
            asset_amount = annual_data[year]["asset_amount"]
            balance_amount = annual_data[year]["balance_amount"]
            balance_asset_rate = balance_amount / asset_amount if asset_amount else None
            if asset_amount or balance_amount:
                all_zero = False

            # 格式化
            formatted_asset_amount = "{:,.2f}".format(asset_amount, 2)
            formatted_balance_amount = "{:,.2f}".format(balance_amount, 2)
            formatted_balance_asset_rate = (
                "{:,.2f}".format(balance_asset_rate * 100, 2) + "%"
                if balance_asset_rate
                else "--"
            )

            result.append(
                {
                    "所属期": year + "年",
                    "总负债（元）": formatted_balance_amount,
                    "总资产（元）": formatted_asset_amount,
                    "资产负债率": formatted_balance_asset_rate,
                }
            )
            if balance_asset_rate > threshold:
                risk_years.append(
                    {
                        "所属期": year + "年",
                        "总负债（元）": formatted_balance_amount,
                        "总资产（元）": formatted_asset_amount,
                        "资产负债率": formatted_balance_asset_rate,
                    }
                )

        if all_zero:
            result = ["所属期", "总负债（元）", "总资产（元）", "资产负债率"]
        formatted_threshold = "{:,.2f}".format(threshold * 100, 2) + "%"
        if risk_years:
            risk_desc = f"风险描述：{company_name}在过去3年中，"
            risk_desc += "、".join(
                [f"{item['所属期']}({item['资产负债率']})" for item in risk_years]
            )
            risk_desc += f"资产负债率高于{formatted_threshold}。资产负债率过高可能表明企业负债水平过高，偿债压力较大，或存在财务杠杆过度使用的风险，需重点关注企业的债务结构和偿债能力。"

        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return result, risk_desc

    # 10.6
    def calculate_long_term_balance_to_capital_ratio(self, dt, data, threshold=0.5):
        # 10.6 长期资本负债率
        # 税号确认
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            default_result = ["所属期", "非流动负债（元）", "股东权益（元）", "长期资本负债率"]
            default_risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, default_risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        annual_data = defaultdict(
            lambda: {"stock_amount": 0.00, "nocurrent_amount": 0.00}
        )

        """资产负债表：期初+期末/2"""
        # 提取financeBalance数据：取年报表
        financeBalance = data["data"]["financeBalance"]
        for balance in financeBalance:
            dt_end_year = datetime.strptime(
                balance["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if balance["period"] != "Year":
                continue
            ending_balance = float(balance["endingBalance"] or 0)
            init_balance = float(balance["initialBalance"] or 0)
            avg_balance = (ending_balance + init_balance) / 2

            if balance["projectName"] == "所有者权益（或股东权益）合计":
                annual_data[dt_end_year]["stock_amount"] = avg_balance
            if balance["projectName"] == "非流动负债合计":
                annual_data[dt_end_year]["nocurrent_amount"] = avg_balance
        """数据分析"""
        result = []
        risk_years = []
        all_zero = True
        sorted_years = sorted(annual_data.keys(), reverse=False)
        for year in sorted_years:
            stock_amount = annual_data[year]["stock_amount"]
            nocurrent_amount = annual_data[year]["nocurrent_amount"]
            longterm_nocurrent_rate = (
                nocurrent_amount / (stock_amount + nocurrent_amount)
                if stock_amount or nocurrent_amount
                else None
            )
            if stock_amount or nocurrent_amount:
                all_zero = False

            # 格式化
            formatted_stock_amount = "{:,.2f}".format(stock_amount, 2)
            formatted_nocurrent_amount = "{:,.2f}".format(nocurrent_amount, 2)
            formatted_longterm_nocurrent_rate = (
                "{:,.2f}".format(longterm_nocurrent_rate * 100, 2) + "%"
                if longterm_nocurrent_rate
                else "--"
            )

            result.append(
                {
                    "所属期": year + "年",
                    "非流动负债（元）": formatted_nocurrent_amount,
                    "股东权益（元）": formatted_stock_amount,
                    "长期资本负债率": formatted_longterm_nocurrent_rate,
                }
            )
            if longterm_nocurrent_rate > threshold:
                risk_years.append(
                    {
                        "所属期": year + "年",
                        "非流动负债（元）": formatted_nocurrent_amount,
                        "股东权益（元）": formatted_stock_amount,
                        "长期资本负债率": formatted_longterm_nocurrent_rate,
                    }
                )

        if all_zero:
            result = ["所属期", "非流动负债（元）", "股东权益（元）", "长期资本负债率"]
        formatted_threshold = "{:,.2f}".format(threshold * 100, 2) + "%"
        if risk_years:
            risk_desc = f"风险描述：{company_name}在过去3年中，"
            risk_desc += "、".join(
                [f"{item['所属期']}({item['长期资本负债率']})" for item in risk_years]
            )
            risk_desc += f"长期资本负债率高于{formatted_threshold}。长期资本负债率过高表明企业长期资金来源中负债比例较高，可能导致企业资本结构不合理，需关注长期债务的管理及股东权益的补充。"

        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return result, risk_desc

    # 10.7
    def calculate_equity_ratio(self, dt, data, threshold=1.5):
        # 10.7 产权比率
        # 税号确认
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            default_result = ["所属期", "总负债（元）", "股东权益（元）", "产权比率"]
            default_risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, default_risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        annual_data = defaultdict(
            lambda: {"stock_amount": 0.00, "balance_amount": 0.00}
        )

        """资产负债表：期初+期末/2"""
        # 提取financeBalance数据：取年报表
        financeBalance = data["data"]["financeBalance"]
        for balance in financeBalance:
            dt_end_year = datetime.strptime(
                balance["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if balance["period"] != "Year":
                continue
            ending_balance = float(balance["endingBalance"] or 0)
            init_balance = float(balance["initialBalance"] or 0)
            avg_balance = (ending_balance + init_balance) / 2

            if balance["projectName"] == "所有者权益（或股东权益）合计":
                annual_data[dt_end_year]["stock_amount"] = avg_balance
            if balance["projectName"] == "负债合计":
                annual_data[dt_end_year]["balance_amount"] = avg_balance
        """数据分析"""
        result = []
        risk_years = []
        all_zero = True
        sorted_years = sorted(annual_data.keys(), reverse=False)
        for year in sorted_years:
            stock_amount = annual_data[year]["stock_amount"]
            balance_amount = annual_data[year]["balance_amount"]
            balance_stock_rate = balance_amount / stock_amount if stock_amount else None
            if stock_amount or balance_amount:
                all_zero = False

            # 格式化
            formatted_stock_amount = "{:,.2f}".format(stock_amount, 2)
            formatted_balance_amount = "{:,.2f}".format(balance_amount, 2)
            formatted_balance_stock_rate = (
                "{:,.2f}".format(balance_stock_rate, 2) if balance_stock_rate else "--"
            )

            result.append(
                {
                    "所属期": year + "年",
                    "总负债（元）": formatted_balance_amount,
                    "股东权益（元）": formatted_stock_amount,
                    "产权比率": formatted_balance_stock_rate,
                }
            )
            if balance_stock_rate > threshold:
                risk_years.append(
                    {
                        "所属期": year + "年",
                        "总负债（元）": formatted_balance_amount,
                        "股东权益（元）": formatted_stock_amount,
                        "产权比率": formatted_balance_stock_rate,
                    }
                )

        if all_zero:
            result = ["所属期", "总负债（元）", "股东权益（元）", "产权比率"]
        formatted_threshold = "{:,.2f}".format(threshold, 2)
        if risk_years:
            risk_desc = f"风险描述：{company_name}在过去3年中，"
            risk_desc += "、".join(
                [f"{item['所属期']}({item['产权比率']})" for item in risk_years]
            )
            risk_desc += f"，产权比率高于{formatted_threshold}。产权比率过高表明企业总负债相较于股东权益比例偏高，可能导致企业财务风险增加，需关注债务偿还能力及资本结构的优化。"

        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return result, risk_desc

    # 10.8
    def calculate_equity_multiplier(self, dt, data, threshold=2.5):
        # 10.8 权益乘数
        # 税号确认
        taxid = data["data"]["enterpriseInfo"]["taxpayerId"]
        if taxid != self.company_taxid:
            default_result = ["所属期", "总资产（元）", "股东权益（元）", "权益乘数"]
            default_risk_desc = "风险描述：该指标项未检测到风险"
            return default_result, default_risk_desc
        company_name = data["data"]["enterpriseInfo"]["taxpayerName"]

        # 时间处理
        dt_format = datetime.strptime(dt, "%Y%m")
        end_year = (dt_format - relativedelta(years=1)).strftime("%Y")
        start_year = (dt_format - relativedelta(years=3)).strftime("%Y")
        annual_data = defaultdict(lambda: {"stock_amount": 0.00, "asset_amount": 0.00})

        """资产负债表：期初+期末/2"""
        # 提取financeBalance数据：取年报表
        financeBalance = data["data"]["financeBalance"]
        for balance in financeBalance:
            dt_end_year = datetime.strptime(
                balance["endDate"], "%Y-%m-%d %H:%M:%S"
            ).strftime("%Y")
            if dt_end_year > end_year or dt_end_year < start_year:
                continue
            if balance["period"] != "Year":
                continue
            ending_balance = float(balance["endingBalance"] or 0)
            init_balance = float(balance["initialBalance"] or 0)
            avg_balance = (ending_balance + init_balance) / 2

            if balance["projectName"] == "所有者权益（或股东权益）合计":
                annual_data[dt_end_year]["stock_amount"] = avg_balance
            if balance["projectName"] == "资产合计":
                annual_data[dt_end_year]["asset_amount"] = avg_balance
        """数据分析"""
        result = []
        risk_years = []
        all_zero = True
        sorted_years = sorted(annual_data.keys(), reverse=False)
        for year in sorted_years:
            stock_amount = annual_data[year]["stock_amount"]
            asset_amount = annual_data[year]["asset_amount"]
            asset_stock_rate = asset_amount / stock_amount if stock_amount else None
            if stock_amount or asset_amount:
                all_zero = False

            # 格式化
            formatted_stock_amount = "{:,.2f}".format(stock_amount, 2)
            formatted_asset_amount = "{:,.2f}".format(asset_amount, 2)
            formatted_asset_stock_rate = (
                "{:,.2f}".format(asset_stock_rate, 2) if asset_stock_rate else "--"
            )

            result.append(
                {
                    "所属期": year + "年",
                    "总资产（元）": formatted_asset_amount,
                    "股东权益（元）": formatted_stock_amount,
                    "权益乘数": formatted_asset_stock_rate,
                }
            )
            if asset_stock_rate > threshold:
                risk_years.append(
                    {
                        "所属期": year + "年",
                        "总资产（元）": formatted_asset_amount,
                        "股东权益（元）": formatted_stock_amount,
                        "权益乘数": formatted_asset_stock_rate,
                    }
                )

        if all_zero:
            result = ["所属期", "总资产（元）", "股东权益（元）", "权益乘数"]
        formatted_threshold = "{:,.2f}".format(threshold, 2)
        if risk_years:
            risk_desc = f"风险描述：{company_name}在过去3年中，"
            risk_desc += "、".join(
                [f"{item['所属期']}({item['权益乘数']})" for item in risk_years]
            )
            risk_desc += f"，权益乘数高于{formatted_threshold}。权益乘数过高反映企业资产对股东权益的依赖较低，但可能伴随较高的财务杠杆，需关注企业是否过度依赖负债融资，增加财务风险。"

        else:
            risk_desc = "风险描述：该指标项未检测到风险"

        return result, risk_desc
