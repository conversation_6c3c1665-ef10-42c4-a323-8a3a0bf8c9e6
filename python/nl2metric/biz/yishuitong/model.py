import enum
from typing import Optional

from pydantic import BaseModel
from sqlalchemy import (
    Integer,
    String,
)
from sqlalchemy.orm import Mapped, mapped_column

from common.db_model.model import Base
from common.fs.fs import get_s3_file_system
from nl2document.common.models.model import ONE_DAY_SECONDS


class CreateTaxReportRequest(BaseModel):
    orderId: str
    taxpayerId: str
    taxNo: str
    email: Optional[str] = None
    # tax_data: Optional[dict] = None
    # fapiao_data: Optional[dict] = None


class CheckReportStatus(BaseModel):
    orderId: str


class ReportStatus(enum.Enum):
    PENDING = "pending"
    COMPLETED = "completed"
    ERROR = "error"


class TaxReport(Base):
    __tablename__ = "tax_report"

    order_id: Mapped[int] = mapped_column(
        Integer, primary_key=True, comment="订单编号唯一标识符"
    )
    status: Mapped[str] = mapped_column(
        String(16), nullable=False, default=ReportStatus.PENDING.value, comment="报告状态"
    )
    path: Mapped[str] = mapped_column(String(1024), nullable=True, comment="生成的报告文件路径")
    message: Mapped[str] = mapped_column(
        String(1024), nullable=True, comment="生成报告状态信息"
    )
    code: Mapped[int] = mapped_column(
        Integer, nullable=True, default=202, comment="状态码"
    )

    @property
    def url(self) -> str:
        if not self.path:
            return ""
        return get_s3_file_system().url(self.path, expires=ONE_DAY_SECONDS)
