def credit_level_records(tax_data):
    creditlevel = tax_data["data"]["creditLevel"]

    result_list = [
        {
            "序号": index,  # 从1开始递增的序号
            "评价年度": item["year"][:4],
            "评价等级": item.get("creditLevel", "未知"),
            "评价得分": (
                float(item["creditPoint"])
                if isinstance(item.get("creditPoint"), (int, float, str))
                and str(item["creditPoint"]).strip() not in ("null", "NaN", "")
                else None
            ),
        }
        for index, item in enumerate(creditlevel, start=1)  # 设置start=1，序号从1开始
    ]

    return result_list
