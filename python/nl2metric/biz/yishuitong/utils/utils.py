from datetime import datetime
from itertools import groupby
from operator import itemgetter


class Utils(object):
    """
    工具类,包含一些通用的静态方法
    """

    __slots__ = ()  # 防止动态添加属性

    @staticmethod
    def get_max_date_name(content, ID, name, dt):
        # 首先按id排序，因为groupby需要输入是排序好的
        content.sort(key=itemgetter(ID))

        result = []
        result_dict = dict()

        # 按id分组
        for key, group in groupby(content, key=itemgetter(ID)):
            # 在每个组中找到date最大的name
            max_date_item = max(group, key=itemgetter(dt))
            result.append(
                {"id": key, "name": max_date_item[name], "date": max_date_item[dt]}
            )
            result_dict[key] = max_date_item[name]
        return result_dict

    @staticmethod
    def convert_to_year_month(date_str):
        # 使用 datetime 解析 YYYYMM 格式的日期
        date = datetime.strptime(date_str, "%Y%m")

        # 格式化为“*年*月”
        return date.strftime("%Y年%m月")

    @staticmethod
    def is_integer_amount(amount: float) -> bool:
        """判断金额是否为整数"""
        return amount.is_integer()
