import json
import os
import uuid
from datetime import datetime
from typing import List

from fastapi import Request
from langchain_core.messages import ChatMessage

from biz.yishuitong.calculate_function_chapter2 import credit_level_records
from biz.yishuitong.graph_generator import (
    radder_graph_generator,
)
from biz.yishuitong.model import CreateTaxReportRequest
from biz.yishuitong.report_base import ReportBase
from common.llm.general import create_chat_model
from common.utils.json_utils import extract_json_from_string
from nl2document.common.utils import save_to_pdf


class Section:
    def __init__(
        self,
        title: str,
        subsections: List,
        data: List = None,
        metric_description: str = None,
        risk_description: str = None,
        has_risk: bool = False,
        image_paths: List[str] = None,
    ):
        self.title = title
        self.subsections = subsections
        self.data = data
        self.image_paths = image_paths
        self.metric_description = metric_description
        self.has_risk = has_risk
        self.risk_description = risk_description

    def add_section(self, section):
        self.subsections.append(section)

    def insert_section(self, section, index):
        self.subsections.insert(index, section)

    def to_json(self):
        ret = {
            "level": self.level,
            "order": self.order,
            "title": self.title,
            "subsection": [section.to_json() for section in self.subsections],
            "metric_description": self.metric_description,
            "risk_description": self.risk_description,
            "has_risk": self.has_risk,
            "image_path": self.image_paths if self.image_paths is not None else "",
        }
        return ret


def generate_chapter_summary_data(
    chapter: Section, sub_chapter_list: List, risk_num, total_num
):
    result = {
        "章节标题": chapter.title,
        "章节风险占比": risk_num / total_num,
        "章节子模块": [
            {
                "子模块标题": sub_chapter["title"],
                "子模块陈述": sub_chapter["metric_description"],
                "风险描述": sub_chapter["risk_description"],
            }
            for sub_chapter in sub_chapter_list
        ],
    }
    return result


yishuitong_model_type = os.environ.get("yishuitong_model_type", "yi")


async def general_report_summary(company_name, chapter_summaries):
    chapter_summaries_str = "\n\n".join([summary[0] for summary in chapter_summaries])
    prompt = f"""
    你是一名专业的分析师，请根据我提供的{company_name}的报告数据，生成一份结构清晰、重点突出的全文总结报告。

    {chapter_summaries_str}

    要求如下：
    语言精炼简洁，用词专业，并涵盖所有主要内容，对企业进行批判。要求字数不超过500字。以陈述句输出，不要包含特殊符号
    以总-分-总的格式输出：
    {{
        "综合评判":"",
        "一、税务合规风险(风险覆盖率)":"",
        "二、财务操纵风险(风险覆盖率)":"",
        "三、盈利能力及运营风险(风险覆盖率)":"",
        "四、债务及资本结构风险(风险覆盖率)":"",
        "结论":""
    }}
    """
    result = await invoke_chat_model(prompt)
    return result


async def general_chapter_summary(company_name, chapter_info):
    prompt1 = f"""
    你是一名专业的分析师，请根据我提供的{company_name}的报告数据，生成一份结构清晰、重点突出的章节报告。

    {json.dumps(chapter_info, ensure_ascii=False, indent=4)}

    请你将章节中对于{company_name}的所有子模块陈述和风险描述进行整合，并做出该章节的总结，要求语言精炼简洁，同时涵盖主要信息，并对企业进行评判，请检查内容是否太宽泛。输出的总结字数不超过150字。请不要用某企业的字眼换成“与不具备真实业务往来的企业”，同时请不要出现“本章节”类似的字眼。
    标准输出格式:
    {{
        "章节总结": {{
            "章节标题": "",
            "风险覆盖率": "",
            "核心风险点": [
                ""
            ],
            "企业评判": ""
        }}
    }}
    """
    result = await invoke_chat_model(prompt1)
    prompt2 = f"""用一段话来精简内容:已知章节信息为：{result} 
    标准输出格式为:
    {{
        "章节标题": "",
        "内容": ""
    }}
    """
    result2 = await invoke_chat_model(prompt2)
    return result, result2


async def invoke_chat_model(prompt: str):
    chat_model = create_chat_model(yishuitong_model_type)
    response = await chat_model.ainvoke([ChatMessage(role="user", content=prompt)])
    return response.content


async def generate_report(
    req: CreateTaxReportRequest, fapiao_data, tax_data, path: str
):
    report_executor = ReportBase(req)
    current_date = datetime.now().strftime("%Y%m")

    report_result_summary = []
    report_chapter_summary_data_for_llm = {}
    llm_results = []

    report = Section(
        title="report",
        subsections=[],
        metric_description="",
        risk_description="",
    )

    ### chapter2
    chapter2 = Section(
        title="PART 02--企业基本信息",
        subsections=[],
        metric_description="",
        risk_description="",
    )
    info_data = tax_data["data"]["enterpriseInfo"]
    credit_level = tax_data["data"].get("creditLevel", [])
    # data2_1 = {
    #     "统一社会信用代码": [info_data["taxpayerId"]],
    #     "企业名称": [info_data["taxpayerName"]],
    #     "纳税人状态": [info_data["taxpayerStatusName"]],
    #     "企业规模": ["未获取"],
    #     "所属行业": [info_data["industryType"]],
    #     "登记日期": [info_data["registeredDate"]],
    #     "纳税信用等级": [credit_level[0]["creditLevel"] if len(credit_level) >= 1 else "暂无"],
    #     "资格认定": [info_data["taxpayerType"]],
    #     "从业人数": [info_data["employeesNumber"]],
    #     "注册资本": [info_data["registerCapital"]],
    #     "生产经营地址": [info_data["businessAddress"]],
    #     "企业经营范围": [info_data["businessScope"]],
    # }
    # df = pd.DataFrame(data2_1)

    # 转换为Markdown格式
    markdown_table = f"""
<table class="min-w-full border-collapse border border-gray-300">
  <!-- 前五行标准四列 -->
  <tr>
    <td class="border border-gray-300 px-4 py-2" style="width: 25%; padding: 8px;">统一社会信用代码</td>
    <td class="border border-gray-300 px-4 py-2" style="width: 25%; padding: 8px;">{info_data["taxpayerId"]}</td>
    <td class="border border-gray-300 px-4 py-2" style="width: 25%; padding: 8px;">企业名称</td>
    <td class="border border-gray-300 px-4 py-2" style="width: 25%; padding: 8px;">{info_data["taxpayerName"]}</td>
  </tr>
  <tr>
    <td class="border border-gray-300 px-4 py-2">纳税人状态</td>
    <td class="border border-gray-300 px-4 py-2">{info_data["taxpayerStatusName"]}</td>
    <td class="border border-gray-300 px-4 py-2">企业规模</td>
    <td class="border border-gray-300 px-4 py-2">未获取</td>
  </tr>
  <tr>
    <td class="border border-gray-300 px-4 py-2">所属行业</td>
    <td class="border border-gray-300 px-4 py-2">{info_data["industryType"]}</td>
    <td class="border border-gray-300 px-4 py-2">登记日期</td>
    <td class="border border-gray-300 px-4 py-2">{info_data["registeredDate"]}</td>
  </tr>
  <tr>
    <td class="border border-gray-300 px-4 py-2">纳税信用等级</td>
    <td class="border border-gray-300 px-4 py-2">{credit_level[0]["creditLevel"] if len(credit_level) >= 1 else "暂无"}</td>
    <td class="border border-gray-300 px-4 py-2">资格认定</td>
    <td class="border border-gray-300 px-4 py-2">{info_data["taxpayerType"]}</td>
  </tr>
  <tr>
    <td class="border border-gray-300 px-4 py-2">从业人数</td>
    <td class="border border-gray-300 px-4 py-2">{info_data["employeesNumber"]}</td>
    <td class="border border-gray-300 px-4 py-2">注册资本</td>
    <td class="border border-gray-300 px-4 py-2">{info_data["registerCapital"]}</td>
  </tr>

  <!-- 最后两行特殊合并 -->
  <tr>
    <td class="border border-gray-300 px-4 py-2">生产经营地址</td>
    <td class="border border-gray-300 px-4 py-2" colspan="3" style="padding: 8px; word-break: break-all;">
      {info_data["businessAddress"]}
    </td>
  </tr>
  <tr>
    <td class="border border-gray-300 px-4 py-2">企业经营范围</td>
    <td class="border border-gray-300 px-4 py-2" colspan="3" style="padding: 8px; word-break: break-all;">
      {info_data["businessScope"]}
    </td>
  </tr>
</table>
    """

    section_2_1 = {
        "title": "2.1 企业基本工商信息",
        "data": None,
        "metric_description": markdown_table,
        "risk_description": None,
        "has_risk": False,
        "image_path": None,
    }
    section_2_2 = {
        "title": "2.2 信用认定信息",
        "data": credit_level_records(tax_data),
        "metric_description": None,
        "risk_description": None,
        "has_risk": False,
        "image_path": None,
    }
    section_list = [section_2_1, section_2_2]
    for order, section in enumerate(section_list):
        chapter2.add_section(
            Section(
                title=section["title"],
                subsections=[],
                data=section["data"],
                metric_description=section["metric_description"],
                risk_description=section["risk_description"],
                has_risk=section["has_risk"],
                image_paths=section["image_path"],
            )
        )
    report.add_section(chapter2)
    #### chapter3
    chapter3 = Section(
        title="PART 03--发票风险检查",
        subsections=[],
        metric_description="",
        has_risk=False,
        risk_description="",
    )
    data, risk_desc = report_executor.calculate_monthly_stats(current_date, fapiao_data)
    section_3_1 = {
        "title": "3.1 增值税发票（专票+普票）用量变动异常检查",
        "data": data,
        "metric_description": "检测近12个月增值税发票（专票+普票）用量变动率=（本期用票量 - 上期用票量）/上期用票量。变动量若波动较大，极易引起税务稽查。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_top10_items(current_date, fapiao_data)
    section_3_2 = {
        "title": "3.2 前十大销售、采购品目不一致风险检查",
        "data": data,
        "metric_description": "该指标检测近12个月来开票量前十的销项和进项品目名称以及交易金额。反映同时间段开具/接收发票商品代码大类是否一致，差异较大的可能有被核查的风险。",
        "risk_description": risk_desc,
    }

    data, risk_desc = report_executor.calculate_mutual_invoices(
        current_date, fapiao_data
    )
    section_3_3 = {
        "title": "3.3 互开发票疑点检查",
        "data": data,
        "metric_description": "该指标检测近12个月中存在互开发票的企业清单。互开发票也称对开发票，是指上下游企业之间互相开具销项发票的情况。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_top10_customers(
        current_date, fapiao_data
    )
    section_3_4 = {
        "title": "3.4 前十大客户风险分析",
        "data": data,
        "metric_description": "该指标检测近12个月销售客户中销售额排名前十的企业的交易情况。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_top10_products_by_year(
        current_date, fapiao_data
    )
    section_3_5 = {
        "title": "3.5 销售发票top10商品种类",
        "data": data,
        "metric_description": "该指标检测近4年货物劳务销售情况。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_top10_suppliers(
        current_date, fapiao_data
    )
    section_3_6 = {
        "title": "3.6 前十大供应商风险分析",
        "data": data,
        "metric_description": "该指标检测近12个月供应商中采购额排名前十的企业的交易情况。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_top10_procurement_items(
        current_date, fapiao_data
    )
    section_3_7 = {
        "title": "3.7 采购发票top10商品种类",
        "data": data,
        "metric_description": "该指标检测近4年货物劳务采购情况。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_voided_ratio(current_date, fapiao_data)
    section_3_8 = {
        "title": "3.8 增值税发票大量作废疑点检查",
        "data": data,
        "metric_description": "该指标检测近12个月，作废金额比例 = （作废份数÷总份数）× 100%；增值税发票用量骤增，除正常业务变化外，可能有虚开现象。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_hc_ratio(current_date, fapiao_data)
    section_3_9 = {
        "title": "3.9 增值税发票大量红冲疑点检查",
        "data": data,
        "metric_description": "该指标检测近12个月，红冲金额比例 = （红冲份数÷总份数）× 100%；发票红冲异常或申报差异巨大，可能存在虚开普通发票或隐瞒销售收入的风险。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_zero_tax_ratio(
        current_date, fapiao_data
    )
    section_3_10 = {
        "title": "3.10 大量取得税额为零的发票风险检查",
        "data": data,
        "metric_description": "该指标检测近12个月，零税额开票金额占比=税额为零的发票金额/营业收入，当取得发票金额合计超过10万元，此指标生效。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_outside_province_ratio(
        current_date, fapiao_data
    )
    section_3_11 = {
        "title": "3.11 向外省企业开具发票占比过多检查",
        "data": data,
        "metric_description": "该指标检测近12个月，省外发票金额占比=省外发票金额/发票金额合计，当开具发票金额合计超过10万元，此指标生效。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_proxy_invoice_ratio(
        current_date, fapiao_data
    )
    section_3_12 = {
        "title": "3.12 收取代开发票过多检查",
        "data": data,
        "metric_description": "该指标检测近12个月，接收代开发票金额占比=接收代开发票金额/接收发票总金额，当接收发票合计超过10万元时指标生效；占比较大时，企业存在人为调整利润，虚构交易、成本等风险，可能需核查业务链条的完整性和合理性。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_service_fee_check(
        current_date, fapiao_data
    )
    section_3_13 = {
        "title": "3.13 取得“咨询服务费”“会议服务费”“手续费”发票风险检查",
        "data": data,
        "metric_description": "该指标检测近12个月，服务费金额占比=服务费发票金额/营业收入或发票金额合计超过10万；“咨询服务费”、“会议费”、“手续费”“其他现代服务费”品目的发票金额占比较大，存在虚列成本、逃避缴纳企业所得税的风险；除指标对应的比例，对于大额整数类的此类品目的发票可能也会被列为重点监控，企业在考虑此类风险时，请综合考虑评估自身的业务合理性。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.check_invoice_limit_risk(
        current_date, fapiao_data
    )
    section_3_14 = {
        "title": "3.14 顶额开票异常检查",
        "data": data,
        "metric_description": "该指标检测近12个月，大量发票票面金额接近发票单张限额，可能存在虚开风险。（非数电票）",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.analyze_monthly_invoice(current_date, fapiao_data)
    section_3_15 = {
        "title": "3.15 收取个体工商户发票检查",
        "data": data,
        "metric_description": "该指标检测近12个月，成本类发票开票主体多为个体户开票，可能存在虚列成本、少缴税款等风险。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.check_large_amounts(current_date, fapiao_data)
    section_3_16 = {
        "title": "3.16 进项发票中单张较大金额且价税合计为整数",
        "data": data,
        "metric_description": "该指标检测近12个月，多张大额整数发票，可能存在接受虚开、虚列成本、虚增进项等风险。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.check_invalid_invoices(current_date, fapiao_data)
    section_3_17 = {
        "title": "3.17 收取大额作废发票",
        "data": data,
        "metric_description": "该指标检测近12个月，进项发票存在大金额发票的作废现象，可能存在虚增进项，骗取期末留抵退税等风险；进项发票被大量作废，上游企业可能存在人为调整增值税税负的风险，牵连下游受票企业。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_receive_outside_province_ratio(
        current_date, fapiao_data
    )
    section_3_18 = {
        "title": "3.18 接收外省企业开具发票占比过多检查",
        "data": data,
        "metric_description": "该指标检测近12个月，接收省外发票金额占比=省外发票金额/接收发票金额合计，当接收发票金额合计超过10元时指标生效。",
        "risk_description": risk_desc,
    }
    section_list = [
        section_3_1,
        section_3_2,
        section_3_3,
        section_3_4,
        section_3_5,
        section_3_6,
        section_3_7,
        section_3_8,
        section_3_9,
        section_3_10,
        section_3_11,
        section_3_12,
        section_3_13,
        section_3_14,
        section_3_15,
        section_3_16,
        section_3_17,
        section_3_18,
    ]
    risk_num = 0
    total_num = 0
    for order, section in enumerate(section_list):
        chapter3.add_section(
            Section(
                title=section["title"],
                subsections=[],
                data=section["data"],
                metric_description=section["metric_description"],
                risk_description=section["risk_description"],
                has_risk="该指标项未检测到" not in section["risk_description"],
            )
        )
        total_num += 1
        if "该指标项未检测到" not in section["risk_description"]:
            risk_num += 1

    # report_chapter_summary_data_for_llm["章节编号3"] = generate_chapter_summary_data(
    #     chapter3, section_list, risk_num, total_num
    # )
    llm_results.append(
        await general_chapter_summary(
            info_data["taxpayerName"],
            generate_chapter_summary_data(chapter3, section_list, risk_num, total_num),
        )
    )
    report_result_summary.append((risk_num, total_num))
    report.add_section(chapter3)
    #### chapter4
    chapter4 = Section(
        title="PART 04--税负风险检查",
        subsections=[],
        metric_description="",
        risk_description="",
        has_risk=False,
    )
    data, risk_desc = report_executor.calculate_tax_burden_modulus(
        current_date, tax_data
    )
    section_4_1 = {
        "title": "4.1 增值税税负变动率检查",
        "data": data,
        "metric_description": "税负率=(本期应纳税额合计)÷(本期增值税应税销售额+免、抵、退办法出口货物销售额+免税销售额)×100%；税负变动率=(本期税负-上期税负)÷上期税负*100%。纳税人自身税负变化过大，可能存在账外经营、已实现纳税义务而未结转收入、取得进项税额不符合规定、享受税收优惠政策期间购进货物不取得可抵扣进项税额发票或虚开发票等问题。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_income_tax_contribution_rate(
        current_date, tax_data
    )
    section_4_2 = {
        "title": "4.2 企业所得税贡献率检查",
        "data": data,
        "metric_description": "企业所得税贡献率=（实际应纳所得税额/营业收入）×100%；企业所得税贡献率反映的是企业所得税缴纳税额与收入的比例关系，在对所得税进行纳税评估时最为关注的指标。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_taxable_income_rate(
        current_date, tax_data
    )
    section_4_3 = {
        "title": "4.3 应税所得率检查",
        "data": data,
        "metric_description": "应税所得率=纳税调整后所得/营业收入×100%；应税所得率就是应纳税所得额与销售收入的比率，行业不同，应税所得率也有所不同，总体区间大约为【5%--25%】。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.zero_declaration_detection(current_date, tax_data)
    section_4_4 = {
        "title": "4.4 增值税连续3个月零申报检测",
        "data": data,
        "metric_description": "增值税一般纳税人需关注“本期应补(退)税额”连续三个月是否等于零。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.sales_volume_exceeded_detection(
        current_date, tax_data
    )
    section_4_5 = {
        "title": "4.5 小规模纳税人销售额超标检测",
        "data": data,
        "metric_description": "小规模纳税人短期内销售超过500万或连续多月零申报，收入额快速超过500万",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.begin_end_tax(current_date, tax_data)
    section_4_6 = {
        "title": "4.6 期初留抵税额与上期期末留抵税额核对分析",
        "data": data,
        "metric_description": "增值税一般纳税人需关注近12个月申报表中是否存在期初留抵税额与上期期末留抵税额不相等的情况。",
        "risk_description": risk_desc,
    }
    section_list = [
        section_4_1,
        section_4_2,
        section_4_3,
        section_4_4,
        section_4_5,
        section_4_6,
    ]
    total_num = 0
    risk_num = 0
    for order, section in enumerate(section_list):
        chapter4.add_section(
            Section(
                title=section["title"],
                subsections=[],
                data=section["data"],
                metric_description=section["metric_description"],
                risk_description=section["risk_description"],
                has_risk="该指标项未检测到" not in section["risk_description"],
            )
        )
        total_num += 1
        if "该指标项未检测到" not in section["risk_description"]:
            risk_num += 1
    # report_chapter_summary_data_for_llm["章节编号4"] = generate_chapter_summary_data(
    #     chapter4, section_list, risk_num, total_num
    # )
    llm_results.append(
        await general_chapter_summary(
            info_data["taxpayerName"],
            generate_chapter_summary_data(chapter4, section_list, risk_num, total_num),
        )
    )
    report_result_summary.append((risk_num, total_num))
    report.add_section(chapter4)
    ###chapter5
    chapter5 = Section(
        title="PART 05--申报真实性风险检查",
        subsections=[],
        metric_description="",
        risk_description="",
        has_risk=False,
    )
    data, risk_desc = report_executor.calculate_elastic_modulus(current_date, tax_data)
    section_5_1 = {
        "title": "5.1 销售额变动率与增值税应纳税额变动率弹性系数检查",
        "data": data,
        "metric_description": """弹性系数=A/B；A=销售额变动率；B、应纳税额合计变动率；① 正常情况下两者应基本同步增长或下降，弹性系数应接近1。 ② 若弹性系数>1且销售额变动率和应纳税额变动率都为正数则可能存在问题; ③ 若弹性系数<1且销售额变动率和应纳税额变动率都为负数则可能存在问题; ④ 若弹性系数<1且销售额变动率和应纳税额变动率都为正数则未获取相应数据问题; ⑤ 若销售额变动率和应纳税额变动率都为负数则可能存在问题; ⑥ 应纳税额变动率为正且销售额变动率前者为负则未获取相应数据问题。 ⑦ 当弹性系数为负数，销售额变动率为正应纳税额变动率为负时，可能存在问题；""",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_tax_growth_rate(current_date, tax_data)
    section_5_2 = {
        "title": "5.2 增值税应纳税额同比变动率检查",
        "data": data,
        "metric_description": "变动率=(A-B)/B；A=本期应纳税额合计累计；B=上年同期应纳税额合计；应纳税额同比变动过大，可能存在延迟开票或者采购虚假发票的风险。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_revenue_sales_diff(
        current_date, tax_data
    )
    section_5_3 = {
        "title": "5.3 所得税和增值税收入匹配检查",
        "data": data,
        "metric_description": "营业收入与销售额比对",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_revenue_diff(current_date, tax_data)
    section_5_4 = {
        "title": "5.4 所得税和利润表收入匹配检查",
        "data": data,
        "metric_description": "企业所得税营业收入与利润表的营业收入比对",
        "risk_description": risk_desc,
    }
    section_list = [section_5_1, section_5_2, section_5_3, section_5_4]
    total_num = 0
    risk_num = 0
    for order, section in enumerate(section_list):
        chapter5.add_section(
            Section(
                title=section["title"],
                subsections=[],
                data=section["data"],
                metric_description=section["metric_description"],
                risk_description=section["risk_description"],
                has_risk="该指标项未检测到" not in section["risk_description"],
            )
        )
        total_num += 1
        if "该指标项未检测到" not in section["risk_description"]:
            risk_num += 1
    # report_chapter_summary_data_for_llm["章节编号5"] = generate_chapter_summary_data(
    #     chapter5, section_list, risk_num, total_num
    # )
    llm_results.append(
        await general_chapter_summary(
            info_data["taxpayerName"],
            generate_chapter_summary_data(chapter5, section_list, risk_num, total_num),
        )
    )
    report_result_summary.append((risk_num, total_num))
    report.add_section(chapter5)

    ###chapter6
    chapter6 = Section(
        title="PART 06--隐匿收入风险检查",
        subsections=[],
        metric_description="",
        risk_description="",
        has_risk=False,
    )
    data, risk_desc = report_executor.calculate_income_tax_elastic_modulus(
        current_date, tax_data
    )
    section_6_1 = {
        "title": "6.1 营业收入与企业所得税贡献变动检查",
        "data": data,
        "metric_description": "弹性系数=A/B；A=营业收入变动率=（本年营业收入-上年营业收入）/上年营业收入×100%；B=应纳企业所得税贡献变动率=（本年所得税贡献率额-上年所得税贡献率）/上年所得税贡献率×100%。营业收入变动率与业所得税贡献变动率弹性系数指的是纳税人当期营业收入和所得税贡献率的变动关系，正常情况下，二者应该同向基本同步变化，通过对弹性系数的分析，反映出纳税人当期营业收入和所得税贡献率的变动方向和变动幅度，对变动异常的纳税人进行预警。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_income_profit_elastic_modulus(
        current_date, tax_data
    )
    section_6_2 = {
        "title": "6.2 营业收入与营业利润变动检查",
        "data": data,
        "metric_description": "弹性系数=A/B；A=营业收入变动率=（本年营业收入-上年营业收入）/上年营业收入×100%；B=营业利润变动率=（本期营业利润-上期营业利润）/上期营业利润×100%；营业收入变动率与营业利润变动率弹性系数指的是纳税人当期营业收入和营业利润的变动关系，正常情况下，二者应该同向基本同步变化，通过对弹性系数的分析，反映出纳税人当期营业收入和营业利润的变动方向和变动幅度，对变动异常的纳税人进行预警。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_gross_margin_detection(
        current_date, tax_data
    )
    section_6_3 = {
        "title": "6.3 毛利率变动检查",
        "data": data,
        "metric_description": "毛利率变动率=（本年营业收入毛利率-上年营业收入毛利率）/上年营业收入毛利率×100%；营业收入毛利率=（营业收入-营业成本）/营业收入×100%；总体来说毛利率的变动原因可主要归结于销售单价和销售成本的变动所致，如果变动较大需要具体分析变动原因。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_gross_margin(current_date, tax_data)
    section_6_4 = {
        "title": "6.4 毛利率过低疑点检查",
        "data": data,
        "metric_description": "营业收入毛利率=（营业收入-营业成本）/营业收入×100%；毛利率反映的是一个商品经过生产转换内部系统以后增值的那一部分。也就是说，增值的越多毛利自然就越多。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_net_profit(current_date, tax_data)
    section_6_5 = {
        "title": "6.5 连续亏损检查",
        "data": data,
        "metric_description": "净利润=利润总额（所得税年报）-所得税费用；净利润是一个企业经营的最终成果，净利润多，企业的经营效益就好；净利润少，企业的经营效益就差。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_profit_detection(current_date, tax_data)
    section_6_6 = {
        "title": "6.6 盈利情况检查",
        "data": data,
        "metric_description": "盈利情况判断：期间费用率=期间费用/营业收入;营业毛利率=（营业收入-营业成本）/营业收入;（期间费用率-营业毛利率）> 0",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_profit_comparison(
        current_date, tax_data
    )
    section_6_7 = {
        "title": "6.7 利润比对检查",
        "data": data,
        "metric_description": "成本利润率=营业利润/营业成本×100%；销售利润率=营业利润/营业销售×100%； 利润率反映企业一定时期利润水平的相对指标。利润率指标既可考核企业利润计划的完成情况，又可比较各企业之间和不同时期的经营管理水平，提高经济效益。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_invoice_statistics_by_year(
        current_date, fapiao_data
    )
    section_6_8 = {
        "title": "6.8 商贸企业有进未获取相应数据销/有进低销风险",
        "data": data,
        "metric_description": "商贸企业进销税额倒挂，可能存在隐匿销售、虚抵进项等风险。",
        "risk_description": risk_desc,
    }
    section_list = [
        section_6_1,
        section_6_2,
        section_6_3,
        section_6_4,
        section_6_5,
        section_6_6,
        section_6_7,
        section_6_8,
    ]
    total_num = 0
    risk_num = 0
    for order, section in enumerate(section_list):
        chapter6.add_section(
            Section(
                title=section["title"],
                subsections=[],
                data=section["data"],
                metric_description=section["metric_description"],
                risk_description=section["risk_description"],
                has_risk="该指标项未检测到" not in section["risk_description"],
            )
        )
        total_num += 1
        if "该指标项未检测到" not in section["risk_description"]:
            risk_num += 1
    # report_chapter_summary_data_for_llm["章节编号6"] = generate_chapter_summary_data(
    #     chapter6, section_list, risk_num, total_num
    # )
    llm_results.append(
        await general_chapter_summary(
            info_data["taxpayerName"],
            generate_chapter_summary_data(chapter6, section_list, risk_num, total_num),
        )
    )
    report_result_summary.append((risk_num, total_num))
    report.add_section(chapter6)
    ###chapter7
    chapter7 = Section(
        title="PART 07--虚增成本风险检查",
        subsections=[],
        metric_description="",
        risk_description="",
        has_risk=False,
    )
    data, risk_desc = report_executor.calculate_period_expense_tax_elastic_modulus(
        current_date, tax_data
    )
    section_7_1 = {
        "title": "7.1 期间费用与企业所得税贡献变动检查",
        "data": data,
        "metric_description": "弹性系数=A/B；A=期间费用变动率；B=企业所得税贡献变动率；期间费用变动率与业所得税贡献变动率弹性系数指的是纳税人当期期间费用和所得税贡献率的变动关系，正常情况下，二者应该同向基本同步变化，通过对弹性系数的分析，反映出纳税人当期期间费用和所得税贡献率的变动方向和变动幅度，对变动异常的纳税人进行预警。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.expense_admin_analyzer(current_date, tax_data)
    section_7_2 = {
        "title": "7.2 管理费用同比检查",
        "data": data,
        "metric_description": "管理费用同比差异值=（本年管理费用/本年营业收入）-（上年管理费用/上年营业收入）；管理费用同比差异值越小，说明企业费用管理控制较好。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.expense_sell_analyzer(current_date, tax_data)
    section_7_3 = {
        "title": "7.3 销售费用同比检查",
        "data": data,
        "metric_description": "销售费用同比差异值=（本年销售费用/本年营业收入）-（上年销售费用/上年营业收入）；销售费用同比差异值越小，说明企业费用管理控制较好。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.expense_fin_analyzer(current_date, tax_data)
    section_7_4 = {
        "title": "7.4 财务费用同比检查",
        "data": data,
        "metric_description": "财务费用同比差异值=（本年财务费用/本年营业收入）-（上年财务费用/上年营业收入）；财务费用同比差异值越小，说明企业费用管理控制较好。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.union_fund_analyzer(tax_data)
    section_7_5 = {
        "title": "7.5 工会经费超支检查",
        "data": data,
        "metric_description": "工会经费超支判断：A>B×2%；A=工会经费支出；B=工资薪金支出税收金额；超支需确认工会经费使用是否规范。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.employee_welfare_fund_analyzer(tax_data)
    section_7_6 = {
        "title": "7.6 职工福利费超支检查",
        "data": data,
        "metric_description": "职工福利费超支判断：A>B×14%；A=职工福利费；B=工资薪金支出税收金额；超支需确认职工福利费使用是否规范。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.advertising_expenses_analyzer(tax_data)
    section_7_7 = {
        "title": "7.7 广宣费超支检查",
        "data": data,
        "metric_description": "广宣费超支判断：1、A=广宣费 B=销售收入 C=行业 A=B×15% 2、若C是化妆品制造和销售、医药制造、饮料制造，行业代码分别为：2682、5134、5234、27、152 则计算公式A=B×30% 3、若C为烟草行业的制造与零售，行业代码为16、5128、5227，广宣费不允许扣除",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.expense_revenue_analyzer(current_date, tax_data)
    section_7_8 = {
        "title": "7.8 营业成本占营业收入比例变动检查",
        "data": data,
        "metric_description": "营业成本占营业收入比例变动=（本年营业成本/本年营业收入）-（上年营业成本/上年营业收入）；成本占收入比例变动较大时，需关注主营业务产品的盈利能力变化，即时调整业务线。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_expense_revenue_elastic_modulus(
        current_date, tax_data
    )
    section_7_9 = {
        "title": "7.9 营业成本与营业收入弹性系数检查",
        "data": data,
        "metric_description": "弹性系数=A/B；A=营业成本变动率=（本期营业成本－上期营业成本）/上期营业成本×100%；B=营业收入变动率=（本期营业收入－上期营业收入）/上期营业收入×100%；营业收入变动率与营业成本变动率弹性系数指的是纳税人当期营业收入和营业成本的变动关系，正常情况下，二者应该同向基本同步变化，通过对弹性系数的分析，反映出纳税人当期营业收入和营业成本的变动方向和变动幅度，对变动异常的纳税人进行预警。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_revenue_period_expense_elastic_modulus(
        current_date, tax_data
    )
    section_7_10 = {
        "title": "7.10 营业收入与期间费用弹性系数检查",
        "data": data,
        "metric_description": "弹性系数=A/B；A=营业收入变动率=（本期营业收入－上期营业收入）/上期营业收入×100%；B=期间费用变动率=（本期期间费用-上期期间费用）/上期期间费用×100%；营业收入变动率与期间费用变动率弹性系数指的是纳税人当期营业收入和期间费用的变动关系，正常情况下，二者应该同向基本同步变化，通过对弹性系数的分析，反映出纳税人当期营业收入和期间费用的变动方向和变动幅度，对变动异常的纳税人进行预警。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_tax_income_elastic_modulus(
        current_date, tax_data
    )
    section_7_11 = {
        "title": "7.11 纳税调整与营业收入弹性系数",
        "data": data,
        "metric_description": "弹性系数=A/B；A=纳税调整后所得变动率＝（本年纳税调整后所得-上年纳税调整后所得）/上年纳税调整后所得×100%；B=营业收入变动率；营业收入变动率与纳税调整变动率弹性系数指的是纳税人当期营业收入和纳税调整的变动关系，正常情况下，二者应该同向基本同步变化，通过对弹性系数的分析，反映出纳税人当期营业收入和纳税调整的变动方向和变动幅度，对变动异常的纳税人进行预警。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_tax_cost_elastic_modulus(
        current_date, tax_data
    )
    section_7_12 = {
        "title": "7.12 纳税调整与营业成本弹性系数",
        "data": data,
        "metric_description": "弹性系数=A/B；A=纳税调整后所得变动率＝（本年纳税调整后所得-上年纳税调整后所得）/上年纳税调整后所得×100%；B=营业成本变动率；营业成本变动率与纳税调整变动率弹性系数指的是纳税人当期营业成本和纳税调整的变动关系，正常情况下，二者应该同向基本同步变化，通过对弹性系数的分析，反映出纳税人当期营业成本和纳税调整的变动方向和变动幅度，对变动异常的纳税人进行预警。",
        "risk_description": risk_desc,
    }
    section_list = [
        section_7_1,
        section_7_2,
        section_7_3,
        section_7_4,
        section_7_5,
        section_7_6,
        section_7_7,
        section_7_8,
        section_7_9,
        section_7_10,
        section_7_11,
        section_7_12,
    ]
    total_num = 0
    risk_num = 0
    for order, section in enumerate(section_list):
        chapter7.add_section(
            Section(
                title=section["title"],
                subsections=[],
                data=section["data"],
                metric_description=section["metric_description"],
                risk_description=section["risk_description"],
                has_risk="该指标项未检测到" not in section["risk_description"],
            )
        )
        total_num += 1
        if "该指标项未检测到" not in section["risk_description"]:
            risk_num += 1
    # report_chapter_summary_data_for_llm["章节编号7"] = generate_chapter_summary_data(
    #     chapter7, section_list, risk_num, total_num
    # )
    llm_results.append(
        await general_chapter_summary(
            info_data["taxpayerName"],
            generate_chapter_summary_data(chapter7, section_list, risk_num, total_num),
        )
    )
    report_result_summary.append((risk_num, total_num))
    report.add_section(chapter7)
    ###chapter8
    chapter8 = Section(
        title="PART 08--盈利风险分析",
        subsections=[],
        metric_description="",
        risk_description="",
        has_risk=False,
    )
    data, risk_desc = report_executor.calculate_business_net_profit_rate(
        current_date, tax_data
    )
    section_8_1 = {
        "title": "8.1 营业净利率",
        "data": data,
        "metric_description": "营业净利率 = （净利润÷营业收入）×100%，反映企业营业收入创造净利润的能力。营业净利率是企业销售的最终获利能力指标，比率越高，说明企业的获利能力越强。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_total_assets_net_profit_rate(
        current_date, tax_data
    )
    section_8_2 = {
        "title": "8.2 总资产净利率",
        "data": data,
        "metric_description": "总资产净利率 = （净利润÷平均总资产）×100%，总资产净利率是对应总资产的投入产出比。该指标越高，表明公司投入产出水平越高，资产运营越有效，成本费用的控制水平越高。利用该指标可与企业历史资料、与计划、与同行业平均水平或先进水平进行对比，分析形成差异的原因。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_equity_net_profit_rate(
        current_date, tax_data
    )
    section_8_3 = {
        "title": "8.3 投资回报率（权益净利率）",
        "data": data,
        "metric_description": "权益净利率 = （净利润÷股东权益）×100%，反映股东权益的收益水平，用以衡量公司运用自有资本的效率。指标值越高，说明投资带来的收益越高。决定权益净利率高低的因素有三个，分别为销售净利率、总资产周转率和资产负债率，其中：1、销售净利率=净利润÷营业收入，反映每单位销售收入创造的利润水平； 2、总资产周转率是反映总资产的周转速度； 3、权益乘数表示企业的负债程度，反映了公司利用财务杠杆进行经营活动的程度。",
        "risk_description": risk_desc,
    }

    section_list = [section_8_1, section_8_2, section_8_3]
    total_num = 0
    risk_num = 0
    for order, section in enumerate(section_list):
        chapter8.add_section(
            Section(
                title=section["title"],
                subsections=[],
                data=section["data"],
                metric_description=section["metric_description"],
                risk_description=section["risk_description"],
                has_risk="该指标项未检测到" not in section["risk_description"],
            )
        )
        total_num += 1
        if "该指标项未检测到" not in section["risk_description"]:
            risk_num += 1
    # report_chapter_summary_data_for_llm["章节编号8"] = generate_chapter_summary_data(
    #     chapter8, section_list, risk_num, total_num
    # )
    llm_results.append(
        await general_chapter_summary(
            info_data["taxpayerName"],
            generate_chapter_summary_data(chapter8, section_list, risk_num, total_num),
        )
    )
    report_result_summary.append((risk_num, total_num))
    report.add_section(chapter8)
    ###chapter9
    chapter9 = Section(
        title="PART 09--运营风险分析",
        subsections=[],
        metric_description="",
        risk_description="",
        has_risk=False,
    )
    data, risk_desc = report_executor.calculate_receivables_turnover(
        current_date, tax_data
    )
    section_9_1 = {
        "title": "9.1 应收账款周转率",
        "data": data,
        "metric_description": "应收账款周转率 = 营业收入÷ 平均应收账款；应收账款周转天数=365 ÷（营业收入÷平均应收账款）。公司的应收账款如能及时收回，公司的资金使用效率便能大幅提高。应收账款周转率就是反映公司应收账款周转速度的比率。它说明一定期间内公司应收账款转为现金的平均次数。用时间表示的应收账款周转速度为应收账款周转天数，也称平均应收账款回收期或平均收现期。它表示公司从获得应收账款的权利到收回款项、变成现金所需要的时间。应收账款周转率高，说明公司回款速度快，平均回款周期短，坏账损失少，资产流动快，偿债能力强。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_inventory_turnover(
        current_date, tax_data
    )
    section_9_2 = {
        "title": "9.2 存货周转率",
        "data": data,
        "metric_description": "存货周转率=营业收入÷平均存货；存货周转天数=365÷（营业收入÷存货）。用于反映存货的周转速度，即存货的流动性及存货资金占用量是否合理，促使企业在保证生产经营连续性的同时，提高资金的使用效率，增强企业的短期偿债能力。存货周转率是对流动资产周转率的补充说明，是衡量企业投入生产、存货管理水平、销售收回能力的综合性指标。如果周转率太小（或天数太长），就要注意公司产品是否能顺利销售。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_current_assets_turnover(
        current_date, tax_data
    )
    section_9_3 = {
        "title": "9.3 流动资产周转率",
        "data": data,
        "metric_description": "流动资产周转率=营业收入÷平均流动资产；流动资产周转天数=365÷（营业收入÷平均流动资产）。流动资产周转率转得越快，每万元销售收入所需占用的流动资金就越少，同一笔资金在一年内可以完成的交易次数越多。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_total_assets_turnover(
        current_date, tax_data
    )
    section_9_4 = {
        "title": "9.4 总资产周转率",
        "data": data,
        "metric_description": "总资产周转率=营业收入÷平均总资产；总资产周转天数=365÷（营业收入÷平均总资产）。该指标越大说明销售能力越强。",
        "risk_description": risk_desc,
    }
    section_list = [section_9_1, section_9_2, section_9_3, section_9_4]
    total_num = 0
    risk_num = 0
    for order, section in enumerate(section_list):
        chapter9.add_section(
            Section(
                title=section["title"],
                subsections=[],
                data=section["data"],
                metric_description=section["metric_description"],
                risk_description=section["risk_description"],
                has_risk="该指标项未检测到" not in section["risk_description"],
            )
        )
        total_num += 1
        if "该指标项未检测到" not in section["risk_description"]:
            risk_num += 1
    # report_chapter_summary_data_for_llm["章节编号9"] = generate_chapter_summary_data(
    #     chapter9, section_list, risk_num, total_num
    # )
    llm_results.append(
        await general_chapter_summary(
            info_data["taxpayerName"],
            generate_chapter_summary_data(chapter9, section_list, risk_num, total_num),
        )
    )
    report_result_summary.append((risk_num, total_num))
    report.add_section(chapter9)
    ###chapter10
    chapter10 = Section(
        title="PART 10--债风险分析",
        subsections=[],
        metric_description="",
        risk_description="",
        has_risk=False,
    )
    data, risk_desc = report_executor.calculate_operating_assets(current_date, tax_data)
    section_10_1 = {
        "title": "10.1 运营资产",
        "data": data,
        "metric_description": "运营资产 = 流动资产-流动负债，运营资产反应企业运作过程中抵御债务风险的能力，运营资产越高抵御风险能力越强。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_current_ratio(current_date, tax_data)
    section_10_2 = {
        "title": "10.2 流动比率",
        "data": data,
        "metric_description": "流动比率 = 流动资产÷流动负债，流动比率反映企业短期偿债能力，流动比率越大，企业短期偿债能力越强。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_quick_ratio(current_date, tax_data)
    section_10_3 = {
        "title": "10.3 速动比率",
        "data": data,
        "metric_description": "速动比率 = 速动资产÷流动负债，减去存货、应收账款等得到的速动资产比流动比率更能反映企业短期偿债能力，速动比率越大，企业短期偿债能力越强。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_cash_ratio(current_date, tax_data)
    section_10_4 = {
        "title": "10.4 现金比率",
        "data": data,
        "metric_description": "现金比率 = 货币资金÷流动负债，现金比率反映企业短期偿债能力，现金比率越大，说明资金流动性好，短期偿债能力强。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_balance_to_asset_ratio(
        current_date, tax_data
    )
    section_10_5 = {
        "title": "10.5 资产负债率",
        "data": data,
        "metric_description": "资产负债率 =（总负债÷总资产）×100%，反映总资产中有多大比例是通过借债得来的。资产负债率越低，公司的负债越少，自有资产越多，反之，资产负债率越高，公司的负债越多，自有资产越少。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_long_term_balance_to_capital_ratio(
        current_date, tax_data
    )
    section_10_6 = {
        "title": "10.6 长期资本负债率",
        "data": data,
        "metric_description": "长期资本负债率 = [非流动负债÷（非流动负债+股东权益）]×100%，属于资本结构性问题，在经济衰退时会给企业带来额外风险。一般该指标值越小，表明公司负债的资本化程度低，长期偿债压力小;如果指标值越大,则表明公司负债的资本化程度高",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_equity_ratio(current_date, tax_data)
    section_10_7 = {
        "title": "10.7 产权比率",
        "data": data,
        "metric_description": "产权比率 = 总负债÷股东权益，比资产负债率更能准确地揭示企业的偿债能力状况，因为公司只能通过增加资本的途径来降低负债率。产权比率高是高风险、高报酬的财务结构；产权比率低，是低风险、低报酬的财务结构。",
        "risk_description": risk_desc,
    }
    data, risk_desc = report_executor.calculate_equity_multiplier(
        current_date, tax_data
    )
    section_10_8 = {
        "title": "10.8 权益乘数",
        "data": data,
        "metric_description": "权益乘数 = 总资产÷股东权益，反映所有者提供的资本在总资产中的比重，反映企业的基本财务结构是否稳定。权益乘数越大表明所有者投入企业的资本占全部资产的比重越小，企业负债的程度越高；反之，该比率越小，表明所有者投入企业的资本占全部资产的比重越大，企业的负债程度越低，债权人权益受保护的程度越高。",
        "risk_description": risk_desc,
    }
    section_list = [
        section_10_1,
        section_10_2,
        section_10_3,
        section_10_4,
        section_10_5,
        section_10_6,
        section_10_7,
        section_10_8,
    ]
    total_num = 0
    risk_num = 0
    for order, section in enumerate(section_list):
        chapter10.add_section(
            Section(
                title=section["title"],
                subsections=[],
                data=section["data"],
                metric_description=section["metric_description"],
                risk_description=section["risk_description"],
                has_risk="该指标项未检测到" not in section["risk_description"],
            )
        )
        total_num += 1
        if "该指标项未检测到" not in section["risk_description"]:
            risk_num += 1
    # report_chapter_summary_data_for_llm["章节编号10"] = generate_chapter_summary_data(
    #     chapter10, section_list, risk_num, total_num
    # )
    llm_results.append(
        await general_chapter_summary(
            info_data["taxpayerName"],
            generate_chapter_summary_data(chapter10, section_list, risk_num, total_num),
        )
    )
    report_result_summary.append((risk_num, total_num))
    report.add_section(chapter10)

    ###chapter1
    chapter1 = Section(
        title="PART 01--报告概述",
        subsections=[],
        metric_description="",
        risk_description="",
    )
    section_1_1 = {
        "title": "1.1 风险雷达图",
        "data": None,
        "metric_description": None,
        "risk_description": None,
        "has_risk": False,
        "image_path": [
            radder_graph_generator(path, report_result_summary),
            # summary_graph_generator(path),
        ],
    }
    sum_risk_num = 0
    sum_total_num = 0
    for risk_num, total_num in report_result_summary:
        sum_risk_num += risk_num
        sum_total_num += total_num
    report_result_summary.append((sum_risk_num, sum_total_num))

    dimensions = [
        "发票风险检查",
        "税负风险检查",
        "申报真实性风险检查",
        "隐匿收入风险检查",
        "虚增成本风险检查",
        "盈利风险分析",
        "运营风险分析",
        "偿债风险分析",
        "合计",
    ]
    data = []
    for dimension, (risk_num, total_num) in zip(dimensions, report_result_summary):
        normal_num = total_num - risk_num
        entry = {
            "报告维度": dimension,
            "监控数据": total_num,
            "检测结果": f"正常:{normal_num} 风险:{risk_num}",
        }
        data.append(entry)

    section_1_2 = {
        "title": "1.2 风险维度表",
        "data": data,
        "metric_description": None,
        "risk_description": None,
        "has_risk": False,
        "image_path": None,
    }
    section_list = [section_1_1, section_1_2]
    for order, section in enumerate(section_list):
        chapter1.add_section(
            Section(
                title=section["title"],
                subsections=[],
                data=section["data"],
                metric_description=section["metric_description"],
                risk_description=section["risk_description"],
                has_risk=section["has_risk"],
                image_paths=section["image_path"],
            )
        )
    report.insert_section(chapter1, 0)

    print(json.dumps(report_chapter_summary_data_for_llm, ensure_ascii=False, indent=4))

    summary_text = await general_report_summary(info_data["taxpayerName"], llm_results)

    return report, summary_text, sum_risk_num


async def async_save_to_pdf(file_name: str, html_content: str, browser):
    # 创建临时HTML文件
    temp_id = uuid.uuid4().hex
    temp_html = f"_temp_{temp_id}.html"
    with open(temp_html, "w", encoding="utf-8") as f:
        f.write(html_content)

    page = await browser.new_page()
    try:
        await page.goto(
            f"file://{os.path.abspath(temp_html)}", wait_until="networkidle"
        )
        await page.pdf(
            path=file_name,
            format="A4",
            print_background=True,
            margin={
                "top": "10mm",
                "bottom": "10mm",
                "left": "10mm",
                "right": "10mm",
            },  # 适当留边
            prefer_css_page_size=True,  # 强制使用 CSS 页面尺寸
            scale=0.95,  # 微调缩放防止溢出
            display_header_footer=False,  # 确保无额外元素干扰
        )
    finally:
        await page.close()
        os.remove(temp_html)


async def save_html_to_pdf(file_name: str, report: str, request: Request):
    import re
    import base64
    import os

    script_dir = os.path.dirname(os.path.abspath(__file__))

    # 增强版HTML处理
    def enhance_html(html):
        # 强制添加打印样式
        print_style = """
            <style>
  @media print {
    /* 核心：定义全局分页规则 */
    @page {
      size: A4;
      margin: 0;  /* 必须与 Playwright 的 margin 参数同步 */
    }

    html, body {
      width: 210mm !important;
      min-height: 297mm !important;  /* 关键：允许内容扩展 */
      margin: 0 !important;
      padding: 0 !important;
      overflow: visible !important;
      background: white !important;
    }

    /* 自动分页规则 */
    div, p, ul, ol, table, section {
      page-break-inside: avoid !important;  /* 元素内部不分页 */
    }

    h1, h2, h3, h4 {
      page-break-after: avoid !important;   /* 标题后不分页 */
    }

    /* 长表格强制顶部对齐 */
    table {
      overflow: visible !important;
      page-break-inside: auto !important;   /* 允许表格跨页 */
      break-inside: auto !important;
    }

    /* 图片控制 */
    img {
      max-height: 250mm !important;         /* 防止单图过高 */
      page-break-inside: avoid !important;
    }
  }
</style>
            """
        return html.replace("</head>", print_style + "</head>", 1)

    # 图片处理正则表达式
    img_pattern = re.compile(r'<img\s+[^>]*?src=(["\'])(.*?)\1[^>]*>', re.IGNORECASE)

    def replace_img(match):
        quote = match.group(1)
        src = match.group(2)

        # 跳过已编码的图片
        if src.startswith("data:"):
            return match.group(0)

        # 获取绝对路径
        abs_path = os.path.join(script_dir, src)
        if not os.path.exists(abs_path):
            print(f"图片不存在：{abs_path}")
            return match.group(0)

        try:
            # 读取并编码图片
            with open(abs_path, "rb") as f:
                encoded = base64.b64encode(f.read()).decode("utf-8")

            # 获取MIME类型
            ext = os.path.splitext(abs_path)[1][1:].lower()
            mime = (
                f"image/{ext}" if ext in ["png", "jpg", "jpeg", "gif"] else "image/png"
            )

            # 保留原始标签其他属性
            original_tag = match.group(0)
            return re.sub(
                r'src=(["\']).*?\1',
                f"src={quote}data:{mime};base64,{encoded}{quote}",
                original_tag,
            )
        except Exception as e:
            print(f"图片处理失败：{abs_path}，错误：{str(e)}")
            return match.group(0)

    # 处理所有图片标签
    processed_html = img_pattern.sub(replace_img, report)
    # 处理后的HTML增强
    processed_html = enhance_html(processed_html)

    browser = request.app.state.browser
    await async_save_to_pdf(file_name, processed_html, browser)
    # with open("temp.html", "w") as temp_file:
    #     temp_file.write(processed_html)
    #     temp_file_path = os.path.abspath(temp_file.name)
    #     options = {"enable-local-file-access": True, "quiet": ""}
    #     import pdfkit
    #     # 可选：配置 pdfkit 以使用特定的 CSS 或选项
    #     pdfkit.from_file(temp_file_path, file_name, options=options)

    return


def dict_data_to_html_table(section_data):
    if section_data.data is None:
        return ""
    if not section_data.data:
        return """
<table class="min-w-full border-collapse border border-gray-300">
  <thead>
    <tr>
      <th class="border border-gray-300 px-4 py-2 bg-gray-100">状态</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td class="border border-gray-300 px-4 py-2">未获取相应数据</td>
    </tr>
  </tbody>
</table>
"""

    if all(isinstance(item, str) for item in section_data.data):
        return f"""
<table class="min-w-full border-collapse border border-gray-300">
  <thead>
    <tr>
      {"".join(f'<th class="border border-gray-300 px-4 py-2 bg-gray-100">{col}</th>' for col in section_data.data)}
    </tr>
  </thead>
  <tbody>
    <tr>
      <td colspan="{len(section_data.data)}" class="border border-gray-300 px-4 py-2 text-center">未获取数据</td>
    </tr>
  </tbody>
</table>
"""

    # 提取列标题
    headers = list(section_data.data[0].keys())

    # 构建HTML表格
    html = [
        '<table class="min-w-full border-collapse border border-gray-300">',
        "  <thead>",
        "    <tr>",
        "".join(
            f'<th class="border border-gray-300 px-4 py-2 bg-gray-100">{header}</th>'
            for header in headers
        ),
        "    </tr>",
        "  </thead>",
        "  <tbody>",
    ]

    # 添加数据行
    for row in section_data.data:
        html.append("    <tr>")
        html.append(
            "".join(
                f'<td class="border border-gray-300 px-4 py-2">{row[header]}</td>'
                for header in headers
            )
        )
        html.append("    </tr>")

    html.extend(["  </tbody>", "</table>"])

    return "\n".join(html)


async def generate_html_report(
    sections: Section, summary_text: str, total_risk_num
) -> str:
    """Generate HTML report content based on the provided sections."""
    html_content = []

    # Add header
    html_content.append(
        """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>税务风险检测报告</title>
        <script src="https://cdn.tailwindcss.com"></script>
    </head>
    <body>
    """
    )

    date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    # Add cover page
    html_content.append(
        f"""
  <div class="content">
    <!-- 封面 -->
    <div class="flex flex-col items-center w-[1000px] h-[1414px] bg-[#ffffff] relative">
      <img src="./img/logo.png" alt="" class="w-[190px] h-[80px] mt-[98px]">

      <div class="relative mt-[26px]">
        <h1 class="text-[90px] font-bold text-[#323233]">税务风险检测报告</h1>
        <img src="./img/title-logo.png" alt="" class="w-[102px] h-[95px] absolute -right-[64px] -top-[40px]">
      </div>

      <p class="text-[34px] font-semibold text-[#D8D9E0] text-center -mt-2">Tax Risk Assessment Report</p>
      <img src="./img/home-logo.png" alt="" class="w-[522px] h-[587px] mt-36">

      <p class="mt-auto mb-[100px] text-[20px] text-[#000]">
        检测日期：{date}
      </p>
    </div>

    <!-- 目录1 -->
    <div class="flex flex-col items-center w-[1000px] h-[1414px] bg-[#ffffff] mt-20 relative">
      <h1 class="text-center text-[32px] font-bold mb-[40px] mt-[98px]">目录</h1>

      <!-- 一、报告概述 -->
      <section class="mb-[20px] w-full px-10">
        <div class="font-semibold mb-4 text-[24px]">一 报告概述</div>
        <div class="flex items-center gap-2 mb-4 ml-4">
          <span class="whitespace-nowrap text-[20px]">1.1 风险雷达图</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4">
          <span class="whitespace-nowrap text-[20px]">1.2 风险维度表</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
      </section>

      <!-- 二、企业基本信息 -->
      <section class="mb-[20px] w-full px-10">
        <div class="font-semibold mb-4 text-[24px]">二 企业基本信息</div>
        <div class="flex items-center gap-2 mb-4 ml-4">
          <span class="whitespace-nowrap text-[20px]">2.1 企业基本工商信息</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4">
          <span class="whitespace-nowrap text-[20px]">2.2 信用认定信息</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
      </section>

      <!-- 三、发票风险检查 -->
      <section class="mb-[20px] w-full px-10">
        <div class="font-semibold mb-4 text-[24px]">三 发票风险检查</div>

        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">3.1
            增值税发票（专票+普票）用量变动异常检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">3.2
            前十大销售、采购品目不一致风险检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">3.3 互开发票疑点检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">3.4 前十大客户风险分析</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">3.5
            销售发票top10商品种类</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">3.6
            前十大供应商风险分析</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">3.7
            采购发票top10商品种类</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">3.8
            增值税发票大量作废疑点检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">3.9
            增值税发票大量红冲疑点检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">3.10
            大量取得税额为零的发票风险检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">3.11
            向外省企业开具发票占比过多检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">3.12
            收取代开发票过多检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">3.13
            取得“咨询服务费”“会议服务费”“手续费”发票风险检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">3.14
            顶额开票异常检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">3.15
            收取个体工商户发票检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">3.16
            进项发票中单张较大金额且价税合计为整数</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>

      </section>
      <!-- 横线：颜色根据宽度设置 -->
      <div class="absolute bottom-0 left-0 w-full h-[4px] flex">
        <div class="h-full bg-[#458DE7]" style="width: 60%;"></div>
        <div class="h-full bg-[#FB8A02]" style="width: 40%;"></div>
      </div>
      <div class="absolute bottom-14 left-4 w-full h-[4px] flex">
        <img src="./img/small-logo.png" alt="" class="w-[26px] h-[26px]">
        <img src="./img/text-logo.png" alt="" class="w-[93px] h-[30px]">
      </div>
    </div>

    <div class="flex flex-col items-center w-[1000px] h-[1414px] bg-[#ffffff] mt-20 relative">
      <section class="mb-[20px] w-full px-10 mt-10">
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">3.17 收取大额作废发票</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">3.18
            接收外省企业开具发票占比过多检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="font-semibold mb-4 text-[24px]">四 税负风险检查</div>

        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">4.1
            增值税税负变动率检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">4.2
            企业所得税贡献率检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">4.3
            应税所得率检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">4.4
            增值税连续3个月零申报检测</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">4.5
            小规模纳税人销售额超标检测</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">4.6
            期初留抵税额与上期期末留抵税额核对分析</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>

        <div class="font-semibold mb-4 text-[24px]">五 申报真实性风险检查</div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">5.1
            销售额变动率与增值税应纳税额变动率弹性系数检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">5.2
            增值税应纳税额同比变动率检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">5.3
            所得税和增值税收入匹配检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">5.4
            所得税和利润表收入匹配检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>

        <div class="font-semibold mb-4 text-[24px]">六 隐匿收入风险检查</div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">6.1
            营业收入与企业所得税贡献变动检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">6.2
            营业收入与营业利润变动检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">6.3
            毛利率变动检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">6.4
            毛利率过低疑点检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">6.5
            连续亏损检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">6.6
            盈利情况检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">6.7
            利润比对检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">6.8
            商贸企业有进未获取相应数据销/有进低销风险</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>

        <div class="font-semibold mb-4 text-[24px]">七 虚增成本风险检查</div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">7.1
            期间费用与企业所得税贡献变动检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">7.2
            管理费用同比检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">7.3
            销售费用同比检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">7.4
            财务费用同比检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>

      </section>
      <!-- 横线：颜色根据宽度设置 -->
      <div class="absolute bottom-0 left-0 w-full h-[4px] flex">
        <div class="h-full bg-[#458DE7]" style="width: 60%;"></div>
        <div class="h-full bg-[#FB8A02]" style="width: 40%;"></div>
      </div>
      <div class="absolute bottom-14 left-4 w-full h-[4px] flex">
        <img src="./img/small-logo.png" alt="" class="w-[26px] h-[26px]">
        <img src="./img/text-logo.png" alt="" class="w-[93px] h-[30px]">
      </div>
    </div>

    <div class="flex flex-col items-center w-[1000px] h-[1414px] bg-[#ffffff] mt-20 relative">
      <section class="mb-[20px] w-full px-10 mt-10">

        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">7.5
            工会经费超支检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">7.6
            职工福利费超支检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">7.7
            广宣费超支检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">7.8
            营业成本占营业收入比例变动检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">7.9
            营业成本与营业收入弹性系数检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">7.10
            营业收入与期间费用弹性系数检查</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">7.11
            纳税调整与营业收入弹性系数</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">7.12
            纳税调整与营业成本弹性系数</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>

        <div class="font-semibold mb-4 text-[24px]">八 盈利风险分析</div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">8.1
            营业净利率</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">8.2
            总资产净利率</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">8.3
            投资回报率（权益净利率）</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>


        <div class="font-semibold mb-4 text-[24px]">九 运营风险分析</div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">9.1
            应收账款周转率</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">9.2
            存货周转率</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">9.3
            流动资产周转率</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">9.4
            总资产周转率</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>


        <div class="font-semibold mb-4 text-[24px]">十 偿债风险分析</div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">10.1
            运营资产</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">10.2
            流动比率</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">10.3
            速动比率</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">10.4
            现金比率</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">10.5
            资产负债率</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">10.6
            长期资本负债率</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">10.7
            产权比率</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
        <div class="flex items-center gap-2 mb-4 ml-4 text-[20px]"><span class="whitespace-nowrap">10.8
            权益乘数</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>

        <div class="flex items-center gap-2 mb-4 text-[20px]"><span class="whitespace-nowrap">
            免责声明</span>
          <div class="flex-1 border-t border-gray-300"></div>
        </div>
      </section>
      <!-- 横线：颜色根据宽度设置 -->
      <div class="absolute bottom-0 left-0 w-full h-[4px] flex">
        <div class="h-full bg-[#458DE7]" style="width: 60%;"></div>
        <div class="h-full bg-[#FB8A02]" style="width: 40%;"></div>
      </div>
      <div class="absolute bottom-14 left-4 w-full h-[4px] flex">
        <img src="./img/small-logo.png" alt="" class="w-[26px] h-[26px]">
        <img src="./img/text-logo.png" alt="" class="w-[93px] h-[30px]">
      </div>
    </div>
  </div>
    """
    )

    summary_json = extract_json_from_string(summary_text)
    summary_list = list(summary_json.items())

    # 风险总览
    html_content.append(
        f"""
  <div class="w-[1000px] h-[1414px] bg-[#ffffff] mt-20 relative">
    <div class="flex mt-[114px] ml-[60px] items-center mt-8">
      <p class="text-[#FB8A02] text-[28px] font-bold mr-3">/</p>
      <p class="text-[24px] font-bold">风险总览</p>
    </div>
    <div class="flex flex-col items-center mt-[100px]">
      <img src="./img/risk-logo.png" alt="" class="w-[120px] h-[120px] mt-26">
      <div class="flex mt-10 items-center">
        <p class="font-bold text-[32px] text-[#F53F3F]">{total_risk_num} 风险</p>
        <p class="ml-2 text-[#80828D]">/ 63 检查项</p>
      </div>
      <div class="w-[690px]">
        <p class="text-[#000] font-bold text-[24px] mt-20 text-center">风险说明</p>
        <p class="text-[#1E1F24] text-[16px] mt-10">
         {summary_list[0][0]}:{summary_list[0][1]}  </p>
        <p class="text-[#1E1F24] text-[16px] mt-8">
         {summary_list[1][0]}:{summary_list[1][1]}  </p>
        <p class="text-[#1E1F24] text-[16px] mt-8">
         {summary_list[2][0]}:{summary_list[2][1]}</p>
        <p class="text-[#1E1F24] text-[16px] mt-8">
        {summary_list[3][0]}:{summary_list[3][1]}</p>
        <p class="text-[#1E1F24] text-[16px] mt-8">
        {summary_list[4][0]}:{summary_list[4][1]}</p>
        <p class="text-[#1E1F24] text-[16px] mt-8">
        {summary_list[5][0]}:{summary_list[5][1]}</p> 
      </div>
    </div>
    <!-- 横线：颜色根据宽度设置 -->
    <div class="absolute bottom-0 left-0 w-full h-[4px] flex">
      <div class="h-full bg-[#458DE7]" style="width: 60%;"></div>
      <div class="h-full bg-[#FB8A02]" style="width: 40%;"></div>
    </div>
    <div class="absolute bottom-14 left-4 w-full h-[4px] flex">
      <img src="./img/small-logo.png" alt="" class="w-[26px] h-[26px]">
      <img src="./img/text-logo.png" alt="" class="w-[93px] h-[30px]">
    </div>
  </div>"""
    )

    html_content.append(
        """
      <div class="w-[1000px] h-full bg-[#ffffff] mt-20 pb-20 relative">
        <div class="flex flex-col mt-[30px] px-10">
    """
    )

    # Add sections
    def add_section_to_html(section: Section, level: int):
        if level == 1:
            html_content.append(
                f"""    
            <div class="flex mt-[114px] ml-[60px] items-center">
                <p class="text-[#FB8A02] text-[28px] font-bold mr-3">/</p>
                <p class="text-[24px] font-bold">{section.title}</p>
            </div>
            <div class="flex flex-col mt-[20px] px-10"> """
            )

        elif level == 2:
            html_content.append(
                f"""
            <div class="flex items-center mb-4 text-[20px]"><span class="whitespace-nowrap">{section.title}</span>
                <div class="flex-1 border-t border-gray-300"></div>
            </div>
            """
            )

        # Add section content
        if section.metric_description:
            html_content.append(f"<p>{section.metric_description}</p>")

        # Add table if exists
        if section.data:
            table_html = dict_data_to_html_table(section)
            html_content.append(
                f'<div class="min-h-[200px] text-center my-10">{table_html}</div>'
            )

        # Add risk description if exists
        if section.risk_description:
            risk_class = "risk-warning" if section.has_risk else "risk-safe"
            risk_icon = "⚠️" if section.has_risk else "✅"
            html_content.append(
                f"""
            <div class="min-h-[300px] text-center">
                <p class="{risk_class}">{risk_icon}{section.risk_description}</p>
            </div>"""
            )

        # Add images if exist
        if section.image_paths:
            for image_path in section.image_paths:
                html_content.append(
                    f"""
                <div class="min-h-[350px] text-center py-10">
                    <img src="{image_path}" alt="{section.title}" class="w-full">
                </div>
            """
                )

        if section.title == "1.1 风险雷达图":
            html_content.append(
                f"""<div id="risk-container" class="grid grid-cols-4 gap-4 gap-y-10"></div>"""
            )

        # Process subsections
        if section.subsections:
            for child in section.subsections:
                add_section_to_html(child, level + 1)

        if level == 1:
            html_content.append(f"""</div>""")

    # Process all sections
    add_section_to_html(sections, 0)

    # Add footer
    html_content.append(
        """
    </div>

    <!-- 横线：颜色根据宽度设置 -->
    <div class="absolute bottom-0 left-0 w-full h-[4px] flex">
      <div class="h-full bg-[#458DE7]" style="width: 60%;"></div>
      <div class="h-full bg-[#FB8A02]" style="width: 40%;"></div>
    </div>
    <div class="absolute bottom-14 left-4 w-full h-[4px] flex">
      <img src="./img/small-logo.png" alt="" class="w-[26px] h-[26px]">
      <img src="./img/text-logo.png" alt="" class="w-[93px] h-[30px]">
    </div>
  </div>

  <div class="w-[1000px] h-[1414px] bg-[#ffffff] mt-20 relative">
    <div class="flex mt-[114px] ml-[60px] items-center">
      <p class="text-[#FB8A02] text-[28px] font-bold mr-3">/</p>
      <p class="text-[24px] font-bold">免责声明</p>
    </div>
    <div class="flex flex-col mt-[100px] px-10 gap-y-10 pb-32">
      <p style="text-indent: 2em;">一、本公司在执行本次税务健康体检业务中,遵循有关法律、法规、恪守独立、客观、
        公正、保密的原则。基于本次体检 业务中所依据的申报表体检报告陈述的内容是客观
        的，体检结论仅供参考。</p>
      <p style="text-indent: 2em;">二、本次体检所依据的申报表由委托方提供，所提供资料的真实性、合法性、完整
        性、以及恰当使用体检报告是委托方的责任。</p>
      <p style="text-indent: 2em;">三、本公司与体检报告中的被体检企业没有现存或者预期的利益关系,对被体检企业不
        存在任何偏见。</p>
      <p style="text-indent: 2em;">四、本公司出具的体检报告中的分析、判断和结论受体检假设、体检报告限定条件和
        体检方法的限制,体检报告使用者应 当充分考虑体检报告中载明的假设、限定条件、特别
        事项说明及其对体检结论的影响。</p>
    </div>
    <!-- 横线：颜色根据宽度设置 -->
    <div class="absolute bottom-0 left-0 w-full h-[4px] flex">
      <div class="h-full bg-[#458DE7]" style="width: 60%;"></div>
      <div class="h-full bg-[#FB8A02]" style="width: 40%;"></div>
    </div>
    <div class="absolute bottom-14 left-4 w-full h-[4px] flex">
      <img src="./img/small-logo.png" alt="" class="w-[26px] h-[26px]">
      <img src="./img/text-logo.png" alt="" class="w-[93px] h-[30px]">
    </div>
  </div>



  <script>
    const data = [
      { icon: './img/icon-1.png', count: 18, label: '发票风险检查' },
      { icon: './img/icon-1.png', count: 6, label: '税负风险检查' },
      { icon: './img/icon-1.png', count: 4, label: '申报真实性风险检查' },
      { icon: './img/icon-1.png', count: 8, label: '隐匿收入风险检查' },
      { icon: './img/icon-1.png', count: 12, label: '虚增成本风险检查' },
      { icon: './img/icon-1.png', count: 3, label: '盈利风险分析' },
      { icon: './img/icon-1.png', count: 4, label: '运营风险分析' },
      { icon: './img/icon-1.png', count: 8, label: '偿债风险分析' },
      // 可继续添加更多项...
    ];

    const container = document.getElementById('risk-container');

    data.forEach(item => {
      const html = `
        <div class="text-center flex h-[42px]">
          <div class="flex items-center">
            <img src="./img/icon-1.png" alt="" class="w-[42px] h-[42px]">
            <div class="text-left -mt-1 ml-2">
              <p class="font-bold text-[24px]">${item.count}<span class="text-xs ml-1">项</span></p>
              <p class="text-xs -mt-1">${item.label}</p>
            </div>
          </div>
        </div>
      `;
      container.insertAdjacentHTML('beforeend', html);
    });

    const elasticityInfo = `
            弹性系数=A/B；A=销售额变动率；B、应纳税额合计变动率；①　正常情况下两者应基本同步增长或下降，弹性系数
            应接近1。 ②　若弹性系数>1且销售额变动率和应纳税额变动率都为正数则可能存在问题; ③　若弹性系数<1且销售
            额变动率和应纳税额变动率都为负数则可能存在问题; ④　若弹性系数<1且销售额变动率和应纳税额变动率都为正数则
            未获取相应数据问题; ⑤　若销售额变动率和应纳税额变动率都为负数则可能存在问题; ⑥　应纳税额变动率为正且销
            售额变动率前者为负则未获取相应数据问题。 ⑦　当弹性系数为负数，销售额变动率为正应纳税额变动率为负时，可
            能存在问题；
        `;

    // 获取页面中的元素，并设置其innerHTML属性为定义的文本
    document.getElementById('elasticity-info').innerHTML = elasticityInfo; 
  </script>

</body>

</html>
    """
    )

    return "\n".join(html_content)
