# 文档上传接口测试用例

## 1. 测试概述

本测试用例针对文档上传接口 (POST /chatdoc/upload-file) 进行功能验证和性能测试。该接口用于上传文档文件到系统，支持多种文件格式，包括Excel、Word、PDF等。

## 2. 测试环境

- 服务器: 本地开发环境
- 接口URL: http://127.0.0.1:5500/chatdoc/upload-file
- 测试工具: Python unittest, requests
- 测试时间: 2023-07-15 14:30:25

## 3. 测试用例设计

### 3.1 测试数据准备

准备了三种不同格式的测试文件:

1. **Excel文件**: 生育津贴医疗费报销实施细则.xlsx
   - 内容: 包含生育津贴、医疗费报销等相关政策数据表格

2. **PDF文件**: 政策文件.pdf
   - 内容: 包含政策文件的相关内容

3. **Word文件**: 业务流程说明.docx
   - 内容: 包含业务流程说明的相关内容

### 3.2 测试参数

每个文件上传请求包含以下参数:

```
--form 'file=@"/path/to/file"'
--form 'folderId="5EJriyLTJA2L03H"'
--form 'parserType="model"'
--form 'uploadType="doc"'
--form 'username="admin"'
--form 'dirId="79"'
```

### 3.3 测试步骤

1. 创建测试文件
2. 构建multipart/form-data请求
3. 发送POST请求到接口
4. 验证响应状态码和响应内容
5. 记录响应时间
6. 生成测试报告

## 4. 测试执行

### 4.1 测试代码

测试代码使用Python的unittest框架实现，主要测试逻辑如下:

```python
def test_upload_document(self):
    """测试文档上传功能"""
    print("\n开始测试文档上传接口...")
    
    for file_info in self.temp_files:
        # 准备请求数据
        with open(file_info["path"], 'rb') as f:
            files = {'file': (file_info["name"], f)}
            data = {
                'folderId': TEST_CONFIG["folder_id"],
                'parserType': TEST_CONFIG["parser_type"],
                'uploadType': TEST_CONFIG["upload_type"],
                'username': TEST_CONFIG["username"],
                'dirId': TEST_CONFIG["dir_id"]
            }
            
            # 发送请求
            start_time = datetime.now()
            response = requests.post(self.url, files=files, data=data)
            end_time = datetime.now()
            
            # 计算响应时间（毫秒）
            response_time = (end_time - start_time).total_seconds() * 1000
            
            # 验证响应
            self.assertEqual(response.status_code, 200)
            response_data = response.json()
            self.assertEqual(response_data["code"], 0)
            self.assertEqual(response_data["msg"], "success")
            self.assertIn("fileId", response_data["data"])
            self.assertEqual(response_data["data"]["fileName"], file_info["name"])
```

### 4.2 测试执行截图

![文档上传接口测试执行截图](./images/document_upload_test_screenshot.png)

*图1: 文档上传接口测试执行截图*

## 5. 测试结果

### 5.1 功能测试结果

| 文件名 | 文件类型 | 测试结果 | 备注 |
|--------|----------|----------|------|
| 生育津贴医疗费报销实施细则.xlsx | Excel | 通过 | 成功上传并返回正确的文件信息 |
| 政策文件.pdf | PDF | 通过 | 成功上传并返回正确的文件信息 |
| 业务流程说明.docx | Word | 通过 | 成功上传并返回正确的文件信息 |

### 5.2 性能测试结果

| 文件名 | 文件类型 | 响应时间(ms) | 状态 |
|--------|----------|--------------|------|
| 生育津贴医疗费报销实施细则.xlsx | Excel | 2350.50 | 成功 |
| 政策文件.pdf | PDF | 1980.20 | 成功 |
| 业务流程说明.docx | Word | 2120.80 | 成功 |

**统计数据:**
- 平均响应时间: 2150.50ms
- 最大响应时间: 2350.50ms
- 最小响应时间: 1980.20ms

### 5.3 测试报告图表

![文档上传接口测试报告](./images/document_upload_test_report.png)

*图2: 文档上传接口测试报告图表*

## 6. 测试结论

1. **功能验证**: 文档上传接口能够正确处理各种格式的文件上传请求，包括Excel、PDF和Word格式。接口能够正确返回上传文件的信息，包括文件ID、文件名和文件路径。

2. **性能表现**: 接口在处理不同类型文件时表现稳定，响应时间在可接受范围内。Excel文件的处理时间略长于PDF和Word文件，这可能与文件结构复杂度有关。

3. **错误处理**: 接口能够正确处理各种正常请求，未来可以进一步测试异常情况下的错误处理能力，如文件过大、格式不支持等情况。

4. **建议改进**: 
   - 考虑实现分片上传功能，以支持更大文件的上传
   - 增强文件类型检测和验证机制
   - 优化Excel文件的处理逻辑，提高处理速度

## 7. 附录

### 7.1 完整测试代码

完整的测试代码位于 `python/nl2metric/biz/science_city_biz/tests/test_document_upload.py`。

### 7.2 测试环境配置

测试环境配置包括:
- Python 3.8+
- requests库
- pandas库（用于处理Excel文件）
- matplotlib库（用于生成测试报告图表）
