#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试文档上传接口 (POST /chatdoc/upload-file)
"""

import os
import json
import unittest
import requests
from unittest.mock import patch, MagicMock
import tempfile
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime

# 测试配置
TEST_CONFIG = {
    "base_url": "http://127.0.0.1:5500",
    "endpoint": "/chatdoc/upload-file",
    "folder_id": "5EJriyLTJA2L03H",
    "parser_type": "model",
    "upload_type": "doc",
    "username": "admin",
    "dir_id": "79",
    "test_files": [
        {"name": "生育津贴医疗费报销实施细则.xlsx", "type": "excel"},
        {"name": "政策文件.pdf", "type": "pdf"},
        {"name": "业务流程说明.docx", "type": "word"},
    ],
}


class DocumentUploadTest(unittest.TestCase):
    """文档上传接口测试类"""

    def setUp(self):
        """测试前准备工作"""
        self.base_url = TEST_CONFIG["base_url"]
        self.endpoint = TEST_CONFIG["endpoint"]
        self.url = f"{self.base_url}{self.endpoint}"

        # 创建临时测试文件
        self.temp_files = []
        for file_info in TEST_CONFIG["test_files"]:
            temp_file = self._create_test_file(file_info["name"], file_info["type"])
            self.temp_files.append(
                {
                    "path": temp_file,
                    "name": file_info["name"],
                    "type": file_info["type"],
                }
            )

        # 测试结果存储
        self.test_results = []

    def tearDown(self):
        """测试后清理工作"""
        # 删除临时文件
        for file_info in self.temp_files:
            if os.path.exists(file_info["path"]):
                os.remove(file_info["path"])

        # 生成测试报告图表
        self._generate_test_report()

    def _create_test_file(self, filename, file_type):
        """创建测试文件"""
        temp_dir = tempfile.gettempdir()
        file_path = os.path.join(temp_dir, filename)

        if file_type == "excel":
            # 创建Excel测试文件
            df = pd.DataFrame(
                {
                    "项目": ["生育津贴", "医疗费报销", "产假工资"],
                    "金额标准": [10000, 5000, 8000],
                    "申请条件": ["符合计划生育政策", "医保参保人员", "正式员工"],
                    "办理流程": ["提交申请->审核->发放", "提交发票->审核->报销", "提交申请->审批->发放"],
                }
            )
            df.to_excel(file_path, index=False)
        elif file_type == "pdf":
            # 模拟PDF文件（实际上创建文本文件）
            with open(file_path, "w", encoding="utf-8") as f:
                f.write("这是一个模拟的PDF文件内容，用于测试文档上传接口。\n")
                f.write("包含政策文件的相关内容。\n")
        elif file_type == "word":
            # 模拟Word文件（实际上创建文本文件）
            with open(file_path, "w", encoding="utf-8") as f:
                f.write("这是一个模拟的Word文件内容，用于测试文档上传接口。\n")
                f.write("包含业务流程说明的相关内容。\n")

        return file_path

    @patch("requests.post")
    def test_upload_document(self, mock_post):
        """测试文档上传功能"""
        print("\n开始测试文档上传接口...")

        for file_info in self.temp_files:
            # 设置模拟响应
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "code": 0,
                "msg": "success",
                "data": {
                    "fileId": f"file_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    "fileName": file_info["name"],
                    "filePath": f"/storage/documents/{file_info['name']}",
                },
            }
            mock_post.return_value = mock_response

            # 准备请求数据
            with open(file_info["path"], "rb") as f:
                files = {"file": (file_info["name"], f)}
                data = {
                    "folderId": TEST_CONFIG["folder_id"],
                    "parserType": TEST_CONFIG["parser_type"],
                    "uploadType": TEST_CONFIG["upload_type"],
                    "username": TEST_CONFIG["username"],
                    "dirId": TEST_CONFIG["dir_id"],
                }

                # 发送请求
                start_time = datetime.now()
                response = requests.post(self.url, files=files, data=data)
                end_time = datetime.now()

                # 计算响应时间（毫秒）
                response_time = (end_time - start_time).total_seconds() * 1000

                # 验证响应
                self.assertEqual(response.status_code, 200)
                response_data = response.json()
                self.assertEqual(response_data["code"], 0)
                self.assertEqual(response_data["msg"], "success")
                self.assertIn("fileId", response_data["data"])
                self.assertEqual(response_data["data"]["fileName"], file_info["name"])

                # 记录测试结果
                self.test_results.append(
                    {
                        "file_type": file_info["type"],
                        "file_name": file_info["name"],
                        "status": "成功",
                        "response_time": response_time,
                    }
                )

                print(f"文件 {file_info['name']} 上传测试成功，响应时间: {response_time:.2f}ms")

    def _generate_test_report(self):
        """生成测试报告图表"""
        if not self.test_results:
            return

        # 创建DataFrame
        df = pd.DataFrame(self.test_results)

        # 设置图表样式
        plt.style.use("ggplot")
        plt.figure(figsize=(12, 8))

        # 绘制响应时间柱状图
        plt.subplot(2, 1, 1)
        bars = plt.bar(df["file_name"], df["response_time"], color="skyblue")
        plt.title("文档上传接口响应时间", fontsize=14)
        plt.xlabel("文件名称", fontsize=12)
        plt.ylabel("响应时间 (ms)", fontsize=12)
        plt.xticks(rotation=45, ha="right")

        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            plt.text(
                bar.get_x() + bar.get_width() / 2.0,
                height + 5,
                f"{height:.1f}ms",
                ha="center",
                va="bottom",
                fontsize=10,
            )

        # 绘制文件类型饼图
        plt.subplot(2, 1, 2)
        file_type_counts = df["file_type"].value_counts()
        plt.pie(
            file_type_counts,
            labels=file_type_counts.index,
            autopct="%1.1f%%",
            colors=["#ff9999", "#66b3ff", "#99ff99"],
            startangle=90,
        )
        plt.title("测试文件类型分布", fontsize=14)
        plt.axis("equal")

        # 保存图表
        plt.tight_layout()
        report_dir = os.path.join(os.path.dirname(__file__), "../doc/images")
        os.makedirs(report_dir, exist_ok=True)
        plt.savefig(os.path.join(report_dir, "document_upload_test_report.png"))
        print(
            f"\n测试报告图表已保存至: {os.path.join(report_dir, 'document_upload_test_report.png')}"
        )


if __name__ == "__main__":
    unittest.main()
