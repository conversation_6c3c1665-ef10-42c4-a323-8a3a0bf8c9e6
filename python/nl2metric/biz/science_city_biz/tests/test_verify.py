import os
import time
import jwt
import pytest
from datetime import datetime, timedelta
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.backends import default_backend

from biz.science_city_biz.service.verify_service import (
    verify_url_token,
    verify_token,
    access_tokens,
)


# 生成RSA密钥对
def generate_rsa_keys():
    """生成RSA公私钥对"""
    # 生成私钥
    private_key = rsa.generate_private_key(
        public_exponent=65537, key_size=2048, backend=default_backend()
    )

    # 获取公钥
    public_key = private_key.public_key()

    # 序列化私钥
    private_pem = private_key.private_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PrivateFormat.PKCS8,
        encryption_algorithm=serialization.NoEncryption(),
    )

    # 序列化公钥
    public_pem = public_key.public_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PublicFormat.SubjectPublicKeyInfo,
    )

    return private_pem.decode(), public_pem.decode()


# 测试fixture
@pytest.fixture
def rsa_keys():
    """提供RSA密钥对的fixture"""
    private_key, public_key = generate_rsa_keys()
    return private_key, public_key


@pytest.fixture
def valid_url_token(rsa_keys):
    """生成有效的URL token"""
    private_key, _ = rsa_keys
    payload = {"s3_key": "test/report.pdf", "exp": int(time.time()) + 3600}  # 1小时后过期
    return jwt.encode(payload, private_key, algorithm="RS256")


@pytest.fixture
def expired_url_token(rsa_keys):
    """生成已过期的URL token"""
    private_key, _ = rsa_keys
    payload = {"s3_key": "test/report.pdf", "exp": int(time.time()) - 3600}  # 1小时前过期
    return jwt.encode(payload, private_key, algorithm="RS256")


@pytest.fixture
def invalid_url_token():
    """生成无效的URL token"""
    return "invalid.token.string"


# 测试用例
def test_verify_url_token_valid(rsa_keys, valid_url_token, monkeypatch):
    """测试验证有效的URL token"""
    _, public_key = rsa_keys
    # 使用monkeypatch设置环境变量
    monkeypatch.setenv("PUBLIC_KEY", public_key)

    result = verify_url_token(valid_url_token, "test/report.pdf")
    assert result is not None
    assert result["s3_key"] == "test/report.pdf"
    assert result["exp"] > time.time()


def test_verify_url_token_expired(rsa_keys, expired_url_token, monkeypatch):
    """测试验证已过期的URL token"""
    _, public_key = rsa_keys
    monkeypatch.setenv("PUBLIC_KEY", public_key)

    result = verify_url_token(expired_url_token, "test/report.pdf")
    assert result is None


def test_verify_url_token_invalid(invalid_url_token):
    """测试验证无效的URL token"""
    result = verify_url_token(invalid_url_token, "test/report.pdf")
    assert result is None


def test_verify_url_token_no_public_key(valid_url_token, monkeypatch):
    """测试未配置公钥的情况"""
    # 使用monkeypatch删除环境变量
    monkeypatch.delenv("PUBLIC_KEY", raising=False)

    result = verify_url_token(valid_url_token, "test/report.pdf")
    assert result is None


def test_verify_token_valid():
    """测试验证有效的内部token"""
    # 生成测试token
    token = "test_token"
    expires = datetime.now() + timedelta(minutes=5)
    access_tokens[token] = {"s3_key": "test/report.pdf", "expires": expires}

    assert verify_token(token) is True
    del access_tokens[token]


def test_verify_token_expired():
    """测试验证已过期的内部token"""
    # 生成过期的token
    token = "expired_token"
    expires = datetime.now() - timedelta(minutes=5)

    # 先确保token不存在
    if token in access_tokens:
        del access_tokens[token]

    # 添加过期的token
    access_tokens[token] = {"s3_key": "test/report.pdf", "expires": expires}

    # 验证token
    assert verify_token(token) is False

    # 验证token已被删除
    assert token not in access_tokens

    # 清理
    if token in access_tokens:
        del access_tokens[token]


def test_verify_token_invalid():
    """测试验证不存在的token"""
    assert verify_token("non_existent_token") is False


def test_verify_token_wrong_file():
    """测试验证token绑定的文件不匹配"""
    # 生成测试token
    token = "test_token"
    expires = datetime.now() + timedelta(minutes=5)
    access_tokens[token] = {"s3_key": "test/report.pdf", "expires": expires}

    assert verify_token(token) is True
    del access_tokens[token]


def test_complete_verification_flow(rsa_keys, valid_url_token, monkeypatch):
    """测试完整的验证流程"""
    private_key, public_key = rsa_keys
    # 使用monkeypatch设置环境变量
    monkeypatch.setenv("PUBLIC_KEY", public_key)

    # 1. 验证URL token
    payload = verify_url_token(valid_url_token, "test/report.pdf")
    assert payload is not None

    # 2. 生成内部token
    token = "internal_token"
    expires = datetime.now() + timedelta(minutes=5)
    access_tokens[token] = {"s3_key": payload["s3_key"], "expires": expires}

    # 3. 验证内部token
    assert verify_token(token) is True

    # 4. 清理
    del access_tokens[token]


def test_generate_keys():
    print(generate_rsa_keys())


def test_verify_url_token():
    verify_url_token(
        "df50f0567340929f0f956222ff6ec6ca08030e9f17175da1a3b071b349510628", "aaa"
    )
