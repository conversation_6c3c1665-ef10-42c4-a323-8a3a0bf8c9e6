import base64
from Crypto.Cipher import A<PERSON>
from Crypto.Util.Padding import pad, unpad
import time
import binascii  # For hex encoding/decoding

# --- Configuration from Java code ---
KEY_STR_B64 = "mfAvZKQRMv0Iti8hIwCzoQ=="

# --- Initialize Key and IV ---
# Decode the Base64 key string to bytes
# In Java: byte[] key_bytes = jodd.util.Base64.decode(keyStr); (assuming decode)
# Or using standard Java Base64: byte[] key_bytes = Base64.getDecoder().decode(keyStr);
try:
    key_bytes = base64.b64decode(KEY_STR_B64)
except base64.binascii.Error as e:
    print(f"Error decoding Base64 key: {e}")
    print("Please ensure 'KEY_STR_B64' is a valid Base64 encoded string.")
    exit()

# Hutool's SecureUtil.aes(key) uses the first 16 bytes of the key as the IV by default.
# Since our key is 16 bytes, the IV is the same as the key.
print("Using the first 16 bytes of the key as the IV.", key_bytes)
iv_bytes = key_bytes


# --- Python AES Encryption Function (emulating aes.encryptHex) ---
def aes_encrypt_hex(plaintext_str: str, key: bytes, iv: bytes) -> str:
    """
    Encrypts a string using AES CBC, then hex-encodes the ciphertext.
    """
    cipher = AES.new(key, AES.MODE_CBC, iv)
    plaintext_bytes = plaintext_str.encode("utf-8")
    # Pad the plaintext to be a multiple of AES.block_size (16 bytes)
    padded_plaintext_bytes = pad(plaintext_bytes, AES.block_size, style="pkcs7")
    ciphertext_bytes = cipher.encrypt(padded_plaintext_bytes)
    # Hex-encode the ciphertext bytes
    encrypted_hex = binascii.hexlify(ciphertext_bytes).decode("utf-8")
    return encrypted_hex


# --- Python AES Decryption Function (emulating aes.decryptStr) ---
def aes_decrypt_str(encrypted_hex: str, key: bytes, iv: bytes) -> str:
    """
    Hex-decodes the input, then decrypts using AES CBC, and returns the original string.
    """
    cipher = AES.new(key, AES.MODE_CBC, iv)
    # Hex-decode the encrypted string to bytes
    try:
        ciphertext_bytes = binascii.unhexlify(encrypted_hex)
    except binascii.Error as e:
        raise ValueError(f"Invalid hex-encoded ciphertext: {e}") from e

    decrypted_padded_bytes = cipher.decrypt(ciphertext_bytes)
    # Unpad the decrypted bytes
    try:
        decrypted_bytes = unpad(decrypted_padded_bytes, AES.block_size, style="pkcs7")
    except ValueError as e:
        # This can happen if the key/IV is wrong, padding is incorrect, or data is corrupt
        raise ValueError(
            f"Error unpadding data. Check key, IV, or data integrity: {e}"
        ) from e

    return decrypted_bytes.decode("utf-8")


# --- Example Usage (emulating the Java snippet's logic) ---
def example():
    # Simulate Java variables
    phone_number = "15708418034"
    current_time_millis = int(time.time() * 1000)

    # Plaintext to be encrypted
    token_to_encrypt = f"{phone_number}{current_time_millis}"
    print(f"Original token (plaintext): {token_to_encrypt}")

    # Encryption
    # Java: String encryptedText = aes.encryptHex(token);
    encrypted_text_hex = aes_encrypt_hex(token_to_encrypt, key_bytes, iv_bytes)
    print(f"Encrypted text (hex): {encrypted_text_hex}")

    # Decryption
    # Java: String decrypted = aes.decryptStr(mkSendSmsVO.getReport());
    # Assuming mkSendSmsVO.getReport() would return 'encrypted_text_hex'
    try:
        decrypted_token = aes_decrypt_str(encrypted_text_hex, key_bytes, iv_bytes)
        print(f"Decrypted token: {decrypted_token}")

        # Verification
        if decrypted_token == token_to_encrypt:
            print("✅ Encryption and Decryption successful, tokens match!")
        else:
            print("❌ Decryption failed or tokens do not match.")

    except ValueError as e:
        print(f"An error occurred during decryption: {e}")

    print("\n--- Simulating checkReport logic (without actual cache) ---")
    # This part simulates the checkReport logic's use of decryption
    # In Java: String decrypted_from_report = aes.decryptStr(mkSendSmsVO.getReport());
    #           String value_from_cache = multiCache.get(decrypted_from_report);
    #           if (!StringUtils.isEmpty(value_from_cache)) { ... }

    # Let's assume 'encrypted_text_hex' is what's coming in mkSendSmsVO.getReport()
    report_data_hex = encrypted_text_hex

    try:
        # 1. Decrypt the report data to get the original token (which was used as a cache key in Java)
        decrypted_original_token = aes_decrypt_str(report_data_hex, key_bytes, iv_bytes)
        print(f"Decrypted original token from report: {decrypted_original_token}")

        # 2. In the Java code, 'decrypted_original_token' would be used to query a cache.
        # The Java cache stores: multiCache.set(token, encryptedText, ...);
        # So, if decrypted_original_token == token_to_encrypt, then querying the cache
        # with 'decrypted_original_token' should yield 'encrypted_text_hex'.

        # Simulate a cache hit if the decrypted token is what we expect
        # (In a real scenario, you'd interact with a cache like Redis here)
        # We are just checking if the decryption process itself is correct.
        # The Java code's `if(!StringUtils.isEmpty(token))` where `token` is `multiCache.get(decrypted)`
        # means it checks if the original token existed in cache (and thus returned its associated encrypted value).

        if decrypted_original_token == token_to_encrypt:
            print(
                "Simulated cache lookup: The decrypted token matches the original token."
            )
            print(
                "This implies the token would likely be found in the cache if it was set correctly."
            )
            print(
                'Java\'s \'checkReport\' would likely return: Response.ok("200", "检验通过")'
            )
        else:
            # This case should ideally not happen if encryption/decryption keys match and data isn't corrupt
            print(
                "Simulated cache lookup: The decrypted token does NOT match the original."
            )
            print(
                'Java\'s \'checkReport\' would likely return: Response.err("0", "检验失败")'
            )

    except ValueError as e:
        print(f"Error in simulated checkReport decryption: {e}")
        print(
            'Java\'s \'checkReport\' would likely return: Response.err("0", "检验失败") due to decryption error'
        )


if __name__ == "__main__":
    example()
    # aes_decrypt_str("df50f0567340929f0f956222ff6ec6ca08030e9f17175da1a3b071b349510628",                key_bytes, iv_bytes)
