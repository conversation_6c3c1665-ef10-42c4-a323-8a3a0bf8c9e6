from common.logging.logger import get_logger
from nl2document.index.chains.re_query_by_rule import (
    ReQueryByRule,
    TrimRule,
    ReplaceRule,
)
from nl2document.index.service.service import DocumentIndexService

logger = get_logger(__name__)


class ScienceCityService(DocumentIndexService):
    def __init__(self):
        super().__init__()

    def get_re_query_by_rule(self):
        return ReQueryByRule(
            [
                TrimRule(),  # 去除首尾空白
                ReplaceRule("旅客登机桥下的", ""),  # 简单替换
                ReplaceRule("登机桥安全", "登机桥功能台账表保障安全"),
                ReplaceRule("登机桥服务", "登机桥保障服务"),
                ReplaceRule("登机桥效能", "登机桥保障效能"),
                ReplaceRule("登机桥运行", "登机桥保障运行"),
                ReplaceRule("保养内容", "保养工作"),
                ReplaceRule("保养项目", "保养工作"),
                ReplaceRule("安全的关键点", "安全的关键功能部位"),
                ReplaceRule("管轮", "滚轮"),
            ]
        )


science_city_route = ScienceCityService().router
