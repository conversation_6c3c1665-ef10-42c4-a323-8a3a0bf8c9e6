from typing import Dict, List
import re
from langchain_core.runnables import RunnableConfig

from common.types import ParamsExtractData, TimeQueryParams, QueryMetricResult
from common.types.base import CHAIN_META, ChainMeta, ChainRuntime
from metastore.base import Dimension, Metric


def doc_retrieve_preprocess_func(query: str, config: RunnableConfig) -> Dict:
    # raw_metrics :List[Metric]= config[CHAIN_META][ChainMeta.RUN_TIME].get(ChainRuntime.RAW_METRICS, [])
    params_extract_data: ParamsExtractData = config[CHAIN_META][ChainMeta.RUN_TIME].get(
        ChainRuntime.PARAMS_EXTRACT_DATA, []
    )
    raw_dimensions: List[Dimension] = config[CHAIN_META][ChainMeta.RUN_TIME].get(
        ChainRuntime.RAW_DIMENSIONS, []
    )
    raw_metrics: List[Metric] = config[CHAIN_META][ChainMeta.RUN_TIME].get(
        ChainRuntime.RAW_METRICS, []
    )

    metric_infos = []
    file_ids = config[CHAIN_META][ChainMeta.DOC_FILE_IDS]
    dir_ids = config[CHAIN_META][ChainMeta.DOC_DIR_IDS]
    if not params_extract_data:
        return {
            "query": query,
            "ids": file_ids or [],
            "dir_ids": dir_ids or [],
            "only_nodes": True,
            "company_id": [],
            "start_year": None,
            "month": None,
            "end_year": None,
            "end_month": None,
        }
    for metric_name in params_extract_data.query_metric.metricNames:
        # 在原始指标元数据中查找匹配项
        matched_metric = next((m for m in raw_metrics if m.name == metric_name), None)
        if matched_metric:
            metric_infos.append(
                {"label": matched_metric.label, "synonyms": matched_metric.synonyms}
            )
    company_ids = []
    # 从where条件中获取
    # where_condition = params_extract_data.query_metric.where
    # if where_condition:
    #     # 这里需要根据实际条件结构解析，示例处理等于条件
    #     company_ids = extract_company_codes(where_condition)

    dimension_sub_wheres = params_extract_data.query_metric.where_json.get(
        "dimension_sub_wheres"
    )
    if dimension_sub_wheres:
        for dim in dimension_sub_wheres:
            if dim.get("dimension_name") == "COMPANY_INNER_CODE_DES":
                dimension_values = dim.get("dimension_values")
                for dim_value in dimension_values:
                    if dim_value.get("dimension_value_name"):
                        company_ids.append(dim_value.get("dimension_value_name"))

    start_year = None
    end_year = None
    start_month = None
    end_month = None
    param: TimeQueryParams = params_extract_data.query_metric.timeQueryParams
    if param is not None:
        if param.timeStartFunction is not None:
            start_year = getattr(param.timeStartFunction, "year") or getattr(
                param.timeStartFunction, "years"
            )
            start_month = getattr(param.timeStartFunction, "month") or getattr(
                param.timeStartFunction, "months"
            )
        if param.timeEndFunction is not None:
            end_year = getattr(param.timeEndFunction, "year") or getattr(
                param.timeEndFunction, "years"
            )
            end_month = getattr(param.timeEndFunction, "month") or getattr(
                param.timeEndFunction, "months"
            )
    metric_desc = [
        f"{metric_info['label']}（同义词：{metric_info['synonyms']}）"
        for metric_info in metric_infos
    ]
    query = f"用户的query：{query}，匹配到的指标：{','.join(metric_desc)}"
    payload = {
        "query": query,
        "ids": file_ids or [],
        "only_nodes": True,
        "company_ids": company_ids,
        "start_year": str(start_year) if start_year else None,
        "start_month": str(start_month) if start_month else None,
        "end_year": str(end_year) if end_year else None,
        "end_month": str(end_month) if end_month else None,
        "dir_ids": dir_ids,
    }
    return payload


def plan_postprocess(query: str, tool_calls, config: RunnableConfig):
    def is_expected_tool_calls():
        if len(tool_calls) != 2:
            return False
        names = {tool.get("name") for tool in tool_calls}
        return names == {"fast_lookup", "early_stop"}

    if is_expected_tool_calls():
        for call in tool_calls:
            if call.get("name") == "fast_lookup" and "args" in call:
                call["args"]["query"] = query
    for call in tool_calls:
        if call.get("name") == "metric_meta":
            call["name"] = "fast_lookup"
    return tool_calls
