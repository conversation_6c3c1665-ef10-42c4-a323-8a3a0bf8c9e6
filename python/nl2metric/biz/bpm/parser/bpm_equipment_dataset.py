import json
import os
import re
import string

import openpyxl
from nl2document.builder.parser.parsed_document import ParsedDocument, ContentType


class ExcelParser:
    def parse(self, file_path, output_path: str = None):
        file_name = os.path.basename(file_path)
        version = "v09_11"
        equipment_type = ""  # “硬件”/“软件”/“云网资源”
        if "硬件" in file_name:
            equipment_type = "硬件"
        elif "软件" in file_name:
            equipment_type = "软件"
        elif "云网" in file_name:
            equipment_type = "云网资源"
        document = ParsedDocument(file_name)

        wb = openpyxl.load_workbook(file_path)
        for sheet_index, sheet_name in enumerate(wb.sheetnames):
            if "清单" not in sheet_name and "产品" not in sheet_name:
                continue
            if sheet_name:
                document.add_title(sheet_name, "", True, 180.0)
            sheet = wb[sheet_name]
            # 删除空行
            for row in reversed(
                list(sheet.iter_rows(min_row=1, max_row=sheet.max_row))
            ):
                if all(cell.value is None for cell in row):
                    sheet.delete_rows(row[0].row, amount=1)
            # 删除空列
            for col in reversed(
                list(sheet.iter_cols(min_col=1, max_col=sheet.max_column))
            ):
                if all(cell.value is None for cell in col):
                    sheet.delete_cols(col[0].col_idx, amount=1)

            sheet_needed_columns_num = []
            if equipment_type == "软件":
                sheet_needed_columns_name = ["一级分类", "原子能力名称", "能力简介"]
            elif equipment_type == "云网资源":
                sheet_needed_columns_name = [
                    "领域",
                    "一级产品类型",
                    "对应集团产品名称",
                    "产品名称（解构用）",
                    "能力简介",
                ]
            for i, row in enumerate(sheet.iter_rows(min_row=1, max_row=sheet.max_row)):
                row_content = ""
                is_excel_title = False
                for cell in row:
                    # print(cell.coordinate)
                    # print(cell.font.bold)  # 字体是否加粗
                    # print(cell.font.size)  # 字体大小
                    value = get_content(sheet, cell.coordinate)
                    cell_column, cell_row = re.findall(r"\d+|\D+", cell.coordinate)
                    # print(value)
                    print(value)
                    if value and "\t" in str(value):
                        value = value.replace("\t", "  ")
                    if value and cell.font.bold:
                        is_excel_title = True
                    if not value:
                        value = "None"
                    if is_excel_title:
                        if value in sheet_needed_columns_name:
                            sheet_needed_columns_name.remove(value)
                            if cell_column not in sheet_needed_columns_num:
                                sheet_needed_columns_num.append(cell_column)
                    if cell_column in sheet_needed_columns_num:
                        if row_content:
                            row_content = row_content + "\t" + str(value)
                        else:
                            row_content = str(value)
                if is_excel_title:
                    if (
                        "一级分类" in row_content
                        or "领域" in row_content
                        or "一级产品类型" in row_content
                    ):
                        row_content = row_content.replace("领域", "一级分类").replace(
                            "一级产品类型", "一级分类"
                        )
                        row_content = row_content.replace("对应集团产品名称", "原子能力名称").replace(
                            "产品名称（解构用）", "原子能力名称"
                        )
                        document.add_content(
                            row_content + "\t" + "产品类型" + "\t" + "版本号",
                            i + 1,
                            ContentType.EXCEL_TITLE,
                        )
                else:
                    document.add_content(
                        row_content + "\t" + equipment_type + "\t" + version,
                        i + 1,
                        ContentType.EXCEL_TEXT,
                    )

        final_text_res = document.to_json()

        if output_path:
            parse_output_file = os.path.join(
                output_path, f"{final_text_res['file_name']}.json"
            )
            with open(parse_output_file, "w") as f:
                json.dump(final_text_res, f, ensure_ascii=False)
            # print("parse_output_dir path is >>> ", parse_output_file)
        return final_text_res


def get_content(sheet, cell_position) -> str:  # 获取单元格内的text
    cell_is_in_merged = False  # 单元格是否在合并区
    content = ""
    cell_column, cell_row = re.findall(r"\d+|\D+", cell_position)
    # 获取所有合并单元格的范围 方便进行内容填充
    merged_cells_ranges = sheet.merged_cells.ranges
    # 遍历并输出合并单元格的范围
    all_positions = []
    for merged_range in merged_cells_ranges:
        coord = merged_range.coord
        positions = {}
        position_num = 0
        positions["coord"] = coord
        for position in coord.split(":"):
            column, row = re.findall(r"\d+|\D+", position)
            positions[position_num] = {"row": row, "column": column}
            position_num += 1
        all_positions.append(positions)
    for positions in all_positions:
        if (
            positions[0]["row"] == positions[1]["row"]
            and cell_row == positions[0]["row"]
        ):  # 同一行 所以是列合并
            column0 = string.ascii_uppercase.index(positions[0]["column"])
            column1 = string.ascii_uppercase.index(positions[1]["column"])
            column_min = column0 if column0 < column1 else column1
            column_max = column0 if column0 > column1 else column1
            value = sheet[
                string.ascii_uppercase[column_min] + positions[0]["row"]
            ].value
            if column_max >= string.ascii_uppercase.index(cell_column) >= column_min:
                cell_is_in_merged = True
                content = value
        elif (
            positions[0]["column"] == positions[1]["column"]
            and cell_column == positions[0]["column"]
        ):  # 同一列 所以是行合并
            row_min = (
                positions[0]["row"]
                if int(positions[0]["row"]) < int(positions[1]["row"])
                else positions[1]["row"]
            )
            row_max = (
                positions[0]["row"]
                if int(positions[0]["row"]) > int(positions[1]["row"])
                else positions[1]["row"]
            )
            value = sheet[positions[0]["column"] + row_min].value
            if int(row_max) >= int(cell_row) >= int(row_min):
                cell_is_in_merged = True
                content = value
        elif (positions[0]["row"] != positions[1]["row"]) and (
            positions[0]["column"] != positions[1]["column"]
        ):  # 多行 多列 合并
            column0 = string.ascii_uppercase.index(positions[0]["column"])
            column1 = string.ascii_uppercase.index(positions[1]["column"])
            column_min = column0 if column0 < column1 else column1
            column_max = column0 if column0 > column1 else column1
            row_min = (
                positions[0]["row"]
                if int(positions[0]["row"]) < int(positions[1]["row"])
                else positions[1]["row"]
            )
            row_max = (
                positions[0]["row"]
                if int(positions[0]["row"]) > int(positions[1]["row"])
                else positions[1]["row"]
            )
            value = sheet[string.ascii_uppercase[column_min] + row_min].value
            if (
                column_max >= string.ascii_uppercase.index(cell_column) >= column_min
            ) and (int(row_max) >= int(cell_row) >= int(row_min)):
                cell_is_in_merged = True
                content = value
    if not cell_is_in_merged:
        content = sheet[cell_position].value
    return content


if __name__ == "__main__":
    parser = ExcelParser()
    res = parser.parse(
        "/Users/<USER>/Desktop/工作内容/电信政企知识库/BMP方案/云网数据/软件列表看这里 -- 2024战新七融产品及能力清单【一季度取数】xlsx (4).xlsx",
        "/Users/<USER>/Desktop/工作内容/电信政企知识库/BMP方案/云网数据",
    )
