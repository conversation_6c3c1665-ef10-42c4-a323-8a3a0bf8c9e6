import json
import os
import csv


class BpmCsvParser:
    def parse_software_resource(self, file_path, output_path: str = None):
        file_name = os.path.basename(file_path)
        if not file_name == "software_resource.csv":
            raise RuntimeError(
                f"not parsing bpm software_resource file: software_resource.csv. filename: {file_name}"
            )
        with open(file_path, mode="r", encoding="utf-8") as file:
            rows = [row for row in csv.reader(file)]
        titles = []
        final_text_res = []
        for i, row in enumerate(rows):
            if i == 0:
                titles = row
            else:
                resource = {}
                for j, title in enumerate(titles):
                    if "序号" == title:
                        resource["id"] = row[j]
                    if "一级（平台/系统）" == title:
                        resource["first_level"] = row[j]
                    if "二级（子系统/子模块）" == title:
                        resource["second_level"] = row[j]
                    if "三级（功能点）" == title:
                        resource["third_level"] = row[j]
                    if "功能描述" == title:
                        resource["function_desc"] = row[j]
                    if "前向收入合计（元）" == title:
                        resource["forward_income_total"] = row[j]
                    if "后向支出合计（元）" == title:
                        resource["back_outcome_total"] = row[j]
                    if "实施方式" == title:
                        resource["implement_method"] = row[j]
                    if "中国电信能力名称" == title:
                        resource["ctc_ability_name"] = row[j]
                    if "统一编码" == title:
                        resource["unify_code"] = row[j]
                final_text_res.append(resource)
        print(titles)

        if output_path:
            parse_output_file = os.path.join(output_path, f"bpm_software_resource.json")
            with open(parse_output_file, "w") as f:
                json.dump(final_text_res, f, ensure_ascii=False)
        return final_text_res

    def parse_cloud_resource(self, file_path, output_path: str = None):
        file_name = os.path.basename(file_path)
        if not file_name == "cloud_resource.csv":
            raise RuntimeError(
                f"not parsing bpm cloud_resource file: cloud_resource.csv. filename: {file_name}"
            )
        with open(file_path, mode="r", encoding="utf-8") as file:
            rows = [row for row in csv.reader(file)]
        titles = []
        final_text_res = []
        for i, row in enumerate(rows):
            if i == 0:
                titles = row
            else:
                resource = {}
                for j, title in enumerate(titles):
                    if "序号" == title:
                        resource["id"] = row[j]
                    if "类型" == title:
                        resource["type"] = row[j]
                    if "产品名称" == title:
                        resource["product_name"] = row[j]
                    if "规格描述" == title:
                        resource["tech_spec"] = row[j]
                    if "厂商" == title:
                        resource["brand"] = row[j]
                    if "单位" == title:
                        resource["unit"] = row[j]
                    if "数量" == title:
                        resource["num"] = row[j]
                    if "前向收入单价（元）" == title:
                        resource["forward_income_price"] = row[j]
                    if "前向收入合计（元）" == title:
                        resource["forward_income_total"] = row[j]
                    if "后向支出单价（元）" == title:
                        resource["back_outcome_price"] = row[j]
                    if "后向支出合计（元）" == title:
                        resource["back_outcome_total"] = row[j]
                    if "实施方式" == title:
                        resource["implement_method"] = row[j]
                    if "产品编码" == title:
                        resource["product_code"] = row[j]
                final_text_res.append(resource)
        print(titles)

        if output_path:
            parse_output_file = os.path.join(output_path, f"bpm_cloud_resource.json")
            with open(parse_output_file, "w") as f:
                json.dump(final_text_res, f, ensure_ascii=False)
        return final_text_res

    def parse_hardware_resource(self, file_path, output_path: str = None):
        file_name = os.path.basename(file_path)
        if not file_name == "hardware_resource.csv":
            raise RuntimeError(
                f"not parsing bpm hardware_resource file: hardware_resource.csv. filename: {file_name}"
            )
        with open(file_path, mode="r", encoding="utf-8") as file:
            rows = [row for row in csv.reader(file)]
        titles = []
        final_text_res = []
        for i, row in enumerate(rows):
            if i == 0:
                titles = row
            else:
                resource = {}
                for j, title in enumerate(titles):
                    if "序号" == title:
                        resource["id"] = row[j]
                    if "设备类型" == title:
                        resource["device_type"] = row[j]
                    if "设备名称" == title:
                        resource["device_name"] = row[j]
                    if "品牌（原厂商）" == title:
                        resource["brand"] = row[j]
                    if "设备型号" == title:
                        resource["equipment_type"] = row[j]
                    if "技术规格" == title:
                        resource["tech_spec"] = row[j]
                    if "单位" == title:
                        resource["unit"] = row[j]
                    if "数量" == title:
                        resource["num"] = row[j]
                    if "前向收入单价（元）" == title:
                        resource["forward_income_price"] = row[j]
                    if "前向收入合计（元）" == title:
                        resource["forward_income_total"] = row[j]
                    if "后向支出单价（元）" == title:
                        resource["back_outcome_price"] = row[j]
                    if "后向支出合计（元）" == title:
                        resource["back_outcome_total"] = row[j]
                    if "实施方式" == title:
                        resource["implement_method"] = row[j]
                    if "物料编码" == title:
                        resource["material_code"] = row[j]
                final_text_res.append(resource)
        print(titles)

        if output_path:
            parse_output_file = os.path.join(output_path, f"bpm_hardware_resource.json")
            with open(parse_output_file, "w") as f:
                json.dump(final_text_res, f, ensure_ascii=False)
        return final_text_res


if __name__ == "__main__":
    # res = BpmCsvParser().parse_software_resource(
    #     "/Users/<USER>/Desktop/工作内容/电信政企知识库/BMP方案/设备数据/software_resource.csv",
    #     "/Users/<USER>/Desktop/工作内容/电信政企知识库/BMP方案/设备数据",
    # )
    # res = BpmCsvParser().parse_cloud_resource(
    #     "/Users/<USER>/Desktop/工作内容/电信政企知识库/BMP方案/设备数据/cloud_resource.csv",
    #     "/Users/<USER>/Desktop/工作内容/电信政企知识库/BMP方案/设备数据",
    # )
    res = BpmCsvParser().parse_hardware_resource(
        "/Users/<USER>/Desktop/工作内容/电信政企知识库/BMP方案/设备数据/hardware_resource.csv",
        "/Users/<USER>/Desktop/工作内容/电信政企知识库/BMP方案/设备数据",
    )
