from abc import ABC, abstractmethod
from typing import List


class FileSystem(ABC):
    @abstractmethod
    def read_file(self, path: str) -> str:
        """读取文件内容"""
        pass

    @abstractmethod
    def move_file(self, src: str, dest: str) -> str:
        """移动文件"""
        pass

    @abstractmethod
    def create_file(self, path: str, content: str) -> None:
        """创建文件并写入内容"""
        pass

    @abstractmethod
    def download_file(self, path: str) -> str:
        """下载文件"""
        pass

    @abstractmethod
    def list_dir(self, path: str) -> list:
        """列出目录下的文件"""
        pass

    @abstractmethod
    def update_file_lock(self, path: str):
        pass

    @abstractmethod
    def acquire_lock(self) -> bool:
        """获取文件锁"""
        pass

    @abstractmethod
    def release_lock(self):
        """释放文件锁"""
        pass

    @abstractmethod
    def check_lock(self) -> bool:
        """检查文件锁是否被占用"""
        pass

    @abstractmethod
    def get_file_mod_time(self, file_name: str):
        pass

    @abstractmethod
    def list_files(self, path: str) -> List[str]:
        """列出 FTP 目录中的所有文件，并返回完整路径"""
        pass
