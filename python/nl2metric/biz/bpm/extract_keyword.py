import json

import pandas as pd

from common.llm.llama_llm import get_llm
from config.app_config import MODEL_TYPE_GPT4


def llm_get_keywords(msg: str):
    llm = get_llm(MODEL_TYPE_GPT4)
    prompt = """你是一个关键词提取专家，擅长于分析解决方案，并从中提取出有关于软硬件需求的关键词；
    你提取的关键词非常重要，有助于我进一步从软硬件列表中召回方案中所需要的软硬件列表。
    解决方案片段如下：
    ```
    {msg}
    ```
    你需要从中提取出能快速找到对应软硬件列表的关键词，输出为list，且每个关键词之间用英文逗号隔开。
    你提取的关键词一定要是于软硬件非常强相关才可以，如果片段中不包含，直接返回空即可
    """
    return llm.complete(prompt.format(msg=msg)).text


def get_keyword_from_resource_list() -> list:
    # open xlsx file
    file_name = "./软件列表看这里 -- 2024战新七融产品及能力清单【一季度取数】xlsx (4).xlsx"
    with open(file_name, "rb") as f:
        data = pd.read_excel(f)
        for i in range(len(data)):
            result = llm_get_keywords(data.iloc[i]["原子能力名称"])
            print(result)


def get_keyword_from_text():
    keywords_list = ""
    with open("./text.json", "r") as f:
        data = json.load(f)
        for i in range(len(data)):
            result = llm_get_keywords(data[i])
            keywords_list += result + "\n"

    with open("./keywords.txt", "w") as f:
        f.write(keywords_list)


def clean_text_keyword():
    result = []
    with open("./keywords.txt", "r") as f:
        data = f.readlines()
        for i in range(len(data)):
            data[i] = data[i].strip()
            data[i] = data[i].replace("]", "\n")
            for s in ["[", "`", ""]:
                data[i] = data[i].replace(s, "")
            data[i] = data[i].replace(",", "\n")
            data[i] = data[i].replace('"', "")
            data[i] = data[i].replace(" ", "")
            data[i] = data[i].strip()
            if data[i] == "":
                continue
            result.append(data[i])
    with open("./keywords_new.txt", "w") as f:
        f.writelines("\n".join(result))


if __name__ == "__main__":
    clean_text_keyword()
