from contextlib import asynccontextmanager
from pathlib import Path

import asyncio
from dotenv import load_dotenv

# specify path to easily change env in docker
env_file = Path(__file__).parent / ".env"
print(f"load dotenv {env_file}")
load_dotenv(dotenv_path=env_file, override=True)
# both app_config and doc_config needs load_dotenv
from fastapi import FastAPI

from config.doc_config import BPM_FTP_FILE_SYSTEM_PREFIX_DIR

from biz.bpm.bpm_file_executor import BpmFileExecutor
from biz.bpm.bpm_common.common import file_system, redis_pool
from biz.bpm.router import bpm_router


async def main():
    executor = BpmFileExecutor(file_system, BPM_FTP_FILE_SYSTEM_PREFIX_DIR, redis_pool)
    await executor.process_directory()


@asynccontextmanager
async def lifespan(app: FastAPI):
    asyncio.create_task(main())
    yield


app = FastAPI(lifespan=lifespan)
app.include_router(bpm_router)

"""
uploading
ready
processing
processed
error
"""
if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)
