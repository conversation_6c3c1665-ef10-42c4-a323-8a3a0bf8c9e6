from typing import List, Optional

from pydantic import BaseModel

from nl2document.common.msg.report_generate_msg import DataTimeParams


class CreateAuthTemplateReportRequest(BaseModel):
    dataTimeParams: Optional[DataTimeParams] = None
    reportTitle: str
    reportIntention: str
    creator: Optional[str] = ""
    department: Optional[str] = None
    province: Optional[str] = None
    reportId: Optional[int] = None
    languageStyle: Optional[str] = None


class ListSpecificColumnCodeValueRequest(BaseModel):
    modelName: str
    columnCode: Optional[str] = None
