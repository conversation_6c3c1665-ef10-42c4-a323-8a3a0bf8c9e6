import traceback
from concurrent.futures import Thread<PERSON>oolExecutor

from fastapi import APIRouter, Request
from fastapi.responses import JSONResponse
from pydantic import ValidationError

from biz.cmcc_jingfen_report_biz.msg import (
    CreateAuthTemplateReportRequest,
    ListSpecificColumnCodeValueRequest,
)
from biz.cmcc_jingfen_report_biz.report_config import (
    report_department_name,
    report_province_name,
)
from common.logging.logger import get_logger
from common.utils.string_utils import class_to_dict
from nl2document.common.models.report_generate_model import (
    get_template_by_id,
    get_report_by_id,
    Report,
    save_or_create_report,
    get_template_filter_params,
    get_template_data_time_params,
    ReportDataTimeParams,
    save_or_create_template_data_time_params_for_report,
    save_or_create_column_filter_values_for_report,
    get_model_meta_from_db,
    list_column_code_values_from_db,
)
from nl2document.common.msg.report_generate_msg import (
    OutlineNode,
    ListColumnCodeValueData,
)
from nl2document.report_generate.report.data_op_executor import generate_report_section
from nl2document.report_generate.report.graph_executor import (
    build_dependency_graph,
    topological_sort,
)
from nl2document.report_generate.report.report_outline import (
    process_nodes_depends,
    get_node_section_id,
    write_section_node_content,
    set_cached_outline_nodes,
)
from nl2document.report_generate.service.service import ReportGenerateService
from datetime import datetime

ALL_INDUSTRIES_NATIONWIDE_TEMPLATE_ID = -1
SINGLE_INDUSTRY_NATIONWIDE_TEMPLATE_ID = -2
ALL_INDUSTRIES_PROVINCE_TEMPLATE_ID = -3
SINGLE_INDUSTRY_PROVINCE_TEMPLATE_ID = -4

logger = get_logger(__name__)

jingfen_report_generate_router = APIRouter()


def choose_template(req: CreateAuthTemplateReportRequest):
    if req.province is None:
        if req.department is None:
            return ALL_INDUSTRIES_NATIONWIDE_TEMPLATE_ID
        else:
            return SINGLE_INDUSTRY_NATIONWIDE_TEMPLATE_ID
    else:
        if req.department is None:
            return ALL_INDUSTRIES_PROVINCE_TEMPLATE_ID
        else:
            return SINGLE_INDUSTRY_PROVINCE_TEMPLATE_ID


def create_auth_template_outline(req: CreateAuthTemplateReportRequest):
    # check if report exists
    template_id = choose_template(req)
    template = get_template_by_id(template_id)
    province = req.province
    department = req.department

    outline_nodes = [OutlineNode(**node) for node in template.outline]
    if req.reportId is not None:
        report = get_report_by_id(req.reportId)
        if report is None:
            raise Exception("report not found")
        report.name = req.reportTitle
        report.intention = req.reportIntention
    else:
        report = Report(
            name=req.reportTitle,
            intention=req.reportIntention,
            model_name=template.model_name,
            template_id=template.template_id,
            outline=class_to_dict(outline_nodes),
            creator=req.creator,
            scene_id=template.scene_id,
        )
        report.id = save_or_create_report(report)

    data_filter_params = get_template_filter_params(template.template_id)
    data_time_params = get_template_data_time_params(template.template_id)
    data_time_params.time_range_start = datetime.strptime(
        req.dataTimeParams.timeRangeStart, "%Y-%m-%d"
    )
    data_time_params.time_range_end = datetime.strptime(
        req.dataTimeParams.timeRangeEnd, "%Y-%m-%d"
    )

    report_data_time_params = ReportDataTimeParams(
        report_id=report.id,
        time_column=data_time_params.time_column,
        time_range_start=req.dataTimeParams.timeRangeStart,
        time_range_end=req.dataTimeParams.timeRangeEnd,
    )
    save_or_create_template_data_time_params_for_report(report_data_time_params)
    save_or_create_column_filter_values_for_report(report.id, data_filter_params)

    # dataTimeParams = TemplateDataTimeParams(
    #     time_range_start=datetime.strptime(
    #         req.dataTimeParams.timeRangeStart, "%Y-%m-%d"
    #     ),
    #     time_range_end=datetime.strptime(req.dataTimeParams.timeRangeEnd, "%Y-%m-%d"),
    #     time_column=req.dataTimeParams.timeColumn,
    # )

    # tasks = [
    #     lambda: copy_section_config(report.id, req.templateId),
    #     lambda: copy_data_section_op(report.id, req.templateId),
    #     lambda: copy_text_op(report.id, req.templateId),
    # ]
    # run_concurrently(tasks, max_workers=3)
    # save data params
    # save_or_create_column_filter_values(report.id, filterParams)
    #
    # save_or_create_data_time_params_msg(report.id, dataTimeParams)

    process_nodes_depends(template_id, outline_nodes)
    section_list = get_node_section_id([], outline_nodes)
    graph, in_degree = build_dependency_graph(section_list)
    execution_order = topological_sort(section_list, graph, in_degree)
    node_map = {node.id: node for node in section_list}
    results = {}  # 用于存储每个节点的 Future 对象
    logger.info(f"execution_order: {execution_order}")
    with ThreadPoolExecutor(max_workers=5) as executor:
        for node_id in execution_order:
            node = node_map[node_id]
            # 提交任务，并将 Future 存储在 results 字典中
            future = executor.submit(
                generate_report_section,
                template,
                data_time_params,
                node,
                results,
                province=province,
                department=department,
                language_style=req.languageStyle,
            )
            results[node_id] = future
        for node_id, future in results.items():
            try:
                result = future.result()  # 获取任务结果
                logger.info(
                    f"Node {node_id} executed successfully with result: {result}"
                )
            except Exception as e:
                logger.info(f"Node {node_id} execution failed with exception: {e}")
                logger.error("Stack trace: %s", traceback.format_exc())
                raise e
        write_section_node_content(outline_nodes, results)
    report.outline = class_to_dict(outline_nodes)
    set_cached_outline_nodes(req, outline_nodes)
    save_or_create_report(report)
    return report.id, outline_nodes


class JingfenReportGenerateService(ReportGenerateService):
    def __init__(self):
        self.register_routes()

    def register_routes(self):
        jingfen_report_generate_router.add_api_route(
            "/api/report-generate/auth-template/create-report",
            self.create_auth_template_report,
            methods=["POST"],
        )
        jingfen_report_generate_router.add_api_route(
            "/api/report-generate/department_value",
            self.department_value,
            methods=["GET"],
        )
        jingfen_report_generate_router.add_api_route(
            "/api/report-generate/province_value", self.province_value, methods=["GET"]
        )

    def create_auth_template_report(self, request: CreateAuthTemplateReportRequest):
        try:
            report_id, outline_nodes = create_auth_template_outline(request)
            return {
                "code": 0,
                "data": {
                    "outline": class_to_dict(outline_nodes),
                    "reportId": report_id,
                },
            }
        except Exception as e:
            logger.error(f"create auth-template report failed: {e}")
            logger.error("Stack trace: %s", traceback.format_exc())

            return {
                "code": 500,
                "msg": str(e),
            }

    def department_value(self, request: Request):
        query_params = request.query_params
        try:
            req = ListSpecificColumnCodeValueRequest(**query_params)
        except ValidationError as e:
            return JSONResponse(status_code=400, content={"error": e.errors()})
        try:
            _, column_classify_data = get_model_meta_from_db(req.modelName)
            column_code = next(
                (
                    dimension.code
                    for dimension in column_classify_data.dimensions
                    if dimension.name == report_department_name
                ),
                None,
            )
            if column_code is None:
                raise Exception(
                    f"can't find column code for department {report_department_name} in {req.modelName}"
                )
            column_values = list_column_code_values_from_db(req.modelName, column_code)
            total = len(column_values)
            return {
                "code": 0,
                "data": class_to_dict(
                    ListColumnCodeValueData(total=total, valueList=column_values)
                ),
            }
        except Exception as e:
            logger.error(f"get department column value failed: {e}")
            logger.error("Stack trace: %s", traceback.format_exc())

            return {
                "code": 1,
                "msg": str(e),
            }

    def province_value(self, request: Request):
        query_params = request.query_params
        try:
            req = ListSpecificColumnCodeValueRequest(**query_params)
        except ValidationError as e:
            return JSONResponse(status_code=400, content={"error": e.errors()})
        try:
            _, column_classify_data = get_model_meta_from_db(req.modelName)
            column_code = next(
                (
                    dimension.code
                    for dimension in column_classify_data.dimensions
                    if dimension.name == report_province_name
                ),
                None,
            )
            if column_code is None:
                raise Exception(
                    f"can't find column code for province  {report_province_name} in {req.modelName}"
                )
            column_values = list_column_code_values_from_db(req.modelName, column_code)
            total = len(column_values)
            return {
                "code": 0,
                "data": class_to_dict(
                    ListColumnCodeValueData(total=total, valueList=column_values)
                ),
            }
        except Exception as e:
            logger.error(f"get department column value failed: {e}")
            logger.error("Stack trace: %s", traceback.format_exc())

            return {
                "code": 1,
                "msg": str(e),
            }


jingfen_report_generate_service = JingfenReportGenerateService()
