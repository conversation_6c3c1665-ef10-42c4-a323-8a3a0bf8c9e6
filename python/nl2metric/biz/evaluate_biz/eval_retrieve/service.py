from llama_index.core import ServiceContext
from llama_index.core.text_splitter import TokenTextSplitter
from fastapi import APIRouter
from typing import List
from pydantic import BaseModel

from common.llm.llama_llm import get_llm
from common.logging.logger import get_logger
from common.llm.embedding import get_embedding_model
from config import doc_config
from biz.evaluate_biz.common.app import app
from biz.evaluate_biz.common.data_model import (
    RetrieveAndReRankData,
    RetrieveResponse,
    RetrieveRequest,
    PreviewResponse,
    NodeUnit,
    get_node_with_score_simple,
)
from common.types.base import CHAIN_META, ChainMeta, ChainRuntime
from config import app_config
from langchain_core.runnables import RunnableLambda, RunnableConfig
from llama_index.core.vector_stores import (
    MetadataFilters,
    MetadataFilter,
    FilterOperator,
    FilterCondition,
)

from nl2document.common.base.const import LLAMA_INDEX_FILE_ID
from nl2document.index.chains.query_document import retrieve


logger = get_logger(__name__)
embed_model, _, _ = get_embedding_model(doc_config.doc_embedding_model)


class EvalRetrieveService:
    def __init__(self):
        self.router = APIRouter(prefix="/api/eval/retrieve")
        self._register_routes()

    def _register_routes(self):
        @self.router.post("/search")
        async def retrieve_eval(req: RetrieveRequest):
            service_context = ServiceContext.from_defaults(
                llm=get_llm(model_type="", temperature=0.5, repetition_penalty=1.2),
                embed_model=embed_model,
                text_splitter=TokenTextSplitter(),
            )
            chain_metadata = {
                ChainMeta.JOB_TYPE: "bj_telecom_search_doc",
                ChainMeta.MODEL_TYPE: "",
                ChainMeta.LLAMAINDEX_SERVICE_CONTEXT: service_context,
                ChainMeta.RUN_TIME: {
                    ChainRuntime.QUESTION: req.question,
                    ChainRuntime.COLLECTION_NAME: req.collection_name,
                },
                ChainMeta.QUERY_PARAMS: {
                    "rank_topk": req.rerank_k,
                    "ask_doc_similarity_top_k": req.top_k,
                },
            }
            cbs = []
            chain_config = {
                CHAIN_META: chain_metadata,
                "callbacks": cbs,
                "max_concurrency": app_config.MAX_CONCURRENCY,
            }
            doc_ids = req.doc_ids
            doc_filters = None
            if doc_ids is not None and len(doc_ids) > 0:
                doc_filters = MetadataFilters(
                    filters=[
                        MetadataFilter(
                            key=LLAMA_INDEX_FILE_ID,
                            value=doc_ids,
                            operator=FilterOperator.IN,
                        )
                    ],
                    condition=FilterCondition.AND,
                )
            chain = RunnableLambda(retrieve, name="retrieve").bind(filters=doc_filters)
            rerank_nodes = await chain.ainvoke(
                req.question,
                config=chain_config,
            )
            retrieve_nodes = chain_config[CHAIN_META][ChainMeta.RUN_TIME][
                ChainRuntime.RETRIEVE_NODES_WITH_SCORE
            ]
            data = RetrieveAndReRankData(
                rerank_results=get_node_with_score_simple(rerank_nodes),
                retrieve_results=get_node_with_score_simple(retrieve_nodes),
            )
            return RetrieveResponse(
                code=0,
                data=data,
                msg="success",
            )
