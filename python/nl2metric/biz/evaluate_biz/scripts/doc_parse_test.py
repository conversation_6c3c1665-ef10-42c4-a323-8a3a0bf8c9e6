from fastapi import Fast<PERSON><PERSON>, File, UploadFile, Form, APIRouter
from typing import Dict, Any, List, Optional
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
import tempfile
import os
from uuid import uuid4
import shutil
from datetime import datetime
import json
from pathlib import Path

from nl2document.builder.parser.document_parser import DocumentParser
from nl2document.common.models.model import CommonDocumentModel
from biz.evaluate_biz.common.data_model import NodeUnit, PreviewResponse
from biz.evaluate_biz.common.app import app
from common.logging.logger import get_logger
from nl2document.common.vector.vector_store import get_vector_store
from common.llm.embedding import get_embedding_model
from config import doc_config
from nl2document.builder.document_index_builder import DocumentIndexBuilder
from llama_index.core.text_splitter import TokenTextSplitter
from llama_index.core import (
    ServiceContext,
    StorageContext,
)
from llama_index.core.vector_stores import (
    MetadataFilters,
    MetadataFilter,
    FilterOperator,
    FilterCondition,
)
from nl2document.builder.index_builder import IndexBuilderBase
from nl2document.common.base.const import LLAMA_INDEX_FILE_PARENT_LIST
from nl2document.common.utils import extract_company_and_date

embed_model, _, _ = get_embedding_model(doc_config.doc_embedding_model)
logger = get_logger(__name__)


class ParseResult:
    def __init__(self, file_path: str, file_id: str):
        self.file_id = file_id
        self.file_path = file_path
        self.file_name = os.path.basename(file_path)
        self.file_type = os.path.splitext(self.file_name)[1]
        self.status = "pending"
        self.error = None
        self.node_count = 0
        self.start_time = None
        self.end_time = None
        self.duration = None

    def start(self):
        self.start_time = datetime.now()
        self.status = "processing"

    def complete(self, node_count: int):
        self.end_time = datetime.now()
        self.duration = (self.end_time - self.start_time).total_seconds()
        self.status = "success"
        self.node_count = node_count

    def fail(self, error: str):
        self.end_time = datetime.now()
        self.duration = (self.end_time - self.start_time).total_seconds()
        self.status = "failed"
        self.error = str(error)

    def to_dict(self) -> dict:
        return {
            "file_id": self.file_id,
            "file_path": self.file_path,
            "file_name": self.file_name,
            "file_type": self.file_type,
            "status": self.status,
            "error": self.error,
            "node_count": self.node_count,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "duration": self.duration,
        }


def create_index(source_path: str, document_model: CommonDocumentModel):
    index_builder = IndexBuilderBase()
    vector_store = get_vector_store()
    storage_context = StorageContext.from_defaults(vector_store=vector_store)
    logger.info(
        f"start parse document nodes to create index, file_id: {document_model.id}"
    )
    node_result = index_builder._doc_parser.parse(source_path, document_model)
    nodes = node_result.nodes
    parent_id_list = []
    parent_name_list = []
    document_model.meta_info[LLAMA_INDEX_FILE_PARENT_LIST] = parent_id_list
    company_id, year, month = extract_company_and_date(
        parent_name_list, document_model.file_name
    )
    document_model.meta_info["company_id"] = company_id
    document_model.meta_info["date"] = f"{year}-{month}"
    document_model.meta_info["year"] = year
    document_model.meta_info["month"] = month

    for node in nodes:
        node.metadata.update(document_model.meta_info)
    index_builder._parse_doc_post_process(node_result, document_model)
    document_index = DocumentIndexBuilder(
        storage_context, index_builder._service_context
    ).build(all_nodes=nodes)
    return document_index, nodes


def parse_single_file(file_path: str) -> ParseResult:
    """解析单个文件并返回结果"""
    file_id = str(uuid4())
    result = ParseResult(file_path, file_id)
    result.start()

    try:
        # 解析文档
        file_name = os.path.basename(file_path)
        file_type = os.path.splitext(file_name)[1]
        doc_model = CommonDocumentModel(
            id=file_id,
            file_type=file_type,
            platform="",
            upload_type="",
            file_name=file_name,
        )

        document_index, nodes = create_index(
            source_path=file_path, document_model=doc_model
        )

        result.complete(len(nodes))
        logger.info(f"成功解析文件: {file_path}")

    except Exception as e:
        result.fail(str(e))
        logger.error(f"解析文件 {file_path} 失败: {str(e)}")

    return result


def generate_report(results: List[ParseResult], output_dir: str):
    """生成测试报告"""
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 生成JSON报告
    json_report = {
        "summary": {
            "total_files": len(results),
            "successful": sum(1 for r in results if r.status == "success"),
            "failed": sum(1 for r in results if r.status == "failed"),
            "total_duration": sum(r.duration or 0 for r in results),
            "average_duration": sum(r.duration or 0 for r in results) / len(results)
            if results
            else 0,
        },
        "results": [r.to_dict() for r in results],
    }

    # 保存JSON报告
    json_path = os.path.join(output_dir, "parse_results.json")
    with open(json_path, "w", encoding="utf-8") as f:
        json.dump(json_report, f, ensure_ascii=False, indent=2)

    # 生成Markdown报告
    md_path = os.path.join(output_dir, "parse_results.md")
    with open(md_path, "w", encoding="utf-8") as f:
        f.write("# 文档解析测试报告\n\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        # 写入摘要
        f.write("## 测试摘要\n\n")
        f.write(f"- 总文件数: {json_report['summary']['total_files']}\n")
        f.write(f"- 成功解析: {json_report['summary']['successful']}\n")
        f.write(f"- 解析失败: {json_report['summary']['failed']}\n")
        f.write(f"- 总耗时: {json_report['summary']['total_duration']:.2f}秒\n")
        f.write(f"- 平均耗时: {json_report['summary']['average_duration']:.2f}秒\n\n")

        # 写入详细结果
        f.write("## 详细结果\n\n")
        for result in json_report["results"]:
            f.write(f"### {result['file_name']}\n\n")
            f.write(f"- id: {result['file_id']}\n")
            f.write(f"- 文件类型: {result['file_type']}\n")
            f.write(f"- 状态: {result['status']}\n")
            if result["status"] == "success":
                f.write(f"- 节点数: {result['node_count']}\n")
            if result["error"]:
                f.write(f"- 错误信息: {result['error']}\n")
            f.write(f"- 耗时: {result['duration']:.2f}秒\n\n")


def doc_parse():
    """主函数：解析测试目录下的所有文件并生成报告"""
    # 测试目录
    test_dir = "/test-files/"
    output_dir = "/results/"

    # 确保目录存在
    if not os.path.exists(test_dir):
        logger.error(f"测试目录 {test_dir} 不存在")
        return

    # 收集所有文件
    all_files = []
    for root, _, files in os.walk(test_dir):
        for file in files:
            all_files.append(os.path.join(root, file))

    if not all_files:
        logger.warning(f"测试目录 {test_dir} 中没有找到文件")
        return

    # 解析所有文件
    results = []
    total_files = len(all_files)

    for i, file_path in enumerate(all_files, 1):
        logger.info(f"处理文件 {i}/{total_files}: {file_path}")
        result = parse_single_file(file_path)
        results.append(result)

        # 显示进度
        success_count = sum(1 for r in results if r.status == "success")
        fail_count = sum(1 for r in results if r.status == "failed")
        logger.info(f"进度: {i}/{total_files} (成功: {success_count}, 失败: {fail_count})")

    # 生成报告
    generate_report(results, output_dir)
    logger.info(f"测试完成! 报告已生成到目录: {output_dir}")

    # 清理test files 目录
    clean_directory_contents(test_dir)


def clean_directory_contents(directory: str):
    """清理目录内容但保留目录本身"""
    try:
        for item in os.listdir(directory):
            item_path = os.path.join(directory, item)
            try:
                if os.path.isfile(item_path):
                    os.remove(item_path)
                elif os.path.isdir(item_path):
                    shutil.rmtree(item_path)
            except Exception as e:
                logger.warning(f"删除 {item_path} 时发生错误: {e}")
    except Exception as e:
        logger.error(f"清理目录 {directory} 时发生错误: {e}")


if __name__ == "__main__":
    doc_parse()
