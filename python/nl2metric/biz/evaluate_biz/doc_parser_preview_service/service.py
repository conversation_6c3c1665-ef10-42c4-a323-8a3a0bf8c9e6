# fastapi server
from fastapi import FastAPI, File, UploadFile, Form, APIRouter
from typing import Dict, Any, List, Optional, Callable
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
import tempfile
import os
from uuid import uuid4
import shutil
import time

from common.trace.perfomance_monitor import performance_monitor, performance_context
from nl2document.builder.parser.document_parser import DocumentParser
from nl2document.common.models.model import CommonDocumentModel
from biz.evaluate_biz.common.data_model import NodeUnit, PreviewResponse
from biz.evaluate_biz.common.app import app
from common.logging.logger import get_logger
from nl2document.common.vector.vector_store import get_vector_store
from common.llm.embedding import get_embedding_model
from config import doc_config
from nl2document.builder.document_index_builder import DocumentIndexBuilder
from llama_index.core.text_splitter import TokenTextSplitter
from llama_index.core import (
    ServiceContext,
    StorageContext,
)
from llama_index.core.vector_stores import (
    MetadataFilters,
    MetadataFilter,
    FilterOperator,
    FilterCondition,
)

embed_model, _, _ = get_embedding_model(doc_config.doc_embedding_model)

logger = get_logger(__name__)


class DocParserPreviewService:
    def __init__(self):
        self.router = APIRouter()
        self._register_routes()

    def _register_routes(self):
        @self.router.post("/api/parser/preview", response_model=PreviewResponse)
        @performance_monitor(name="文档解析预览接口", log_args=True)
        async def preview(
            file: UploadFile = File(...),
            file_name: str = Form(...),
            platform: str = Form(...),
            upload_type: str = Form(...),
            chunk_size: int = Form(500),
            chunk_overlap: int = Form(10),
            table_chunk_size: int = Form(4096 * 6),
            table_chunk_overlap: int = Form(100),
        ):
            """
            文档解析预览接口
            Args:
                file (UploadFile): 上传的文件对象
                file_name (str): 文件名
                platform (str): 平台类型,如 "mobile"
                upload_type (str): 上传类型,如 "doc"

            Returns:
                PreviewResponse: 包含以下字段的响应对象
                    - code (int): 状态码,0表示成功,非0表示失败
                    - msg (str): 状态信息
                    - data (List[NodeUnit]): 解析结果,每个NodeUnit包含:
                        - text (str): 节点文本内容
                        - metadata (Dict): 节点元数据

            Raises:
                Exception: 文档解析过程中的异常
            """
            doc_parser = DocumentParser(
                general_parser_chunk_size_=chunk_size,
                general_parser_chunk_overlap_=chunk_overlap,
                general_parser_table_chunk_size_=table_chunk_size,
                general_parser_table_chunk_overlap_=table_chunk_overlap,
            )
            temp_file_path = None
            file_type = os.path.splitext(file_name)[1] if file_name else ""
            file_id = str(uuid4())
            doc_model = CommonDocumentModel(
                id=file_id,
                file_type=file_type,
                platform=platform,
                upload_type=upload_type,
                file_name=file_name,
            )
            try:
                # 创建临时文件
                with tempfile.NamedTemporaryFile(
                    delete=False, suffix=file_type
                ) as temp_file:
                    temp_file_path = temp_file.name
                    # 保存上传的文件内容
                    shutil.copyfileobj(file.file, temp_file)

                # 使用上下文管理器监控文档解析
                with performance_context("文档解析阶段"):
                    node_result = doc_parser.parse(temp_file_path, doc_model)

                nodes = node_result.nodes

                # 使用上下文管理器监控向量存储初始化
                with performance_context("向量存储初始化阶段"):
                    logger.info(f"start try insert to milvus: {file_id} ")
                    vector_store = get_vector_store()
                    storage_context = StorageContext.from_defaults(
                        vector_store=vector_store
                    )
                    service_context = ServiceContext.from_defaults(
                        llm=None,
                        embed_model=embed_model,
                        text_splitter=TokenTextSplitter(),
                    )

                # 使用上下文管理器监控索引构建
                with performance_context("索引构建阶段"):
                    DocumentIndexBuilder(
                        storage_context=storage_context, service_context=service_context
                    ).build(all_nodes=nodes)

                # 使用上下文管理器监控节点清理
                with performance_context("节点清理阶段"):
                    filters = MetadataFilters(
                        filters=[
                            MetadataFilter(
                                key="file_id", value=file_id, operator=FilterOperator.EQ
                            )
                        ]
                    )
                    logger.info(f"start clean nodes with filters {filters}")
                    vector_store.delete_nodes(filters=filters)

                # 将node_result转换为NodeUnit列表
                data = [
                    NodeUnit(text=node.text, metadata=node.metadata)
                    for node in node_result.nodes
                ]
                return PreviewResponse(code=0, msg="文档解析成功", data=data)

            except Exception as e:
                import traceback

                logger.error(f"文档解析失败: {str(e)}\n {traceback.format_exc()}")
                return PreviewResponse(code=1, msg=f"解析失败: {str(e)}")
            finally:
                # 清理临时文件
                if temp_file_path and os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
