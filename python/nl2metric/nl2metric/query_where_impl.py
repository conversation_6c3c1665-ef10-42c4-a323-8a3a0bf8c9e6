import copy
import jieba

from enum import Enum
from langchain_core.runnables import (
    RunnableConfig,
)
from pydantic import BaseModel
from typing import List, Set, Dict

from common.logging.logger import get_logger
from common.logging.utils import chain_ok_log
from common.types.base import CHAIN_META, ChainMeta, ChainRuntime
from common.utils.string_utils import str_is_num, remove_substrings
from config.project_config import get_project_config
from metastore.base import Metric, Dimension, DimensionValueWithMeta
from metastore.score import (
    match_metric_score,
    jieba_match_without_stopwords,
    find_unique_longest_words,
    filter_words_by_score,
)

logger = get_logger(__name__)


class SubWhereLink(Enum):
    AND = "AND"
    OR = "OR"


DEFAULT_SUB_WHERE_LINK = SubWhereLink.AND


class MetricSubWhere(BaseModel):
    metric: Metric
    operator: str
    condition: str

    # otherwise there are too many print in log
    class Config:
        json_encoders = {
            Metric: lambda v: v.name,
        }

    @property
    def name(self):
        return self.metric.name

    def valid(self) -> bool:
        return bool(self.operator) and bool(self.condition)

    def __bool__(self):
        return self.valid()

    def __str__(self):
        return str(self.sql())

    def __repr__(self):
        return str(self.sql())

    def sql(self) -> str:
        assert self.valid()
        return f"{self.name} {self.operator} {self.condition}"

    def json(self) -> Dict:
        assert self.valid()
        return {
            "metric_name": self.name,
            "operator": self.operator,
            "condition": self.condition,
        }

    def check_by_wordcount(
        self,
        config: RunnableConfig,
    ):
        question = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.QUESTION]
        threshold = get_project_config(
            config[CHAIN_META][ChainMeta.PROJECT_NAME],
            config[CHAIN_META][ChainMeta.MODEL_NAME],
        ).param_extract_confidence_threshold
        return match_metric_score(question, self.metric, threshold, config, False, True)


class OperatorMode(str, Enum):
    positive = "positive"
    negative = "negative"
    num_compare = "num_compare"
    unknown = "unknown"

    @classmethod
    def decode(cls, operator):
        if operator.upper() in {"IN", "=", "IS"}:
            return cls.positive
        elif operator.upper() in {"!=", "<>", "IS NOT", "NOT IN"}:
            return cls.negative
        elif operator.upper() in {">", ">=", "<", "<="}:
            return cls.num_compare
        else:
            return cls.unknown


class DimensionSubWhere(BaseModel):
    operator: OperatorMode
    dimension: Dimension
    dimension_values: List[DimensionValueWithMeta] = []
    illegal_dimension_values: List[str] = []
    raw_operator: str
    raw_condition: str
    include_illegal_dimension_values: bool

    # otherwise there are too many print in log
    class Config:
        json_encoders = {
            Dimension: lambda v: v.name,
        }

    def verify_and_deduplicate(self):
        keys = set()
        ret = []
        for dv in self.dimension_values:
            if dv.key in keys:
                continue
            tmp = self.dimension.search_value(dv.name, dv.is_synonym)
            if not tmp:
                continue
            keys.add(dv.key)
            ret.append(dv)
        self.dimension_values = ret

    @property
    def name(self):
        return self.dimension.name

    def valid(self) -> bool:
        if (
            self.operator == OperatorMode.positive
            or self.operator == OperatorMode.negative
        ):
            if self.include_illegal_dimension_values:
                return bool(self.dimension_values) or bool(
                    self.illegal_dimension_values
                )
            return bool(self.dimension_values)
        elif self.operator == OperatorMode.num_compare:
            return self.raw_operator and str_is_num(self.raw_condition)
        return False

    def __bool__(self):
        return self.valid()

    def __str__(self):
        return str(self.sql())

    def __repr__(self):
        return str(self.sql())

    def sql_parts(self):
        assert self.valid()
        if (
            self.operator == OperatorMode.positive
            or self.operator == OperatorMode.negative
        ):
            dimension_values_str = [f"'{dv.name}'" for dv in self.dimension_values]
            illegal_dimension_values_str = [
                f"'{dv}'" for dv in self.illegal_dimension_values
            ]
            if self.include_illegal_dimension_values:
                dimension_values_str.extend(illegal_dimension_values_str)
            if len(dimension_values_str) == 1:
                if self.operator == OperatorMode.positive:
                    return self.name, "=", dimension_values_str[0]
                else:
                    return self.name, "!=", dimension_values_str[0]
            else:
                condition = ", ".join(dimension_values_str)
                condition = "(" + condition + ")"
                if self.operator == OperatorMode.positive:
                    return self.name, "IN", condition
                else:
                    return self.name, "NOT IN", condition
        elif self.operator == OperatorMode.num_compare:
            return self.name, self.raw_operator, self.raw_condition
        else:
            raise RuntimeError(f"sql_parts does not support operator {self.operator}")

    def sql(self) -> str:
        name, opetator, condition = self.sql_parts()
        return f"{name} {opetator} {condition}"

    def json(self) -> Dict:
        assert self.valid()
        if (
            self.operator == OperatorMode.positive
            or self.operator == OperatorMode.negative
        ):
            dimension_values = []
            for dv in self.dimension_values:
                dimension_values.append(
                    {
                        "dimension_value_name": dv.name,
                        "score": dv.score,
                    }
                )

            return {
                "dimension_name": self.name,
                "operator": self.operator.value,
                "dimension_values": dimension_values,
                "illegal_dimension_values": self.illegal_dimension_values,
            }
        elif self.operator == OperatorMode.num_compare:
            return {
                "dimension_name": self.name,
                "operator": self.raw_operator,
                "condition": self.raw_condition,
            }
        else:
            raise RuntimeError(f"json does not support operator {self.operator}")

    def unfold_synonyms(self):
        result = []
        result_strs = set()
        for dv in self.dimension_values:
            if dv.is_synonym:
                if dv.synonyms:
                    for s in dv.synonyms:
                        if s not in result_strs:
                            result_strs.add(s)
                            s_dv = self.dimension.search_value(s, False)
                            if s_dv:
                                s_dv_meta = dv.model_copy()
                                s_dv_meta.dimension_value = s_dv
                                result.append(s_dv_meta)
            elif dv.name not in result_strs:
                result_strs.add(dv.name)
                result.append(dv)
        self.dimension_values = result

    def sort_dimension_values(self):
        self.dimension_values.sort(
            key=lambda x: (-x.score if x.score is not None else float("-inf"), x.name)
        )

    # for JobType.PROJECT_PARAMS_EXTRACT, here we donnot have model_id
    def llm_gives_all_dimension_values_in_prompt(self, config: RunnableConfig) -> bool:
        if (
            self.operator != OperatorMode.positive
            and self.operator != OperatorMode.negative
        ):
            return False

        if not get_project_config(
            config[CHAIN_META][ChainMeta.PROJECT_NAME], None
        ).check_in_all_dimension_value:
            return False

        if len(self.dimension_values) <= 2:
            return False

        def _get_dimension_value_from_config(
            s: str, config: RunnableConfig
        ) -> List[str]:
            for item in config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.DIMENSIONS]:
                if s == item.name:
                    return [f"'{value.name}'" for value in item.values]
            return None

        # 如果召回的IN里面的码值就是传给大模型的所有码值，那这个IN大概率是无效的。这是大模型的能力不行。
        # > 2这个条件是为了减少误报，比如我召回了三个，db里面有10个，然后我确实需要IN这三个。
        # 注意这里dimension_value_names_from_db里面存的是本场景的维度的所有码值，这个是多于召回维度的所有码值的
        # 另外，多场景召回时，召回的维度的码值是合并多场景所有同名维度的码值之后的结果，所以可能有不在本场景的码值
        # 因此，检查dimension_value_names_from_db的时候码值要从db取，但是检查无效IN的话码值要从召回结果里面取
        # 此外，检查无效IN的码值不能是dimension_value_names_from_db过滤后的码值，
        # 因为经过dimension_value_names_from_db过滤之后可能就去掉了一些码值，导致无效IN的检查过不了
        # add rule: remove where condition if llm select all dimension values from retrieved results
        # todo: remove this rule when llm is ready
        retrieved_dimension_values = _get_dimension_value_from_config(self.name, config)
        all_dimension_values = [f"'{d.name}'" for d in self.dimension_values]
        if retrieved_dimension_values != None and sorted(
            retrieved_dimension_values
        ) == sorted(all_dimension_values):
            logger.error(
                f"dimension_sub_where {self} ignored, condition of IN contains all values"
            )
            return True
        return False

    def _filter_by_wordcount_impl(
        self,
        question: str,
        config: RunnableConfig,
        as_mang_as_possible: bool,
        fuzzy_match_limit: int = 0,
    ):
        if not question:
            return []
        question_list = list(jieba.cut(question))
        # search based purely by rule, condition is no longer needed
        # use retrived dimension, not what's in db
        project_config = get_project_config(
            config[CHAIN_META][ChainMeta.PROJECT_NAME],
            config[CHAIN_META][ChainMeta.MODEL_NAME],
        )
        match_cnt_limit = min(project_config.filter_by_wordcount_limit, len(question))

        # 宝武客户那边问到‘集团’的时候默认要指向宝武集团，所以给宝武集团配了个同义词’集团‘
        # 但是‘集团’在码值中出现的概率又很高，所以要最后匹配集团
        # 所以集团要加到停用词里面，避免前面几步模糊匹配的时候匹配到集团
        all_high_risk_dimension_values = (
            self.dimension.high_risk_dimension_value_strs
            | set(project_config.high_risk_dimension_values)
        )
        all_keywords = self.dimension.high_risk_dimension_value_strs | set(
            project_config.dimension_value_keywords
        )
        if as_mang_as_possible:
            stop_words = copy.copy(project_config.dimension_value_stopwords)
            for dimension_value in self.dimension.values:
                if dimension_value.name in stop_words:
                    stop_words.remove(dimension_value.name)
        else:
            # 这里用的是dimension.high_risk_dimension_value_strs
            # 不是project_config.high_risk_dimension_values
            # 达到的效果就是，如果我配了'股份'这个同义词，那我模糊匹配的时候就不会看'股份'，fallback就能找到'股份'这个精确匹配
            # 如果我没有配'股份'这个同义词，只是在project_config.high_risk_dimension_values里面配了，那我模糊匹配的时候还是能找到带股份的公司
            stop_words = (
                self.dimension.high_risk_dimension_value_strs
                | project_config.dimension_value_stopwords
            )
            # 宝武成本场景'股份'同义词只在其它码值中出现了十几次，没有到50，所以没有算在dimension.high_risk_dimension_value_strs里面
            # 所以这里得手动把配置文件中的high_risk_dimension_value_strs加一下
            for (
                high_risk_dimension_value_name
            ) in project_config.high_risk_dimension_values:
                if self.dimension.search_value(high_risk_dimension_value_name, None):
                    stop_words.add(high_risk_dimension_value_name)

        dimension_values_keyword_match = []
        dimension_values_exact_match = []
        dimension_values_fuzzy_match = []
        dimension_values_fallback_match = []

        # topN question with orderby needs more dimension values, so no exact match
        # if not as_mang_as_possible:
        # 宝武Q：‘马钢股份和重钢集团总资产排名’
        # 不走精确匹配的话，召回了50多个特别长的码值，长的码值命中的关键词特别多，就会导致把正确的码值‘马钢股份’和‘重钢集团’顶替掉
        # 所以都要走精确匹配，就算orderby也要走

        # search exact match
        for dimension_value in self.dimension.values:
            if (dimension_value.name in question) and (
                dimension_value.name not in all_high_risk_dimension_values
            ):
                dimension_values_exact_match.append(dimension_value)
        dimension_values_exact_match = find_unique_longest_words(
            dimension_values_exact_match
        )
        if not dimension_values_exact_match:
            # search fuzzy match
            dimension_values_fuzzy_match_score = []
            for dimension_value in self.dimension.values:
                dimension_value_text = dimension_value.name
                substr = jieba_match_without_stopwords(
                    question=question,
                    keyword=dimension_value_text,
                    stop_words=stop_words,
                    question_list=question_list,
                    keyword_list=dimension_value.jieba_cut,
                )
                if len(substr) >= match_cnt_limit or len(substr) >= len(
                    dimension_value_text
                ):
                    min_length = min(len(question), len(dimension_value_text))
                    # consider word count first, then ratio
                    # use 0.5 to make sure ratio < 1
                    score = 0.5 * len(substr) / min_length + len(substr)
                    dimension_values_fuzzy_match_score.append((dimension_value, score))
            dimension_values_fuzzy_match = filter_words_by_score(
                dimension_values_fuzzy_match_score
            )

        if (not dimension_values_exact_match) and (not dimension_values_fuzzy_match):
            # search for fallback dimension values
            for fb in all_high_risk_dimension_values:
                if fb in question:
                    fb_dimension_value = self.dimension.search_value(fb, None)
                    if fb_dimension_value:
                        dimension_values_fallback_match.append(fb_dimension_value)

        def _expand_dimension_value_synonyms(dimension, dimension_values):
            result = []
            keys = set()
            for dimension_value in dimension_values:
                if dimension_value.key not in keys:
                    result.append(dimension_value)
                    keys.add(dimension_value.key)
                if dimension_value.is_synonym and dimension_value.synonyms:
                    for s in dimension_value.synonyms:
                        s_item = dimension.search_value(s, False)
                        if s_item is not None and s_item.key not in keys:
                            result.append(s_item)
                            keys.add(s_item.key)
            return result

        def _as_many_as_possible_add_exact_match(match_list):
            # '集团子公司营业利润排名'因为集团是高风险词，所以召回的时候就拿不到集团，导致ner不准
            if not as_mang_as_possible:
                return
            old_match_names = [dv.name for dv in match_list]
            for dimension_value in self.dimension.values:
                if (dimension_value.name in question) and (
                    dimension_value.name not in old_match_names
                ):
                    match_list.insert(0, dimension_value)

        if dimension_values_exact_match:
            _as_many_as_possible_add_exact_match(dimension_values_exact_match)
            dimension_values_for_keyword_match = _expand_dimension_value_synonyms(
                self.dimension, dimension_values_exact_match
            )
        elif dimension_values_fuzzy_match:
            _as_many_as_possible_add_exact_match(dimension_values_fuzzy_match)
            dimension_values_for_keyword_match = _expand_dimension_value_synonyms(
                self.dimension, dimension_values_fuzzy_match
            )
        else:
            dimension_values_for_keyword_match = _expand_dimension_value_synonyms(
                self.dimension, dimension_values_fallback_match
            )

        if (
            fuzzy_match_limit > 0
            and len(dimension_values_for_keyword_match) <= fuzzy_match_limit
        ):
            # 宝武Q：‘马钢股份和重钢集团总资产分别是多少’
            # all_keywords里面会有‘马钢’，导致‘马钢股份’比‘重钢集团’多一个关键词，所以召回阶段就不能召回‘重钢集团’
            # 后处理阶段因为是对两个ner（‘马钢股份’，‘重钢集团’）分别处理，所以不存在该问题
            dimension_values_keyword_match = dimension_values_for_keyword_match
        else:
            # search for keywords
            keywords = [k for k in all_keywords if k in question]
            if keywords and len(dimension_values_for_keyword_match) > 1:
                dimension_values_keyword_match_score = []
                # here we cannot scan dimension_values_fuzzy_match_score
                # eg. '上海化工单体的资产总计' keyword ['上海', '单体']
                # if we scan dimension_values_fuzzy_match_score, '化工' is ignored,
                # which gives hundreds of result for baowu
                for dimension_value in dimension_values_for_keyword_match:
                    # here score should consider keyword cnt
                    substr_keyword = ""
                    dimension_value_text = dimension_value.name
                    for keyword in keywords:
                        if keyword in dimension_value_text:
                            substr_keyword += keyword
                    if substr_keyword:
                        min_length = min(len(question), len(dimension_value_text))
                        # consider word count first, then ratio
                        # use 0.5 to make sure ratio < 1
                        score_keyword = 0.5 * len(substr_keyword) / min_length + len(
                            substr_keyword
                        )
                        dimension_values_keyword_match_score.append(
                            (dimension_value, score_keyword)
                        )
                dimension_values_keyword_match = filter_words_by_score(
                    dimension_values_keyword_match_score
                )

        if not fuzzy_match_limit:
            fuzzy_match_limit = project_config.dimension_value_fuzzy_match_cnt
        if dimension_values_keyword_match:
            match_kind = "keyword_match"
            dimension_values = dimension_values_keyword_match
        elif dimension_values_exact_match:
            match_kind = "exact_match"
            dimension_values = dimension_values_exact_match
        elif dimension_values_fuzzy_match:
            match_kind = "fuzzy_match"
            dimension_values = dimension_values_fuzzy_match[:fuzzy_match_limit]
        else:
            match_kind = "fallback_match"
            dimension_values = dimension_values_fallback_match

        chain_ok_log(
            logger,
            config,
            f"filter_dimension_sub_where_by_wordcount question {question}, "
            f"dimension_name {self.name}, match_kind {match_kind}, dimension_values {dimension_values}",
        )
        return dimension_values

    def check_by_wordcount(
        self,
        as_mang_as_possible: bool,
        config: RunnableConfig,
    ):
        if (
            self.operator != OperatorMode.positive
            and self.operator != OperatorMode.negative
        ):
            return True

        project_name = config[CHAIN_META][ChainMeta.PROJECT_NAME]
        model_name = config[CHAIN_META][ChainMeta.MODEL_NAME]
        dimension_where_rules = get_project_config(
            project_name, model_name
        ).dimension_where_rules
        if dimension_where_rules:
            rule_dimension_names = []
            for rule in dimension_where_rules:
                (
                    rule_dimension_name,
                    _,
                    rule_type,
                ) = rule
                assert rule_type == "word_count"
                rule_dimension_names.append(rule_dimension_name)
            if self.dimension.name not in rule_dimension_names:
                return True

        # merge new_where of multi ner is too complicatedq
        question = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.QUESTION]
        ner_questions = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.WHERE_NER]
        metric_ners = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.METRIC_NER]
        if ner_questions:
            dimension_values = []
            for ner_question in ner_questions:
                tmp_dimension_values = self._filter_by_wordcount_impl(
                    question=ner_question,
                    config=config,
                    as_mang_as_possible=as_mang_as_possible,
                )
                if tmp_dimension_values:
                    dimension_values.extend(
                        tmp_dimension_values
                    )  # self.verify_and_deduplicate() will do deduplicate
        else:
            # 最开始的方案是分别对question和metric_ner_question做_filter_by_wordcount_impl
            # 然后取差集，但是这样是不准的。_filter_by_wordcount_impl会优先取字数多的结果，
            # 所以会导致一个码值同时出现在dimension_values_question和dimension_values_metric_ner_question，
            # 导致最终结果为空
            question_without_metric_ner = remove_substrings(question, metric_ners)
            dimension_values = self._filter_by_wordcount_impl(
                question=question_without_metric_ner,
                config=config,
                as_mang_as_possible=as_mang_as_possible,
            )

        self.dimension_values = [
            DimensionValueWithMeta(dimension_value=dv) for dv in dimension_values
        ]
        self.unfold_synonyms()
        self.verify_and_deduplicate()
        return self.valid()


class Where(BaseModel):
    sub_where_link: SubWhereLink = DEFAULT_SUB_WHERE_LINK
    metric_sub_wheres: List[MetricSubWhere] = []
    dimension_sub_wheres: List[DimensionSubWhere] = []

    def valid(self):
        return len(self.metric_sub_wheres) + len(self.dimension_sub_wheres) > 0

    def __bool__(self):
        return self.valid()

    def __str__(self):
        return str(self.sql())

    def __repr__(self):
        return str(self.sql())

    def sql(self) -> str:
        if not self.valid():
            return ""

        sub_sqls = []
        for tmp in self.metric_sub_wheres:
            if tmp.valid():
                sub_sqls.append(tmp.sql())
        for tmp in self.dimension_sub_wheres:
            if tmp.valid():
                sub_sqls.append(tmp.sql())
        link = f" {self.sub_where_link.value} "
        return link.join(sub_sqls)

    def json(self) -> Dict:
        if not self.valid():
            return {}

        return {
            "sub_where_link": self.sub_where_link.value,
            "metric_sub_wheres": [m.json() for m in self.metric_sub_wheres],
            "dimension_sub_wheres": [d.json() for d in self.dimension_sub_wheres],
        }

    def filter_valid(self):
        tmp_metric = [m for m in self.metric_sub_wheres if m.valid()]
        self.metric_sub_wheres = tmp_metric
        tmp_dimension = [d for d in self.dimension_sub_wheres if d.valid()]
        self.dimension_sub_wheres = tmp_dimension

    def filter_dimension_names(self, filter_dimension_names: Set[str]):
        if not filter_dimension_names:
            return
        tmp = [
            d for d in self.dimension_sub_wheres if d.name not in filter_dimension_names
        ]
        self.dimension_sub_wheres = tmp

    # for JobType.PROJECT_PARAMS_EXTRACT, here we donnot have model_id
    def filter_dimension_sub_where_no_modelid(self, config: RunnableConfig):
        tmp = [
            d
            for d in self.dimension_sub_wheres
            if not d.llm_gives_all_dimension_values_in_prompt(config)
        ]
        self.dimension_sub_wheres = tmp

    def filter_metric_sub_where_by_wordcount(self, config: RunnableConfig):
        tmp = [m for m in self.metric_sub_wheres if m.check_by_wordcount(config)]
        self.metric_sub_wheres = tmp

    def filter_dimension_sub_where_by_wordcount(
        self, as_mang_as_possible: bool, config: RunnableConfig
    ):
        tmp = [
            d
            for d in self.dimension_sub_wheres
            if d.check_by_wordcount(as_mang_as_possible, config)
        ]
        self.dimension_sub_wheres = tmp

    def split_parts(self):
        where_by_metric_name = {}
        where_by_dimension_name = {}

        for metric_sub_where in self.metric_sub_wheres:
            where_by_metric_name[metric_sub_where.name] = (
                metric_sub_where.operator,
                metric_sub_where.condition,
                metric_sub_where.metric,
            )

        for dimension_sub_where in self.dimension_sub_wheres:
            name, operator, condition = dimension_sub_where.sql_parts()
            where_by_dimension_name[name] = (operator, condition)

        return where_by_metric_name, where_by_dimension_name, self.sub_where_link.value
