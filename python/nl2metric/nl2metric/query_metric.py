import re
from typing import List, Optional

from langchain_core.pydantic_v1 import BaseModel

from common.llm.general import create_chat_model, create_chat_model_in_chain
from common.logging.logger import get_logger
from common.logging.utils import chain_ok_log
from common.parser.custom_pydantic import CustomPydanticOutputParser
from common.prompt_selector.prompt_selector_base import PromptSelectorBase
from common.types.base import (
    ParamsExtractStage,
    CHAIN_META,
    ChainMeta,
    ChainRuntime,
    JobType,
)
from common.utils.match_utils import CandidateValue, match_labels_in_content
from common.utils.string_utils import trim_escape_char, trim_space
from config.project_config import get_project_config
from langchain_core.runnables import (
    Runnable,
    RunnableLambda,
    RunnableConfig,
)
from langchain_core.runnables.utils import Input, Output
from langchain_core.outputs import Generation

from metastore import get_metastore
from metastore.base import Metric
from metastore.score import (
    find_unique_longest_words,
    filter_words_by_score,
    jieba_match_without_stopwords,
)
from nl2intent.nl2intent import wait_for_intent
from nl2metric.few_shots import (
    BAOWU_PROJECT_NAME,
    FENGHUO_PROJECT_NAME,
)
from config.app_config import ENABLE_LLM_CONFIDENCE_CHECK

logger = get_logger(__name__)


class QueryMetricsResult(BaseModel):
    metrics: Optional[List[str]] = []
    ner: Optional[List[str]] = []
    notExistMetricNames: Optional[List[str]] = []


# invoke with metrics and question
def query_metrics(
    prompt_selector: PromptSelectorBase,
    model_type: str,
) -> Runnable[Input, Output]:
    chain = (
        RunnableLambda(
            prompt_selector.gen_prompt,
            name="PromptSelectorBase.gen_prompt:"
            + ParamsExtractStage.NL2METRIC_METRICS,
        ).bind(stage=ParamsExtractStage.NL2METRIC_METRICS)
        | RunnableLambda(create_chat_model_in_chain).bind(
            stage=ParamsExtractStage.NL2METRIC_METRICS
        )
        | RunnableLambda(save_llm_output, name="save_llm_output")
        | CustomPydanticOutputParser(pydantic_object=QueryMetricsResult)
        | RunnableLambda(query_metrics_postprocess, name="query_metrics_postprocess")
        | RunnableLambda(query_metrics_verify, name="query_metrics_verify")
    )
    chain.name = ParamsExtractStage.NL2METRIC_METRICS
    return chain


def save_llm_output(
    result: List[Generation], *, partial: bool = False, config: RunnableConfig
):
    # save for compute llm output confidence
    config[CHAIN_META][ChainMeta.RUN_TIME][
        ChainRuntime.LLM_OUTPUT_QUERY_METRIC
    ] = result

    return result


def query_metrics_postprocess(data: QueryMetricsResult, config: RunnableConfig):
    if len(data.ner) > 0:
        config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.METRIC_NER] = data.ner
    question = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.QUESTION]
    metrics = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.METRICS]
    project_name = config[CHAIN_META][ChainMeta.PROJECT_NAME]
    model_name = config[CHAIN_META][ChainMeta.MODEL_NAME]
    project_config = get_project_config(project_name, model_name)
    metastore = get_metastore(config[CHAIN_META][ChainMeta.PROJECT_ID])

    if ENABLE_LLM_CONFIDENCE_CHECK:
        # todo: enable llm confidence check
        confidence = compute_metric_confidence(config)

    result = trim_space(trim_escape_char(data.metrics)) or []

    # metric的规则必须在这，因为多场景提参要用指标来区分场景
    if project_config.do_metric_rule:
        metric_items = []
        for m_name in result:
            metric_item = metastore.safe_get_metric(m_name)
            if metric_item != None:
                metric_items.append(metric_item)
        metrics_result = filter_metrics_by_wordcount(metric_items, config)
        result = [m.name for m in metrics_result]

    return result


def query_metrics_verify(metrics, config: RunnableConfig):
    project_name = config[CHAIN_META][ChainMeta.PROJECT_NAME]
    model_name = config[CHAIN_META][ChainMeta.MODEL_NAME]
    question = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.QUESTION]
    metastore = get_metastore(config[CHAIN_META][ChainMeta.PROJECT_ID])
    fenghuo_filter_metric_labels = {
        "销售收入完成情况",
        "回款完成情况",
        "有效合同完成情况",
    }
    metrics_result = []
    for m_name in metrics:
        metric = metastore.safe_get_metric(m_name)
        if metric == None:
            continue
        if model_name and (model_name not in metric.model_names):
            continue
        # same as fenghuo_filter_metrics
        if (
            (project_name == FENGHUO_PROJECT_NAME)
            and ("情况" not in question)
            and any(label in fenghuo_filter_metric_labels for label in metric.label)
        ):
            continue
        metrics_result.append(m_name)
    return metrics_result


def compute_metric_confidence(config: RunnableConfig):
    tokens = config[CHAIN_META][ChainMeta.RUN_TIME][
        ChainRuntime.LLM_OUTPUT_QUERY_METRIC
    ].response_metadata["logprobs"]["content"]
    return sum([token["logprob"] for token in tokens]) / len(tokens)


def _merge_metrics(input):
    result_name = set()
    result = []
    for metrics in input:
        for m in metrics:
            if m.name not in result_name:
                result_name.add(m.name)
                result.append(m)
    return result


def filter_metrics_by_wordcount(
    metrics: List[Metric], config: RunnableConfig, as_many_as_possible=False
) -> List[Metric]:
    metrics_result = []
    metrics_result_list = []
    ner_questions = config[CHAIN_META][ChainMeta.RUN_TIME].get(
        ChainRuntime.METRIC_NER, []
    )
    question = config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.QUESTION]
    # add llm metrics before adding ner metrics
    metrics_result_list.append(metrics)
    if ner_questions:
        for ner_question in ner_questions:
            metrics_result_list.append(
                _filter_metrics_by_wordcount_impl(ner_question, config)
            )
    elif as_many_as_possible:
        if question:
            metrics_result_list.append(
                _filter_metrics_by_wordcount_impl(question, config)
            )
    elif metrics:
        metrics_result_list.append(_filter_metrics_by_wordcount_impl(question, config))
    # no ner and no metrics means there are really no metrics in this question

    metrics_result = _merge_metrics(metrics_result_list)

    # add retrive result
    if not metrics_result:
        project_config = get_project_config(
            config[CHAIN_META][ChainMeta.PROJECT_NAME],
            config[CHAIN_META][ChainMeta.MODEL_NAME],
        )
        is_follow_up = False
        if not as_many_as_possible:
            _, tags = wait_for_intent(True, config)
            if tags:
                is_follow_up = tags.is_follow_up
        if (not is_follow_up) and project_config.recommend_prefilter_if_no_metric > 0:
            # here ChainRuntime.RAW_METRICS is changed to llm result
            metrics = config[CHAIN_META][ChainMeta.RUN_TIME].get(
                ChainRuntime.RAW_METRICS, []
            )
            if metrics:
                metrics_result = metrics[
                    : project_config.recommend_prefilter_if_no_metric
                ]
                # follow-up question will use merge_history
                # this is added before we have tags.is_follow_up
                # if project_config.recommend_history_and_prefilter:
                #     metastore = get_metastore(config[CHAIN_META][ChainMeta.PROJECT_ID])
                #     history_params_extract_data = config[CHAIN_META][ChainMeta.HISTORY_PARAMS_EXTRACT_DATA]
                #     if (
                #         history_params_extract_data and
                #         history_params_extract_data.is_query_metric() and
                #         history_params_extract_data.query_metric and
                #         history_params_extract_data.query_metric.metricNames
                #     ):
                #         for metric_name in history_params_extract_data.query_metric.metricNames:
                #             if (metric_name not in metrics_result) and (metastore.safe_get_metric(metric_name) != None):
                #                 metrics_result.append(metric_name)
                chain_ok_log(
                    logger,
                    config,
                    f"filter_metrics_by_wordcount question {question} add "
                    f"{len(metrics_result)} metrics from retrive",
                )
    return metrics_result


def _filter_metrics_by_wordcount_impl(question, config: RunnableConfig) -> List[Metric]:
    project_id = config[CHAIN_META][ChainMeta.PROJECT_ID]
    project_name = config[CHAIN_META][ChainMeta.PROJECT_NAME]
    model_name = config[CHAIN_META][ChainMeta.MODEL_NAME]
    project_config = get_project_config(
        project_name,
        model_name,
    )
    match_cnt_limit = min(project_config.filter_by_wordcount_limit, len(question))

    metastore = get_metastore(project_id)
    metrics_label_and_synonyms = (
        metastore.list_metrics_label_and_synonyms_by_model_name(model_name)
    )

    def get_metrics_from_words(words, metrics_label_and_synonyms):
        result = []
        metric_names = set()
        for word in words:
            for m in metrics_label_and_synonyms[word]:
                if m.name not in metric_names:
                    metric_names.add(m.name)
                    result.append(m)
        return result

    exact_match_metric_words = []
    fuzzy_match_metric_words = []

    # search exact match
    raw_exact_match_metric_words = set()
    # add llm selected metrics label and synonyms, because we donnot know why llm select this
    # for m in metrics:
    #     for word in m.label_and_synonyms:
    #         raw_exact_match_metric_words.add(word)
    # scan all label_and_synonyms
    for word in metrics_label_and_synonyms.keys():
        if word in question:
            raw_exact_match_metric_words.add(word)
    exact_match_metric_words = find_unique_longest_words(
        list(raw_exact_match_metric_words)
    )

    if not exact_match_metric_words:
        # search fuzzy match
        # no need to add metrics from llm,
        # because with that exact_match_metric_words is not empty
        fuzzy_match_metric_words_score = []
        for word in metrics_label_and_synonyms.keys():
            substr = jieba_match_without_stopwords(
                question, word, project_config.metric_stopwords
            )
            if len(substr) >= match_cnt_limit or len(substr) >= len(word):
                min_length = min(len(question), len(word))
                # consider word count first, then ratio
                # use 0.5 to make sure ratio < 1
                score = 0.5 * len(substr) / min_length + len(substr)
                fuzzy_match_metric_words_score.append((word, score))
        fuzzy_match_metric_words = filter_words_by_score(fuzzy_match_metric_words_score)

    chain_ok_log(
        logger,
        config,
        f"filter_metrics_by_wordcount question {question} "
        f"exact_match_metric_words {exact_match_metric_words} "
        f"fuzzy_match_metric_words {fuzzy_match_metric_words}",
    )
    if exact_match_metric_words:
        return get_metrics_from_words(
            exact_match_metric_words, metrics_label_and_synonyms
        )
    else:
        return get_metrics_from_words(
            fuzzy_match_metric_words, metrics_label_and_synonyms
        )
