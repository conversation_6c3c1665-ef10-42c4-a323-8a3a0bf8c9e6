from typing import List, Optional

from llama_index.core import QueryBundle
from llama_index.core.indices.postprocessor import BaseNodePostprocessor
from llama_index.core.schema import MetadataMode, NodeWithScore
import numpy as np

from common.llm.embedding import get_embedding_model


class EmbeddingRerankPostprocessor(BaseNodePostprocessor):
    def _postprocess_nodes(
        self, nodes: List[NodeWithScore], query_bundle: Optional[QueryBundle] = None
    ) -> List[NodeWithScore]:
        if not nodes or len(nodes) <= 1:
            return nodes
        node_embeddings = np.array([node.embedding for node in nodes], dtype=np.float32)
        query_embedding = np.array(query_bundle.embedding, dtype=np.float32).reshape(
            1, -1
        )
        similarities = (
            np.dot(query_embedding, node_embeddings.T)
            / (
                np.linalg.norm(query_embedding, axis=1)[:, np.newaxis]
                * np.linalg.norm(node_embeddings, axis=1)
            )
        ).squeeze()
        # this will fail if len(nodes) == 1
        node_with_similarity = list(zip(nodes, similarities))
        node_with_similarity.sort(key=lambda x: x[1], reverse=True)
        return [x[0] for x in node_with_similarity]
