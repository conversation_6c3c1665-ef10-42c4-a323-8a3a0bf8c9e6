from typing import Any, Dict, List, Optional

from llama_index.core import QueryBundle, Settings
from llama_index.core.indices.base_retriever import BaseRetriever
from llama_index.core.schema import NodeWithScore
from llama_index.core.vector_stores.types import VectorStoreQuery, MetadataFilters

from common.logging.logger import get_logger
from config import app_config
from pre_filter.index.cascade_index.base import CascadeIndex

logger = get_logger(__name__)


class CascadeIndexEmbeddingRetriever(BaseRetriever):
    def __init__(
        self,
        index: CascadeIndex,
        similarity_top_k: int = 1,
        relative_score_threshold: float = 0.1,
        node_ids: Optional[List[str]] = None,
        name: str = "",
        **kwargs: Any
    ) -> None:
        """Init params."""
        self._name = name
        self._node_ids = node_ids
        self._index = index
        self._vector_store = self._index._vector_store
        self._docstore = self._index.docstore
        self._index_struct = self._index.index_struct

        self._similarity_top_k = similarity_top_k
        self._relative_score_threshold = relative_score_threshold

        # TODO(bhx)
        # if app_config.VECTOR_STORE_TYPE:
        #     self.custom_name = f'CascadeIndexEmbeddingRetriever_{app_config.VECTOR_STORE_TYPE}'
        # else:
        #     self.custom_name = 'CascadeIndexEmbeddingRetriever'

    def _retrieve_once(
        self, query_bundle, top_k: int, filters: Optional[MetadataFilters]
    ) -> List[NodeWithScore]:
        query = VectorStoreQuery(
            query_embedding=query_bundle.embedding,
            similarity_top_k=top_k,
            node_ids=self._node_ids,
            filters=filters,
        )
        query_result = self._vector_store.query(query)

        top_k_ids: List[str]
        if query_result.ids is not None:
            top_k_ids = query_result.ids
        elif query_result.nodes is not None:
            top_k_ids = [n.node_id for n in query_result.nodes]
        else:
            raise ValueError(
                "Vector store query result should return "
                "at least one of nodes or ids."
            )
        if self._index._new_ids_map:
            top_k_ids = [self._index._new_ids_map[node_id] for node_id in top_k_ids]

        top_node_ids: Dict[str, float] = {}
        for node_id, score in zip(top_k_ids, query_result.similarities or []):
            if node_id in self._index_struct.ref_node_id_to_node_ids:
                node_ids = list(self._index_struct.ref_node_id_to_node_ids[node_id])
                for node_id in node_ids:
                    top_node_ids[node_id] = max(top_node_ids.get(node_id, 0), score)
            else:
                assert node_id in self._index_struct.source_ids
                top_node_ids[node_id] = max(top_node_ids.get(node_id, 0), score)
        results: List[NodeWithScore] = []
        for node_id, score in top_node_ids.items():
            node = self._docstore.get_node(node_id)
            results.append(NodeWithScore(node=node, score=score))
        results.sort(key=lambda n: n.get_score(), reverse=True)
        return results

    def _retrieve(
        self,
        query_bundle: QueryBundle,
    ) -> List[NodeWithScore]:
        """Retrieve nodes."""
        if self._vector_store.is_embedding_query:
            if query_bundle.embedding is None:
                query_bundle.embedding = Settings.embed_model.get_text_embedding(
                    query_bundle.query_str
                )
        filters = getattr(query_bundle, "filters", None)
        if self._relative_score_threshold == 0:
            return self._retrieve_once(query_bundle, self._similarity_top_k, filters)

        last_retrieve_count = 0
        for scale in range(1, 30):
            nodes = self._retrieve_once(
                query_bundle, self._similarity_top_k * (2**scale), filters
            )
            if len(nodes) == 0:
                return []
            score_threshold = nodes[0].get_score() - self._relative_score_threshold
            if nodes[-1].get_score() < score_threshold:
                over_scores = [n for n in nodes if n.get_score() > score_threshold]
                if len(over_scores) < self._similarity_top_k:
                    return nodes[: self._similarity_top_k]
                else:
                    return over_scores
            if last_retrieve_count == len(nodes):
                return nodes
            last_retrieve_count = len(nodes)
        return []
