import json
import re
from typing import List

from llama_index.core.data_structs.registry import (
    INDEX_STRUCT_TYPE_TO_INDEX_STRUCT_CLASS,
)
from llama_index.core.indices.registry import INDEX_STRUCT_TYPE_TO_INDEX_CLASS
from llama_index.core.schema import BaseNode, TextNode

from common.utils.string_utils import split_words
from pre_filter.index.cascade_index.base import CascadeIndex
from pre_filter.index.cascade_index.index_struct import (
    DIMENSION_VALUE_INDEX,
    SYNONYMS_INDEX,
    IndexSynonymsCascade,
)
from pre_filter.index.const import EXTRA_KEYS_KEY


class SynonymsCascadeIndex(CascadeIndex):
    index_struct_cls = IndexSynonymsCascade

    def extract_nodes(self, node: BaseNode) -> List[BaseNode]:
        extra_keys_str = node.metadata.get(EXTRA_KEYS_KEY)
        if not extra_keys_str:
            return []
        ret: List[BaseNode] = []
        for key in json.loads(extra_keys_str):
            ret.append(TextNode(text=key))
        return ret


INDEX_STRUCT_TYPE_TO_INDEX_CLASS[SYNONYMS_INDEX] = SynonymsCascadeIndex
INDEX_STRUCT_TYPE_TO_INDEX_STRUCT_CLASS[DIMENSION_VALUE_INDEX] = IndexSynonymsCascade
