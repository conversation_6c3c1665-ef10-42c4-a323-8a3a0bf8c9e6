import jieba
import jieba.analyse
import math
import numpy as np
from collections import defaultdict
from common.logging.logger import get_logger
from common.jieba.base import modify_jieba_freq_dianxin
from common.stopwords.base import dipeak_stopwords_path, dipeak_single_stopwords
from llama_index.core.base.base_retriever import BaseRetriever
from llama_index.core.indices.keyword_table.base import BaseKeywordTableIndex
from llama_index.core.indices.keyword_table.retrievers import BaseKeywordTableRetriever
from llama_index.core.schema import NodeWithScore, QueryBundle

from typing import Set, List, Dict

from pre_filter.index.const import DIMENSION_NAME_MODEL_KEY

modify_jieba_freq_dianxin()
logger = get_logger(__name__)

# TODO(bhx): use project_config.stopwords
jieba.analyse.set_stop_words(str(dipeak_stopwords_path))
# use dipeak_single_stopwords here because l<PERSON><PERSON> needs keywords like '其他'


def extend_for_search(keywords) -> List[str]:
    # copied from jieba.cut_for_search
    # eg. get 海南 from 海南省
    result = []
    seen = set()
    for w in keywords:
        if len(w) > 2:
            for i in range(len(w) - 1):
                gram2 = w[i : i + 2]
                if (
                    jieba.get_FREQ(gram2)
                    and (gram2 not in dipeak_single_stopwords)
                    and (gram2 not in seen)
                ):
                    result.append(gram2)
                    seen.add(gram2)
        if len(w) > 3:
            for i in range(len(w) - 2):
                gram3 = w[i : i + 3]
                if (
                    jieba.get_FREQ(gram3)
                    and (gram3 not in dipeak_single_stopwords)
                    and (gram3 not in seen)
                ):
                    result.append(gram3)
                    seen.add(gram3)
        if w not in seen:
            result.append(w)
            seen.add(w)
    return result


def can_be_formed(s, word_set):
    # check if s can be formed by any combination of words in word_set
    if not word_set:
        return False
    lengths = {len(word) for word in word_set}
    n = len(s)
    dp = [False] * (n + 1)
    dp[0] = True
    for i in range(1, n + 1):
        for l in lengths:
            if l > i:
                continue
            if dp[i - l] and s[i - l : i] in word_set:
                dp[i] = True
                break
    return dp[n]


def narrow_words(words):
    # remove all words that meet the condition of can_be_formed
    word_set = set(words)
    result = []
    for word in words:
        temp_set = word_set - {word}
        if not can_be_formed(word, temp_set):
            result.append(word)
    return result


def remove_substrings(words):
    from collections import defaultdict

    length_to_words = defaultdict(list)
    for word in words:
        length_to_words[len(word)].append(word)

    result = []
    for word in words:
        current_len = len(word)
        longer_words = []
        for l in length_to_words:
            if l > current_len:
                longer_words.extend(length_to_words[l])
        is_substring = any(word in longer_word for longer_word in longer_words)
        if not is_substring:
            result.append(word)
    return result


def jieba_extract_keyword(text: str, max_keywords) -> List[str]:
    # keywords = jieba.analyse.extract_tags(text, topK=max_keywords)
    keywords = jieba.lcut_for_search(text, HMM=False)
    keywords = [k for k in keywords if k not in dipeak_single_stopwords]
    keywords = extend_for_search(keywords)
    return narrow_words(keywords)


class BaseKeywordTableJiebaRetriever(BaseKeywordTableRetriever):
    def _get_keywords(self, query_str: str) -> List[str]:
        """Extract keywords."""
        result = jieba_extract_keyword(
            query_str, max_keywords=self.max_keywords_per_query
        )
        logger.info(f"jieba_extract_keyword query_str {query_str} result {result}")
        return result

    # copied from BaseKeywordTableRetriever::_retrieve
    def _retrieve(
        self,
        query_bundle: QueryBundle,
    ) -> List[NodeWithScore]:
        """Get nodes for response."""
        logger.info(f"> Starting query: {query_bundle.query_str}")
        keywords = self._get_keywords(query_bundle.query_str)
        logger.info(f"query keywords: {keywords}")

        # go through text chunks in order of most matching keywords
        chunk_indices_count: Dict[str, int] = defaultdict(int)
        keywords = [k for k in keywords if k in self._index_struct.keywords]
        keywords = remove_substrings(keywords)
        logger.info(f"> Extracted keywords: {keywords}")
        for k in keywords:
            for node_id in self._index_struct.table[k]:
                # chunk_indices_count[node_id] += 1
                # chunk_indices_count[node_id] += len(k)/len(node_id)
                chunk_indices_count[node_id] += len(k) / (
                    math.log(len(node_id) + 1) / math.log(1.5)
                )
                # chunk_indices_count[node_id] += len(k) / len(node_id)**0.6
                # chunk_indices_count[node_id] += len(k) / (1 / (1 + np.exp(-0.5 * len(node_id))))
        sorted_chunk_indices = sorted(
            chunk_indices_count.keys(),
            # 这里chunk_indices_count[x]表示关键词的个数，
            # 原代码中只按照关键词个数排序，如果个数相同，排序结果就是随机的
            # 所以这里增加了按照nodeid排序，并且把nodeid直接设置成码值
            key=lambda x: (chunk_indices_count[x], x),
            reverse=True,
        )
        sorted_chunk_indices = sorted_chunk_indices[: self.num_chunks_per_query]
        sorted_nodes = self._docstore.get_nodes(sorted_chunk_indices)
        ret = [
            NodeWithScore(node=node, score=chunk_indices_count[node.node_id])
            for node in sorted_nodes
        ]
        return ret


class JiebaKeywordTableIndex(BaseKeywordTableIndex):
    def _extract_keywords(self, text: str) -> Set[str]:
        """Extract keywords from text."""
        return set(
            jieba_extract_keyword(text, max_keywords=self.max_keywords_per_chunk)
        )

    def as_retriever(
        self,
        **kwargs,
    ) -> BaseRetriever:
        return KeywordTableJiebaRetriever(self, object_map=self._object_map, **kwargs)


class KeywordTableJiebaShortRetriever(BaseKeywordTableJiebaRetriever):
    pass


# JiebaKeywordTableIndex tend to select long dimension value
# "金融行业事业部" and "金融行业" cannot do exact match
class JiebaShortKeywordTableIndex(BaseKeywordTableIndex):
    def __init__(self, threshold, **kwargs):
        self._threshold = threshold
        super().__init__(**kwargs)

    def _extract_keywords(self, text: str) -> Set[str]:
        if len(text) <= self._threshold:
            return set(
                jieba_extract_keyword(text, max_keywords=self.max_keywords_per_chunk)
            )
        else:
            return set()

    def as_retriever(
        self,
        **kwargs,
    ) -> BaseRetriever:
        return KeywordTableJiebaShortRetriever(
            self, object_map=self._object_map, **kwargs
        )


class KeywordTableJiebaExactRetriever(BaseKeywordTableJiebaRetriever):
    pass


# JiebaKeywordTableIndex tend to select long dimension value
class JiebaExactKeywordTableIndex(BaseKeywordTableIndex):
    def __init__(self, threshold, **kwargs):
        self._threshold = threshold
        super().__init__(**kwargs)

    def _extract_keywords(self, text: str) -> Set[str]:
        """Extract keywords from text."""
        if self._threshold > 0:
            if len(text) <= self._threshold:
                return {text}
            else:
                return set()
        else:
            return {text}

    def as_retriever(
        self,
        **kwargs,
    ) -> BaseRetriever:
        return KeywordTableJiebaExactRetriever(
            self, object_map=self._object_map, **kwargs
        )


class KeywordTableJiebaRetriever(BaseKeywordTableJiebaRetriever):
    def __init__(self, index, object_map, num_nodes_per_dimension, **kwargs):
        self._num_nodes_per_dimension = num_nodes_per_dimension
        super().__init__(index=index, object_map=object_map, **kwargs)

    def _retrieve(
        self,
        query_bundle: QueryBundle,
    ) -> List[NodeWithScore]:
        ret = super()._retrieve(query_bundle)
        if self._num_nodes_per_dimension > 0:
            filtered_ret = []
            dimension_counter = defaultdict(int)
            for node_with_score in ret:
                for dimension, model_name in node_with_score.node.metadata[
                    DIMENSION_NAME_MODEL_KEY
                ]:
                    if dimension_counter[dimension] < self._num_nodes_per_dimension:
                        dimension_counter[dimension] += 1
                        filtered_ret.append(node_with_score)
            ret = filtered_ret
        return ret
