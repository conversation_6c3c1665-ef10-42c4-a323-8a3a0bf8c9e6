import faiss
import time
import os

from pathlib import Path
from typing import Sequence
from llama_index.core.storage.docstore.simple_docstore import (
    SimpleDocumentStore,
)
from llama_index.core.storage.index_store.simple_index_store import (
    SimpleIndexStore,
)
from llama_index.core import Settings, StorageContext, VectorStoreIndex
from llama_index.core.schema import BaseNode
from llama_index.vector_stores.faiss import FaissVectorStore
from llama_index.core.indices.loading import load_index_from_storage
from llama_index.core.service_context import ServiceContext
from llama_index.core.node_parser import SimpleNodeParser

from common.llm.embedding import get_embedding_model
from common.llm.llama_llm import get_llm
from common.logging.logger import get_logger
from config import app_config

logger = get_logger(__name__)


embed_model, tokenizer, dimension_num = get_embedding_model()

Settings.llm = get_llm(app_config.LLM_BASE_MODEL)
Settings.node_parser = SimpleNodeParser.from_defaults()
Settings.embed_model = embed_model


def build_or_load_simple_faiss_index(nodes: Sequence[BaseNode], persist_dir: str):
    try:
        storage_context = StorageContext.from_defaults(
            docstore=SimpleDocumentStore.from_persist_dir(persist_dir),
            index_store=SimpleIndexStore.from_persist_dir(persist_dir),
            vector_store=FaissVectorStore.from_persist_dir(persist_dir),
        )
        index = load_index_from_storage(storage_context)
        logger.info(
            f"load faiss index for {len(nodes)} nodes from {persist_dir} succeed"
        )
        return index
    except Exception as e:
        index, storage_context = build_simple_faiss_index(nodes)
        os.makedirs(persist_dir, exist_ok=True)
        storage_context.persist(persist_dir=persist_dir)
        return index


def build_simple_faiss_index(nodes: Sequence[BaseNode]):
    start_time = time.time()
    logger.info(f"start building new faiss index for {len(nodes)} nodes")
    storage_context = StorageContext.from_defaults(
        docstore=SimpleDocumentStore(),
        index_store=SimpleIndexStore(),
        vector_store=FaissVectorStore(faiss_index=faiss.IndexFlatL2(dimension_num)),
    )

    # construct index
    index = VectorStoreIndex(
        nodes=nodes,
        storage_context=storage_context,
    )
    execution_time = time.time() - start_time
    logger.info(
        f"build faiss index complete for {len(nodes)} nodes, execution_time {execution_time} secs"
    )
    return index, storage_context
