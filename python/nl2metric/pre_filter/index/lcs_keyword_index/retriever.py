from typing import Any, List, Optional

from llama_index.core.indices.base_retriever import BaseRetriever
from llama_index.core.indices.query.schema import QueryBundle
from llama_index.core.schema import NodeWithScore
from llama_index.core.vector_stores.simple import _build_metadata_filter_fn

from common.logging.logger import get_logger
from common.utils.match_utils import CandidateValue, match_labels_in_content
from common.utils.string_utils import find_longest_discontinuous_substring
from metastore.score import match_common_str_without_stopwords
from pre_filter.index.lcs_keyword_index.base import LcsKeywordIndex

logger = get_logger(__name__)


class LcsKeywordMatchRetriever(BaseRetriever):
    def __init__(
        self,
        index: LcsKeywordIndex,
        match_ratio,
        top_k: int = 10,
        **kwargs: Any,
    ) -> None:
        self._index = index
        self._index_struct = index.index_struct
        self._docstore = index.docstore
        self.top_k = top_k
        self._match_ratio = float(match_ratio)

    def _retrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
        query_str = query_bundle.query_str
        if not query_str:
            return []
        all_keywords = getattr(query_bundle, "limited_keywords", None)
        if not all_keywords:
            all_keywords = [k for k in self._index_struct.keywords]
        keywords = []
        for keyword in all_keywords:
            score = self._match_score(query=query_str, keyword=keyword)
            if score > 0:
                keywords.append((keyword, score))
        top_node_ids = set()
        keywords.sort(key=lambda item: (item[1], item[0]), reverse=True)
        filters = getattr(query_bundle, "filters", None)
        sorted_nodes = []
        for k, score in keywords:
            for node_id in self._index_struct.table[k]:
                if node_id in top_node_ids:
                    continue
                top_node_ids.add(node_id)
                node = self._docstore.get_node(node_id)
                if filters != None:
                    query_filter_fn = _build_metadata_filter_fn(
                        lambda node_id: node.metadata, filters
                    )
                    if not query_filter_fn(node_id):
                        continue
                sorted_nodes.append(node)
            if len(top_node_ids) >= self.top_k:
                break
        result = [NodeWithScore(node=node) for node in sorted_nodes]
        # sort this so that every retrieve gives the same answer
        # otherwise llm ans will change after index rebuild
        # TODO(bhx): fix keywords.sort by node.text, then this sorted is uncessary
        return sorted(result, key=lambda item: item.text)

    def _match_score(self, query: str, keyword: str) -> float:
        substr = find_longest_discontinuous_substring(keyword, query)
        min_length = min(len(query), len(keyword))
        match_score = float(len(substr)) / min_length
        if match_score >= self._match_ratio:
            return match_score
        return 0


# this retriver is not accurate
class LcsKeywordCntMatchRetriever(BaseRetriever):
    def __init__(
        self,
        index: LcsKeywordIndex,
        match_cnt: int,
        top_k: int = 10,
        **kwargs: Any,
    ) -> None:
        self._index = index
        self._index_struct = index.index_struct
        self._docstore = index.docstore
        self.top_k = top_k
        self._match_cnt = match_cnt
        assert match_cnt > 0

    def _retrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
        query_str = query_bundle.query_str
        if not query_str:
            return []
        all_keywords = getattr(query_bundle, "limited_keywords", None)
        if not all_keywords:
            all_keywords = [k for k in self._index_struct.keywords]
        keywords = []
        match_cnt_limit = min(self._match_cnt, len(query_str))
        for keyword in all_keywords:
            substr = match_common_str_without_stopwords(query_str, keyword)
            if len(substr) >= match_cnt_limit or len(substr) >= len(keyword):
                min_length = min(len(query_str), len(keyword))
                score = float(len(substr)) / min_length
                keywords.append((keyword, score))
        top_node_ids = set()
        filters = getattr(query_bundle, "filters", None)
        sorted_nodes = []
        for k, score in keywords:
            for node_id in self._index_struct.table[k]:
                if node_id in top_node_ids:
                    continue
                top_node_ids.add(node_id)
                node = self._docstore.get_node(node_id)
                if filters != None:
                    query_filter_fn = _build_metadata_filter_fn(
                        lambda node_id: node.metadata, filters
                    )
                    if not query_filter_fn(node_id):
                        continue
                sorted_nodes.append((node, score))
            if len(top_node_ids) >= self.top_k:
                break
        sorted_nodes.sort(key=lambda item: (item[1], item[0].text), reverse=True)
        return [NodeWithScore(node=x[0]) for x in sorted_nodes]


class NoOverlapKeywordMatchRetriever(BaseRetriever):
    def __init__(
        self,
        index: LcsKeywordIndex,
        top_k: int = 10,
        **kwargs: Any,
    ) -> None:
        self._index = index
        self._index_struct = index.index_struct
        self._docstore = index.docstore
        self.top_k = top_k

    def _retrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
        query_str = query_bundle.query_str
        if not query_str:
            return []
        all_keywords = getattr(query_bundle, "limited_keywords", None)
        if not all_keywords:
            all_keywords = [k for k in self._index_struct.keywords]
        all_keywords.sort()
        keywords = match_labels_in_content(
            query_str,
            [CandidateValue(key=keyword, values=[keyword]) for keyword in all_keywords],
        )
        top_node_ids = set()
        filters = getattr(query_bundle, "filters", None)
        sorted_nodes = []
        for k in keywords:
            for node_id in self._index_struct.table[k]:
                if node_id in top_node_ids:
                    continue
                top_node_ids.add(node_id)
                node = self._docstore.get_node(node_id)
                if filters != None:
                    query_filter_fn = _build_metadata_filter_fn(
                        lambda node_id: node.metadata, filters
                    )
                    if not query_filter_fn(node_id):
                        continue
                sorted_nodes.append(node)
            if len(top_node_ids) >= self.top_k:
                break
        result = [NodeWithScore(node=node) for node in sorted_nodes]
        # sort this so that every retrieve gives the same answer
        # otherwise llm ans will change after index rebuild
        return sorted(result, key=lambda item: item.text)
