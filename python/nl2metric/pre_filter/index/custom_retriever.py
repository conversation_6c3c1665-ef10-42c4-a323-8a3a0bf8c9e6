from typing import List, Any

from llama_index.core.indices.base_retriever import BaseRetriever
from llama_index.core.indices.query.schema import QueryBundle
from llama_index.core.schema import NodeWithScore

from common.logging.logger import get_logger

logger = get_logger(__name__)


class CustomRetriever(BaseRetriever):
    def __init__(
        self,
        **kwargs: Any,
    ) -> None:
        return

    def _retrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
        query_str = query_bundle.query_str
        result: List[NodeWithScore] = []

        # Add retrieve code here

        return result
