from typing import List, Optional, Any

from langchain_core.retrievers import BaseR<PERSON>riever
from llama_index.core import VectorStoreIndex, get_response_synthesizer
from llama_index.core.indices.keyword_table.base import KeywordTableRetrieverMode
from llama_index.core.indices.postprocessor import LLMRerank
from llama_index.core.indices.postprocessor import BaseNodePostprocessor
from llama_index.core.query_engine import RetrieverQueryEngine
from llama_index.core.schema import BaseNode

from common.db_model.model import get_semantic_project_by_id
from common.llm.embedding import get_embedding_model
from config import app_config
from config.project_config import get_project_config
from metastore import get_metastore
from nl2document.common.base.trace import RetrieverTracer
from pre_filter.index.multi_retriever import (
    MultiRetriever,
    metric_merge_multi_retrievers_result,
    dimension_merge_multi_retrievers_result,
    dimension_value_merge_multi_retrievers_result,
)
from pre_filter.index.post_processors.time_filter import Time<PERSON>ilterPostProcessor
from pre_filter.rerank import Embedding<PERSON>erankPostprocessor
from pydantic import BaseModel

# embed_model, tokenizer = get_embedding_model()


class SchemaIndexList(BaseModel):
    metric_vector_index: Optional[Any] = None
    metric_keyword_index: Optional[Any] = None
    dimension_vector_index: Optional[Any] = None
    dimension_lcs_index: Optional[Any] = None

    dimension_value_lcs_index: Optional[Any] = None
    dimension_value_jieba_index: Optional[Any] = None
    dimension_value_jieba_exact_index: Optional[Any] = None
    dimension_value_jieba_short_index: Optional[Any] = None

    def is_dimension_value_index_jieba(self):
        return (
            self.dimension_value_jieba_index != None
            and self.dimension_value_jieba_exact_index != None
            and self.dimension_value_jieba_short_index != None
        )


class SchemaIndex:
    def __init__(
        self,
        project_id,
        indexs: SchemaIndexList,
        dimension_value_nodes,
    ) -> None:
        self._meta_store = get_metastore(project_id)
        self._project_name = get_semantic_project_by_id(project_id).name
        self.indexs = indexs
        self.dimension_value_nodes: List[BaseNode] = dimension_value_nodes
        self.metric_retrievers: List[BaseRetriever] = []
        self.dimension_retrievers: List[BaseRetriever] = []
        self.dimension_value_retrievers: List[BaseRetriever] = []

    def add_metric_retriever(self, retriever: BaseRetriever) -> None:
        self.metric_retrievers.append(retriever)

    def add_dimension_retriever(self, retriever: BaseRetriever) -> None:
        self.dimension_retrievers.append(retriever)

    def add_dimension_value_retriever(self, retriever: BaseRetriever) -> None:
        self.dimension_value_retrievers.append(retriever)

    def create_metric_retriever(
        self, top_k=10, relative_score_threshold=0.1
    ) -> RetrieverQueryEngine:
        created_retrievers = []
        if self.indexs.metric_vector_index is not None:
            metric_index_retriever = self.indexs.metric_vector_index.as_retriever(
                similarity_top_k=top_k,
                relative_score_threshold=relative_score_threshold,
            )
            created_retrievers.append(metric_index_retriever)
        if self.indexs.metric_keyword_index is not None:
            # put keyword_retriever first so that its result will be front
            metric_keyword_retriever = self.indexs.metric_keyword_index.as_retriever(
                top_k=top_k
            )
            created_retrievers.append(metric_keyword_retriever)

        multi_retriever = MultiRetriever(
            retrievers=self.metric_retrievers + created_retrievers,
            name="metric",
        )
        postprocessors: List[BaseNodePostprocessor] = [EmbeddingRerankPostprocessor()]
        query_engine = RetrieverQueryEngine.from_args(
            retriever=multi_retriever,
            node_postprocessors=postprocessors,
            response_synthesizer=get_response_synthesizer(),
        )
        return query_engine

    def create_dimension_retriever(
        self, top_k=10, relative_score_threshold=0.1
    ) -> RetrieverQueryEngine:
        created_retrievers = []
        if self.indexs.dimension_vector_index is not None:
            dimension_index_retriever = self.indexs.dimension_vector_index.as_retriever(
                similarity_top_k=top_k,
                relative_score_threshold=relative_score_threshold,
            )
            created_retrievers.append(dimension_index_retriever)
        # put keyword_retriever first so that its result will be front
        if self.indexs.dimension_lcs_index is not None:
            dimension_lcs_retriever = self.indexs.dimension_lcs_index.as_retriever(
                top_k=top_k, match_ratio=3 / 4
            )
            created_retrievers.append(dimension_lcs_retriever)

        multi_retriever = MultiRetriever(
            retrievers=self.dimension_retrievers + created_retrievers,
            name="dimension",
        )
        postprocessors: List[BaseNodePostprocessor] = [EmbeddingRerankPostprocessor()]
        if app_config.ENABLE_TIME_FILTER:
            postprocessors.append(TimeFilterPostProcessor(self._meta_store))
        query_engine = RetrieverQueryEngine.from_args(
            retriever=multi_retriever,
            node_postprocessors=postprocessors,
            response_synthesizer=get_response_synthesizer(),
        )
        return query_engine

    def create_dimension_value_retriever(self, lcs_match_ratio, top_k=10):
        if self.indexs.dimension_value_lcs_index != None:
            return self._create_dimension_value_lcs_retriever(lcs_match_ratio, top_k)
        elif self.indexs.is_dimension_value_index_jieba():
            return self._create_dimension_value_jieba_retriever(top_k)
        else:
            return MultiRetriever(
                retrievers=None,
                name="dimension_value_non",
            )

    def _create_dimension_value_lcs_retriever(self, lcs_match_ratio, top_k):
        created_retrievers = []
        dimension_value_no_overlap_lcs_retriever = (
            self.indexs.dimension_value_lcs_index.as_retriever(
                top_k=top_k, no_overlap=True
            )
        )
        created_retrievers.append(dimension_value_no_overlap_lcs_retriever)
        dimension_value_lcs_retriever = (
            self.indexs.dimension_value_lcs_index.as_retriever(
                top_k=top_k,
                match_ratio=lcs_match_ratio,
            )
        )
        created_retrievers.append(dimension_value_lcs_retriever)
        multi_retriever = MultiRetriever(
            retrievers=self.dimension_value_retrievers + created_retrievers,
            name="dimension_value",
            func=dimension_value_merge_multi_retrievers_result,
            dimension_value_nodes=self.dimension_value_nodes,
            project_name=self._project_name,
        )
        # postprocessors: List[BaseNodePostprocessor] = [EmbeddingRerankPostprocessor()]
        query_engine = RetrieverQueryEngine.from_args(
            retriever=multi_retriever,
            # node_postprocessors=postprocessors,
            response_synthesizer=get_response_synthesizer(),
        )
        return query_engine

    def _create_dimension_value_jieba_retriever(self, top_k):
        p_config = get_project_config(self._project_name, None)
        return MultiRetriever(
            # dimension_value_jieba_exact_retriever should be front,
            # dimension_value_jieba_retriever tend to get long dimension value
            retrievers=[
                self.indexs.dimension_value_jieba_exact_index.as_retriever(
                    num_chunks_per_query=p_config.dimension_value_jieba_exact_topk
                ),
                self.indexs.dimension_value_jieba_short_index.as_retriever(
                    num_chunks_per_query=p_config.dimension_value_jieba_short_topk
                ),
                self.indexs.dimension_value_jieba_index.as_retriever(
                    num_chunks_per_query=p_config.dimension_value_jieba_topk,
                    num_nodes_per_dimension=p_config.dimension_value_jieba_topk_per_dimension,
                ),
            ],
            name="dimension_value",
            func=dimension_value_merge_multi_retrievers_result,
            dimension_value_nodes=self.dimension_value_nodes,
            project_name=self._project_name,
        )
