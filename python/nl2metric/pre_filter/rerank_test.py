from llama_index import QueryBundle
from llama_index.schema import TextNode

from pre_filter.rerank import EmbeddingRerankPostprocessor


def test_embedding_rerank():
    p = EmbeddingRerankPostprocessor()
    query = "aaaa"
    node1 = TextNode(text="aaaa")
    node2 = TextNode(text="aaab")
    node3 = TextNode(text="aabb")
    nodes = [node2, node3, node1]
    print(p.postprocess_nodes(nodes=nodes, query_str=query))
