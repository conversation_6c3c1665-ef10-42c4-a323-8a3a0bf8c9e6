from common.llm.embedding import get_embedding_model
from config import app_config
from typing import Dict

from llama_index.core import Settings, StorageContext
from llama_index.core.node_parser import SimpleNodeParser
from llama_index.core.schema import TextNode

from common.llm.llama_llm import get_llm
from metastore.meeting.base import Meeting, Personnel
from pre_filter.index.cascade_index.synonyms import SynonymsCascadeIndex
from pre_filter.index.const import (
    EXCLUDE_METADATA_KEYS,
    EXTRA_KEYS_KEY,
    NODE_TYPE_KEY,
    MEETING_ID_KEY,
    MEETING_PERSONNEL_KEY,
    PERSONNEL_NAME_KEY,
    NodeType,
)
from pre_filter.meeting_index.index import MeetingSchemaIndex
from pre_filter.index.lcs_keyword_index.base import LcsKeywordIndex

Settings.llm = get_llm(app_config.LLM_BASE_MODEL)
Settings.node_parser = SimpleNodeParser.from_defaults()
Settings.embed_model, _, dim = get_embedding_model()


class MeetingIndexBuilder:
    # build index by user may leads to massive indexs
    def __init__(self) -> None:
        self._meetings: Dict[str, Meeting] = {}
        self._personnels: Dict[str, Personnel] = {}
        self._storage_context = StorageContext.from_defaults()

    def with_meetings(self, meetings: Dict[str, Meeting]):
        self._meetings.update(meetings)

    def with_personnels(self, personnels: Dict[str, Personnel]):
        self._personnels.update(personnels)

    def build(self) -> MeetingSchemaIndex:
        # TODO(bhx): same topic meetings can be quite common
        # but we need to filter by MEETING_PERSONNEL_KEY
        # so merge same topic meeting can be quite difficult
        meeting_topic_nodes = []
        for meeting in self._meetings.values():
            if not meeting.topic:
                continue
            for topic in meeting.topic:
                if not topic:
                    continue
                node = TextNode(
                    text=topic,
                    excluded_embed_metadata_keys=EXCLUDE_METADATA_KEYS,
                    excluded_llm_metadata_keys=EXCLUDE_METADATA_KEYS,
                )
                node.metadata = {
                    MEETING_ID_KEY: meeting.id,
                    MEETING_PERSONNEL_KEY: list(meeting.user_ids),
                    NODE_TYPE_KEY: NodeType.MEETING.value,
                }
                meeting_topic_nodes.append(node)
        meeting_topic_vector_index = SynonymsCascadeIndex(
            meeting_topic_nodes,
            storage_context=self._storage_context,
        )
        meeting_topic_keyword_index = LcsKeywordIndex(
            meeting_topic_nodes,
            storage_context=self._storage_context,
        )

        personnel_nodes = []
        for personnel in self._personnels.values():
            node = TextNode(
                text=personnel.name,
                excluded_embed_metadata_keys=EXCLUDE_METADATA_KEYS,
                excluded_llm_metadata_keys=EXCLUDE_METADATA_KEYS,
            )
            node.metadata = {
                PERSONNEL_NAME_KEY: personnel.name,
                MEETING_PERSONNEL_KEY: list(personnel.meeting_user_ids),
                NODE_TYPE_KEY: NodeType.PERSONNEL.value,
            }
            personnel_nodes.append(node)
        personnel_keyword_index = LcsKeywordIndex(
            personnel_nodes,
            storage_context=self._storage_context,
        )

        return MeetingSchemaIndex(
            meeting_topic_vector_index=meeting_topic_vector_index,
            meeting_topic_keyword_index=meeting_topic_keyword_index,
            personnel_keyword_index=personnel_keyword_index,
        )
