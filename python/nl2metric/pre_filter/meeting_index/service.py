from collections import defaultdict
from typing import Dict, List, Optional

from llama_index.core import QueryBundle
from llama_index.core.vector_stores.types import (
    MetadataFilter,
    FilterOperator,
    MetadataFilters,
)

from common.logging.logger import get_logger
from cache_updater.cached import cached
from metastore.meeting.db_meeting_metastore import get_db_meeting_metastore
from metastore.meeting.base import Meeting, Personnel, BaseMeetingMetaStore
from pre_filter.meeting_index.builder import MeetingIndexBuilder
from pre_filter.index.const import (
    MEETING_ID_KEY,
    MEETING_PERSONNEL_KEY,
    PERSONNEL_NAME_KEY,
)

HANLP_TASKS = ("tok/fine", "pos/pku")

logger = get_logger(__name__)


class MeetingPreFilterService:
    def __init__(
        self,
        meetings: Dict[str, Meeting],
        personnels: Dict[str, Personnel],
    ) -> None:
        logger.debug(f"MeetingPreFilterService for creating...")
        builder = MeetingIndexBuilder()
        builder.with_meetings(meetings)
        builder.with_personnels(personnels)
        self._meetings = meetings
        self._personnels = personnels
        self._schema_index = builder.build()
        logger.info(f"MeetingPreFilterService for created")

    @classmethod
    def from_meeting_meta(cls, meta: BaseMeetingMetaStore):
        return cls(
            meetings=meta.meetings_by_id,
            personnels=meta.personnel_by_name,
        )

    async def _retrieve_meeting_nodes(
        self, query: str, top_k: int = 10, user_id: Optional[str] = None
    ):
        meeting_retriever = self._schema_index.create_meeting_retriever(
            top_k=top_k,
        )
        query_buddle = QueryBundle(query)
        if user_id:
            filters = MetadataFilters(
                filters=[
                    MetadataFilter(
                        key=MEETING_PERSONNEL_KEY,
                        value=user_id,
                        operator=FilterOperator.ANY,
                    )
                ]
            )
            query_buddle.filters = filters

        return await meeting_retriever.aretrieve(query_buddle)

    async def retrieve_meetings(
        self, query: str, top_k: int = 10, user_id: Optional[str] = None
    ) -> List[Meeting]:
        nodes = await self._retrieve_meeting_nodes(query, top_k, user_id)
        meeting_ids = [n.metadata[MEETING_ID_KEY] for n in nodes]
        ret = []
        seen = set()
        for id in meeting_ids:
            meeting = self._meetings.get(id, None)
            if not meeting:
                logger.error("meeting not found, id=%s", id)
                continue
            if meeting.id not in seen:
                seen.add(meeting.id)
                ret.append(meeting)
        return ret

    async def _retrieve_personnel_nodes(
        self, query: str, top_k: int = 10, user_id: Optional[str] = None
    ):
        personnel_retriever = self._schema_index.create_personnel_retriever(
            top_k=top_k,
        )
        query_buddle = QueryBundle(query)
        if user_id:
            filters = MetadataFilters(
                filters=[
                    MetadataFilter(
                        key=MEETING_PERSONNEL_KEY,
                        value=user_id,
                        operator=FilterOperator.ANY,
                    )
                ]
            )
            query_buddle.filters = filters
        return await personnel_retriever.aretrieve(query_buddle)

    async def retrieve_personnels(
        self, query: str, top_k: int = 10, user_id: Optional[str] = None
    ) -> List[Personnel]:
        nodes = await self._retrieve_personnel_nodes(query, top_k, user_id)
        personnel_names = [n.metadata[PERSONNEL_NAME_KEY] for n in nodes]
        ret = []
        for name in personnel_names:
            personnel = self._personnels.get(name, None)
            if not personnel:
                logger.error("personnel not found, name=%s", name)
                continue
            ret.append(personnel)
        return ret


# index per user can leads to massive amount of indexs
# use filter instead
# TODO(bhx): project_id is mocked here
@cached("{project_id}")
def get_meeting_pre_filter_service(project_id="") -> MeetingPreFilterService:
    meta = get_db_meeting_metastore()
    return MeetingPreFilterService.from_meeting_meta(meta)
