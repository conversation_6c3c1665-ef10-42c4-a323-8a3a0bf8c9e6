import copy
import functools
import threading

from common.logging.logger import get_logger
from cache_updater.async_cached_service.client import Async<PERSON>achedClient
from cache_updater.common import (
    func_key,
    func_call_key,
    get_cached_impl,
    load_cache_from_path,
    load_all_cache,
    refresh_cache_impl,
)

logger = get_logger(__name__)


class AsyncCacheHolder:
    def __init__(self) -> None:
        # dict is not thread safe
        self._cache, self._cache_timestamp = load_all_cache()
        self._cache_lock: threading.Lock = threading.Lock()

    def get_cache(self, key: str):
        with self._cache_lock:
            return self._cache.get(key, None)

    def get_cache_timestamps(self):
        with self._cache_lock:
            return copy.deepcopy(self._cache_timestamp)

    # add cache after acquiring cache from remote server
    def add_cache_if_absent(self, key, ts, path):
        with self._cache_lock:
            if key in self._cache:
                return self._cache[key]
            else:
                value = load_cache_from_path(path)
                self._cache[key] = value
                self._cache_timestamp[key] = ts
                logger.info(f"new key {key} added, ts {ts}")
                return value

    def refresh_cache(self, delta_caches, delta_timestamps):
        if not delta_caches:
            return
        assert len(delta_caches) == len(delta_timestamps)
        with self._cache_lock:
            self._cache.update(delta_caches)
            self._cache_timestamp.update(delta_timestamps)

    def clear(self):
        with self._cache_lock:
            self._cache.clear()


here_is_server = False


def set_is_server(b):
    global here_is_server
    here_is_server = b


async_cache_holder = None


def _get_holder():
    if here_is_server:
        # 这里逻辑比较绕：在server上需要刷新缓存，所以也会调到async_cached装饰器。
        # 但是此时缓存就在本地，不能再去server拿。
        # 此外还有预热的逻辑也会调到async_cached装饰器。
        # 另外也不能直接在外面async_cache_holder = async_cached_server
        # 因为import本模块的时候here_is_server一定是False
        from cache_updater.async_cached_service.server import async_cached_server

        return async_cached_server
    else:
        return async_cache_holder


def init_async_cached_holder():
    global async_cache_holder
    if async_cache_holder is None:
        async_cache_holder = AsyncCacheHolder()


def clear_async_cached():
    _get_holder().clear()


def async_refresh_cache():
    local_timestamps = _get_holder().get_cache_timestamps()
    remote_timestamps, remote_paths = AsyncCachedClient.get_all_cached()
    delta_caches = {}
    delta_timestamps = {}
    if not remote_timestamps:
        return
    for key, ts in remote_timestamps.items():
        local_ts = local_timestamps.get(key, -1)
        if local_ts < ts:
            delta_caches[key] = load_cache_from_path(remote_paths[key])
            delta_timestamps[key] = ts
            logger.info(f"key {key} updated, ts {local_ts} -> {ts}")
    async_cache_holder.refresh_cache(delta_caches, delta_timestamps)


def async_cached(format_string, refresh_checker=None):
    def decorator(fn):
        @functools.wraps(fn)
        def wrapper(*args, **kwargs):
            if here_is_server:
                result, _, _ = get_cached_impl(
                    _get_holder(), fn, format_string, args, kwargs
                )
                return result

            key = func_call_key(format_string, fn, *args, **kwargs)
            cached = _get_holder().get_cache(key)
            if cached != None:
                return cached
            # acquire cache from remote server
            ts, path = AsyncCachedClient.get_cached(func_key(fn), args, kwargs)
            return _get_holder().add_cache_if_absent(key, ts, path)

        def get_cached(*args, **kwargs):
            key = func_call_key(format_string, fn, *args, **kwargs)
            cached = _get_holder().get_cache(key)
            if cached != None:
                return cached
            raise RuntimeError(f"key {key} no cache found")

        # refresh_cache only happens in server
        # in client, refresh_cache is replaced by async_refresh_cache
        def refresh_cache():
            refresh_cache_impl(_get_holder(), refresh_checker, fn, format_string)

        wrapper.refresh_cache = refresh_cache
        wrapper.get_cached = get_cached
        wrapper.direct_run = fn
        wrapper.format_string = format_string

        return wrapper

    return decorator
