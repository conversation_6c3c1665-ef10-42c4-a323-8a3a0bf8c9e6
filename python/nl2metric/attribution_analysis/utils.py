import json
import math
import re
from datetime import datetime, timedelta
from decimal import ROUND_HALF_UP, Decimal
from itertools import permutations
from typing import Dict
from urllib.parse import quote, urljoin

import numpy as np
import pandas as pd
import requests
import scipy
from dateutil.relativedelta import relativedelta
from pandas.api.types import CategoricalDtype
from sql_metadata import Parser

from common.db_model.model import (
    get_semantic_dimensions,
    get_semantic_measures,
    get_semantic_model,
)
from common.logging.logger import get_logger
from common.types.exceptions import AttributionAnalysisNoData
from config.app_config import ASK_BI_HOST
from metastore import get_metastore
from metastore.metric_tree import get_metric_tree_manager

logger = get_logger(__name__)


def query(params_query, model_id: str):
    metrics = ",".join(list(params_query["metric"].keys()))
    where = quote(params_query["filter_info"])
    timeQuery = quote(str(json.dumps(params_query["time"])))
    groupbys = ",".join(params_query["groupbys"])
    if groupbys:
        url = urljoin(
            ASK_BI_HOST,
            f"api/metrics/metric2sql2data-attr?metricNames={metrics}&where={where}&modelId={model_id}&timeQueryParams={timeQuery}&groupBys={groupbys}",
        )
    else:
        url = urljoin(
            ASK_BI_HOST,
            f"api/metrics/metric2sql2data-attr?metricNames={metrics}&where={where}&modelId={model_id}&timeQueryParams={timeQuery}",
        )
    response = requests.get(url)
    # print(response.status_code)
    data_base = response.json().get("data", {}).get("baseRows")
    data_compare = response.json().get("data", {}).get("compareRows")
    metric_sql = {
        "sqlBase": response.json().get("data", {}).get("sqlBase"),
        "sqlCompare": response.json().get("data", {}).get("sqlCompare"),
    }
    if not data_base or not data_compare:
        time_str = time_param_to_date(params_query["time"])
        msg = "metric2sql2data-attr response invalid format: %s" % response.text
        logger.error(msg)
        raise AttributionAnalysisNoData(
            f"{time_str['baseTime']}或{time_str['compareTime']}下缺失相关数据，因此无法进行归因分析。"
        )

    baseTime = pd.DataFrame(data_base)
    compareTime = pd.DataFrame(data_compare)
    baseTime["_DATE_TYPE_"] = "baseTime"
    compareTime["_DATE_TYPE_"] = "compareTime"
    df = pd.concat([baseTime, compareTime])
    metric_with_null = list(df.isnull().any()[df.isnull().any()].index)
    # check if contains null value
    if metric_with_null:
        time_str = time_param_to_date(params_query["time"])
        metric_label_with_null = []
        for metric in metric_with_null:
            if metric in params_query["metric"]:
                metric_label_with_null.append(params_query["metric"][metric])
        if metric_label_with_null:
            raise AttributionAnalysisNoData(
                f"指标{';'.join(metric_label_with_null)}在{time_str['baseTime']}或{time_str['compareTime']}下无数据，因此无法进行归因分析。"
            )

    # define _DATE_TYPE_ order and sort dataframe
    time_order = CategoricalDtype(["baseTime", "compareTime"], ordered=True)
    df["_DATE_TYPE_"] = df["_DATE_TYPE_"].astype(time_order)
    df = df.sort_values(["_DATE_TYPE_"]).reset_index(drop=True)

    # convert to float
    for metric in list(params_query["metric"].keys()):
        df[metric] = df[metric].astype(float)
    return df, metric_sql


def time_param_to_date(time_params: Dict) -> Dict:
    if not time_params.get("baseTime") and not time_params.get("compareTime"):
        return None
    time_dict = time_params.copy()
    today = datetime.now()
    converted_time_dict = {}
    for base_compare, time in time_dict.items():
        if time["type"].startswith("recent"):
            if time["type"] == "recentDays" and time["days"] is not None:
                converted_time_dict[base_compare] = (
                    today - timedelta(days=time["days"])
                ).strftime("%Y年%m月%d日")
            elif time["type"] == "recentMonths" and time["months"] is not None:
                converted_time_dict[base_compare] = (
                    today - relativedelta(months=time["months"])
                ).strftime("%Y年%m月")
            elif time["type"] == "recentQuarters" and time["quarters"] is not None:
                converted_time_dict[base_compare] = (
                    today - relativedelta(months=time["quarters"] * 3)
                ).strftime("%Y年%m月")
            else:  # params['type'] == "recentYears" and params['years'] is not None:
                converted_time_dict[base_compare] = (
                    today - relativedelta(years=time["years"])
                ).strftime("%Y年")
        else:
            if time["type"] == "specificDate":
                converted_time_dict[base_compare] = (
                    datetime(time["year"], time["month"], time["day"])
                ).strftime("%Y年%m月%d日")
            elif time["type"] == "specificMonth":
                converted_time_dict[base_compare] = (
                    datetime(time["year"], time["month"], 1)
                ).strftime("%Y年%m月")
            elif time["type"] == "specificQuarter":
                # Calculate the start month of the quarter
                start_month = (time["quarter"] - 1) * 3 + 1
                converted_time_dict[base_compare] = (
                    datetime(time["year"], start_month, 1)
                ).strftime("%Y年%m月")
            else:  # params['type'] == "specificYear":
                converted_time_dict[base_compare] = (
                    datetime(time["year"], 1, 1)
                ).strftime("%Y年")
    return converted_time_dict


def get_metric_tree(metric: str, model_id: str):
    model = get_semantic_model(model_id)
    project_id = model.semantic_project_id
    return get_metric_tree_manager(project_id).build_tree(metric)


def get_dimensions(project_id: str):
    dimensions = get_semantic_dimensions(project_id)
    return dict([(dim.name, dim.label) for dim in dimensions])


def get_measures(project_id: str):
    measures = get_semantic_measures(project_id)
    return dict([(measure.name, measure.label) for measure in measures])


def meta_extraction(metric_label: str, project_id: str, model_name: str):
    metastore = get_metastore(project_id=project_id)
    raw_metric_dict = metastore.list_metrics_by_model_name(model_name)
    measure_dict = {m.name: m.label for m in raw_metric_dict.values()}
    raw_dimension_dict = metastore.list_dimensions_by_model_name(model_name)
    # Filter all non categorical dimensions out (xcf)
    dimension_dict = {
        d.name: d.label for d in raw_dimension_dict.values() if d.type == "categorical"
    }
    if model_name == "ggj_dtl_info" and (
        "A001" in metric_label or "A016" in metric_label
    ):
        dimension_dict = {"org_name": dimension_dict["org_name"]}
    return {metric_label: measure_dict[metric_label]}, dimension_dict


def cal_metric_contribute(df, X, y, method, pos_neg_dict):
    """
    calculate contribution
    :param df: data frame containing X and y
        type: <class "pandas.core.frame.DataFrame">
    :param X: list of metrics
        type: <class "list">
    :param y: target
        type: <class "str">
    :param method: method of decomposition
        type: <class "str">
        example: "lmdi" or "abs"
    :return: a data frame containing contributions of metrics to the target
    """
    df = df.copy()
    for col in df.columns:
        if col in y:
            df[col] = df[col].astype("float")
    all_change = df[y][1] - df[y][0]

    if method == "lmdi":
        df_base = df[df["_DATE_TYPE_"] == "baseTime"]
        df_cont = df[df["_DATE_TYPE_"] == "compareTime"]
        y_change_rate = df_cont[y].values[0] / df_base[y].values[0] - 1
        ln_y_change = np.log(df_cont[y].values[0] / df_base[y].values[0])
        ln_x_change = []
        for j in X:
            ln_x_change.append(np.log(df_cont[j].values[0] / df_base[j].values[0]))
        ln_x_weight = ln_x_change / ln_y_change
        x_contribute = ln_x_weight * y_change_rate
        df_result = pd.DataFrame(x_contribute, index=X, columns=["contribute"])

    elif method == "abs":
        for metric in X:
            if pos_neg_dict[metric] == "-":
                df[metric] = -df[metric]
        all_change = df[y][1] - df[y][0]
        y_change_rate = (df[y][1] - df[y][0]) / df[y][0]

        y = "value"
        df_temp = pd.DataFrame(columns=["_DATE_TYPE_", y, "metric_decomp"])
        for metric in X:
            df_sub = df[["_DATE_TYPE_", metric]].copy()
            df_sub.columns = ["_DATE_TYPE_", y]
            df_sub["metric_decomp"] = metric
            df_temp = pd.concat([df_temp, df_sub])
        df = df_temp.copy()
        df_base = df[df["_DATE_TYPE_"] == "baseTime"]
        df_cont = df[df["_DATE_TYPE_"] == "compareTime"]

        if all_change == 0:
            all_change = 1e-4
        dc = (
            df_cont.groupby(["metric_decomp"])[y].sum()
            - df_base.groupby(["metric_decomp"])[y].sum()
        ).astype(float)
        # dr = (dc / all_change) * y_change_rate
        dr = dc / all_change
        dr = dr.dropna()
        df_result = pd.DataFrame(
            dr.values, index=list(dr.index), columns=["contribute"]
        )

    elif method == "rate":
        numerator = X[0]
        denominator = X[1]
        df_base = df[df["_DATE_TYPE_"] == "baseTime"]
        df_cont = df[df["_DATE_TYPE_"] == "compareTime"]

        y_change_rate = df_cont[y].values[0] / df_base[y].values[0] - 1
        ln_y_change = np.log(df_cont[y].values[0] / df_base[y].values[0])
        ln_numerator_change = np.log(
            df_cont[numerator].values[0] / df_base[numerator].values[0]
        )
        ln_denominator_change = -np.log(
            df_cont[denominator].values[0] / df_base[denominator].values[0]
        )
        ln_numerator_weight = ln_numerator_change / ln_y_change
        ln_denominator_weight = ln_denominator_change / ln_y_change
        numerator_contribute = ln_numerator_weight * y_change_rate
        denominator_contribute = ln_denominator_weight * y_change_rate
        # print([numerator_contribute, denominator_contribute])
        df_result = pd.DataFrame(
            [numerator_contribute, denominator_contribute],
            index=X,
            columns=["contribute"],
        )

    df_result["metric"] = df_result.index
    df_result["rank"] = df_result["contribute"].rank(ascending=False)
    return df_result


def check_operator(expr):
    if "+" in expr or "-" in expr:
        method = "abs"
    elif "*" in expr:
        method = "lmdi"
    elif "/" in expr:
        method = "rate"
    else:
        method = None
    return method


def parse_expression(expression):
    expression = expression.replace(" ", "")
    metrics = re.split("\+|-", expression)
    pos_neg_dict = {}
    for metric in metrics:
        try:
            ope = expression.split(metric)[0][-1]
        except:
            ope = "+"
        pos_neg_dict[metric] = ope
    return pos_neg_dict


def priority(z):
    if z in ["×", "*", "/"]:
        return 2
    elif z in ["+", "-"]:
        return 1


def in2post(expr):
    """:param expr: 前缀表达式
    :return: 后缀表达式

    Example：
        "1+((2+3)×4)-5"
        "1 2 3 + 4 × + 5 -"
    """
    stack = []
    post = []
    for z in expr:
        if z not in ["×", "*", "/", "+", "-", "(", ")"]:
            post.append(z)
        #             print(1, post)
        else:
            if z != ")" and (
                not stack
                or z == "("
                or stack[-1] == "("
                or priority(z) > priority(stack[-1])
            ):  # stack 不空；栈顶为（；优先级大于
                stack.append(z)  # 运算符入栈

            elif z == ")":  # 右括号出栈
                while True:
                    x = stack.pop()
                    if x != "(":
                        post.append(x)
                    #                         print(2, post)
                    else:
                        break

            else:  # 比较运算符优先级，看是否入栈出栈
                while True:
                    if (
                        stack
                        and stack[-1] != "("
                        and priority(z) <= priority(stack[-1])
                    ):
                        post.append(stack.pop())
                    #                         print(3, post)
                    else:
                        stack.append(z)
                        break
    while stack:  # 还未出栈的运算符，需要加到表达式末尾
        post.append(stack.pop())
    return post


def check_ope(fullstr, operators):
    for ope in operators:
        if ope in fullstr:
            return 1
    return 0


def metric_decomposition(post):
    post = [i for i in post if i != " "]
    # print(post)
    operators = ["×", "*", "/", "+", "-"]
    metric_dict = {}
    stack = []
    cnt = 0
    for i in post:
        # print(i)
        if i not in operators:
            stack.append(i)
        else:
            latter = stack.pop()
            former = stack.pop()
            # print('fli', former, latter, i)
            dict_inverse = {v: k for k, v in metric_dict.items()}
            if (check_ope(former, operators) and i not in former) or (
                check_ope(latter, operators) and i not in latter
            ):
                # print('1')
                cnt += 1
                metric = f"{dict_inverse.get(former, former)} {i} {dict_inverse.get(latter, latter)}"
                metric_dict[f"metric_{cnt}"] = metric
            else:
                # print('2')
                # print(dict_inverse)
                # print('former & latter & dict_inverse', former, latter, dict_inverse.get(former, 0))
                if not dict_inverse.get(former, 0) and not dict_inverse.get(latter, 0):
                    # print('3')
                    cnt += 1
                metric = f"{former} {i} {latter}"
                metric_dict[f"metric_{cnt}"] = metric

            stack.append(metric)
        # print('stack', stack)
    # print(metric_dict)
    return metric_dict


def ori_expression_extraction(metric_info, expression):
    rep = [
        substring for substring in list(metric_info.keys()) if substring in expression
    ]
    while [
        substring for substring in list(metric_info.keys()) if substring in expression
    ]:
        for i in rep:
            expression = expression.replace(i, f"({metric_info[i]})")
        rep = [
            substring
            for substring in list(metric_info.keys())
            if substring in expression
        ]
    return expression


def smart_round(x, n):
    x = float(x)
    return str(Decimal(x).quantize(Decimal("0." + "0" * n), rounding=ROUND_HALF_UP))


def recursion(root):
    if not root["children"]:
        return
    for index, node in enumerate(root["children"]):
        root["children"][index] = node.__dict__
        recursion(root["children"][index])


def cal_ele_contribution_abs(df, dimension, metric_name, dt="_DATE_TYPE_"):
    df = df.copy()
    base_date = "baseTime"
    cont_date = "compareTime"
    df = (
        df.pivot(index=dimension, columns=dt, values=metric_name)
        .reset_index()
        .fillna(0)
    )
    df = pd.melt(
        df,
        id_vars=dimension,
        value_vars=[base_date, cont_date],
        value_name=metric_name,
        var_name=dt,
    )
    df_base = df[df[dt] == base_date]
    df_cont = df[df[dt] == cont_date]
    all_change = float(df_cont[metric_name].sum() - df_base[metric_name].sum())
    if all_change == 0:
        all_change = 1e-4
    dc = (
        df_cont.groupby([dimension])[metric_name].sum()
        - df_base.groupby([dimension])[metric_name].sum()
    ).astype(float)
    dr = dc / all_change
    dr = dr.dropna()
    df_contribution = pd.DataFrame(dr).reset_index(drop=True).astype(float)
    df_contribution["element"] = dr.index
    df_contribution["dim_name"] = str(dr.index.names)
    df_contribution["base"] = df_base.groupby([dimension])[metric_name].sum().values
    df_contribution["change"] = dc.values
    df_contribution["all_base"] = df_base[metric_name].sum()
    df_contribution["all_change"] = all_change
    df_contribution.columns = [
        "contribution",
        "element",
        "dim_name",
        "base",
        "change",
        "all_base",
        "all_change",
    ]
    df_result = (
        df_contribution[["element", "contribution", "change"]]
        .sort_values(by="contribution", ascending=False)
        .reset_index(drop=True)
    )
    return df_result


def js_convergence(df, metric_name, dimension):
    df = df.copy()
    # for col in df.columns:
    #     if col != "_DATE_TYPE_" and col != dimension:
    #         df[col] = df[col].astype("float")
    df = (
        df.pivot(
            index=dimension, columns="_DATE_TYPE_", values=metric_name.replace(" ", "_")
        )
        .reset_index()
        .dropna()
    )

    if len(df) == 0:
        return 0
    df.columns = ["element", "before", "after"]
    distribution = df["before"].tolist()
    distribution.extend(df["after"].tolist())
    distribution = np.array(distribution)
    h = 3.5 * np.std(distribution) / (len(distribution) ** (1 / 3))
    upper = max(max(df["before"]), max(df["after"])) + 1e-4
    lower = min(min(df["before"]), min(df["after"])) - 1e-4
    if h == 0:
        # means std of distribution = 0
        return 0

    num_bins = math.ceil((upper - lower) / h)
    if num_bins <= 2:
        num_bins = 3
    bins = np.linspace(lower, upper, num=num_bins)
    pi = pd.cut(np.array(df["before"]), bins).value_counts() / len(df)
    qi = pd.cut(np.array(df["after"]), bins).value_counts() / len(df)
    Mi = (pi + qi) / 2
    js = 0.5 * scipy.stats.entropy(pi, Mi, base=2) + 0.5 * scipy.stats.entropy(
        qi, Mi, base=2
    )

    return None if np.isnan(js) else js


def schema_link_extraction(sql):
    schema_link = []
    try:
        column = Parser(sql).columns_dict
    except:
        column = {}

    if column:
        for key, value in column.items():
            if key != "join":
                schema_link.extend(value)

    try:
        join = Parser(sql).columns_dict["join"]
        join = list(permutations(join, 2))
        join = [tuple[0] + " = " + tuple[1] for tuple in join]
        sql_re = replaceMulti(
            sql,
            list(Parser(sql).tables_aliases.keys()),
            list(Parser(sql).tables_aliases.values()),
        )
        new_join = []
        for cons in join:
            if cons in sql_re:
                new_join.append(cons)
        schema_link.extend(new_join)
    except:
        pass

    schema_link = list(set(schema_link))
    return schema_link


def replaceMulti(text: str, olds: list, news: list):
    if len(olds) != len(news):
        raise IndexError
    else:
        new_text = text[:]
        i = 0
        for word in olds:
            i += 1
            new_text = replaceFomat(new_text, word, i)
        i = 0
        for word in news:
            i += 1
            new_text = replaceFomat(new_text, word, i, True)
        return new_text


def replaceFomat(text: str, word: str, n: int, reverse=False):
    new_text = text[:]
    fmt = "<{}>".format(n)
    if reverse is False:
        new_text = new_text.replace(word, fmt)
        return new_text
    elif reverse is True:
        new_text = new_text.replace(fmt, word)
        return new_text
    else:
        raise TypeError


def query_metric_analysis(params_query: Dict, model_id: str):
    metrics = ",".join(list(params_query["metric"].keys()))
    where = quote(params_query["filter_info"])
    groupbys = ",".join(params_query["groupbys"])
    if params_query["time"]:
        timeQuery = quote(str(json.dumps(params_query["time"])))
        if groupbys:
            url = urljoin(
                ASK_BI_HOST,
                f"api/metrics/metric2sql2data?metricNames={metrics}&where={where}&modelId={model_id}&timeQueryParams={timeQuery}&groupBys={groupbys}",
            )
        else:
            url = urljoin(
                ASK_BI_HOST,
                f"api/metrics/metric2sql2data?metricNames={metrics}&where={where}&modelId={model_id}&timeQueryParams={timeQuery}",
            )
    else:
        if groupbys:
            url = urljoin(
                ASK_BI_HOST,
                f"api/metrics/metric2sql2data?metricNames={metrics}&where={where}&modelId={model_id}&groupBys={groupbys}",
            )
        else:
            url = urljoin(
                ASK_BI_HOST,
                f"/api/metrics/metric2sql2data?metricNames={metrics}&where={where}&modelId={model_id}",
            )
    response = requests.get(url)
    if response.json().get("code") != 0:
        raise Exception("Error: Attr Query")
    data = response.json().get("data", {}).get("rows")
    if groupbys:
        data = [d for d in data if not d[groupbys] == "汇总"]
    metric_sql = {
        "sqlCompare": response.json().get("data", {}).get("sql"),
    }
    # if not data:
    #     if params_query["time"]:
    #         time_str = time_param_to_date(
    #             {
    #                 "compareTime": params_query["time"]["timeEndFunction"]
    #             }
    #         )
    #     else:
    #         time_str = None
    #     if not time_str:
    #         raise AttributionAnalysisNoData(
    #             f"缺失相关数据，因此无法进行归因分析"
    #         )
    #     else:
    #         msg = "metric2sql2data-attr response invalid format: %s" % response.text
    #         logger.error(msg)
    #         raise AttributionAnalysisNoData(
    #             f"{time_str['compareTime']}下缺失相关数据，因此无法进行归因分析。"
    #         )
    #
    # df = pd.DataFrame(data).dropna()
    # if not groupbys:
    #     metric_with_null = list(df.isnull().any()[df.isnull().any()].index)
    #     if metric_with_null:
    #         if params_query["time"]:
    #             time_str = time_param_to_date(
    #                 {
    #                     "compareTime": params_query["time"]["timeEndFunction"]
    #                 }
    #             )
    #         else:
    #             time_str = None
    #         metric_label_with_null = []
    #         for metric in metric_with_null:
    #             if metric in params_query["metric"]:
    #                 metric_label_with_null.append(params_query["metric"][metric])
    #         if metric_label_with_null:
    #             if time_str:
    #                 raise AttributionAnalysisNoData(
    #                     f"指标{'，'.join(metric_label_with_null)}在{time_str['compareTime']}下存在缺失数据，因此无法进行归因分析。"
    #                 )
    #         else:
    #             raise AttributionAnalysisNoData(
    #                 f"指标{'，'.join(metric_label_with_null)}存在缺失数据，因此无法进行归因分析。"
    #             )
    #
    # # convert to float
    # for metric in list(params_query["metric"].keys()):
    #     df[metric] = df[metric].astype(float).round(3)
    #
    # return df, metric_sql

    if params_query["time"]:
        time_str = time_param_to_date(
            {"compareTime": params_query["time"]["timeEndFunction"]}
        )
    else:
        time_str = None

    if data:
        df = pd.DataFrame(data)
        if not groupbys:
            metric_with_null = list(df.isnull().any()[df.isnull().any()].index)
            if metric_with_null:
                metric_label_with_null = []
                for metric in metric_with_null:
                    if metric in params_query["metric"]:
                        metric_label_with_null.append(params_query["metric"][metric])
                if metric_label_with_null:
                    if time_str:
                        raise AttributionAnalysisNoData(
                            f"指标{'，'.join(metric_label_with_null)}在{time_str['compareTime']}下存在缺失数据，因此无法进行归因分析。"
                        )
                else:
                    raise AttributionAnalysisNoData(
                        f"指标{'，'.join(metric_label_with_null)}存在缺失数据，因此无法进行归因分析。"
                    )
        else:
            df = df.dropna()
            if df.shape[0] == 0:
                return None, metric_sql

        for metric in list(params_query["metric"].keys()):
            df[metric] = df[metric].astype(float).round(3)

        return df, metric_sql
    else:
        if not groupbys:
            if not time_str:
                raise AttributionAnalysisNoData(f"缺失相关数据，因此无法进行归因分析")
            else:
                msg = "metric2sql2data-attr response invalid format: %s" % response.text
                logger.error(msg)
                raise AttributionAnalysisNoData(
                    f"{time_str['compareTime']}下缺失相关数据，因此无法进行归因分析。"
                )
        else:
            return None, metric_sql
