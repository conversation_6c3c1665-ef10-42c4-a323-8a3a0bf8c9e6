import ast
import json
from enum import Enum

import numpy as np

from attribution_analysis.algorithms.dim_attr import dimension_analysis
from attribution_analysis.utils import (
    cal_metric_contribute,
    check_operator,
    get_metric_tree,
    parse_expression,
    query,
    time_param_to_date,
)
from common.logging.logger import get_logger
from common.types.exceptions import MetricTreeNotExist
from common.utils.string_utils import class_to_dict
from nl2time_attr.nl2time_attr import AnalysisTimeQueryParams

logger = get_logger(__name__)


class nodeType(Enum):
    summable = 1
    non_summable = 2


class Node:
    """tree node definition"""

    def __init__(
        self,
        label,
        name,
        children_names,
        expr_calc,
        expr_relation,
        node_type: nodeType,
        attribution_analysis_result=None,
    ):
        self.label = label
        self.name = name
        self.children_names = children_names
        self.expr_calc = expr_calc
        self.expr_relation = expr_relation
        self.node_type = node_type
        self.attribution_analysis_result = attribution_analysis_result

    def add_child(self, node):
        """add child node"""
        self.children_names.append(node)

    def to_json(self):
        """node to json"""
        node_dict = {
            "label": self.label,
            "name": self.name,
            "expr_calc": self.expr_calc,
            "expr_relation": self.expr_relation,
            "children_names": [child.to_json() for child in self.children_names],
            "node_type": str(self.node_type.name),
            "attribution_analysis_result": self.attribution_analysis_result,
        }
        return node_dict

    # def __str__(self):
    #     return f"{self.name} ({self.attribution_analysis_result})"


def attribution_analysis(
    metric_name: str,
    time_query_params: AnalysisTimeQueryParams,
    filter_info: str,
    model_id: str,
    model_name: str,
):
    date = class_to_dict(time_query_params)

    tree_node = get_metric_tree(metric_name, model_id)
    if tree_node is None:
        raise MetricTreeNotExist()

    tree = json.loads(tree_node.to_json(indent=2).replace("null", '""'))
    if (
        tree["data"]
        and not tree["data"]["expr_relation"]
        and tree["data"]["children_names"]
        and tree["data"]["expr_calc"]
    ):
        # composite
        X = {}
        y = {tree["metric"]["name"]: tree["metric"]["label"]}
        for child in tree["children"]:
            X[child["metric"]["name"]] = child["metric"]["label"]
        method = check_operator(tree["data"]["expr_calc"])
        pos_neg_dict = parse_expression(tree["data"]["expr_calc"])
        metrics = dict(X, **y)
        params_query = {
            "metric": metrics,
            "filter_info": filter_info,
            "time": date,
            "groupbys": [],
        }
        df, metric_sql = query(params_query, model_id)
        contribute_df = cal_metric_contribute(
            df, list(X.keys()), list(y.keys())[0], method, pos_neg_dict
        )
        abs_change = float(df[tree["name"]].values[1] - df[tree["name"]].values[0])
        try:
            rate_change = float(
                df[tree["name"]].values[1] - df[tree["name"]].values[0]
            ) / float(df[tree["name"]].values[0])
        except:
            rate_change = None
        cal_result = {
            "values": [df[tree["name"]].values[0], df[tree["name"]].values[1]],
            "abs_change": abs_change,
            "rate_change": rate_change,
            "contribution": 1,
        }
        root = Node(
            label=tree["metric"]["label"],
            name=tree["name"],
            children_names=[],
            expr_calc=tree["data"]["expr_calc"] if tree["data"] else "",
            expr_relation=tree["data"]["expr_relation"] if tree["data"] else "",
            node_type=nodeType.summable,
            attribution_analysis_result=cal_result,
        )
        for child in tree["children"]:
            abs_change = float(
                df[child["name"]].values[1] - df[child["name"]].values[0]
            )
            try:
                rate_change = float(
                    df[child["name"]].values[1] - df[child["name"]].values[0]
                ) / float(df[child["name"]].values[0])
            except:
                rate_change = None
            cal_result = {
                "values": [
                    df[child["name"]].values[0],
                    df[child["name"]].values[1],
                ],
                "abs_change": abs_change,
                "rate_change": rate_change,
                "contribution": contribute_df[contribute_df["metric"] == child["name"]][
                    "contribute"
                ].values[0],
                "rank": contribute_df[contribute_df["metric"] == child["name"]][
                    "rank"
                ].values[0],
            }

            root.children_names.append(
                Node(
                    label=child["metric"]["label"],
                    name=child["name"],
                    expr_calc=child["data"]["expr_calc"] if child["data"] else "",
                    expr_relation=child["data"]["expr_relation"]
                    if child["data"]
                    else "",
                    children_names=child["children"],
                    node_type=nodeType.summable,
                    attribution_analysis_result=cal_result,
                )
            )
        traverse(root, date, filter_info, model_id)
    elif (
        tree["data"]
        and not tree["data"]["expr_calc"]
        and tree["data"]["children_names"]
        and tree["data"]["expr_relation"]
    ):
        X = {}
        y = {tree["metric"]["name"]: tree["metric"]["label"]}
        for child in tree["children"]:
            X[child["metric"]["name"]] = child["metric"]["label"]
        metrics = dict(X, **y)

        params_query = {
            "metric": metrics,
            "filter_info": filter_info,
            "time": date,
            "groupbys": [],
        }
        df, metric_sql = query(params_query, model_id)
        abs_change = float(df[tree["name"]].values[1] - df[tree["name"]].values[0])
        try:
            rate_change = float(
                df[tree["name"]].values[1] - df[tree["name"]].values[0]
            ) / float(df[tree["name"]].values[0])
        except:
            rate_change = None
        cal_result = {
            "values": [df[tree["name"]].values[0], df[tree["name"]].values[1]],
            "abs_change": abs_change,
            "rate_change": rate_change,
            "contribution": 1,
        }
        root = Node(
            label=tree["metric"]["label"],
            name=tree["name"],
            children_names=[],
            expr_calc=tree["data"]["expr_calc"] if tree["data"] else "",
            expr_relation=tree["data"]["expr_relation"] if tree["data"] else "",
            node_type=nodeType.summable,
            attribution_analysis_result=cal_result,
        )
        for child in tree["children"]:
            abs_change = float(
                df[child["name"]].values[1] - df[child["name"]].values[0]
            )
            try:
                rate_change = float(
                    df[child["name"]].values[1] - df[child["name"]].values[0]
                ) / float(df[child["name"]].values[0])
            except:
                rate_change = None
            cal_result = {
                "values": [
                    df[child["name"]].values[0],
                    df[child["name"]].values[1],
                ],
                "abs_change": abs_change,
                "rate_change": rate_change,
            }
            root.children_names.append(
                Node(
                    label=child["metric"]["label"],
                    name=child["name"],
                    expr_calc=child["data"]["expr_calc"] if child["data"] else "",
                    expr_relation=child["data"]["expr_relation"]
                    if child["data"]
                    else "",
                    children_names=child["children"],
                    node_type=nodeType.non_summable,
                    attribution_analysis_result=cal_result,
                )
            )
    else:
        metrics = {tree["metric"]["name"]: tree["metric"]["label"]}
        params_query = {
            "metric": metrics,
            "filter_info": filter_info,
            "time": date,
            "groupbys": [],
        }
        df, metric_sql = query(params_query, model_id)
        abs_change = float(df[tree["name"]].values[1] - df[tree["name"]].values[0])
        try:
            rate_change = float(
                df[tree["name"]].values[1] - df[tree["name"]].values[0]
            ) / float(df[tree["name"]].values[0])
        except:
            rate_change = None
        cal_result = {
            "values": [df[tree["name"]].values[0], df[tree["name"]].values[1]],
            "abs_change": abs_change,
            "rate_change": rate_change,
            "contribution": 1,
        }
        root = Node(
            label=tree["metric"]["label"],
            name=tree["name"],
            children_names=[],
            expr_calc=tree["data"]["expr_calc"] if tree["data"] else "",
            expr_relation=tree["data"]["expr_relation"] if tree["data"] else "",
            node_type=nodeType.non_summable,
            attribution_analysis_result=cal_result,
        )

    # dimension analysis
    _, single_dim = dimension_analysis(
        metric_name,
        date,
        filter_info,
        3,
        model_id,
        model_name,
    )
    single_dim = [
        {
            "metric": row["metric"],
            "dim": row["dim"],
            "js": row["js"],
            "element_pos": row["element_pos"].split(",") if row["element_pos"] else [],
            "element_neg": row["element_neg"].split(",") if row["element_neg"] else [],
            "contribution_pos": list(
                np.array(row["contribution_pos"].split(","), dtype="float")
            )
            if row["contribution_pos"]
            else [],
            "contribution_neg": list(
                np.array(row["contribution_neg"].split(","), dtype="float")
            )
            if row["contribution_neg"]
            else [],
            "change_pos": list(
                np.array(row["change_pos"].split(","), dtype="float")
                if row["change_pos"]
                else []
            ),
            "change_neg": list(
                np.array(row["change_neg"].split(","), dtype="float")
                if row["change_neg"]
                else []
            ),
        }
        for idx, row in single_dim.iterrows()
        if idx <= 2
    ]

    output = {
        "tree": ast.literal_eval(str(root.to_json())),
        # "tree": eval(str(root.to_json()).replace("'", '"')),
        "dimension": single_dim,
        "base_compare": date,
        "attr_params": {"metric": [metric_name], "where": filter_info},
        "metric_sql": metric_sql,
    }
    attr_result_in_text = attr_result_to_text(output)

    return output, attr_result_in_text


def traverse(root, date, filter_info, model_id: str):
    if not root:
        return

    for cur_node in root.children_names:
        if (
            not cur_node.expr_relation
            and cur_node.children_names
            and cur_node.expr_calc
        ):
            X = {}
            y = {cur_node.name: cur_node.label}
            for child in cur_node.children_names:
                X[child["metric"]["name"]] = child["metric"]["label"]

            method = check_operator(
                cur_node.expr_calc
            )  # what if method is None(parent = child)?
            pos_neg_dict = parse_expression(cur_node.expr_calc)

            metrics = dict(X, **y)

            params_query = {
                "metric": metrics,
                "filter_info": filter_info,
                "time": date,
                "groupbys": [],
            }
            df, _ = query(params_query, model_id)
            contribute_df = cal_metric_contribute(
                df, list(X.keys()), list(y.keys())[0], method, pos_neg_dict
            )

            temp_children = []
            for child in cur_node.children_names:
                abs_change = float(
                    df[child["name"]].values[1] - df[child["name"]].values[0]
                )
                try:
                    rate_change = float(
                        df[child["name"]].values[1] - df[child["name"]].values[0]
                    ) / float(df[child["name"]].values[0])
                except:
                    rate_change = None
                cal_result = {
                    "values": [
                        df[child["name"]].values[0],
                        df[child["name"]].values[1],
                    ],
                    "abs_change": abs_change,
                    "rate_change": rate_change,
                    "contribution": contribute_df[
                        contribute_df["metric"] == child["name"]
                    ]["contribute"].values[0],
                    "rank": contribute_df[contribute_df["metric"] == child["name"]][
                        "rank"
                    ].values[0],
                }
                temp_children.append(
                    Node(
                        label=child["metric"]["label"],
                        name=child["name"],
                        expr_calc=child["data"]["expr_calc"] if child["data"] else "",
                        expr_relation=child["data"]["expr_relation"]
                        if child["data"]
                        else "",
                        children_names=child["children"],
                        node_type=nodeType.summable,
                        attribution_analysis_result=cal_result,
                    )
                )
            cur_node.children_names = temp_children
            traverse(cur_node, date, filter_info, model_id)

        elif (
            not cur_node.expr_calc
            and cur_node.expr_relation
            and cur_node.children_names
        ):
            X = {}
            for child in cur_node.children_names:
                X[child["metric"]["name"]] = child["metric"]["label"]

            params_query = {
                "metric": X,
                "filter_info": filter_info,
                "time": date,
                "groupbys": [],
            }
            df, _ = query(params_query, model_id)
            temp_children = []
            for child in cur_node.children_names:
                abs_change = float(
                    df[child["name"]].values[1] - df[child["name"]].values[0]
                )
                try:
                    rate_change = float(
                        df[child["name"]].values[1] - df[child["name"]].values[0]
                    ) / float(df[child["name"]].values[0])
                except:
                    rate_change = None
                cal_result = {
                    "values": [
                        df[child["name"]].values[0],
                        df[child["name"]].values[1],
                    ],
                    "abs_change": abs_change,
                    "rate_change": rate_change,
                }
                temp_children.append(
                    Node(
                        label=child["metric"]["label"],
                        name=child["name"],
                        expr_calc="",
                        expr_relation="",
                        children_names=[],
                        node_type=nodeType.non_summable,
                        attribution_analysis_result=cal_result,
                    )
                )
            cur_node.children_names = temp_children


def attr_result_to_text(attr_result):
    dates = time_param_to_date(attr_result["base_compare"])
    metrics_res = attr_result["tree"]
    dimension_res = attr_result["dimension"]

    # convert base & compare time to string
    title = f"""#{dates["baseTime"]} --> {dates["compareTime"]}指标`{attr_result["tree"]["label"]}`异动归因报告\n\n"""

    # target
    base_value = round(
        attr_result["tree"]["attribution_analysis_result"]["values"][0], 2
    )
    compare_value = round(
        attr_result["tree"]["attribution_analysis_result"]["values"][1], 2
    )

    target = f"""##大盘指标情况分析：指标`{attr_result["tree"]["label"]}`在{dates["baseTime"]}的数值为{base_value}，在{dates["compareTime"]}的数值为{compare_value}，变化了{round(compare_value - base_value, 2)}，变化幅度为{round(attr_result["tree"]["attribution_analysis_result"]["rate_change"]*100,2)}%。\n\n"""

    # construct metrics analysis
    metrics_contribution = ""
    if metrics_res["children_names"] and metrics_res["expr_calc"]:
        expr_calc = metrics_res["expr_calc"]
        for metric in metrics_res["children_names"]:
            expr_calc = expr_calc.replace(metric["name"], metric["label"])
            base = round(metric["attribution_analysis_result"]["values"][0], 2)
            compare = round(metric["attribution_analysis_result"]["values"][1], 2)
            contribution = round(
                metric["attribution_analysis_result"]["contribution"] * 100, 2
            )
            metrics_contribution += f""" - 指标`{metric["label"]}`: {base} --> {compare}, 贡献度为{contribution}%\n"""
        metrics_contribution = f"""##大盘指标计算方式：{attr_result["tree"]["label"]} = {expr_calc}\n次级指标的波动和对大盘指标的贡献度情况：\n{metrics_contribution}\n"""

    dimensions_contribution = ""
    if attr_result["dimension"]:
        for idx, dim in enumerate(attr_result["dimension"]):
            pos_analysis = ""
            for idx_, ele in enumerate(dim["element_pos"]):
                pos_analysis += f"""  - `{ele}`（贡献度 {round(dim["contribution_pos"][idx_]*100, 2)}%）\n"""

            neg_analysis = ""
            for idx_, ele in enumerate(dim["element_neg"][::-1]):
                neg_analysis += f"""  - `{ele}`（贡献度{round(dim["contribution_neg"][::-1][idx_]*100, 2)}%）\n"""

            if pos_analysis:
                pos_analysis = f"""- 同向贡献度前三的码值分别为：\n""" + pos_analysis
            else:
                pos_analysis = f"""- 无同向贡献度的码值\n"""

            if neg_analysis:
                neg_analysis = f"""- 反向贡献度前三的码值为：\n""" + neg_analysis
            else:
                neg_analysis = f"""- 无反向贡献度的码值\n"""

            dimensions_contribution += (
                f"""{idx+1}. 维度`{dim["dim"]}`中：\n""" + pos_analysis + neg_analysis
            )
        dimensions_contribution = f"""##大盘指标的维度分析：\n""" + dimensions_contribution

    if metrics_contribution:
        CONTRIBUTION_REPORT = (
            title + target + metrics_contribution + dimensions_contribution
        )
    else:
        CONTRIBUTION_REPORT = title + target + dimensions_contribution

    return CONTRIBUTION_REPORT
