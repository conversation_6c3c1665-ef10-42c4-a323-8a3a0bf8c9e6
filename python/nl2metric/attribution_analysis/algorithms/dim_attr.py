import concurrent.futures
import os

import pandas as pd

from attribution_analysis.utils import (
    cal_ele_contribution_abs,
    js_convergence,
    meta_extraction,
    query,
    recursion,
    schema_link_extraction,
)
from common.db_model.model import get_semantic_model


class Node:
    def __init__(self, label, value, desc, item):
        self.nodeLabel = label
        self.nodeValue = value
        self.nodeDesc = desc
        self.items = item
        self.children = []


def filterExtension(path, filter_intr):
    where_list = []
    for i in path:
        if isinstance(i[1], str):
            where_list.append(f"({i[0]}='{i[1]}')")
        else:
            where_list.append(f"({i[0]}={i[1]})")

    if filter_intr:
        where = filter_intr + " AND "
        where += " AND ".join(where_list)
    else:
        where = " AND ".join(where_list)

    return where


def dimension_analysis_process(
    dimension_name,
    dimension_dict,
    metric_dict,
    date,
    filter_intr,
    topn,
    change,
    has_change,
    model_id,
):
    params_query = {
        "metric": metric_dict,
        "groupbys": [dimension_name],
        "time": date,
        "filter_info": filter_intr,
    }
    df, _ = query(params_query, model_id)
    if len(df) == 0:
        return
    js = js_convergence(df, list(metric_dict.keys())[0], dimension_name)
    dimension_value_contribution = cal_ele_contribution_abs(
        df, dimension_name, list(metric_dict.keys())[0]
    )
    dimension_value_contribution_pos = dimension_value_contribution[
        dimension_value_contribution["contribution"] > 0
    ]
    dimension_value_contribution_neg = (
        dimension_value_contribution[dimension_value_contribution["contribution"] < 0]
        .sort_values("contribution", ascending=True)
        .reset_index(drop=True)
    )

    # filter with a threshold = 0.8
    if dimension_value_contribution_pos["contribution"].sum() != 0:
        dimension_value_contribution_pos["relative_cum_sum"] = (
            dimension_value_contribution_pos["contribution"].cumsum()
            / dimension_value_contribution_pos["contribution"].sum()
        )
        idx_pos = list(
            dimension_value_contribution_pos[
                dimension_value_contribution_pos["relative_cum_sum"] >= 0.8
            ].index
        )
        if idx_pos:
            dimension_value_contribution_pos = dimension_value_contribution_pos[
                : idx_pos[0] + 1
            ]

    if dimension_value_contribution_neg["contribution"].sum() != 0:
        dimension_value_contribution_neg["relative_cum_sum"] = (
            dimension_value_contribution_neg["contribution"].cumsum()
            / dimension_value_contribution_neg["contribution"].sum()
        )
        print(dimension_value_contribution_neg)
        idx_neg = list(
            dimension_value_contribution_neg[
                dimension_value_contribution_neg["relative_cum_sum"] >= 0.8
            ].index
        )
        if idx_neg:
            dimension_value_contribution_neg = dimension_value_contribution_neg[
                : idx_neg[0] + 1
            ]

    topn_elements_pos = list(
        map(str, dimension_value_contribution_pos["element"][:topn])
    )
    topn_elements_neg = list(
        map(str, dimension_value_contribution_neg["element"][:topn][::-1])
    )
    topn_contribution_pos = list(
        map(str, dimension_value_contribution_pos["contribution"][:topn])
    )
    topn_contribution_neg = list(
        map(str, dimension_value_contribution_neg["contribution"][:topn][::-1])
    )
    topn_change_pos = list(map(str, dimension_value_contribution_pos["change"][:topn]))
    topn_change_neg = list(
        map(str, dimension_value_contribution_neg["change"][:topn][::-1])
    )
    if has_change:
        dimension_value_contribution["contribution_rate_of_change"] = (
            dimension_value_contribution["contribution"] * change
        )
        topn_contribution_rate_of_change = list(
            map(str, dimension_value_contribution["contribution_rate_of_change"][:topn])
        )
        return pd.DataFrame(
            {
                "dim": [dimension_dict[dimension_name]],
                "js": js,
                "element_pos": ",".join(topn_elements_pos),
                "element_neg": ",".join(topn_elements_neg),
                "contribution_pos": ",".join(topn_contribution_pos),
                "contribution_neg": ",".join(topn_contribution_neg),
                # "contribution_rate_of_change": ",".join(
                #     topn_contribution_rate_of_change
                # ),
                "change_pos": ",".join(topn_change_pos),
                "change_neg": ",".join(topn_change_neg),
            }
        )
    return pd.DataFrame(
        {
            "dim": [dimension_dict[dimension_name]],
            "js": js,
            "element_pos": ",".join(topn_elements_pos),
            "element_neg": ",".join(topn_elements_neg),
            "contribution_pos": ",".join(topn_contribution_pos),
            "contribution_neg": ",".join(topn_contribution_neg),
            "change_pos": ",".join(topn_change_pos),
            "change_neg": ",".join(topn_change_neg),
        }
    )


def dimensions_analysis_concurrent(
    dimensions: list[str],
    dimension_dict,
    metric_dict,
    date,
    filter_intr,
    topn,
    js_contribution_df,
    change,
    has_change: bool,
    model_id,
):
    max_workers = os.environ.get("analysis_concurrency_num", 5)
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_tasks = [
            executor.submit(
                dimension_analysis_process,
                dimension_name,
                dimension_dict,
                metric_dict,
                date,
                filter_intr,
                topn,
                change,
                has_change,
                model_id,
            )
            for dimension_name in dimensions
        ]
        results = [
            future.result() for future in concurrent.futures.as_completed(future_tasks)
        ]
    return pd.concat([js_contribution_df] + results, ignore_index=True)


def dimension_analysis(
    metric_label,
    date,
    filter_intr,
    topn,
    model_id: str,
    model_name: str,
):
    # some meta info extraction
    model = get_semantic_model(model_id)
    project_id = model.semantic_project_id
    metric_dict, dimension_dict = meta_extraction(metric_label, project_id, model_name)
    metric_meta = {
        "metric_desc": metric_dict[metric_label],
        "metric_label": metric_label,
        "dimension_dict": dimension_dict,
        "time": date,
    }
    dimensions = list(dimension_dict.keys())
    if filter_intr:
        filter_groupbys = schema_link_extraction(
            f"SELECT COUNT(*) FROM TABLE WHERE {filter_intr}"
        )
        for dim in filter_groupbys:
            dimensions.remove(dim)

    metric_dim = {}
    js_contribution_df = pd.DataFrame(
        columns=[
            "dim",
            "js",
            "element_pos",
            "element_neg",
            "contribution_pos",
            "contribution_neg",
            "change_pos",
            "change_neg",
        ]
    )
    params_query = {
        "metric": metric_dict,
        "groupbys": [],
        "time": date,
        "filter_info": filter_intr,
    }
    target_values, _ = query(params_query, model_id)
    target_values = target_values.sort_values(by="_DATE_TYPE_", ascending=True)
    if target_values[metric_label].values[0] != 0:
        change = float(
            (
                target_values[metric_label].values[1]
                - target_values[metric_label].values[0]
            )
            / target_values[metric_label].values[0]
        )
    else:
        change = float(
            target_values[metric_label].values[1]
            - target_values[metric_label].values[0]
        )
    if target_values[metric_label].values[0] > target_values[metric_label].values[1]:
        trend = "下降"
    elif target_values[metric_label].values[0] < target_values[metric_label].values[1]:
        trend = "上升"
    else:
        trend = "不变"

    js_contribution_df = dimensions_analysis_concurrent(
        dimensions,
        dimension_dict,
        metric_dict,
        date,
        filter_intr,
        topn,
        js_contribution_df,
        change,
        True,
        model_id,
    )

    js_contribution_df = js_contribution_df.sort_values(
        by="js", ascending=False
    ).reset_index(drop=True)
    # js_contribution_df = js_contribution_df[js_contribution_df["js"] != 0]
    js_contribution_df["metric"] = metric_label

    try:
        if not (
            len(dimension_dict.keys()) == 1
            and list(dimension_dict.keys())[0] == "org_name"
        ):
            # 中原特殊场景。只对A001 A016相关指标下，维度org_name生效
            js_contribution_df["drop_or_not"] = js_contribution_df.apply(
                lambda x: len(x["element_pos"].split(",")) == 1
                and len(x["element_neg"].split(",")) == 1,
                axis=1,
            )
            js_contribution_df = js_contribution_df[
                js_contribution_df["drop_or_not"] == False
            ][
                [
                    "metric",
                    "dim",
                    "js",
                    "element_pos",
                    "element_neg",
                    "contribution_pos",
                    "contribution_neg",
                    "change_pos",
                    "change_neg"
                    # "contribution_rate_of_change",
                ]
            ].reset_index(
                drop=True
            )
    except:
        pass
    single_dim = js_contribution_df.copy()
    # print(single_dim["contribution"].apply(lambda x: sum(list(map(float, x.split(','))))))

    return target_values, single_dim


def dfs(start, path, dimensions, params, res, max_depth=3):
    metric_label = list(params["metric"])[0]
    metric_desc = params["metric"][metric_label]
    date = params["time"]
    topn = params["topn"]
    dimension_dict = params["dimension_dict"]
    filter_intr = params["filter_intr"]
    model_id = params["model_id"]

    if (
        len(dimensions) == 0
        or (len(path) >= max_depth and not start)
        or (len(path) >= (max_depth - 1) and start)
    ):
        if not start:
            # reformat path and js
            path_temp = [[i[:2] for i in path.copy()]]
            path_temp.extend(map(float, path.copy()[-1][2:]))
            res.append(path_temp)
        else:
            for node in start.children:
                dim = node.nodeLabel
                ele = node.nodeValue
                contribution = float(node.items[0]["value"])
                contribution_rate_of_change = float(node.items[-1]["value"])
                path_temp = [i[:2] for i in path.copy()]
                path_temp.append((dim, ele))
                path_temp = [path_temp, contribution, contribution_rate_of_change]
                res.append(path_temp)
        return res

    for node in start.children:
        dim = node.nodeLabel
        ele = node.nodeValue
        contribution = node.items[0]["value"]
        contribution_rate_of_change = node.items[-1]["value"]
        cur_path = path.copy()
        cur_path.append((dim, ele, contribution, contribution_rate_of_change))
        filter = filterExtension(cur_path, filter_intr)
        js_contribution_df = pd.DataFrame(
            columns=["dim", "js", "element", "contribution"]
        )
        js_contribution_df = dimensions_analysis_concurrent(
            dimensions,
            params["metric"],
            date,
            filter,
            topn,
            js_contribution_df,
            0,
            False,
            model_id,
        )
        js_contribution_df = js_contribution_df.sort_values(
            by="js", ascending=False
        ).reset_index()
        # js_contribution_df = js_contribution_df[js_contribution_df["js"] != 0][
        #    ["dim", "js", "element", "contribution"]
        # ]
        js_contribution_df = js_contribution_df[
            ["dim", "js", "element", "contribution"]
        ]

        try:
            js_contribution_df["drop_or_not"] = js_contribution_df.apply(
                lambda x: len(x["element"].split(",")) == 1, axis=1
            )
            js_contribution_df = js_contribution_df[
                js_contribution_df["drop_or_not"] == False
            ][["dim", "js", "element", "contribution"]].reset_index()
        except:
            pass

        if len(js_contribution_df) != 0:
            for i in range(len(js_contribution_df.element[0].split(","))):
                node.children.append(
                    Node(
                        label=js_contribution_df.dim[0],
                        value=js_contribution_df.element[0].split(",")[i],
                        desc=dimension_dict[js_contribution_df.dim[0]],
                        item=[
                            {
                                "text": "contribution",
                                "value": float(
                                    js_contribution_df.contribution[0].split(",")[i]
                                ),
                            },
                            {
                                "text": "contribution",
                                "value": float(
                                    js_contribution_df.contribution[0].split(",")[i]
                                )
                                * node.items[-1]["value"],
                            },
                        ],
                    )
                )
            next_dimensions = dimensions.copy()
            next_dimensions.remove(js_contribution_df.dim[0])
            res = dfs(node, cur_path, next_dimensions, params, res)
        else:
            res = dfs([], cur_path, [], params, res)
    return res
