import os
from pathlib import Path
from dotenv import load_dotenv

from nl2document.index.service.service import document_index_service

env_file = Path(__file__).parent / ".env"
print(f"load dotenv {env_file}")
load_dotenv(dotenv_path=env_file, override=True)

import uvicorn
from fastapi import FastAPI

from nl2document.bj_telecom_biz.service.service import bj_telecom_biz_service

# specify path to easily change env in docker


if __name__ == "__main__":
    app = FastAPI()
    app.include_router(bj_telecom_biz_service.router)
    app.include_router(document_index_service.router)
    uvicorn.run(app, host="0.0.0.0", port=9099, workers=1)
