import asyncio

import httpx
import threading
import queue
import pickle

from common.logging.logger import get_logger
from config import app_config

logger = get_logger(__name__)

cb_queue: queue.Queue = queue.Queue()
cb_worker: threading.Thread = None


class CbWorker(threading.Thread):
    def run(self):
        while True:
            item = cb_queue.get()
            if item is None:  # 退出条件
                continue
            self.process_task(item)
            cb_queue.task_done()

    def process_task(self, data):
        url = f"http://127.0.0.1:{app_config.SHARED_SERVICE_PORT}/reporter/cb"
        try:
            with httpx.Client() as client:
                resp = client.post(
                    url=url,
                    content=pickle.dumps(data),
                    headers={"Content-Type": "application/octet-stream"},
                )
                if not resp.is_success:
                    logger.error(
                        f"sending cb failed with status {resp.status_code}: {resp.text}"
                    )
        except Exception as e:
            logger.error(f"sending cb failed with exception {e}")


def start_cb_mgr():
    global cb_worker

    if cb_worker is not None and cb_worker.is_alive():
        return
    if cb_worker is not None:
        logger.error("cb_worker exited. Restarting...")
    cb_worker = CbWorker()
    cb_worker.start()
    logger.info(f"cb_worker started, is_alive {cb_worker.is_alive()}")


def cb_mgr_add_cbs(data):
    cb_queue.put(data)


async def process_cb_task(event: asyncio.Event, backend_cb):
    await event.wait()
    try:
        if backend_cb is not None:
            backend_cb.close()
    except Exception as e:
        logger.error(f"sending cb failed with exception {e}")

    # url = f"http://127.0.0.1:{app_config.SHARED_SERVICE_PORT}/reporter/cb"
    # try:
    #     async with httpx.AsyncClient() as client:
    #         resp = await client.post(
    #             url=url,
    #             content=pickle.dumps(data),
    #             headers={'Content-Type': 'application/octet-stream'}
    #         )
    #         if not resp.is_success:
    #             logger.error(f"sending cb failed with status {resp.status_code}: {resp.text}")
    # except Exception as e:
    #     logger.error(f"sending cb failed with exception {e}")
