import simplejson as json
import os
import requests
import threading
import time
import traceback

from enum import Enum
from langchain_core.messages import get_buffer_string
from multiprocessing import Process, Queue
from pydantic import BaseModel
from typing import Dict, Any
from urllib.parse import urljoin
from uuid import UUID

from common.types.callback_handler import (
    B<PERSON><PERSON><PERSON><PERSON>nco<PERSON>,
    BackendReporterQueueCallbackHandler,
    LogCallbackHandler,
    DebugLogCallbackHandler,
)
from common.logging.logger import get_logger
from common.trace import tracer
from common.trace.langfuse import get_langfuse_callback
from common.types.base import CB_CLOSE_MARK, ParamsExtractStage
from config import app_config
from nl2document.common.models.model import insert_or_update_request_stage_info
from nl2agent.types import ToolType

logger = get_logger(__name__)


class ProgressRunStatus(Enum):
    NULL = 0
    SUCCEED = 1
    FAILED = 2
    NEED_MANUAL_SELECT = 3


class ProgressStatus(BaseModel):
    task_id: str = None
    nl2intent: ProgressRunStatus = ProgressRunStatus.NULL
    nl2intent_msg: str = ""
    nl2metric_time_query: ProgressRunStatus = ProgressRunStatus.NULL
    nl2metric_time_query_msg: str = ""
    nl2metric_metrics: ProgressRunStatus = ProgressRunStatus.NULL
    nl2metric_metrics_msg: str = ""
    nl2metric_group_bys: ProgressRunStatus = ProgressRunStatus.NULL
    nl2metric_group_bys_msg: str = ""
    nl2metric_order_bys: ProgressRunStatus = ProgressRunStatus.NULL
    nl2metric_order_bys_msg: str = ""
    nl2metric_where: ProgressRunStatus = ProgressRunStatus.NULL
    nl2metric_where_msg: str = ""
    nl2meta: ProgressRunStatus = ProgressRunStatus.NULL
    nl2meta_msg: str = ""
    attr_analysis_time: ProgressRunStatus = ProgressRunStatus.NULL
    attr_analysis_time_msg: str = ""
    attr_analysis_param: ProgressRunStatus = ProgressRunStatus.NULL
    attr_analysis_param_msg: str = ""

    nl2agent_build: ProgressRunStatus = ProgressRunStatus.NULL
    nl2agent_build_msg: str = ""
    nl2agent_delta_builds: list = []
    nl2agent_steps_list: list = []
    nl2agent_status: list = []

    close: bool = False

    def to_dict(self):
        return {
            "task_id": self.task_id,
            "nl2intent": self.nl2intent.value,
            "nl2intent_msg": self.nl2intent_msg,
            "nl2metric_time_query": self.nl2metric_time_query.value,
            "nl2metric_time_query_msg": self.nl2metric_time_query_msg,
            "nl2metric_metrics": self.nl2metric_metrics.value,
            "nl2metric_metrics_msg": self.nl2metric_metrics_msg,
            "nl2metric_group_bys": self.nl2metric_group_bys.value,
            "nl2metric_group_bys_msg": self.nl2metric_group_bys_msg,
            "nl2metric_order_bys": self.nl2metric_order_bys.value,
            "nl2metric_order_bys_msg": self.nl2metric_order_bys_msg,
            "nl2metric_where": self.nl2metric_where.value,
            "nl2metric_where_msg": self.nl2metric_where_msg,
            "nl2meta": self.nl2meta.value,
            "nl2meta_msg": self.nl2meta_msg,
            "attr_analysis_time": self.attr_analysis_time.value,
            "attr_analysis_time_msg": self.attr_analysis_time_msg,
            "attr_analysis_param": self.attr_analysis_param.value,
            "attr_analysis_param_msg": self.attr_analysis_param_msg,
            "nl2agent_build": self.nl2agent_build.value,
            "nl2agent_build_msg": self.nl2agent_build_msg,
            "nl2agent_delta_builds": self.nl2agent_delta_builds,
            "nl2agent_steps_list": self.nl2agent_steps_list,
            "nl2agent_status": self.nl2agent_status,
            "close": self.close,
        }


queue: Queue = None
remote_queue: Queue = None
reporter_process: Process = None
agent_delta_builds_types, agent_steps_list_types = ToolType.report_values()


def get_queue():
    return queue


def set_remote_queue(queue):
    global remote_queue
    remote_queue = queue


def get_reporter_cb(trace_id, task_id, trace_name, user_id):
    if remote_queue is not None:
        return BackendReporterQueueCallbackHandler(
            trace_id, task_id, trace_name, user_id, remote_queue
        )
    elif queue is not None:
        return BackendReporterQueueCallbackHandler(
            trace_id, task_id, trace_name, user_id, queue
        )
    else:
        return None


def post_progress_status(trace_id, task_id, progress_status: ProgressStatus):
    if not app_config.ASK_BI_HOST:
        raise ValueError("ASK_BI_HOST is not set")
    header = {tracer.TRACE_ID_KEY: trace_id, "Content-Type": "application/json"}
    progress_status.task_id = task_id
    data = progress_status.to_dict()
    url = urljoin(
        app_config.ASK_BI_HOST,
        f"api/chats/chat-progress-callback?taskId={task_id}",
    )
    if progress_status.close:
        logger.info(f"start post_progress_status, url: {url}, data {data}")

    try:
        response = requests.post(url, headers=header, json=data, timeout=10)
        if response.json().get("code", 0) != 0:
            logger.error(
                f"post_progress_status response invalid: {response.text}, url: {url}"
            )
    except requests.exceptions.Timeout:
        logger.error(f"post_progress_status timeout, url: {url}")
    except Exception as e:
        logger.error(f"post_progress_status failed {e}, url: {url}")


def report_nl2document_progress_status_end(
    trace_id, task_id, uuid_to_stages, progress_status, *args, **kwargs
):
    uuid = kwargs.get("run_id", None)
    stage_name = uuid_to_stages.get(uuid, None)
    if stage_name is None:
        return
    if stage_name == ParamsExtractStage.QUERY_MEETING:
        insert_or_update_request_stage_info(trace_id, "complete")


def report_progress_status_close(trace_id, task_id, progress_status: ProgressStatus):
    if not app_config.ENABLE_PROGRESS_STATUS:
        return
    if not task_id:
        return
    progress_status.close = True
    post_progress_status(trace_id, task_id, progress_status)


def _get_msg_from_args(args):
    if not args:
        return ""
    msg = args[0]
    if msg is None:
        return ""
    if isinstance(msg, str):
        return msg
    elif isinstance(msg, Exception):
        return str(msg)
    return json.dumps(
        msg, indent=2, ensure_ascii=False, cls=BIJsonEncoder, ignore_nan=True
    )


def report_progress_status_end(
    run_status,
    trace_id,
    task_id,
    uuid_to_stages,
    progress_status: ProgressStatus,
    *args,
    **kwargs,
):
    if not app_config.ENABLE_PROGRESS_STATUS:
        return
    if not task_id:
        return
    uuid = kwargs.get("run_id", None)
    stage_name = uuid_to_stages.get(uuid, None)
    if stage_name is None:
        return

    if stage_name == "nl2intent":
        progress_status.nl2intent = run_status
        progress_status.nl2intent_msg = _get_msg_from_args(args)
    elif stage_name == "query_metrics":
        progress_status.nl2metric_metrics = run_status
        progress_status.nl2metric_metrics_msg = _get_msg_from_args(args)
    elif stage_name == "query_groupbys":
        progress_status.nl2metric_group_bys = run_status
        progress_status.nl2metric_group_bys_msg = _get_msg_from_args(args)
    elif stage_name == "query_time":
        progress_status.nl2metric_time_query = run_status
        progress_status.nl2metric_time_query_msg = _get_msg_from_args(args)
    elif stage_name == "query_where":
        progress_status.nl2metric_where = run_status
        progress_status.nl2metric_where_msg = _get_msg_from_args(args)
    elif stage_name == "query_orderbys":
        progress_status.nl2metric_order_bys = run_status
        progress_status.nl2metric_order_bys_msg = _get_msg_from_args(args)
    elif stage_name == "nl2meta":
        progress_status.nl2meta = run_status
        progress_status.nl2meta_msg = _get_msg_from_args(args)
    elif stage_name == "attr_analysis_time":
        progress_status.attr_analysis_time = run_status
        progress_status.attr_analysis_time_msg = _get_msg_from_args(args)
    elif stage_name == "attr_analysis_param":
        progress_status.attr_analysis_param = run_status
        progress_status.attr_analysis_param_msg = _get_msg_from_args(args)
    elif stage_name == "agent_build":
        progress_status.nl2agent_build = run_status
        progress_status.nl2agent_build_msg = _get_msg_from_args(args)
    elif stage_name in agent_delta_builds_types:
        progress_status.nl2agent_delta_builds.append(
            [run_status.value, stage_name, _get_msg_from_args(args)]
        )
    elif stage_name in agent_steps_list_types:
        progress_status.nl2agent_steps_list.append(
            [run_status.value, _get_msg_from_args(args)]
        )
    elif stage_name == "agent_reporter":
        progress_status.nl2agent_status = args[0]
    post_progress_status(trace_id, task_id, progress_status)
    return


def report_progress_status_begin(uuid_to_stages, trace_id, task_id, *args, **kwargs):
    if not task_id:
        return
    chain_name = kwargs.get("name", None)
    uuid = kwargs.get("run_id", None)
    if chain_name is not None:
        if (
            chain_name == "parse_intent_response"
            or chain_name == "parse_intent_by_tag_response"
        ):
            uuid_to_stages[uuid] = "nl2intent"
        elif chain_name == "query_metrics_verify":
            uuid_to_stages[uuid] = "query_metrics"
        elif chain_name == "query_groupbys_verify":
            uuid_to_stages[uuid] = "query_groupbys"
        elif chain_name == "parse_time_query_response":
            uuid_to_stages[uuid] = "query_time"
        elif chain_name == "query_where_verify":
            uuid_to_stages[uuid] = "query_where"
        elif chain_name == "query_orderbys_verify":
            uuid_to_stages[uuid] = "query_orderbys"
        elif chain_name == "nl2meta":
            uuid_to_stages[uuid] = "nl2meta"
        elif chain_name == "attr_analysis_time":
            uuid_to_stages[uuid] = "attr_analysis_time"
        elif chain_name == "attr_analysis_param":
            uuid_to_stages[uuid] = "attr_analysis_param"
        elif chain_name == "agent_build":
            uuid_to_stages[uuid] = "agent_build"
        elif chain_name == "agent_reporter":
            uuid_to_stages[uuid] = "agent_reporter"
        elif chain_name in agent_delta_builds_types:
            uuid_to_stages[uuid] = chain_name
        elif chain_name in agent_steps_list_types:
            uuid_to_stages[uuid] = chain_name
        elif chain_name == ParamsExtractStage.QUERY_MEETING:
            uuid_to_stages[uuid] = chain_name
            insert_or_update_request_stage_info(trace_id, "create")
        elif chain_name in [
            ParamsExtractStage.NL2MEETING_PARAMS,
            ParamsExtractStage.NL2DOCUMENT_RETRIEVE_NODES,
            ParamsExtractStage.NL2DOCUMENT_SYNTHESIZE_ANSWER,
        ]:
            uuid_to_stages[uuid] = chain_name
            insert_or_update_request_stage_info(trace_id, chain_name)
    return


def reporter(queue):
    cleaner_thread = threading.Thread(
        target=cleaner_task, args=(data_holder,), daemon=True
    )
    cleaner_thread.start()

    print("Reporter processing started")

    while True:
        try:
            trace_id, user_id, task_id, name, message, args, kwargs = queue.get()

            if message == "exit":
                break

            data_holder.thread_safe_operation(
                trace_id, user_id, task_id, name, message, args, kwargs
            )
        except Exception as e:
            logger.error(
                f"an exception occurred in reporter processing: {e}, traceback: {traceback.format_exc()}"
            )


def backend_stage_reporter_monitor_start():
    global reporter_process
    if reporter_process is not None and reporter_process.is_alive():
        return
    if reporter_process is not None:
        logger.error(
            f"backend reporter {reporter_process.pid} exited code {reporter_process.exitcode}. Restarting..."
        )
    global queue
    queue = Queue()
    reporter_process = Process(target=reporter, name="ask_bi_reporter", args=(queue,))
    reporter_process.start()
    logger.info(
        f"backend reporter {reporter_process.pid} start succeed is_alive {reporter_process.is_alive()}"
    )


class MemDataHolder:
    def __init__(self) -> None:
        self.__data: Dict[str, Any] = {}
        self.__holder_lock: threading.Lock = threading.Lock()

    def get_data(self, key: str):
        with self.__holder_lock:
            if key in self.__data:
                return self.__data[key]
            else:
                return None

    def get_data_without_lock(self, key: str):
        if key in self.__data:
            return self.__data[key]
        else:
            return None

    def add_data(self, key, value):
        with self.__holder_lock:
            self.__data[key] = value

    def add_data_without_lock(self, key, value):
        self.__data[key] = value

    def add_data_if_absent(self, key, value):
        with self.__holder_lock:
            if key in self.__data:
                return self.__data[key]
            else:
                self.__data[key] = value
                return None

    def cleanup(self):
        def remove_condition(value):
            ts, *rest = value
            current_timestamp = time.time()
            if current_timestamp - ts > 600:
                return True
            return False

        with self.__holder_lock:
            keys_to_clean = [
                key for key in self.__data if remove_condition(self.__data[key])
            ]
            for key in keys_to_clean:
                del self.__data[key]

    def thread_safe_operation(
        self,
        trace_id: str,
        user_id: str,
        task_id: str,
        callback_name: str,
        message: str,
        args: Any,
        kwargs: Any,
    ) -> None:
        with self.__holder_lock:
            data_tuple = data_holder.get_data_without_lock(trace_id)
            if data_tuple is None:
                callbacks = []
                if app_config.USE_ASYNC_LANGCHAIN_LOG:
                    callbacks.append(
                        LogCallbackHandler(
                            id=trace_id, host=app_config.CLUSTER_ID, user_id=user_id
                        )
                    )
                langfuse_cb = get_langfuse_callback(
                    trace_id=trace_id,
                    trace_name=callback_name,
                    user_id=user_id,
                )
                if langfuse_cb != None:
                    callbacks.append(langfuse_cb)
                if app_config.ENABLE_LANGCHAIN_DEBUG_LOGGER:
                    callbacks.append(
                        DebugLogCallbackHandler(id=trace_id, trace_name=callback_name)
                    )
                progress_status = ProgressStatus()
                uuid_to_stages: Dict[UUID, str] = {}
            else:
                # unpack callback
                _, callbacks, progress_status, uuid_to_stages = data_tuple

            for callback in callbacks:
                try:
                    data_holder._run_callback(callback, message, args, kwargs)
                except Exception as e:
                    logger.error(
                        f"{callback.name}: trace_id {trace_id}, task_id {task_id}, name {callback_name}, message {message}, exception {e}"
                    )

            if message == "on_chain_start":
                report_progress_status_begin(
                    uuid_to_stages, trace_id, task_id, *args, **kwargs
                )
            elif message == "on_chain_end":
                report_progress_status_end(
                    ProgressRunStatus.SUCCEED,
                    trace_id,
                    task_id,
                    uuid_to_stages,
                    progress_status,
                    *args,
                    **kwargs,
                )
                if app_config.ENABLE_NL2DOCUMENT:
                    report_nl2document_progress_status_end(
                        trace_id,
                        task_id,
                        uuid_to_stages,
                        progress_status,
                        *args,
                        **kwargs,
                    )
            elif message == "on_chain_error":
                report_progress_status_end(
                    ProgressRunStatus.FAILED,
                    trace_id,
                    task_id,
                    uuid_to_stages,
                    progress_status,
                    *args,
                    **kwargs,
                )
            elif message == CB_CLOSE_MARK:
                report_progress_status_close(trace_id, task_id, progress_status)
            current_timestamp = time.time()
            data_holder.add_data_without_lock(
                trace_id,
                (current_timestamp, callbacks, progress_status, uuid_to_stages),
            )

    def _run_callback(self, callback, message, args, kwargs):
        if callback is None:
            return
        if message == "on_agent_finish":
            callback.on_agent_finish(*args, **kwargs)
        elif message == "on_chain_start":
            callback.on_chain_start(*args, **kwargs)
        elif message == "on_chat_model_start":
            try:
                callback.on_chat_model_start(*args, **kwargs)
            except Exception as e:
                # this code is copied from langchain_core/callbacks/manager.py
                message_strings = [get_buffer_string(m) for m in args[1]]
                callback.on_llm_start(
                    args[0],
                    message_strings,
                    *args[2:],
                    **kwargs,
                )
        elif message == "on_llm_start":
            callback.on_llm_start(*args, **kwargs)
        elif message == "on_agent_action":
            callback.on_agent_action(*args, **kwargs)
        elif message == "on_retriever_start":
            callback.on_retriever_start(*args, **kwargs)
        elif message == "on_chain_end":
            callback.on_chain_end(*args, **kwargs)
        elif message == "on_chain_error":
            callback.on_chain_error(*args, **kwargs)
        elif message == "on_retriever_end":
            callback.on_retriever_end(*args, **kwargs)
        elif message == "on_tool_start":
            callback.on_tool_start(*args, **kwargs)
        elif message == "on_retriever_error":
            callback.on_retriever_error(*args, **kwargs)
        elif message == "on_tool_end":
            callback.on_tool_end(*args, **kwargs)
        elif message == "on_tool_error":
            callback.on_tool_error(*args, **kwargs)
        elif message == "on_llm_end":
            callback.on_llm_end(*args, **kwargs)
        elif message == "on_llm_error":
            callback.on_llm_error(*args, **kwargs)
        elif message != CB_CLOSE_MARK:
            logger.error(f"Unknown message type: {message}")


data_holder = MemDataHolder()


def cleaner_task(holder: MemDataHolder):
    while True:
        holder.cleanup()
        time.sleep(2)
