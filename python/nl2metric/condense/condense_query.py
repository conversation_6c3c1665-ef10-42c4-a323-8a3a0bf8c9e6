from typing import List

from jinja2 import Template
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import Runna<PERSON>, <PERSON>nableLambda, RunnableConfig
from langchain_core.runnables.utils import Input, Output

from common.llm.general import create_chat_model, llm_predict
from common.logging.logger import get_logger
from common.prompt_selector.prompt_selector_base import PromptSelectorBase
from common.prompt_selector.prompts.gen_condense_query import PROMPT_DIFF
from common.types import Message
from common.types.base import ParamsExtractStage, CHAIN_META, ChainMeta, ChainRuntime
from common.types.exceptions import QuestionNotSupported
from common.utils.json_utils import extract_json_from_string
from condense.condense_query_v2 import get_condense_query_v2
from config.project_config import get_project_config
from metastore import get_metastore
from nl2metric.few_shots import JINGFEN_BI, BAOWU_PROJECT_NAME
from nl2metric.service import replace_keywords

logger = get_logger(__name__)


def condense_query_postprocess(response: str, config: RunnableConfig):
    data = extract_json_from_string(response, "condense_query_postprocess")
    condense_question = data.get("query")
    config[CHAIN_META][ChainMeta.RUN_TIME][
        ChainRuntime.CONDENSE_QUESTION
    ] = condense_question
    return condense_question


def condense_query(
    messages: List[Message],
    general_chain: Runnable[Input, Output],
    general_chain_v2: Runnable[Input, Output],
    config: RunnableConfig,
):
    project_name = config[CHAIN_META][ChainMeta.PROJECT_NAME]
    model_name = config[CHAIN_META][ChainMeta.MODEL_NAME]
    if not messages:
        raise RuntimeError(
            f"project {project_name} model {model_name} no messages for condense query"
        )
    if messages[-1].role != "user" or (not messages[-1].content):
        raise RuntimeError(f"condense_query_v2 query msg err, messages {messages}")

    project_config = get_project_config(project_name, model_name)
    condense_query_params = project_config.special_subchain_params(
        ParamsExtractStage.CONDENSE_QUERY
    )
    use_v2 = False
    if condense_query_params:
        use_v2 = condense_query_params["type"] == ParamsExtractStage.CONDENSE_QUERY_V2

    if len(messages) == 1 or project_config.no_condense:
        query = messages[-1].content
    elif use_v2:
        query = general_chain_v2.invoke(messages, config=config)
    else:
        query = general_chain.invoke(messages, config=config)
    # 问题重写
    # 从 query 中去掉”如何“2个字
    query = query.replace("如何", "")
    query = query.replace("上个月", "上月")

    if project_name == JINGFEN_BI:
        logger.info(f"before replace key words, input: {input}")
        query = replace_keywords(query)
        logger.info(f"after replace key words, args: {input}")

    if project_name == BAOWU_PROJECT_NAME:
        project_id = config[CHAIN_META][ChainMeta.PROJECT_ID]
        metastore = get_metastore(project_id)
        metrics = metastore.list_metrics_by_model_name(None)
        if "盈利" in query:
            query = query.replace("盈利", "利润总额大于0")
        if "亏损" in query:
            query = query.replace("亏损", "利润总额小于0")
        if any(query == m.label for m in metrics.values()):
            query = query + "是多少"

    # check keyword blacklist
    keyword_black_list = project_config.question_keyword_black_list
    if keyword_black_list:
        for keyword in keyword_black_list:
            if keyword in query:
                raise QuestionNotSupported(
                    f"project {project_name} model {model_name} does not support "
                    + f"{keyword}, query is {query}"
                )

    config[CHAIN_META][ChainMeta.RUN_TIME][ChainRuntime.QUESTION] = query
    return {"question": query}


# invoke with messages: List[Message]
def call_condense_query(
    prompt_selector: PromptSelectorBase, model_type: str
) -> Runnable[Input, Output]:
    general_chain = (
        RunnableLambda(
            lambda messages: {"history": messages[:-1], "query": messages[-1].content},
            name="condense_query_preprocess",
        )
        | RunnableLambda(
            prompt_selector.gen_prompt,
            name="PromptSelectorBase.gen_prompt:" + ParamsExtractStage.CONDENSE_QUERY,
        ).bind(stage=ParamsExtractStage.CONDENSE_QUERY)
        | create_chat_model(model_type)
        | StrOutputParser()
        | RunnableLambda(condense_query_postprocess, name="condense_query_postprocess")
    )
    general_chain.name = "llm_condense_query"

    general_chain_v2 = get_condense_query_v2(prompt_selector)

    chain = RunnableLambda(condense_query, name=ParamsExtractStage.CONDENSE_QUERY).bind(
        general_chain=general_chain,
        general_chain_v2=general_chain_v2,
    )
    return chain


def diff_nl(nls: List[str], model_type: str) -> str:
    if len(nls) == 1:
        return "Only single nl"
    prompt = Template(PROMPT_DIFF).render(nls=nls)
    response = llm_predict(prompt, model_type)
    logger.info("Prompt: \n%s", prompt)
    logger.info("Response: \n%s", response)
    return response
    # data = extract_json_from_string(response)
    # return data.get("query")
