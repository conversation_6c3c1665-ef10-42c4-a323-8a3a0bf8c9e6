import json

from langchain_core.runnables import <PERSON><PERSON><PERSON>, RunnableLambda, RunnableConfig
from langchain_core.runnables.utils import Input, Output
from pydantic import BaseModel
from typing import List

from common.llm.general import create_chat_model_in_chain, create_chat_model
from common.parser.custom_pydantic import CustomPydanticOutputParser
from common.prompt_selector.prompt_selector_base import PromptSelectorBase
from common.types import MessageWithExtraInfo
from common.types.base import ParamsExtractStage, CHAIN_META, ChainMeta, ChainRuntime
from config.app_config import MODEL_TYPE_BAOWU_GLM_4_9B


class CondenseQueryV2Result(BaseModel):
    new_query: List[str]


def condense_query_v2_preprocess(messages: List[MessageWithExtraInfo]):
    # only need previously condensed question,
    # just one is enough, it contains all the info we need
    query = messages[-1].content
    i = len(messages) - 2
    while i >= 0:
        message = messages[i]
        # find the last assistant msg
        if message.role != "assistant":
            i -= 1
            continue
        content_list = json.loads(message.content)
        content = message.content if len(content_list) <= 15 else "[]"
        pre_condense = message.get_extra_info(ChainRuntime.CONDENSE_QUESTION)
        if pre_condense:
            return {
                "pre_condense": pre_condense,
                "assistant": content,
                "question": query,
            }
        elif i > 0:
            # first question does not have condense query
            # pre_condense is the user msg before the current msg
            user_message = messages[i - 1]
            if user_message.role == "user":
                if not user_message.content:
                    raise RuntimeError(
                        f"found empty user content, user_message {user_message}, messages {messages}"
                    )
                return {
                    "pre_condense": user_message.content,
                    "assistant": content,
                    "question": query,
                }
        i -= 1

    raise RuntimeError(
        f"condense_query_v2 cannot find pre_condense, messages {messages}"
    )


def condense_query_v2_postprocess(
    result: CondenseQueryV2Result, config: RunnableConfig
):
    if result.new_query:
        for tmp in result.new_query:
            condense_query = tmp.strip()
            if not condense_query:
                continue
            config[CHAIN_META][ChainMeta.RUN_TIME][
                ChainRuntime.CONDENSE_QUESTION
            ] = condense_query
            return condense_query
    raise RuntimeError(f"condense_query_v2 got nothing, new_query {result.new_query}")


def get_condense_query_v2(
    prompt_selector: PromptSelectorBase,
) -> Runnable[Input, Output]:
    chain = (
        RunnableLambda(
            condense_query_v2_preprocess,
            name="condense_query_v2_preprocess",
        )
        | RunnableLambda(
            prompt_selector.gen_prompt,
            name="PromptSelectorBase.gen_prompt:"
            + ParamsExtractStage.CONDENSE_QUERY_V2,
        ).bind(stage=ParamsExtractStage.CONDENSE_QUERY_V2)
        | RunnableLambda(create_chat_model_in_chain).bind(
            stage=ParamsExtractStage.CONDENSE_QUERY
        )
        | CustomPydanticOutputParser(pydantic_object=CondenseQueryV2Result)
        | RunnableLambda(
            condense_query_v2_postprocess, name="condense_query_v2_postprocess"
        )
    )
    chain.name = "llm_condense_query_v2"
    return chain
